package com.xhgj.srm.payload;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> @date 2023/6/4
 * 对账单导出payload
 */
@Data
public class OrderAccountPayload {

  @ApiModelProperty("用户id")
  private String userId;
  @ApiModelProperty("对账单id")
  private List<String> accountIds;

  @ApiModelProperty("提交时间范围 开始")
  private Long startCommitTime;

  @ApiModelProperty("提交时间范围 结束")
  private Long endCommitTime;

  @ApiModelProperty("审核时间范围 开始")
  private Long startAssessTime;

  @ApiModelProperty("审核时间范围 开始")
  private Long endAssessTime;

  @ApiModelProperty("查询方案 id ")
  private String schemeId;

  @ApiModelProperty("关联平台")
  private String platforms;

  @ApiModelProperty("对账单号")
  private String accountNo;

  @ApiModelProperty("对账单状态")
  private String accountStatus;

  @ApiModelProperty("提交人")
  private String createSupplier;

  @ApiModelProperty("对账开票状态")
  private String accountOpenInvoiceStatus;
}
