package com.xhgj.srm.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class SrmProductInfoDTO {

  @ApiModelProperty("物料编码")
  private String code;

  @ApiModelProperty("名称")
  private String name;

  @ApiModelProperty("品牌")
  private String brandName;

  @ApiModelProperty("规格型号")
  private String manuCode;

  @ApiModelProperty("单位名称")
  private String unitName;

  @ApiModelProperty("单位编码")
  private String unit;

  @ApiModelProperty("物料描述")
  private String desc;

  @ApiModelProperty("结算价")
  private BigDecimal transferPrice;

  @ApiModelProperty("加价系数")
  private BigDecimal markupCoefficient;

  @ApiModelProperty("税率")
  private BigDecimal productTaxRate;
  /**
   * 报关关税
   */
  @ApiModelProperty("关税")
  private BigDecimal tariff;

}
