package com.xhgj.srm.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ImportOrderFilingDetailDTO {

    @ApiModelProperty("物料编码")
    private String code;
    @ApiModelProperty("品牌")
    private String brand;
    @ApiModelProperty("商品名称")
    private String name;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("数量")
    private BigDecimal num;
    @ApiModelProperty("点单价")
    private BigDecimal price;

}
