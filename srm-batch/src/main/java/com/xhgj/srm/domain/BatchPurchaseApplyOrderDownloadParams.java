package com.xhgj.srm.domain;

import com.xhgj.srm.jpa.entity.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class BatchPurchaseApplyOrderDownloadParams {
  @ApiModelProperty("申请单号")
  @NotEmpty(message = "请选择申请单")
  private List<String> applyNoList;
  @ApiModelProperty("采购员id")
  private String purchaserId;
  @ApiModelProperty("订货状态（0代表不可订货，1代表可订货）")
  private String orderGoodsState;
  @ApiModelProperty("取消状态（0代表未取消，1代表已取消）")
  private String cancellationState;
  private User user;


}
