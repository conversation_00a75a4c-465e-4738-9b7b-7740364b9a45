package com.xhgj.srm.batch.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.batch.factory.MapStructFactory;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.dto.OAUserInfoDTO;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.PaymentApplyTypeEnums;
import com.xhgj.srm.common.enums.PaymentAuditStateEnum;
import com.xhgj.srm.common.enums.ShortMessageEnum;
import com.xhgj.srm.common.enums.VoucherPaymentStateEnum;
import com.xhgj.srm.jpa.dao.OrderFilingDetailDao;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.OrderFiling;
import com.xhgj.srm.jpa.entity.OrderFilingDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyRecord;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.repository.OrderFilingRepository;
import com.xhgj.srm.jpa.repository.SupplierUserRepository;
import com.xhgj.srm.request.dto.oms.OMSFilingSheetAddParam.OMSProduct;
import com.xhgj.srm.request.service.third.xhgj.XhgjMPMRequest;
import com.xhgj.srm.request.service.third.xhgj.XhgjPersonRequest;
import com.xhgj.srm.request.service.third.xhgj.XhgjSMSRequest;
import com.xhgj.srm.service.FinancialVouchersService;
import com.xhgj.srm.service.ShareOrderFilingService;
import com.xhgj.srm.service.SharePurchaseInfoRecordService;
import com.xhgj.srm.service.impl.DingAssessServiceImpl;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.ExcelUtil;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/25 10:47
 */
@RestController
@RequestMapping("ding")
@Slf4j
public class DingRepairController {

  @Resource
  private ApplicationContext applicationContext;

  @Resource
  private OrderFilingRepository orderFilingRepository;
  @Resource
  private OrderFilingDetailDao orderFilingDetailDao;
  @Resource
  private ShareOrderFilingService shareOrderFilingService;
  @Resource
  private SupplierUserRepository supplierUserRepository;
  @Resource
  private XhgjSMSRequest xhgjSMSRequest;
  @Resource
  private XhgjPersonRequest xhgjPersonRequest;
  @Resource
  private XhgjMPMRequest xhgjMPMRequest;
  @Resource
  private DingAssessServiceImpl dingAssessService;
  @Resource
  SharePurchaseInfoRecordService sharePurchaseInfoRecordService;


  private static final List<String> REPAIR_TITLE =
      CollUtil.toList("钉钉通知类型*", "钉钉通知Id", "审核通过(0/1)*", "审批意见*", "审核人名称*", "唯一值Id*");

  /**
   * 钉钉统计类型，报备单
   */
  public static final String type_1 = "1";

  /**
   * 钉钉统计类型，供应商审核
   */
  public static final String type_2 = "2";

  /**
   * 钉钉统计类型，采购价格库审核
   */
  public static final String type_3 = "3";

  @ApiOperation("导入修复提款申请对应的财务凭证")
  @PostMapping("repair")
  public ResultBean<Boolean> repairDrawPaymentFinancialVoucher(MultipartFile file)
      throws IOException {
    if (file == null || file.isEmpty()) {
      throw new CheckException("文件为空！");
    }
    DingRepairController proxy = applicationContext.getBean(DingRepairController.class);
    proxy.repair(file.getInputStream());
    return new ResultBean<>(true);
  }

  @Transactional(rollbackFor = Exception.class)
  public void repair(InputStream inputStream) {
    ExcelUtil.simpleReadExcel(inputStream, 0, 0, REPAIR_TITLE, 1, (row) -> {
      String type = row.getCell(0).getStringCellValue();
      String dingId = row.getCell(1).getStringCellValue();
      String pass = row.getCell(2).getStringCellValue();
      String remark = row.getCell(3).getStringCellValue();
      String approvalMan = row.getCell(4).getStringCellValue();
      String uniqueId = row.getCell(5).getStringCellValue();
      if (StrUtil.isBlank(type)) {
        return 0;
      }
      switch (type) {
        case type_1:
          repairFiling(uniqueId, pass, remark, approvalMan);
          break;
        case type_2:
          repairSupplierAudit(dingId, pass, remark, approvalMan);
          break;
        case type_3:
          repairPurchasePriceAudit(dingId, pass, remark, approvalMan);
          break;
        default:
          throw new CheckException("不支持的类型");
      }
      return 1;
    });
  }

  private void repairPurchasePriceAudit(String dingId, String pass, String remark, String approvalMan) {
    ApprovalResult approvalResult = new ApprovalResult();
    approvalResult.setProcessInstanceId(dingId);
    approvalResult.setStaffId(approvalMan);
    approvalResult.setRemark(remark);
    if ("1".equals(pass)) {
      sharePurchaseInfoRecordService.doPassHandle(approvalResult);
    } else {
      sharePurchaseInfoRecordService.doRejectHandle(approvalResult);
    }
  }

  private void repairSupplierAudit(String dingId, String pass, String remark, String approvalMan) {
    ApprovalResult approvalResult = new ApprovalResult();
    approvalResult.setProcessInstanceId(dingId);
    approvalResult.setStaffId(approvalMan);
    approvalResult.setRemark(remark);
    if ("1".equals(pass)) {
      dingAssessService.doPassHandle(approvalResult);
    } else {
      dingAssessService.doRejectHandle(approvalResult);
    }
  }

  private void repairFiling(String uniqueId, String pass, String remark, String approvalMan) {
    ApprovalResult approvalResult = new ApprovalResult();
    approvalResult.setProcessInstanceId(uniqueId);
    approvalResult.setStaffId(approvalMan);
    approvalResult.setRemark(remark);
    if ("1".equals(pass)) {
      doPassHandle(approvalResult);
    } else {
      doRejectHandle(approvalResult);
    }
  }

  private void doPassHandle(ApprovalResult approvalResult) {
    String uniqueId = approvalResult.getProcessInstanceId();
    String dingApprovalOpinion = approvalResult.getRemark();
    String approvalMan = approvalResult.getStaffId();
    OrderFiling orderFiling =
        orderFilingRepository.findByFilingNoAndState(uniqueId, Constants.STATE_OK)
            .orElseThrow(() -> new CheckException("报备单不存在"));
    if (!Objects.equals(orderFiling.getFilingState(), Constants_order.FILING_STATE_IN_REVIEW)) {
      throw new CheckException("报备单不在审核中状态");
    }
    //审批人
    if (StrUtil.isNotBlank(approvalMan)) {
      orderFiling.setDingApprovalMan(approvalMan);
    }
    //修改状态
    orderFiling.setFilingState(Constants_order.FILING_STATE_ING);
    orderFiling.setDingApprovalOpinion(dingApprovalOpinion);
    orderFilingRepository.save(orderFiling);
    //商品报备同步至OMS
    if (Objects.equals(orderFiling.getFilingType(), Constants.FILING_TYPE_PRODUCT)) {
      if (StrUtil.isBlank(orderFiling.getReceiveAddressName())) {
        throw new CheckException(orderFiling.getOrderNo() + "商品报备单客户收货地址数据错误");
      }
      List<OrderFilingDetail> orderFilingDetails =
          orderFilingDetailDao.getOrderFilingDetailListByFiling(orderFiling.getId());
      if (CollUtil.isEmpty(orderFilingDetails)) {
        throw new CheckException("脏数据！商品报备单未找到相关联的商品");
      }
      List<FilingParamProductDTO> filingParamProductDTOS =
          orderFilingDetails.stream().filter(Objects::nonNull).map(FilingParamProductDTO::new)
              .collect(Collectors.toList());
      updateNoPushPlatform(orderFilingDetails, orderFiling.getType());
      // 统一同步至OMS
      List<OMSProduct> productList = filingParamProductDTOS.stream().map(
          MapStructFactory.INSTANCE::toOMSProduct).collect(Collectors.toList());
      shareOrderFilingService.syncFilingToOms(orderFiling);
    } else {
      //订单报备同步至OMS
      shareOrderFilingService.syncFilingToOms(orderFiling);
    }
    //发送短信通知
    if (StrUtil.isNotBlank(orderFiling.getCreateMan())) {
      Optional<SupplierUser> supplierUserOptional =
          supplierUserRepository.findById(orderFiling.getCreateMan());
      supplierUserOptional.ifPresent(
          supplierUser -> xhgjSMSRequest.sendSms(ShortMessageEnum.FILING_VERIFY_PASS,
              supplierUser.getMobile(), new HashMap<String, String>() {{
                put("cocustomerName", orderFiling.getCustomer());
                put("filingNo", orderFiling.getFilingNo());
              }}));
    }
  }

  public void doRejectHandle(ApprovalResult approvalResult) {
    String uniqueId =  approvalResult.getProcessInstanceId();
    String dingApprovalOpinion = approvalResult.getRemark();
    String approvalMan = approvalResult.getStaffId();
    OrderFiling orderFiling =
        orderFilingRepository.findByFilingNoAndState(uniqueId, Constants.STATE_OK)
            .orElseThrow(() -> new CheckException("报备单不存在"));
    if (!Objects.equals(orderFiling.getFilingState(), Constants_order.FILING_STATE_IN_REVIEW)) {
      throw new CheckException("报备单不在审核中状态");
    }
    //查询oa用户信息
    if (StrUtil.isNotBlank(approvalMan)) {
      orderFiling.setDingApprovalMan(approvalMan);
    }
    orderFiling.setFilingState(Constants_order.FILING_STATE_REJECT);
    orderFiling.setDingApprovalOpinion(dingApprovalOpinion);
    orderFilingRepository.save(orderFiling);
    // 同步至OMS
    shareOrderFilingService.syncFilingToOms(orderFiling);
    //发送短信通知
    if (StrUtil.isNotBlank(orderFiling.getCreateMan())) {
      Optional<SupplierUser> supplierUserOptional =
          supplierUserRepository.findById(orderFiling.getCreateMan());
      supplierUserOptional.ifPresent(
          supplierUser -> xhgjSMSRequest.sendSms(ShortMessageEnum.FILING_VERIFY_REJECT,
              supplierUser.getMobile(), new HashMap<String, String>(){{
                put("cocustomerName", orderFiling.getCustomer());
                put("filingNo", orderFiling.getFilingNo());
              }}));
    }
  }


  public OAUserInfoDTO getOAUserInfoByDingTalkId(String dingTalkId) {
    Assert.notBlank(dingTalkId);
    return xhgjPersonRequest.findPersonInfoByDingTalkId(dingTalkId);
  }

  /**
   * 商品报备单删除物料禁止推送标记
   * @param orderFilingDetails 报备单详情
   * @param platformCode 下单平台code
   */
  private void updateNoPushPlatform(List<OrderFilingDetail> orderFilingDetails,
      String platformCode) {
    if (CollUtil.isEmpty(orderFilingDetails) || StrUtil.isBlank(platformCode)) {
      throw new CheckException("参数异常！");
    }
    orderFilingDetails =
        orderFilingDetails.stream().filter(Objects::nonNull).collect(Collectors.toList());
    for (OrderFilingDetail orderFilingDetail : orderFilingDetails) {
      xhgjMPMRequest.updateNoPushPlatform(orderFilingDetail.getCode(), ListUtil.of(platformCode));
    }
  }

  @Data
  public static class FilingParamProductDTO {

    @ApiModelProperty("报备单商品明细id(修改传入)")
    private String id;
    @ApiModelProperty("物料编码")
    @NotBlank(message = "请输入物料编码")
    private String code;
    @ApiModelProperty("品牌")
    private String brand;
    @ApiModelProperty("商品名称")
    private String name;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("数量")
    private BigDecimal num;
    @ApiModelProperty("点单价")
    private BigDecimal price;

    public FilingParamProductDTO(OrderFilingDetail orderFilingDetail) {
      this.code = orderFilingDetail.getCode();
      this.brand = orderFilingDetail.getBrand();
      this.name = orderFilingDetail.getName();
      this.model = orderFilingDetail.getModel();
      this.num = orderFilingDetail.getNum();
      this.price = orderFilingDetail.getPrice();
    }

  }


}
