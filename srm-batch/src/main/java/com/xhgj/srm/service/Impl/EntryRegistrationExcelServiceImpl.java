package com.xhgj.srm.service.Impl;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.PurchaseOrderInvoiceType;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationCooperationTypeEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationExportEnums;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationPaymentConditionEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationStatusEnum;
import com.xhgj.srm.common.map.TypeAwareMap;
import com.xhgj.srm.jpa.dao.EntryRegistrationDao;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.MissionDetail;
import com.xhgj.srm.jpa.entity.Platform;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.MissionDetailRepository;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import com.xhgj.srm.registration.repository.EntryRegistrationRepository;
import com.xhgj.srm.request.utils.UploadPartUtil;
import com.xhgj.srm.service.EntryRegistrationExcelService;
import com.xhgj.srm.service.ShareEntryRegistrationService;
import com.xhgj.srm.service.UserService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.upload.util.OssUtil;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EntryRegistrationExcelServiceImpl implements EntryRegistrationExcelService {
  @Resource
  private MissionRepository missionRepository;
  @Resource
  private MissionDetailRepository missionDetailRepository;
  @Resource private OssUtil ossUtil;
  @Resource
  private EntryRegistrationDao entryRegistrationDao;
  @Resource
  private UserService userService;
  @Resource
  private EntryRegistrationRepository entryRegistrationRepository;
  @Resource
  private ShareEntryRegistrationService shareEntryRegistrationService;
  @Resource
  private UploadPartUtil uploadPartUtil;



  @Override
  public void exportEntryRegistrationExcel(Mission mission, String params) {
    User user = Optional.ofNullable(userService.get(mission.getCreateManId())).orElseThrow(() -> {
      missionFail(mission, "未找到任务创建人");
      return CheckException.noFindException(User.class, mission.getCreateManId());
    });
    List<EntryRegistrationOrder> entryRegistrationOrders;
    SXSSFWorkbook workbook;
    try {
      JSONObject jsonObject = JSON.parseObject(params);
      TypeAwareMap typeAwareMap = TypeAwareMap.loadMap(jsonObject.getJSONObject("query"));
      Page<EntryRegistrationOrder> pageRef = entryRegistrationDao.getPageRef(typeAwareMap);
      entryRegistrationOrders = pageRef.getContent();

      EntryRegistrationExportEnums normalExport = EntryRegistrationExportEnums.NORMAL_EXPORT;
      XSSFWorkbook xssfWorkbook = null;
      try {
         xssfWorkbook = new XSSFWorkbook(IoUtil.toStream(HttpUtil.downloadBytes(ossUtil.buildOssFileUrl(normalExport.getTempFileUrl()))));
      } catch (Exception e) {
        log.error("导出入驻报备单异常,未找到对应的模板:" + normalExport.getTempFileUrl() + "异常信息为:"
            + ExceptionUtil.stacktraceToString(e, -1));
        missionFail(mission, "未找到模板，请联系管理员放置模板");
        throw new CheckException("未找到模板，请联系管理员放置模板");
      }
      XSSFSheet originSheet = xssfWorkbook.getSheetAt(0);
      workbook = new SXSSFWorkbook(xssfWorkbook);
      missionTotalRow(mission, entryRegistrationOrders.size());
      Sheet sheet = workbook.getSheetAt(0);
      // 批量获取入驻报备商家
      List<EntryRegistrationOrder> finalEntryRegistrationOrders = entryRegistrationOrders;
      shareEntryRegistrationService.batchGetEntryRegistrationInfo(entryRegistrationOrders, (entryId2Merchant, id2Platform) -> {
        // 写入数据
        int rowIndex = originSheet.getLastRowNum() + 1;// 假设从第二行开始写入数据
        for (EntryRegistrationOrder EntryRegistrationOrder : finalEntryRegistrationOrders) {
          try {
            Row row = sheet.createRow(rowIndex++);
            EntryRegistrationLandingMerchant merchant = entryId2Merchant.get(EntryRegistrationOrder.getId());
            if (merchant == null) {
              log.error("导出入驻报备单异常,未找到对应的商家信息,报备单id为:{}", EntryRegistrationOrder.getId());
              missionDetailFail(mission, rowIndex-1, "未找到对应的商家信息");
              continue;
            }
            EntryRegistrationEntity entity = new EntryRegistrationEntity(entryRegistrationRepository,
                EntryRegistrationOrder);
            int filedIndex = 0;
            // 报备单号
            row.createCell(filedIndex++).setCellValue(EntryRegistrationOrder.getRegistrationNumber());
            // 报备单状态
            EntryRegistrationStatusEnum entryRegistrationStatus = entity.getRealEntryRegistrationStatus();
            row.createCell(filedIndex++).setCellValue(entryRegistrationStatus.getDescription());
            // 业务员
            row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(EntryRegistrationOrder.getSalesmanName(), "-"));
            // 报备时间
            String registrationTimeStr = "-";
            Long registrationTime = EntryRegistrationOrder.getRegistrationTime();
            //转换为时间格式
            if (registrationTime != null) {
              LocalDateTime
                  dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(registrationTime), ZoneId.systemDefault());
              DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
              registrationTimeStr = dateTime.format(formatter);
            }
            row.createCell(filedIndex++).setCellValue(registrationTimeStr);
            //项目大类
            row.createCell(filedIndex++).setCellValue(EntryRegistrationOrder.getProjectCategory());
            // 项目全称
            row.createCell(filedIndex++).setCellValue(EntryRegistrationOrder.getProjectName());
            // 平台名称
            String platform = EntryRegistrationOrder.getOriginPlatformList().stream()
                .map(id2Platform::get)                       // 获取 Platform 对象
                .filter(Objects::nonNull)                    // 排除 NULL 的 Platform 对象
                .map(Platform::getName)                      // 获取名称
                .filter(StrUtil::isNotBlank)                 // 排除空白名称
                .distinct()                                  // 去重
                .collect(Collectors.joining(","));           // 连接为字符串
            row.createCell(filedIndex++).setCellValue(platform);
            // 合作类型
            String typeOfCooperation = EntryRegistrationOrder.getTypeOfCooperation();
            EntryRegistrationCooperationTypeEnum cooperationTypeEnum = EntryRegistrationCooperationTypeEnum.valueOfByKey(typeOfCooperation);
            row.createCell(filedIndex++).setCellValue(cooperationTypeEnum == null ? "-" : cooperationTypeEnum.getDescription());
            // 合作单位名称
            row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(EntryRegistrationOrder.getPartnerName(), "-"));
            // 合作时间
            String cooperationTime = "-";
            Long cooperationStartTime = EntryRegistrationOrder.getCooperationStartTime();
            Long cooperationEndTime = EntryRegistrationOrder.getCooperationEndTime();
            if (cooperationStartTime != null && cooperationEndTime != null) {
              LocalDateTime
                  startTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(cooperationStartTime), ZoneId.systemDefault());
              LocalDateTime
                  endTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(cooperationEndTime), ZoneId.systemDefault());
              DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
              cooperationTime = startTime.format(formatter) + "至" + endTime.format(formatter);
            }
            row.createCell(filedIndex++).setCellValue(cooperationTime);
            // 合作品牌
            row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(EntryRegistrationOrder.getCooperationBrand(), "-"));
            // 合作区域
            row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(EntryRegistrationOrder.getCooperationRegion(), "-"));
            // 合作比例
            BigDecimal initialDiscountRatio = EntryRegistrationOrder.getInitialDiscountRatio();
               // 转换为百分比
            String initialDiscountRatioStr = initialDiscountRatio == null ? "-" : initialDiscountRatio.stripTrailingZeros().toPlainString() + "%";
            row.createCell(filedIndex++).setCellValue(initialDiscountRatioStr);
            // 合作联系人
            row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(EntryRegistrationOrder.getCooperationContactName(), "-"));
            // 联系电话
            row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(EntryRegistrationOrder.getCooperationContactPhone(), "-"));
            // 职务
            row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(EntryRegistrationOrder.getPosition(), "-"));
            // 联系地址
            row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(EntryRegistrationOrder.getContactAddress(), "-"));
            // 保证金
            String deposit = EntryRegistrationOrder.getDeposit() == null ? "-" :
                EntryRegistrationOrder.getDeposit().stripTrailingZeros().toPlainString() + "元";
            row.createCell(filedIndex++).setCellValue(deposit);
            // 账期
            Integer accountPeriod = EntryRegistrationOrder.getAccountPeriod();
            Boolean backToBack = EntryRegistrationOrder.getBackToBack();
            String accountPeriodStr = accountPeriod == null ? "-" : accountPeriod + "天";
            if (Boolean.TRUE.equals(backToBack)) {
              accountPeriodStr = "背靠背";
            }
            row.createCell(filedIndex++).setCellValue(accountPeriodStr);
            // 付款条件
            String paymentTerms = EntryRegistrationOrder.getPaymentTerms();
               //多个逗号分割，从EntryRegistrationPaymentConditionEnum中取值
            String paymentTermsStr = StrUtil.emptyIfNull(paymentTerms);
            ArrayList<String> paymentTermsOne = CollUtil.toList(paymentTermsStr.split(","));
            paymentTermsStr = paymentTermsOne.stream().map(EntryRegistrationPaymentConditionEnum::valueOfByKey)
                .map(EntryRegistrationPaymentConditionEnum::getDescription)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.joining(","));
            row.createCell(filedIndex++).setCellValue(paymentTermsStr);
            // 付款方式
            String paymentType = EntryRegistrationOrder.getPaymentType();
            PayTypeSAPEnums payTypeSAPEnums = PayTypeSAPEnums.fromKey(paymentType);
            String paymentTypeInput = EntryRegistrationOrder.getPaymentTypeInput();
            String paymentTypeStr = payTypeSAPEnums == null ? "-" : payTypeSAPEnums.getName();
            if (payTypeSAPEnums != null && StrUtil.isNotBlank(paymentTypeInput)) {
              paymentTypeStr += "," + paymentTypeInput;
              // 商业汇票 + 银行汇票 + 供应链金融拼接 个月
              if (PayTypeSAPEnums.COMMERCIAL.equals(payTypeSAPEnums)
                  || PayTypeSAPEnums.DRAFT.equals(payTypeSAPEnums)
                  || PayTypeSAPEnums.CHAINED_FINANCIALL.equals(payTypeSAPEnums)) {
                paymentTypeStr += "个月";
              }
            }
            row.createCell(filedIndex++).setCellValue(paymentTypeStr);
            // 发票类型
            String invoiceType = merchant.getInvoiceType();
            PurchaseOrderInvoiceType purchaseOrderInvoiceType = PurchaseOrderInvoiceType.fromKey(invoiceType);
            row.createCell(filedIndex++).setCellValue(purchaseOrderInvoiceType == null ? "-" : purchaseOrderInvoiceType.getDescription());
            // 税率
            BigDecimal taxRate = Optional.ofNullable(merchant.getTaxRate()).orElse(BigDecimal.ZERO);
               // 转换为百分比（已为百分比 不需要乘100）
//            taxRate = taxRate.multiply(BigDecimal.valueOf(100));
            String taxRateStr = taxRate == null ? "-" : taxRate.stripTrailingZeros().toPlainString() + "%";
            row.createCell(filedIndex++).setCellValue(taxRateStr);
            // 仓储
            String storage = EntryRegistrationOrder.getStorage();
            String storageAddress = EntryRegistrationOrder.getStorageAddress();
            BigDecimal storageArea = EntryRegistrationOrder.getStorageArea();
            String storageStr = "-";
            if ("1".equals(storage)) {
              if (StrUtil.isNotBlank(storageAddress)) {
                storageStr = storageAddress;
              }
              if (storageArea != null) {
                storageStr += "," + storageArea.stripTrailingZeros().toPlainString() + "m²";
              }
            }
            row.createCell(filedIndex++).setCellValue(storageStr);
            // 保底金额
            BigDecimal guaranteedAmount = EntryRegistrationOrder.getGuaranteedAmount();
            String guaranteedAmountStr = guaranteedAmount == null ? "-"
                : guaranteedAmount.stripTrailingZeros().toPlainString() + "万元";
            row.createCell(filedIndex++).setCellValue(guaranteedAmountStr);
            // SCP账号使用人
            row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(merchant.getAccountUser(), "-"));
            // 手机号
            row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(merchant.getAccountUserPhone(), "-"));
            // 邮箱
            row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(merchant.getEmailAddress(), "-"));
            // 准入说明
            row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(EntryRegistrationOrder.getNotes(), "-"));
            missionDetailSuccess(mission, rowIndex-1);
          } catch (Exception e) {
            e.printStackTrace();
            log.error("导出入驻报备单异常,第【{}】行数据写入失败,异常信息为:{}", rowIndex - 1, e);
            missionDetailFail(mission, rowIndex-1, String.format("第【%s】行数据写入失败，原因:%s", rowIndex-1, e.getMessage()));
          }
        }
      });

      String uploadPath = normalExport.getUploadPath();
      String fileName = String.format(normalExport.getFileName(), System.currentTimeMillis());
      uploadPartUtil.uploadChunk(workbook, fileName, uploadPath);
      missionSuccess(mission, "导出入驻报备单成功", uploadPath, fileName);
    } catch (Exception e) {
      missionFail(mission, "导出入驻报备单失败,原因:" + e.getMessage());
    }
  }

  private SXSSFWorkbook getTemplate(Mission mission) {
    EntryRegistrationExportEnums normalExport = EntryRegistrationExportEnums.NORMAL_EXPORT;
    SXSSFWorkbook sxssfWorkbook = null;
    try {
      // 读取文件
      XSSFWorkbook workbook = new XSSFWorkbook(IoUtil.toStream(HttpUtil.downloadBytes(ossUtil.buildOssFileUrl(normalExport.getTempFileUrl()))));
      sxssfWorkbook = new SXSSFWorkbook(workbook);
    } catch (Exception e) {
      log.error("导出入驻报备单异常,未找到对应的模板:" + normalExport.getTempFileUrl() + "异常信息为:"
          + ExceptionUtil.stacktraceToString(e, -1));
      missionFail(mission, "未找到模板，请联系管理员放置模板");
      throw new CheckException("未找到模板，请联系管理员放置模板");
    }
    return sxssfWorkbook;
  }

  /**
   * 任务失败
   * @param mission
   * @param msg
   */
  private void missionFail(Mission mission, String msg) {
    mission.setCompleteTime(System.currentTimeMillis());
    mission.setReason(msg);
    mission.setState(Constants.MISSION_STATE_FAIL);
    missionRepository.save(mission);
    throw new CheckException(msg);
  }

  /**
   * 任务成功
   */
  private void missionSuccess(Mission mission, String msg, String uploadPath, String fileName) {
    mission.setCompleteTime(System.currentTimeMillis());
    mission.setState(Constants.MISSION_STATE_SUCCESS);
    mission.setReason(msg);
    mission.setFileName(fileName);
    mission.setLink(uploadPath + "/" + fileName);
    missionRepository.save(mission);
  }

  /**
   * 设置任务总行数
   */
  private void missionTotalRow(Mission mission, int totalRow) {
    mission.setTotalRow(totalRow);
    missionRepository.save(mission);
  }

  /**
   * 生成新的mission Detail
   */
  private void missionDetailSuccess(Mission mission, int rowIndex) {
    MissionDetail missionDetail = new MissionDetail();
    missionDetail.setMissionId(mission.getId());
    missionDetail.setSign("第【" + rowIndex + "】行");
    missionDetail.setCreateTime(System.currentTimeMillis());
    missionDetail.setState(Constants.MISSION_STATE_SUCCESS);
    missionDetailRepository.save(missionDetail);
  }

  private void missionDetailFail(Mission mission, int rowIndex, String msg) {
    MissionDetail missionDetail = new MissionDetail();
    missionDetail.setMissionId(mission.getId());
    missionDetail.setSign("第【" + rowIndex + "】行");
    missionDetail.setCreateTime(System.currentTimeMillis());
    missionDetail.setState(Constants.MISSION_STATE_FAIL);
    missionDetail.setInformation(msg);
    missionDetailRepository.save(missionDetail);
  }
}
