package com.xhgj.srm.service;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2024/6/25 10:50
 */
public interface FinancialVouchersService {

  /**
   * 导入修复提款申请对应的财务凭证
   *
   * <p>异常数据查询：{@code select par.id,pad.c_financial_vouchers,par.`c_payment_apply_no` from
   * `t_payment_apply_record` par left join `t_payment_apply_detail` pad on `par`.`id` =
   * pad.payment_apply_record_id where par.`c_apply_type` ='4' and par.`c_apply_state` ='2' and
   * par.`c_state` ='1' and pad.new_financial_vouchers_id = '' order by par.`c_apply_time` desc; }
   *
   * @param inputStream 文件流
   * @return 成功处理数
   */
  int repairDrawPaymentFinancialVoucher(InputStream inputStream);
}
