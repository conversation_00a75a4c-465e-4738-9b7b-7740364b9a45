package com.xhgj.srm.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.batch.factory.MapStructFactory;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.PaymentApplyTypeEnums;
import com.xhgj.srm.common.enums.PaymentAuditStateEnum;
import com.xhgj.srm.common.enums.paymentApply.PaymentApplyRecordExportEnums;
import com.xhgj.srm.jpa.dao.PaymentApplyRecordDao;
import com.xhgj.srm.jpa.dao.UserToGroupDao;
import com.xhgj.srm.jpa.dto.ThisAmountDTO;
import com.xhgj.srm.jpa.dto.payment.apply.record.PaymentApplyRecordDaoPageParam;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.MissionDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyRecord;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.UserToGroup;
import com.xhgj.srm.jpa.repository.MissionDetailRepository;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.repository.PaymentApplyDetailRepository;
import com.xhgj.srm.jpa.repository.PaymentApplyRecordRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.service.GroupService;
import com.xhgj.srm.service.PaymentApplyRecordExcelService;
import com.xhgj.srm.service.PermissionTypeService;
import com.xhgj.srm.service.SharePaymentApplyDetailService;
import com.xhgj.srm.service.UserService;
import com.xhgj.srm.utils.ExcelFileUtils;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.upload.util.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PaymentApplyRecordExcelServiceImpl implements PaymentApplyRecordExcelService {

  @Resource
  MissionRepository missionRepository;
  @Resource
  PaymentApplyRecordRepository paymentApplyRecordRepository;
  @Resource
  PaymentApplyDetailRepository paymentApplyDetailRepository;
  @Resource
  MissionDetailRepository missionDetailRepository;
  @Resource private OssUtil ossUtil;
  @Resource
  SupplierOrderRepository supplierOrderRepository;
  @Resource
  ExcelFileUtils excelFileUtils;
  @Resource
  private PermissionTypeService permissionTypeService;
  @Resource
  UserService userService;
  @Resource
  UserToGroupDao userToGroupDao;
  @Resource
  GroupService groupService;
  @Resource
  PaymentApplyRecordDao paymentApplyRecordDao;
  @Resource
  SharePaymentApplyDetailService sharePaymentApplyDetailService;



  @Override
  public void exportApplyRecordExcel(Mission mission, String params) {
    User user = Optional.ofNullable(userService.get(mission.getCreateManId())).orElseThrow(() -> {
      missionFail(mission, "未找到任务创建人");
      return CheckException.noFindException(User.class, mission.getCreateManId());
    });
    List<PaymentApplyRecord> paymentApplyRecords = new ArrayList<>();
    try {
      PaymentApplyRecordDaoPageParam daoPageParam = new PaymentApplyRecordDaoPageParam();
      JSONObject jsonObject = JSON.parseObject(params);
      List<String> ids = jsonObject.getJSONArray("ids").toJavaList(String.class);
      if (CollUtil.isNotEmpty(ids)) {
        paymentApplyRecords = paymentApplyRecordRepository.findAllById(ids);
      }else{
        daoPageParam = MapStructFactory.INSTANCE.toPaymentApplyRecordDaoPageParam(jsonObject);
        Page<PaymentApplyRecord> page = paymentApplyRecordDao.getPage(daoPageParam);
        paymentApplyRecords = page.getContent();
        ids = paymentApplyRecords.stream().map(PaymentApplyRecord::getId).collect(Collectors.toList());
      }
      Map<String, PaymentApplyRecord> idToRecordMap = paymentApplyRecords.stream()
          .collect(Collectors.toMap(PaymentApplyRecord::getId, record -> record));
      // 根据 ids 的顺序对结果进行排序
      paymentApplyRecords = ids.stream()
          .filter(idToRecordMap::containsKey) // 过滤出存在于 idToRecordMap 中的 ID
          .map(idToRecordMap::get) // 从 idToRecordMap 中获取对应的 PaymentApplyRecord
          .collect(Collectors.toList()); // 收集成最终的列表
      // 查询申请详情
      Map<String, List<PaymentApplyDetail>> id2PaymentApplyDetail =
          paymentApplyDetailRepository.findByPaymentApplyRecordIdIn(ids).stream()
              .collect(Collectors.groupingBy(PaymentApplyDetail::getPaymentApplyRecordId));
      // 查询采购单号
      List<String> orderNos = paymentApplyRecords.stream()
          .flatMap(item -> item.getSupplierOrderNoList().stream())  // 使用 flatMap 展平列表
          .distinct()
          .collect(Collectors.toList());
      orderNos.add("-1");
      Map<String, SupplierOrder> code2SupplierOrder =
          supplierOrderRepository.findAllByCodeIn(orderNos).stream()
              .collect(Collectors.toMap(SupplierOrder::getCode, item -> item, (a, b) -> b));

      // // 判断导出权限
      String permissionCode = permissionTypeService.getUserPermissionCodeByUserIdAndType(user.getId(),
          Constants.USER_PERMISSION_EXPORT_PAYMENT_APPLY);
      if (Constants.NOT_EXPORT_IMPORT_KEY.equals(permissionCode)) {
        missionFail(mission, "导出失败，无导出权限");
        return;
      }
      // 判断权限类型--仅导出自己部门的
      List<String> departs = new ArrayList<>();
      // 特殊需求如果查询条件带供应商名称，则不进行部门权限过滤
      if (StrUtil.isBlank(daoPageParam.getSupplierName())) {
        if (Constants.EXPORT_DEPT_TYPE.equals(permissionCode)) {
          departs.addAll(
              CollUtil.emptyIfNull(userToGroupDao.getUserToGroupList(user.getId())).stream()
                  .map(UserToGroup::getDeptId).distinct().map(groupService::get)
                  .filter(ObjectUtil::isNotNull).map(Group::getErpCode).collect(Collectors.toList()));
          // paymentApplyRecords 进行过滤
          paymentApplyRecords = paymentApplyRecords.stream().filter(record -> {
            // 判断采购部门是否在 departs 中
            List<String> supplierOrderNoList = record.getSupplierOrderNoList();
            List<String> purchaseDeptList = supplierOrderNoList.stream().map(item -> {
              SupplierOrder order = code2SupplierOrder.get(item);
              return Optional.ofNullable(order).map(SupplierOrder::getPurchaseDeptCode).orElse(null);
            }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            return CollUtil.containsAny(departs, purchaseDeptList);
          }).collect(Collectors.toList());
        }
      }
      if (CollUtil.isEmpty(paymentApplyRecords)) {
        missionFail(mission, "导出数据为空");
      }

      PaymentApplyRecordExportEnums normalExport = PaymentApplyRecordExportEnums.NORMAL_EXPORT;
      XSSFWorkbook workbook = null;
      try {
        // 读取文件
        workbook = new XSSFWorkbook(IoUtil.toStream(HttpUtil.downloadBytes(ossUtil.buildOssFileUrl(normalExport.getTempFileUrl()))));
      } catch (Exception e) {
        log.error("导出付款申请单异常,未找到对应的模板:" + normalExport.getTempFileUrl() + "异常信息为:"
            + ExceptionUtil.stacktraceToString(e, -1));
        missionFail(mission, "未找到模板，请联系管理员放置模板");
        throw new CheckException("未找到模板，请联系管理员放置模板");
      }
      Sheet sheet = workbook.getSheetAt(0);
      missionTotalRow(mission, paymentApplyRecords.size());
      // 写入数据
      int rowIndex = 1; // 假设从第二行开始写入数据
      for (PaymentApplyRecord paymentApplyRecord : paymentApplyRecords) {
        try {
          Row row = sheet.createRow(rowIndex++);
          int filedIndex = 0;
          // 付款申请单号
          row.createCell(filedIndex++)
              .setCellValue(StrUtil.emptyIfNull(paymentApplyRecord.getPaymentApplyNo()));
          // 申请类型
          String applyTypeValue =
              Optional.ofNullable(PaymentApplyTypeEnums.fromKey(paymentApplyRecord.getApplyType()))
                  .map(PaymentApplyTypeEnums::getName).orElse("-");
          row.createCell(filedIndex++).setCellValue(applyTypeValue);
          // 申请金额
          List<PaymentApplyDetail> paymentApplyDetails =
              Optional.ofNullable(id2PaymentApplyDetail.get(paymentApplyRecord.getId())).orElse(new ArrayList<>());

          BigDecimal applyAmount =
              Optional.ofNullable(sharePaymentApplyDetailService.getApplyAmount(paymentApplyDetails, paymentApplyRecord.getApplyType())).orElse(BigDecimal.ZERO);
          row.createCell(filedIndex++).setCellValue(applyAmount.stripTrailingZeros().toPlainString());
          // 供应商
          List<String> supplierNameList =
              paymentApplyDetails.stream().map(PaymentApplyDetail::getSupplierName)
                  .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
          String supplierName = StrUtil.join(StrUtil.COMMA, supplierNameList);
          if (StrUtil.isBlank(supplierName)) {
            supplierName = "-";
          }
          row.createCell(filedIndex++).setCellValue(supplierName);
          // 申请备注
          String remark = paymentApplyDetails.stream().map(PaymentApplyDetail::getRemark)
              .filter(StrUtil::isNotBlank).findFirst().orElse("-");
          row.createCell(filedIndex++).setCellValue(remark);
          // 付款方式
          List<String> payTypeList =
              paymentApplyDetails.stream().map(PaymentApplyDetail::getPayType)
                  .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
          payTypeList = payTypeList.stream().map(item -> {
            if (PayTypeSAPEnums.getNameByCode(item) != null) {
              item = PayTypeSAPEnums.getNameByCode(item);
            }
            return item;
          }).collect(Collectors.toList());
          String payType = StrUtil.join(StrUtil.COMMA, payTypeList);
          if (StrUtil.isBlank(payType)) {
            payType = "-";
          }
          row.createCell(filedIndex++).setCellValue(payType);
          // 审核状态
          String applyState = Optional.ofNullable(PaymentAuditStateEnum.fromKey(paymentApplyRecord.getApplyState()))
              .map(PaymentAuditStateEnum::getName).orElse("-");

          row.createCell(filedIndex++).setCellValue(applyState);
          // 申请采购订单号
          row.createCell(filedIndex++).setCellValue(StrUtil.emptyIfNull(paymentApplyRecord.getSupplierOrderNo()));
          // 进项发票号
          String invoiceNumber = paymentApplyRecord.getInvoiceNumber();
          if (StrUtil.isNotBlank(invoiceNumber)) {
            // 去除null字符串
            invoiceNumber = StrUtil.replace(invoiceNumber, "null", "");
            //去除首尾的,
            invoiceNumber = StrUtil.removeSuffix(StrUtil.removePrefix(invoiceNumber, StrUtil.COMMA), StrUtil.COMMA);
          }
          row.createCell(filedIndex++).setCellValue(StrUtil.emptyToDefault(invoiceNumber, "-"));
          // 财务凭证号
          List<String> financialVouchersList =
              paymentApplyDetails.stream().map(PaymentApplyDetail::getFinancialVouchers)
                  .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
          String financialVouchers = StrUtil.join(StrUtil.COMMA, financialVouchersList);
          if (StrUtil.isBlank(financialVouchers)) {
            financialVouchers = "-";
          }
          row.createCell(filedIndex++).setCellValue(financialVouchers);
          // 采购部门
          List<String> supplierOrderNoList = paymentApplyRecord.getSupplierOrderNoList();
          List<String> purchaseDeptList = supplierOrderNoList.stream().map(item -> {
            SupplierOrder order = code2SupplierOrder.get(item);
            return Optional.ofNullable(order).map(SupplierOrder::getPurchaseDept).orElse(null);
          }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
          String purchaseDept = StrUtil.join(StrUtil.COMMA, purchaseDeptList);
          if (StrUtil.isBlank(purchaseDept)) {
            purchaseDept = "-";
          }
          row.createCell(filedIndex++).setCellValue(purchaseDept);
          // 申请人
          row.createCell(filedIndex++).setCellValue(StrUtil.emptyIfNull(paymentApplyRecord.getApplyMan()));
          // 申请时间
          Long applyTime = paymentApplyRecord.getApplyTime();
          String applyTimeStr = "-";
          //转换为时间格式
          if (applyTime != null) {
            LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(applyTime), ZoneId.systemDefault());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            applyTimeStr = dateTime.format(formatter);
          }
          row.createCell(filedIndex++).setCellValue(applyTimeStr);
          // 上次审核时间
          Long auditTime = paymentApplyRecord.getAuditTime();
          String lastAuditTimeStr = "-";
          if (auditTime != null) {
            LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(auditTime), ZoneId.systemDefault());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            lastAuditTimeStr = dateTime.format(formatter);
          }
          row.createCell(filedIndex++).setCellValue(lastAuditTimeStr);
          missionDetailSuccess(mission, rowIndex-1);
        } catch (Exception e) {
          e.printStackTrace();
          log.error("导出付款申请单异常,第【{}】行数据写入失败,异常信息为:{}", rowIndex - 1, e);
          missionDetailFail(mission, rowIndex-1, String.format("第【%s】行数据写入失败，原因:%s", rowIndex-1, e.getMessage()));
        }
      }
      String uploadPath = PaymentApplyRecordExportEnums.NORMAL_EXPORT.getUploadPath();
      String fileName = String.format(PaymentApplyRecordExportEnums.NORMAL_EXPORT.getFileName(), System.currentTimeMillis());
      excelFileUtils.saveExportExcel(null, workbook, uploadPath, fileName);
      missionSuccess(mission, "导出付款申请列表成功", uploadPath, fileName);
    } catch (Exception e) {
      missionFail(mission, "导出付款申请列表失败,原因:" + e.getMessage());
    }
  }

  /**
   * 任务失败
   * @param mission
   * @param msg
   */
  private void missionFail(Mission mission, String msg) {
    mission.setCompleteTime(System.currentTimeMillis());
    mission.setReason(msg);
    mission.setState(Constants.MISSION_STATE_FAIL);
    missionRepository.save(mission);
    throw new CheckException(msg);
  }

  /**
   * 任务成功
   */
  private void missionSuccess(Mission mission, String msg, String uploadPath, String fileName) {
    mission.setCompleteTime(System.currentTimeMillis());
    mission.setState(Constants.MISSION_STATE_SUCCESS);
    mission.setReason(msg);
    mission.setFileName(fileName);
    mission.setLink(uploadPath + "/" + fileName);
    missionRepository.save(mission);
  }

  /**
   * 设置任务总行数
   */
  private void missionTotalRow(Mission mission, int totalRow) {
    mission.setTotalRow(totalRow);
    missionRepository.save(mission);
  }

  /**
   * 生成新的mission Detail
   */
  private void missionDetailSuccess(Mission mission, int rowIndex) {
    MissionDetail missionDetail = new MissionDetail();
    missionDetail.setMissionId(mission.getId());
    missionDetail.setSign("第【" + rowIndex + "】行");
    missionDetail.setCreateTime(System.currentTimeMillis());
    missionDetail.setState(Constants.MISSION_STATE_SUCCESS);
    missionDetailRepository.save(missionDetail);
  }

  private void missionDetailFail(Mission mission, int rowIndex, String msg) {
    MissionDetail missionDetail = new MissionDetail();
    missionDetail.setMissionId(mission.getId());
    missionDetail.setSign("第【" + rowIndex + "】行");
    missionDetail.setCreateTime(System.currentTimeMillis());
    missionDetail.setState(Constants.MISSION_STATE_FAIL);
    missionDetail.setInformation(msg);
    missionDetailRepository.save(missionDetail);
  }

}

