<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>srm-boot</artifactId>
        <groupId>com.xhgj</groupId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>srm-batch</artifactId>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <easyexcel.version>3.1.0</easyexcel.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
    </properties>
    <dependencies>
        <!--  mapstruct-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--引入easyExcel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
          <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.3.3</version>
          <exclusions>
            <exclusion>
              <artifactId>commons-io</artifactId>
              <groupId>commons-io</groupId>
            </exclusion>
          </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <!-- 加上这个才能辨认到log4j2.yml文件 -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhgj</groupId>
            <artifactId>srm-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhgj</groupId>
            <artifactId>srm-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhgj</groupId>
            <artifactId>srm-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhgj</groupId>
            <artifactId>srm-request</artifactId>
          <exclusions>
            <exclusion>
              <artifactId>commons-io</artifactId>
              <groupId>commons-io</groupId>
            </exclusion>
          </exclusions>
        </dependency>
        <dependency>
          <groupId>com.xhiot.xhiot-boot</groupId>
          <artifactId>boot-redis</artifactId>
        </dependency>
      <dependency>
        <groupId>com.xhgj</groupId>
        <artifactId>srm-service</artifactId>
      </dependency>
        <dependency>
            <groupId>com.xhgj</groupId>
            <artifactId>srm-entry-registration-ddd</artifactId>
            <version>3.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
    </dependencies>
    <build>
        <!-- java -Dloader.path=./lib -jar srm-api-manage.jar -->
        <finalName>srm-batch</finalName>
        <plugins>
            <!--<plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-deploy-plugin</artifactId>
              <configuration>
                <skip>true</skip>
              </configuration>
            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <layout>ZIP</layout>
                    <includes>
                        <include>
                            <groupId>nothing</groupId>
                            <artifactId>nothing</artifactId>
                        </include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
