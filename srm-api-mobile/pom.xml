<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xhgj</groupId>
    <artifactId>srm-boot</artifactId>
    <version>3.0.0-SNAPSHOT</version>
  </parent>

  <artifactId>srm-api-mobile</artifactId>
  <packaging>jar</packaging>

  <name>srm-mobile</name>
  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>

    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhiot.xhiot-boot</groupId>
      <artifactId>boot-security</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhiot.xhiot-boot.framework</groupId>
      <artifactId>framework-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-mq</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhiot.xhiot-boot.framework</groupId>
      <artifactId>framework-web</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-log4j2</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-yaml</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-jpa</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-common</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-request</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>commons-io</artifactId>
          <groupId>commons-io</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-service</artifactId>
    </dependency>
    <!-- 整合redis -->
    <dependency>
      <groupId>com.xhiot.xhiot-boot</groupId>
      <artifactId>boot-redis</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>logback-classic</artifactId>
          <groupId>ch.qos.logback</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-boot-starter-logging</artifactId>
          <groupId>org.springframework.boot</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.xhiot.xhiot-boot</groupId>
      <artifactId>boot-ding-auth</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhiot.xhiot-boot</groupId>
      <artifactId>boot-security</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.8.0</version>
    </dependency>
  </dependencies>
  <build>
    <!-- java -Dloader.path=./lib -jar srm-api-manage.jar -->
    <finalName>srm-api-mobile</finalName>
    <plugins>
      <!--<plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>-->
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <layout>ZIP</layout>
          <includes>
            <include>
              <groupId>nothing</groupId>
              <artifactId>nothing</artifactId>
            </include>
          </includes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>copy-dependencies</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>copy-dependencies</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/lib</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>

