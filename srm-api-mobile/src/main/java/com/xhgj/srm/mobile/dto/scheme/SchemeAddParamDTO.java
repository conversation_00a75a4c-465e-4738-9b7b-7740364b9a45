package com.xhgj.srm.mobile.dto.scheme;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.SearchScheme;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @ClassName SchemeAddParamDTO Create by Liuyq on 2021/6/18 16:35
 */
@Data
public class SchemeAddParamDTO {

  @ApiModelProperty(value = "方案类型", required = true)
  @NotBlank(message = "方案类型不能为空")
  private String type;

  @ApiModelProperty(value = "方案内容", required = true)
  @NotBlank(message = "方案内容不能为空")
  private String content;

  @ApiModelProperty(value = "方案名称", required = true)
  @NotBlank(message = "方案名称不能为空")
  private String name;

  @ApiModelProperty(value = "登陆供应商用户id", required = true)
  @NotBlank(message = "登陆供应商用户id不能为空")
  private String supplierUserId;

  @ApiModelProperty(value = "是否默认 0-否/1-是")
  @NotBlank(message = "是否默认不能为空")
  private String isDefault;

  public SearchScheme bulidSearchScheme() {
    SearchScheme searchScheme = new SearchScheme();
    searchScheme.setType(type);
    searchScheme.setContent(content);
    searchScheme.setName(name);
    searchScheme.setCreateMan(supplierUserId);
    searchScheme.setIsDefault(isDefault);
    searchScheme.setCreateTime(System.currentTimeMillis());
    searchScheme.setState(Constants.STATE_OK);
    return searchScheme;
  }
}
