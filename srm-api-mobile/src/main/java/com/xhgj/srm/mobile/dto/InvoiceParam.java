package com.xhgj.srm.mobile.dto;

import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/** Created by Geng Shy on 2023/8/11 */
@Data
public class InvoiceParam {
  @ApiModelProperty("id，修改时传入")
  private String id;

  /** {@link com.xhgj.srm.common.enums.InvoiceTypeEnum} */
  @ApiModelProperty("发票类型")
  @NotBlank
  private String invoiceType;

  @ApiModelProperty("发票号")
  @NotBlank(message = "发票号 必传")
  private String invoiceNum;

  @ApiModelProperty("发票代码")
  private String invoiceCode;

  @ApiModelProperty("开票时间")
  @NotNull
  private Long invoiceTime;

  @ApiModelProperty("合计金额")
  @NotNull
  private BigDecimal totalAmount;

  @ApiModelProperty("合计税额")
  private BigDecimal totalTaxAmount;

  @ApiModelProperty("价税合计")
  @NotNull
  private BigDecimal totalAmountIncludingTax;

  @ApiModelProperty("发票附件")
  private FileDTO file;

  @ApiModelProperty("验真类型")
  private String verificationType;

  @ApiModelProperty(value = "关联数据id")
  private String relationId;

  @ApiModelProperty(value = "校验码")
  private String checkCode;

  @ApiModelProperty(value = "销方")
  private String seller;

  public void copyTo(OrderSupplierInvoice orderSupplierInvoice) {
    orderSupplierInvoice.setInvoiceNum(invoiceNum);
    orderSupplierInvoice.setInvoiceCode(invoiceCode);
    orderSupplierInvoice.setInvoiceTime(invoiceTime);
    orderSupplierInvoice.setInvoiceType(invoiceType);
    orderSupplierInvoice.setTotalTaxAmount(totalTaxAmount);
    orderSupplierInvoice.setTotalAmountIncludingTax(totalAmountIncludingTax);
    orderSupplierInvoice.setTotalAmount(totalAmount);
    orderSupplierInvoice.setVerificationType(verificationType);
    orderSupplierInvoice.setCheckCode(checkCode);
    orderSupplierInvoice.setUpdateTime(System.currentTimeMillis());
  }
}
