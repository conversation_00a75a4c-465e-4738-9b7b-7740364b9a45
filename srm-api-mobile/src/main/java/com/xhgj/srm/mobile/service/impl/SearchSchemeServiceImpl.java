package com.xhgj.srm.mobile.service.impl;

import com.xhgj.srm.jpa.dao.SearchSchemeDao;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.repository.SearchSchemeRepository;
import com.xhgj.srm.mobile.service.SearchSchemeService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> @ClassName SearchSchemeServiceImpl
 */
@Service
public class SearchSchemeServiceImpl implements SearchSchemeService {

  @Autowired private SearchSchemeRepository repository;

  @Autowired private SearchSchemeDao dao;

  @Override
  public BootBaseRepository<SearchScheme, String> getRepository() {
    return repository;
  }

  @Override
  public SearchScheme getDefaultSearchScheme(String useId, String searchTypeSupplier) {
    return dao.getDefaultSearchScheme(useId, searchTypeSupplier);
  }
}
