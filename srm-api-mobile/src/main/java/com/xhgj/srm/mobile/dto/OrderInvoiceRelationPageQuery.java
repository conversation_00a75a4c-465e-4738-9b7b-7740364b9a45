package com.xhgj.srm.mobile.dto;

import com.xhiot.boot.framework.web.dto.param.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** Created by <PERSON><PERSON> on 2023/8/20 */
@Data
public class OrderInvoiceRelationPageQuery extends PageParam {
  /** 订单号 */
  @ApiModelProperty("订单号")
  private String orderNo;

  /** 发票号 */
  @ApiModelProperty("发票号")
  private String invoiceNum;

  /** 下单平台 */
  @ApiModelProperty("下单平台")
  private String platform;

  /** 1 审核中 2 暂存 3 通过 4 驳回 */
  @ApiModelProperty("1 审核中 2 暂存 3 通过 4 驳回")
  private String invoiceState;
  /** 方案id */
  @ApiModelProperty("方案id")
  private String schemeId;

  @ApiModelProperty("用户id")
  private String userId;

  @ApiModelProperty("供应商id")
  private String supplierId;

  @ApiModelProperty("原单类型")
  private String orderSource;

  @ApiModelProperty("采购订单号")
  private String supplierOrderCode;

  /**
   * 是否不包含 已冲销的
   */
  @ApiModelProperty(hidden = true, value = "是否包含已冲销带的")
  private Boolean excludeOffset  = true;

  /**
   * 是否包含已冲销带的
   */
  public Boolean getExcludeOffset() {
    if (excludeOffset == null) {
      return true;
    }
    return excludeOffset;
  }
}
