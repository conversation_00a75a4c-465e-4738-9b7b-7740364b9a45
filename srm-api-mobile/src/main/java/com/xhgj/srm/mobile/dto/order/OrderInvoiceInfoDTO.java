package com.xhgj.srm.mobile.dto.order;

import com.xhgj.srm.jpa.entity.OrderInvoice;
import com.xhgj.srm.mobile.dto.FileDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023-02-02 10:19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class OrderInvoiceInfoDTO extends BaseOrderInvoice {

  @ApiModelProperty("附件")
  private List<FileDTO> fileList;

  @ApiModelProperty("开票信息")
  private List<OpenInvoiceDTO> openInvoiceList;

  @ApiModelProperty("开票状态")
  private String invoiceType;

  public OrderInvoiceInfoDTO(OrderInvoice orderInvoice) {
    super(orderInvoice);
  }
}
