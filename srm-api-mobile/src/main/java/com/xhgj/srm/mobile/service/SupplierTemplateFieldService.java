package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.SupplierTemplateField;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/12 11:06
 */
public interface SupplierTemplateFieldService
    extends BootBaseService<SupplierTemplateField, String> {

  /**
   * 根据模板的 id 查询供应商模板的字段配置
   *
   * @param supplierTempId 模板 id 必传
   * @param type 模板类型 必传
   */
  List<SupplierTemplateField> getSupplierTemplateFieldBySupplierTempIdAndType(
      String supplierTempId, String type);
}
