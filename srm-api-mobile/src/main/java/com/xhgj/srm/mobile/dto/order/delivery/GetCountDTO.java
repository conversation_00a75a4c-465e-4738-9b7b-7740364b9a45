package com.xhgj.srm.mobile.dto.order.delivery;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 主页相关数量
 *
 * <AUTHOR>
 * @date 2024/06/16 13:36
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetCountDTO {

  @ApiModelProperty("发货数量")
  private Long deliveryCount;

  @ApiModelProperty("验收单数量")
  private Long acceptanceCount;

  @ApiModelProperty("发票数量")
  private Long invoiceCount;

  @ApiModelProperty("付款申请数量")
  private Long paymentApplicationCount;

  @ApiModelProperty("可付款订单数量")
  private Long payableOrdersTotal;

  @ApiModelProperty("电商供应商数量(用来判断接单发货tab页)")
  @Builder.Default
  private Long orderCount = 1L;

  @ApiModelProperty("供应商订单数量(用来判断接单发货tab页)")
  @Builder.Default
  private Long supplierOrderCount = 2L;
}
