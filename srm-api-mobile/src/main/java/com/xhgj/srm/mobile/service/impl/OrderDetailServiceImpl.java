package com.xhgj.srm.mobile.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.common.vo.order.OrderCancelVO;
import com.xhgj.srm.dto.order.ReturnInfoDTO;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.dao.OrderDetailDao;
import com.xhgj.srm.jpa.dao.OrderInvoiceDao;
import com.xhgj.srm.jpa.dao.OrderInvoiceToOrderDao;
import com.xhgj.srm.jpa.dao.OrderReturnDetailDao;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderAccept;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.entity.OrderInvoice;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.OrderDetailRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.mobile.dto.FileDTO;
import com.xhgj.srm.mobile.dto.order.AcceptInfoDTO;
import com.xhgj.srm.mobile.dto.order.OrderProductDetailDTO;
import com.xhgj.srm.mobile.dto.order.delivery.OrderDeliveryProductDetailDTO;
import com.xhgj.srm.mobile.dto.order.vo.OrderDetailVO;
import com.xhgj.srm.mobile.dto.order.vo.OrderProductInfoVO;
import com.xhgj.srm.mobile.dto.supplier.invoice.OrderAmount;
import com.xhgj.srm.mobile.service.ChangeRecordService;
import com.xhgj.srm.mobile.service.OrderAccountToOrderService;
import com.xhgj.srm.mobile.service.OrderDetailService;
import com.xhgj.srm.mobile.service.OrderService;
import com.xhgj.srm.mobile.service.SupplierUserService;
import com.xhgj.srm.mobile.service.UserService;
import com.xhgj.srm.request.dto.erp.OpenOrCloseOrderParams.OpenOrCloseOrderIdsDTO;
import com.xhgj.srm.request.enums.OpenOrCloseOrderType;
import com.xhgj.srm.request.service.third.erp.ERPRequest;
import com.xhgj.srm.service.OrderAcceptService;
import com.xhgj.srm.service.OrderDeliveryService;
import com.xhgj.srm.service.OrderPaymentToOrderService;
import com.xhgj.srm.service.OrderReturnTempService;
import com.xhgj.srm.util.PlatformUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.config.BootConfig;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-06-13 13:42
 */
@Service
public class OrderDetailServiceImpl implements OrderDetailService {
  private final String url;
  @Autowired private OrderDetailRepository repository;
  @Resource private OrderDetailDao orderDetailDao;
  @Resource private OrderRepository orderRepository;
  @Resource private OrderService orderService;
  @Resource private ERPRequest erpRequest;
  @Resource private BootConfig bootConfig;
  @Resource private OrderInvoiceToOrderDao orderInvoiceToOrderDao;
  @Resource private OrderInvoiceDao orderInvoiceDao;
  @Resource private OrderAccountToOrderService orderAccountToOrderService;
  @Resource private OrderReturnTempService orderReturnTempService;
  @Resource private OrderReturnDetailDao orderReturnDetailDao;
  @Resource private OrderPaymentToOrderService orderPaymentToOrderService;
  @Resource private OrderAcceptService orderAcceptService;
  @Resource private UserService userService;
  @Resource private SupplierUserService supplierUserService;
  @Resource private FileDao fileDao;
  @Resource private OrderDeliveryService orderDeliveryService;
  @Autowired
  private ChangeRecordService changeRecordService;
  @Resource
  FileRepository fileRepository;
  public OrderDetailServiceImpl(SrmConfig config) {
    this.url = config.getUploadUrl();
  }

  @Override
  public BootBaseRepository<OrderDetail, String> getRepository() {
    return repository;
  }

  @Override
  public OrderAmount getOrderAmount(String orderId) {
    Assert.notBlank(orderId);
    Optional<Order> orderOptional = orderRepository.findById(orderId);
    if (!orderOptional.isPresent()) {
      throw new CheckException("参数异常");
    }
    Order order = orderOptional.get();
    List<OrderDetail> orderDetails =
        CollUtil.emptyIfNull(orderDetailDao.getOrderDetailByOrderId(orderId));
    BigDecimal totalAmount =
        orderDetails.stream()
            .map(OrderDetail::getTotalAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal totalAmountIncludingTax =
        NumberUtil.sub(
            NumberUtil.null2Zero(order.getPrice()), NumberUtil.null2Zero(order.getRefundPrice()));
    BigDecimal totalTaxAmount =
        orderDetails.stream()
            .map(OrderDetail::getTotalTaxAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    return OrderAmount.builder()
        .totalAmount(totalAmount)
        .totalTaxAmount(totalTaxAmount)
        .totalAmountIncludingTax(totalAmountIncludingTax)
        .build();
  }

  @Override
  public Optional<OrderDetail> findByErpRowId(String erpRowId) {
    Assert.notBlank(erpRowId);
    return repository.findFirstByErpRowIdAndState(erpRowId, Constants.STATE_OK);
  }

  @Override
  public OrderDetailVO getOrderDetail(String orderId) {
    if (StrUtil.isBlank(orderId)) {
      throw new CheckException("接口请求有误");
    }
    Order order =
        orderService.get(orderId, () -> CheckException.noFindException(Order.class, orderId));
    String orderInvoiceId = orderInvoiceToOrderDao.getFirstOrderInvoiceIdByOrderId(order.getId());
    String invoiceApplicationNum = null;
    if (StrUtil.isNotBlank(orderInvoiceId)) {
      OrderInvoice orderInvoice = orderInvoiceDao.get(orderInvoiceId);
      if (orderInvoice == null) {
        throw new CheckException("出现订单发票脏数据！");
      }
      invoiceApplicationNum = orderInvoice.getInvoiceApplicationNumber();
    }
    // 获取采购单合同附件
    FileDTO fileDTO =
        fileRepository.findFirstByRelationIdAndRelationTypeAndState(orderId,
            Constants.FILE_TYPE_LANDING_PURCHASE_CONTRACT, Constants.STATE_OK).map(FileDTO::new).orElse(null);
    if (fileDTO != null) {
      fileDTO.setBaseUrl(url);
    }
    String platformName = PlatformUtil.getPlatformNameByCode(order.getType());
    OrderDetailVO orderDetailVO = new OrderDetailVO(order, platformName, fileDTO);
    orderDetailVO.setAccountId(orderAccountToOrderService.getAccountIdByOrderId(order.getId()));
    orderDetailVO.setOrderPaymentId(
        orderPaymentToOrderService.getAllByRelationIdAndStateAndType(orderId));
    Object[] count = orderDetailDao.getReturnCountAndShipCountAndSkuNumCount(order.getId());
    BigDecimal totalReturnNum = (BigDecimal) count[0];
    BigDecimal totalShipNum = (BigDecimal) count[1];
    // 订单商品总数
    BigDecimal totalNum = (BigDecimal) count[2];
    // 取消数量
    BigDecimal totalCancelNum = (BigDecimal) count[3];
    if (totalNum.compareTo(BigDecimal.ZERO) > 0) {
      orderDetailVO.setProgress(
          NumberUtil.sub(totalShipNum, totalReturnNum)
              + "/"
              + NumberUtil.sub(totalNum, totalReturnNum, totalCancelNum));
    } else {
      orderDetailVO.setProgress("-");
    }
    orderDetailVO.setAcceptInfoDTOS(getAcceptInfo(order.getId()));
    if(CollUtil.isNotEmpty(orderDetailVO.getAcceptInfoDTOS())){
      orderDetailVO.setAcceptAuditMan(orderDetailVO.getAcceptInfoDTOS().get(0).getAuditMan());
      orderDetailVO.setAcceptGroundsForRejection(orderDetailVO.getAcceptInfoDTOS().get(0).getGroundsForRejection());
    }else{
      // 兼容驳回凭证后用户又把凭证删除的情况，尝试从修改记录表中获取
      changeRecordService.getLatestOrderAcceptAuditManNameToComment(order.getId())
          .ifPresent(pair->{
            orderDetailVO.setAcceptAuditMan(pair.getKey());
            orderDetailVO.setAcceptGroundsForRejection(pair.getValue());
          });
    }
    orderDetailVO.setInvoiceApplicationNum(invoiceApplicationNum);
    orderDetailVO.setOrderInvoiceId(orderInvoiceId);
    String acceptState = orderAcceptService.getAcceptState(order.getId());
    orderDetailVO.setSignVoucherState(acceptState);
    // 查询客户信息附件
    List<FileDTO> customerInfoAttachments = fileRepository.findAllByRelationIdAndRelationTypeAndState(orderId,
            Constants.FILE_TYPE_ORDER_CUSTOMER_INFO, Constants.STATE_OK)
        .orElse(new ArrayList<>()).stream()
        .map(FileDTO::new).collect(Collectors.toList());
    orderDetailVO.setCustomerInfoAttachments(customerInfoAttachments);
    return orderDetailVO;
  }

  @Override
  public OrderProductInfoVO getOrderProductInfo(String orderId) {
    // 商品明细
    List<OrderProductDetailDTO> orderProductDetailVOs = getOrderDetailVOByOrderId(orderId);
    // 发货明细
    List<com.xhgj.srm.dto.order.DeliveryDetailDTO> deliveryDetailVOs =
        orderDeliveryService.getDeliveryDetailVOByOrderId(orderId);
    // 退货明细
    List<ReturnInfoDTO> returnVOs = orderReturnTempService.getOrderReturnVOByOrderId(orderId);
    List<OrderCancelVO> orderCancelVOS =
        orderReturnTempService.getOrderCancelDetailByOrderId(orderId);
    Order order =
        orderService.get(orderId, () -> CheckException.noFindException(Order.class, orderId));
    return new OrderProductInfoVO(orderProductDetailVOs, deliveryDetailVOs, returnVOs,
        orderCancelVOS, orderId , order.getOrderNo());
  }

  @Override
  public List<OrderDetailVO> batchGetOrderProductById(List<String> orderIds) {
    List<OrderDetailVO> result = new ArrayList<>();
    orderIds.forEach(
        orderId -> {
          result.add(getOrderDetail(orderId));
        });
    return result;
  }

  public List<OrderProductDetailDTO> getOrderDetailVOByOrderId(String orderId) {
    Assert.notBlank(orderId);
    // 商品明细
    List<OrderDetail> orderDetails = orderDetailDao.getOrderDetailByOrderId(orderId);
    List<OrderProductDetailDTO> orderProductDetailDTOS = new ArrayList<>();
    if (CollUtil.isNotEmpty(orderDetails)) {
      for (OrderDetail orderDetail : orderDetails) {
        OrderProductDetailDTO orderProductDetailDTO = new OrderProductDetailDTO(orderDetail);
        orderProductDetailDTOS.add(orderProductDetailDTO);
      }
    }
    return orderProductDetailDTOS;
  }

  /**
   * 根据供应商订单 id 获取签收信息
   *
   * @param orderId 供应商订单 必传
   */
  private List<AcceptInfoDTO> getAcceptInfo(String orderId) {
    List<AcceptInfoDTO> acceptInfoDTOS = new ArrayList<>();
    List<OrderAccept> orderAccepts = orderAcceptService.getByOrderId(orderId);
    for (OrderAccept orderAccept : orderAccepts) {
      String fileIds = orderAccept.getFileIds();
      if (StrUtil.isNotBlank(fileIds)) {
        AcceptInfoDTO acceptInfoDTO = new AcceptInfoDTO();
        // v5.8.0对签收信息新增的字段
        acceptInfoDTO.setAuditState(orderAccept.getAuditStatus());
        if (StrUtil.isNotBlank(orderAccept.getAuditMan())) {
          User user =
              userService.get(
                  orderAccept.getAuditMan(),
                  () -> CheckException.noFindException(User.class, orderAccept.getAuditMan()));
          acceptInfoDTO.setAuditMan(user.getRealName() == null ? "" : user.getRealName());
        }
        acceptInfoDTO.setGroundsForRejection(orderAccept.getGroundsForRejection());
        acceptInfoDTO.setId(orderAccept.getId());
        Optional<String> supplierUserRealName =
            supplierUserService.getSupplierUserRealName(orderAccept.getFistUploadMan());
        if (supplierUserRealName.isPresent()) {
          acceptInfoDTO.setUploadMan(supplierUserRealName.get());
        } else {
          acceptInfoDTO.setUploadMan("");
        }
        acceptInfoDTO.setCreateTime(orderAccept.getCreateTime());
        acceptInfoDTO.setType(orderAccept.getType());
        List<FileDTO> acceptList = new ArrayList<>();
        List<String> fileIdList =
            CollUtil.emptyIfNull(StrUtil.split(fileIds, CharUtil.COMMA, true, true));
        for (String fileId : fileIdList) {
          File file = fileDao.get(fileId);
          if (file != null) {
            FileDTO fileDTO = new FileDTO(file);
            fileDTO.setBaseUrl(url);
            acceptList.add(fileDTO);
          }
        }
        acceptInfoDTO.setAcceptList(acceptList);
        acceptInfoDTOS.add(acceptInfoDTO);
      }
    }
    return acceptInfoDTOS;
  }

  @Override
  public List<OrderProductInfoVO> batchGetOrderProductInfo(List<String> orderIds) {
    LinkedList<OrderProductInfoVO> results = new LinkedList<>();
    for (String orderId : orderIds) {
      results.add(getOrderProductInfo(orderId));
    }
    return results;
  }

  @Override
  public PageResult<OrderDeliveryProductDetailDTO> getProductInfoPage(
      String orderId, String keyWord, Integer pageNo, Integer pageSize, String ids) {
    List<String> split = StrUtil.split(ids, ',', true, true);
    // 商品明细
    return PageResultBuilder.buildPageResult(
        orderDetailDao.getOrderDetailPageByOrderId(orderId, keyWord, pageNo, pageSize, split),
        OrderDeliveryProductDetailDTO::new);
  }
}
