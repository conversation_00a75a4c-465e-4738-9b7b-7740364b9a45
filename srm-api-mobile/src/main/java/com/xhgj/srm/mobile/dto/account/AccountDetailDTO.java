package com.xhgj.srm.mobile.dto.account;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.OrderAccount;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AccountDetailDTO {

  @ApiModelProperty("对账单id")
  private String id;

  @ApiModelProperty("对账单号")
  private String accountNo;

  @ApiModelProperty("创建时间")
  private Long createTime;

  @ApiModelProperty("创建人")
  private String createSupplier;

  @ApiModelProperty("提交时间")
  private Long commitTime;

  @ApiModelProperty("对账单状态")
  private String accountStatus;

  @ApiModelProperty("对账金额")
  private BigDecimal accountPrice;

  @ApiModelProperty("确认时间")
  private Long confirmTime;

  @ApiModelProperty("确认人")
  private String confirmMan;

  @ApiModelProperty("对账开票状态")
  private String accountOpenInvoiceStatus;

  @ApiModelProperty("供应商备注")
  private String supplierRemark;

  @ApiModelProperty("发票信息")
  private List<OrderAccountInvoiceInfo> orderAccountInvoiceInfoList;

  @ApiModelProperty("订单明细")
  private List<OrderInfoDTO> orderInfoList;

  @ApiModelProperty("物料明细")
  private List<ProductInfoDTO> productInfoList;

  @ApiModelProperty("驳回理由")
  private String groundsForRejection;

  public AccountDetailDTO(OrderAccount orderAccount, BigDecimal accountPrice) {
    this.id = orderAccount.getId();
    this.accountNo = orderAccount.getAccountNo();
    this.createTime = orderAccount.getCreateTime();
    this.createSupplier = orderAccount.getCreateSupplier();
    this.commitTime = orderAccount.getCommitTime();
    this.accountStatus = StrUtil.emptyIfNull(orderAccount.getAccountState());
    this.accountPrice = accountPrice;
    this.confirmTime = orderAccount.getAssessTime();
    this.confirmMan = StrUtil.emptyIfNull(orderAccount.getConfirmMan());
    String accountOpenInvoiceStatus1 = orderAccount.getAccountOpenInvoiceStatus();
    this.accountOpenInvoiceStatus =
        StrUtil.isBlank(StrUtil.emptyIfNull(accountOpenInvoiceStatus1))
            ? Constants.ORDER_INVOICE_STATE_NOT_DONE
            : accountOpenInvoiceStatus1;
    this.supplierRemark = StrUtil.emptyIfNull(orderAccount.getRemark());
    this.groundsForRejection = StrUtil.emptyIfNull(orderAccount.getGroundsForRejection());
  }
}
