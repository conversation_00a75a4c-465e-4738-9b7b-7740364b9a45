package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.Contact;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.mobile.dto.ContactAddParam;
import com.xhgj.srm.mobile.dto.ContactPageData;
import com.xhgj.srm.mobile.dto.login.ArrayBaseParam;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;

/**
 * @ClassName ContactService Create by Liuyq on 2021/6/4 17:05
 */
public interface ContactService extends BootBaseService<Contact, String> {
  /**
   * @Description 新增联系人 @Auther: liuyq @Date: 2021/3/25 11:18
   *
   * @param addParam
   * @return void
   */
  void addContact(ContactAddParam addParam);

  /**
   * @Description 分页获取联系人 @Auther: liuyq @Date: 2021/3/26 14:27
   *
   * @param supplierId
   * @param supplierUserId
   * @return com.xhiot.project.dto.ContactPage<com.xhiot.project.dto.ContactPageData>
   */
  PageResult<ContactPageData> getCurContactList(
      String supplierId, String supplierUserId, String pageNo, String pageSize);

  /**
   * 批量删除联系人 @Author: liuyq @Date: 2021/6/7 10:59
   *
   * @param deleteParam
   * @return void
   */
  void deleteContactById(ArrayBaseParam deleteParam);

  /**
   * 将供应商的联系人复制到目标供应商副本
   *
   * @param supplierId 供应商 id
   * @param supplierFb 目标供应商副本实体
   */
  void copySupplierContactToSupplierFb(String supplierId, SupplierFb supplierFb);
}
