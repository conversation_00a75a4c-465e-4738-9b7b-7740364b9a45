package com.xhgj.srm.mobile.dto.order;

import cn.hutool.core.util.NumberUtil;
import com.xhgj.srm.common.enums.OrderDeliveryProgress;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class OrderProductDetailDTO {

  @ApiModelProperty("明细id")
  private String id;

  @ApiModelProperty("物料编码")
  private String code;

  @ApiModelProperty("品牌")
  private String brand;

  @ApiModelProperty("商品名称")
  private String name;

  @ApiModelProperty("型号")
  private String model;

  @ApiModelProperty("数量")
  private BigDecimal num;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("单价")
  private BigDecimal price;

  @ApiModelProperty("发货数量")
  private BigDecimal delCount;

  @ApiModelProperty("未发数量")
  private BigDecimal unCount;

  @ApiModelProperty("退货数量")
  private BigDecimal reCount;

  @ApiModelProperty("取消数量")
  private BigDecimal cancelCount;

  @ApiModelProperty("是否可以发货")
  private Boolean isDisabled;

  @ApiModelProperty("发货进度")
  private String progress;

  @ApiModelProperty("销售单价")
  private BigDecimal salePrice;

  @ApiModelProperty("行id")
  private String rowId;

  @ApiModelProperty("成本价税率")
  private String costPriceTaxRate;

  @ApiModelProperty("客户订单号")
  private String orderNo;

  @ApiModelProperty("订单id")
  private String orderId;

  public OrderProductDetailDTO(OrderDetail orderDetail) {
    this.id = orderDetail.getId();
    this.code = StringUtils.emptyIfNull(orderDetail.getCode());
    this.brand = StringUtils.emptyIfNull(orderDetail.getBrand());
    this.name = StringUtils.emptyIfNull(orderDetail.getName());
    this.model = StringUtils.emptyIfNull(orderDetail.getModel());
    this.num = orderDetail.getNum();
    this.unit = StringUtils.emptyIfNull(orderDetail.getUnit());
    this.price = orderDetail.getPrice();
    this.delCount = orderDetail.getShipNum();
    this.unCount = orderDetail.getUnshipNum();
    this.reCount = orderDetail.getReturnNum();
    this.cancelCount = orderDetail.getCancelNum();
    this.salePrice = orderDetail.getSalePrice();
    BigDecimal remain =
        NumberUtil.sub(orderDetail.getNum(), orderDetail.getShipNum(), orderDetail.getCancelNum());
    // 如果remain数量小于等于0则isDisabled为true
    this.isDisabled = remain.compareTo(BigDecimal.ZERO) <= 0;
    this.progress =
        !StringUtils.isNullOrEmpty(orderDetail.getDeliveryState())
            ? orderDetail.getDeliveryState()
            : OrderDeliveryProgress.ORDER_DETAIL_DELIVERY_PROGRESS_UN.getCode();
    this.rowId = orderDetail.getErpRowId();
    this.costPriceTaxRate = orderDetail.getCostPriceTaxRate();
    Order order = orderDetail.getOrder();
    if (order != null) {
      this.orderId = order.getId();
      this.orderNo = order.getOrderNo();
    }
  }
}
