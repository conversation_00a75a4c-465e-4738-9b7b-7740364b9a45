package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.mobile.dto.order.SupplierOrderDeliveryProductDetailDTO;
import com.xhgj.srm.mobile.dto.supplierOrder.ProductDetailParam;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @ClassName SupplierOrderDetailService
 */
public interface SupplierOrderDetailService extends BootBaseService<SupplierOrderDetail, String> {

  /**
   * 根据关联表单 id 查询订单详情
   *
   * @param orderToFormId 关联表单 id 必传
   */
  List<SupplierOrderDetail> getByOrderToFormId(String orderToFormId);

  /**
   * 报错单据明细 Author: liuyq @Date: 2022/12/5 13:44
   *
   * @param orderFormId 单据id
   * @param productDetailList 保存的单据明细列表
   * @return void
   */
  void saveOrderDetail(
      String orderFormId,
      List<ProductDetailParam> productDetailList,
      Map<String, BigDecimal> shipProductMap);

  /**
   * @param orderId 订单id
   * @return 该订单下结算件数
   */
  BigDecimal countSettleQtyBySupplierOrderId(String orderId);

  /**
   * 根据查询条件及订单id获取订单 <AUTHOR> @Date: 2024年6月17日 17:25:44
   *
   * @param orderToFormId 订单id
   * @param keyWord 物料编码以及品牌商品名称型号
   * @param pageNo 页数
   * @param pageSize 条数
   * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.OrderDetail>
   */
  PageResult<SupplierOrderDeliveryProductDetailDTO> getProductInfoPage(
      String orderToFormId, String keyWord, Integer pageNo, Integer pageSize, List<String> ids);
}
