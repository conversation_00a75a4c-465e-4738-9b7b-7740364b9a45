package com.xhgj.srm.mobile.dto.supplierinvoice;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.mobile.dto.FileDTO;
import java.util.LinkedList;
import java.util.List;
import lombok.Data;

/** Created by Geng Shy on 2023/12/4 */
@Data
public class DingFileDTO {

  /** 文件完整的url */
  private String url;
  /** 文件名称 */
  private String name;

  public static List<DingFileDTO> buildByFileList(List<FileDTO> fileDTOS) {
    LinkedList<DingFileDTO> result = new LinkedList<>();
    if (CollUtil.isEmpty(fileDTOS)) {
      return result;
    }
    String file_name = "附件";
    for (int i = 0; i < fileDTOS.size(); i++) {
      FileDTO fileDTO = fileDTOS.get(i);
      DingFileDTO dingFileDTO = new DingFileDTO();
      dingFileDTO.setName(file_name + (i + 1));
      dingFileDTO.setUrl(fileDTO.getBaseUrl() + fileDTO.getUrl());
      result.add(dingFileDTO);
    }
    return result;
  }
}
