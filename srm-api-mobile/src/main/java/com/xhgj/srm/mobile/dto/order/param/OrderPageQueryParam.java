package com.xhgj.srm.mobile.dto.order.param;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class OrderPageQueryParam {

  @NotBlank(message = "登录人id不能为空")
  @ApiModelProperty("登录人id")
  private String userId;

  @NotBlank(message = "供应商id不能为空")
  @ApiModelProperty("供应商id")
  private String supplierId;

  @ApiModelProperty("订单编号")
  private String orderNo;

  @ApiModelProperty("下单平台")
  private String platform;

  @ApiModelProperty("客户名称")
  private String customer;

  @ApiModelProperty("收件人")
  private String consignee;

  @ApiModelProperty("联系方式")
  private String mobile;

  @ApiModelProperty("收货地址")
  private String address;

  @ApiModelProperty("订单状态(1--待履约,2--履约中,3--待验收,4--已取消,5--已退货,6--已验收)搜索")
  private String orderState;

  @ApiModelProperty("订单页签状态(0--全部,1--待履约,2--履约中,3--待验收,4--已完成,5--待履约和履约中)")
  private String orderPageState;

  @ApiModelProperty("开票状态(1--已开票,2--未开票)")
  private String invoicingState;

  @ApiModelProperty("下单日期(起始)")
  private String startDate;

  @ApiModelProperty("下单日期(截止)")
  private String endDate;

  @ApiModelProperty("方案id")
  private String schemeId;

  @ApiModelProperty("签收凭证(1--已确认,2--未确认)")
  private String signVoucher;

  @ApiModelProperty("客户回款(1--已完成,2--未完成)")
  private String customerPayback;

  @ApiModelProperty("开票金额")
  private String price;

  @ApiModelProperty("发票类型")
  private String type;

  @ApiModelProperty("发票抬头")
  private String title;

  @ApiModelProperty("物流单号")
  private String logisticsNum;

  @ApiModelProperty(value = "当前页", example = "1")
  private Integer pageNo;

  @ApiModelProperty(value = "每页展示数量", example = "10")
  private Integer pageSize;

  @ApiModelProperty("对账状态")
  private String accountStatus;

  @ApiModelProperty("对账单开票状态")
  private String accountOpenInvoiceStatus;

  @ApiModelProperty("付款状态")
  private String paymentStatus;

  @ApiModelProperty("是否有退货")
  private Boolean salesReturnState;

  @ApiModelProperty("最新核销时间（开始）")
  private String startWriteOffTime;

  @ApiModelProperty("最新核销时间（结束）")
  private String endWriteOffTime;

  @ApiModelProperty("最新回款方式(1-电汇 2-汇票 3-支票 4-现金 5-电易宝 6-支付宝)")
  private String paymentTypeName;
}
