package com.xhgj.srm.mobile.service.impl;

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.jpa.dao.SupplierTemplateFieldDao;
import com.xhgj.srm.jpa.entity.SupplierTemplateField;
import com.xhgj.srm.jpa.repository.SupplierTemplateFieldRepository;
import com.xhgj.srm.mobile.service.SupplierTemplateFieldService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/10/12 11:08
 */
@Service
@Slf4j
public class SupplierTemplateFieldServiceImpl implements SupplierTemplateFieldService {

  @Autowired private SupplierTemplateFieldRepository repository;
  @Autowired private SupplierTemplateFieldDao dao;

  @Override
  public List<SupplierTemplateField> getSupplierTemplateFieldBySupplierTempIdAndType(
      String supplierTempId, String type) {
    Assert.notEmpty(supplierTempId);
    Assert.notEmpty(type);
    return dao.getSupplierTemplateFieldByTemplateIdAndType(supplierTempId, type);
  }

  @Override
  public BootBaseRepository<SupplierTemplateField, String> getRepository() {
    return repository;
  }
}
