package com.xhgj.srm.mobile.dto.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("对账单参数，用于修改对账单")
@NoArgsConstructor
public class AccountUpdateParamDTO {

  @ApiModelProperty("对账单id(修改传入)")
  @NotBlank(message = "对账单id不能为空")
  private String id;

  @ApiModelProperty(value = "订单明细id", required = true)
  private List<String> detailIds;

  @ApiModelProperty(value = "提交类型(1--提交开票,2--暂存)", required = true)
  private String type;

  @ApiModelProperty("订单明细")
  private List<OrderParamDTO> orderList;

  @ApiModelProperty("发票信息")
  private List<AddInvoiceInfoParams> addInvoiceInfoParams;
}
