package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.mobile.dto.SupplierUserLoginData;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.Optional;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * @ClassName SupplierUserService Create by Liuyq on 2021/6/8 19:02
 */
public interface SupplierUserService extends BootBaseService<SupplierUser, String> {

  /**
   * 账号登陆 @Author: liuyq @Date: 2021/6/9 15:37
   *
   * @param name
   * @param pwd
   * @return com.xhgj.srm.api.dto.SupplierUserLoginData
   */
  SupplierUserLoginData supplierUserLogin(
      HttpServletRequest request,
      HttpServletResponse response,
      String name,
      String pwd,
      String code);

  /**
   * 获取账号信息 @Author: liuyq @Date: 2021/6/9 15:37
   *
   * @param supplierUser
   * @return org.springframework.security.core.userdetails.UserDetails
   */
  UserDetails loadSupplierUserInfo(SupplierUser supplierUser);

  /**
   * 生成验证码 @Author: liuyq @Date: 2021/6/28 8:55
   *
   * @param response
   * @return void
   */
  String getCodeImg(HttpServletResponse response);

  /** 查询用户真实姓名 */
  Optional<String> getSupplierUserRealName(String userId);
}
