package com.xhgj.srm.mobile.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import com.xhgj.srm.jpa.repository.OrderSupplierInvoiceRepository;
import com.xhgj.srm.mobile.dto.FileDTO;
import com.xhgj.srm.mobile.dto.account.OrderAccountInvoiceInfo;
import com.xhgj.srm.mobile.service.OrderAccountInvoiceService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/1/9 14:53
 */
@Service
public class OrderAccountInvoiceServiceImpl implements OrderAccountInvoiceService {

  @Autowired private OrderSupplierInvoiceRepository repository;
  @Autowired private FileDao fileDao;

  @Override
  public BootBaseRepository<OrderSupplierInvoice, String> getRepository() {
    return repository;
  }

  @Override
  public List<OrderAccountInvoiceInfo> getByAccountId(String accountId, String url) {
    Assert.notBlank(accountId);
    return CollUtil.emptyIfNull(repository.findAllByOrderAccountId(accountId)).stream()
        .map(
            orderAccountInvoice -> {
              OrderAccountInvoiceInfo orderAccountInvoiceInfo =
                  new OrderAccountInvoiceInfo(orderAccountInvoice);
              List<File> files =
                  fileDao.getFileListBySId(
                      orderAccountInvoice.getId(), Constants.FILE_TYPE_INVOICE);
              List<FileDTO> fileDTOS = new ArrayList<>();
              if (files != null && files.size() > 0) {
                for (File file : files) {
                  FileDTO fileDTO = new FileDTO(file);
                  fileDTO.setBaseUrl(url);
                  fileDTOS.add(fileDTO);
                }
              }
              orderAccountInvoiceInfo.setFileList(fileDTOS);
              return orderAccountInvoiceInfo;
            })
        .collect(Collectors.toList());
  }

  @Override
  public OrderSupplierInvoice createOrderAccountInvoice(
      String invoiceNum,
      String invoiceCode,
      Long invoiceTime,
      BigDecimal price,
      String logisticsCompany,
      String logisticsNum,
      String orderAccountId) {
    OrderSupplierInvoice orderSupplierInvoice = new OrderSupplierInvoice();
    orderSupplierInvoice.setInvoiceNum(invoiceNum);
    orderSupplierInvoice.setInvoiceCode(invoiceCode);
    orderSupplierInvoice.setInvoiceTime(invoiceTime);
    orderSupplierInvoice.setTotalAmountIncludingTax(price);
    orderSupplierInvoice.setLogisticsCompany(logisticsCompany);
    orderSupplierInvoice.setLogisticsNum(logisticsNum);
    orderSupplierInvoice.setCreateTime(System.currentTimeMillis());
    orderSupplierInvoice.setOrderAccountId(orderAccountId);
    return save(orderSupplierInvoice);
  }
}
