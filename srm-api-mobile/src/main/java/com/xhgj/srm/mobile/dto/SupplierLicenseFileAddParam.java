package com.xhgj.srm.mobile.dto;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @ClassName SupplierLicenseFileAddParam Create by Liuyq on 2021/7/14 20:09
 */
@Data
public class SupplierLicenseFileAddParam {
  @NotBlank(message = "url不能为空")
  @ApiModelProperty("url")
  private String licenceUrl;

  @NotBlank(message = "fileName不能为空")
  @ApiModelProperty("文件名称")
  private String fileName;
}
