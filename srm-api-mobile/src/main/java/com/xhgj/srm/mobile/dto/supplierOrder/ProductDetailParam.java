package com.xhgj.srm.mobile.dto.supplierOrder;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR> @ClassName SupplierOrderDeliveryProductParam
 */
@Data
public class ProductDetailParam extends BaseOrderDetailProductDTO {

  @ApiModelProperty(value = "id")
  private String detailId;

  @ApiModelProperty("erp id")
  private String erpId;

  @ApiModelProperty("erp 行号")
  private Integer erpRowNum;

  @ApiModelProperty("待发数量")
  private BigDecimal waitQty;

  @ApiModelProperty(value = "本次发货数量")
  private BigDecimal deliveryQty;
}
