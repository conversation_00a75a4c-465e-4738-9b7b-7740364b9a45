package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.PlatformToMenu;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/** Created by Geng Shy on 2023/10/23 */
public interface PlatformToMenuService extends BootBaseService<PlatformToMenu, String> {

  /**
   * 根据平台编码查询
   *
   * @param platformCode 平台编码
   * @return List<PlatformToMenu>
   */
  List<PlatformToMenu> findAllByPlatformCode(String platformCode);
}
