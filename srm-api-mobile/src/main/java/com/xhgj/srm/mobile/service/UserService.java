package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import org.springframework.lang.Nullable;

/**
 * <AUTHOR> @ClassName UserService
 */
public interface UserService extends BootBaseService<User, String> {
  /**
   * 根据（ERP）编码获取用户
   *
   * @param code 编码，必传
   */
  @Nullable
  User getByCode(String code);

  /** 查询用户真是姓名 */
  String getUserRealName(String userId);
}
