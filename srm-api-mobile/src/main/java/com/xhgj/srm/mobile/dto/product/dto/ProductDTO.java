package com.xhgj.srm.mobile.dto.product.dto;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2019/8/26 16:07
 */
@Data
public class ProductDTO implements Serializable {

  private static final long serialVersionUID = -8183032937150460101L;

  @ApiModelProperty("商品名称")
  @NotEmpty(message = "商品名称必填")
  private String name;

  @ApiModelProperty("市场价")
  private String marketPrice;

  @ApiModelProperty("厂家型号")
  @NotEmpty(message = "型号规格必填")
  private String model;

  @ApiModelProperty("基本单位")
  @NotEmpty(message = "基本单位必填")
  private String basicUnit;

  @ApiModelProperty("毛重")
  @NotEmpty(message = "毛重必填")
  private String grossWeight;

  @ApiModelProperty("净重")
  private String netWeight;

  @ApiModelProperty("长")
  private String length;

  @ApiModelProperty("宽")
  private String width;

  @ApiModelProperty("高")
  private String height;

  @ApiModelProperty("图文详情")
  private String info;

  @ApiModelProperty("描述")
  @NotNull(message = "描述不能为 null")
  private String remark;

  @ApiModelProperty("品牌Id")
  @NotEmpty(message = "品牌 Id 必填")
  private String brandMdmId;

  @ApiModelProperty("类目Id")
  @NotEmpty(message = "类目 Id 必填")
  private String cateMdmId;

  @ApiModelProperty("物料编码")
  private String code;

  //  v1.1.0

  @ApiModelProperty("条形码")
  private String barCode;

  @ApiModelProperty("体积")
  private String volume;
  // 3.3.0
  @ApiModelProperty("货主编码")
  @NotEmpty(message = "货主必填")
  private String shipperCode;

  @ApiModelProperty("发货日（期），传空代表【详询客服】")
  private String deliveryDay;

  @ApiModelProperty("是否包邮：1 包邮、0 不包邮")
  private String isFreeShipping;

  /** 对应数据库字段：是否停产 */
  @ApiModelProperty("是否退市，1是，0否")
  private String isDelisting;

  @ApiModelProperty("采购价")
  private String supplyPrice;

  @ApiModelProperty("测试报告")
  private List<ProductFileDTO> testReportFileList;

  @ApiModelProperty("质量证明")
  private List<ProductFileDTO> certificateOfQuality;

  @ApiModelProperty("附件（宣传资料）")
  private List<ProductFileDTO> fileList;

  @ApiModelProperty("起订量")
  @Positive(message = "起订量必须为正整数！")
  @NotNull(message = "请输入起订量")
  @Max(value = 9999999999L, message = "起订量长度限制 10！")
  private Long moq;

  @ApiModelProperty("助记码")
  private String mnemonicCode;

  @ApiModelProperty("外部链接和外部链接标签")
  @Size(min = 4, max = 4, message = "外部链接只有4个")
  private List<SrmExternalLinkAndLabelDTO> externalLinks;

  @ApiModelProperty("是否含检测费：0：不含; 1：含第三方检测费; 2:含出厂检测费")
  private String testingFee;

  @ApiModelProperty("是否含安装费")
  private String packingExpense;

  @ApiModelProperty("图片关系")
  private String pictureRelationship;

  @ApiModelProperty("srm物料类型")
  private String srmProductType;

  @ApiModelProperty("调拨价是否含运费")
  private String isTransferPriceFreightIncluded;
}
