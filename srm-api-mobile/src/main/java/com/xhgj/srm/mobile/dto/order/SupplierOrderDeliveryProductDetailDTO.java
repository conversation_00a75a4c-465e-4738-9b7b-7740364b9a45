package com.xhgj.srm.mobile.dto.order;

import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class SupplierOrderDeliveryProductDetailDTO {

  @ApiModelProperty("明细id")
  private String id;

  @ApiModelProperty("物料编码")
  private String code;

  @ApiModelProperty("品牌")
  private String brand;

  @ApiModelProperty("商品名称")
  private String name;

  @ApiModelProperty("型号")
  private String model;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("未发数量")
  private BigDecimal unCount;

  @ApiModelProperty("数量")
  private BigDecimal num;

  @ApiModelProperty("订单id")
  private String orderId;

  @ApiModelProperty("单价")
  private BigDecimal price;

  public SupplierOrderDeliveryProductDetailDTO(
      SupplierOrderProduct supplierOrderProduct, SupplierOrderDetail supplierOrderDetail) {
    this.id = supplierOrderDetail.getId();
    Integer unitDigit = supplierOrderProduct.getUnitDigit();
    this.code = StringUtils.emptyIfNull(supplierOrderProduct.getCode());
    this.brand = StringUtils.emptyIfNull(supplierOrderProduct.getBrand());
    this.name = StringUtils.emptyIfNull(supplierOrderProduct.getName());
    this.model = StringUtils.emptyIfNull(supplierOrderProduct.getManuCode());
    this.unit = StringUtils.emptyIfNull(supplierOrderProduct.getUnit());
    this.unCount =
        BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getWaitQty(), unitDigit);
    this.num = BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getNum(), unitDigit);
    this.price = supplierOrderDetail.getPrice();
  }
}
