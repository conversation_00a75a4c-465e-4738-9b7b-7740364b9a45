package com.xhgj.srm.mobile.dto;

import com.xhgj.srm.common.Constants;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** Created by Geng Shy on 2023/8/16 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierInvoiceSaveParam {

  @ApiModelProperty(value = "发票信息")
  @NotEmpty
  private List<InvoiceParam> invoiceParam;

  @ApiModelProperty(value = "订单id集合")
  @NotEmpty
  private List<String> orderIds;

  @ApiModelProperty(value = "被删除的发票id")
  private List<String> deleteInvoiceIds;

  @ApiModelProperty(value = "被删除的订单id")
  private List<String> deleteOrderIds;

  @ApiModelProperty(value = "被删除的发票附件id")
  private List<String> deleteFileIds;

  /** {@link Constants.ORDER_SUPPLIER_INVOICE_STATE_TYPE} 只能是审核中或者暂存 */
  @ApiModelProperty(value = "提交类型")
  private String dateState;

  @ApiModelProperty(value = "供应商id")
  @NotBlank
  private String supplierId;

  @ApiModelProperty(value = "操作人")
  @NotBlank
  private String userId;

  @ApiModelProperty(value = "发票单id")
  private String relationId;

  @ApiModelProperty(value = "物流公司")
  private String logisticsCompany;

  @ApiModelProperty(value = "物流单号")
  private String logisticsNum;

  @ApiModelProperty(value = "关联订单类型，1：供应商、2：落地商、空代表未确定关联订单类型")
  private String orderType;
}
