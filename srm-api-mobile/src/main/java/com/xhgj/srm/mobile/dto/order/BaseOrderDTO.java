package com.xhgj.srm.mobile.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/06/06 16:36
 */
@Data
@NoArgsConstructor
public abstract class BaseOrderDTO {
  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("订单号")
  private String orderNo;

  @ApiModelProperty("订单状态")
  private String orderState;

  @ApiModelProperty("订单状态名称")
  private String orderStateToName;

  public BaseOrderDTO(String id,String orderNo, String orderState, String orderStateToName) {
    this.id = id;
    this.orderNo = orderNo;
    this.orderState = orderState;
    this.orderStateToName = orderStateToName;
  }
}
