package com.xhgj.srm.mobile.dto.account;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.TitleOfTheContractEnum;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.mobile.dto.supplier.invoice.OrderAmount;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023-02-22 16:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class InvoiceOrderPageDTO extends BaseInvoiceDTO {
  @ApiModelProperty("最终结算金额")
  private BigDecimal finalPrice;

  @ApiModelProperty("签收凭证")
  private String signVoucherState;

  @ApiModelProperty("回款状态")
  private String returnState;

  @ApiModelProperty("收件人")
  private String receiveMan;

  @ApiModelProperty("是否为不可勾选")
  private Boolean isDisabled;

  @ApiModelProperty("下单时间")
  private Long orderTime;

  @ApiModelProperty("合计金额")
  private BigDecimal totalAmount;

  @ApiModelProperty("合计税额")
  private BigDecimal totalTaxAmount;

  @ApiModelProperty("价税合计")
  private BigDecimal totalAmountIncludingTax;

  @ApiModelProperty("签约抬头")
  private String titleOfTheContract;

  public InvoiceOrderPageDTO(Order order, OrderAmount orderAmount, String platformName) {
    super(order, platformName);
    this.finalPrice = NumberUtil.sub(order.getPrice(), order.getRefundPrice());
    this.returnState = StrUtil.emptyIfNull(order.getCustomerReturnProgress());
    this.receiveMan = StrUtil.emptyIfNull(order.getConsignee());
    this.orderTime = order.getCreateTime();
    this.signVoucherState = order.getConfirmVoucherAuditStatus();
    if (orderAmount != null) {
      totalAmount = orderAmount.getTotalAmount();
      totalTaxAmount = orderAmount.getTotalTaxAmount();
      totalAmountIncludingTax = orderAmount.getTotalAmountIncludingTax();
    }
    TitleOfTheContractEnum titleOfTheContractEnum =
        TitleOfTheContractEnum.getEnumByCode(order.getTitleOfTheContract());
    this.titleOfTheContract =
        titleOfTheContractEnum == null ? StrUtil.EMPTY : titleOfTheContractEnum.getName();
  }
}
