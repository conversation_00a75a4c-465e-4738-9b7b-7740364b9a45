package com.xhgj.srm.mobile.dto.filing;

import com.xhgj.srm.jpa.entity.OrderFilingDetail;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class FilingParamProductDTO {

  @ApiModelProperty("报备单商品明细id(修改传入)")
  private String id;

  @ApiModelProperty("物料编码")
  @NotBlank(message = "请输入物料编码")
  private String code;

  @ApiModelProperty("品牌")
  private String brand;

  @ApiModelProperty("商品名称")
  private String name;

  @ApiModelProperty("型号")
  private String model;

  @ApiModelProperty("数量")
  private BigDecimal num;

  @ApiModelProperty("点单价")
  private BigDecimal price;

  public FilingParamProductDTO(OrderFilingDetail orderFilingDetail) {
    this.code = orderFilingDetail.getCode();
    this.brand = orderFilingDetail.getBrand();
    this.name = orderFilingDetail.getName();
    this.model = orderFilingDetail.getModel();
    this.num = orderFilingDetail.getNum();
    this.price = orderFilingDetail.getPrice();
  }
}
