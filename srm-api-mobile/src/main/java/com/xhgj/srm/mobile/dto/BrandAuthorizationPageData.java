package com.xhgj.srm.mobile.dto;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName BrandAuthorizationPageData Create by Liuyq on 2021/6/4 14:40
 */
@Data
public class BrandAuthorizationPageData {

  @ApiModelProperty("授权类型")
  private String type;

  @ApiModelProperty("授权供应商")
  private String supplierName;

  @ApiModelProperty("授权附件名称")
  private String fileName;

  @ApiModelProperty("授权附件id")
  private String fileId;

  @ApiModelProperty("授权品牌")
  private String brandName;

  @ApiModelProperty("授权品牌 Id")
  private String brandIds;

  @ApiModelProperty("授权文件路径")
  private String brandUrl;
  // 分页响应 - 序号
  private int sort;

  public BrandAuthorizationPageData(
      File file, Supplier supplier, String uploadUrl, String allFileName) {
    this.fileId = file.getId();
    this.fileName = allFileName;
    this.supplierName = supplier.getEnterpriseName();
    this.type =
        !StringUtils.isNullOrEmpty(file.getType())
            ? Constants.BRAND_AUTHORIZATION_TYPE_TO_NAME.get(file.getType())
            : "";
    this.brandUrl = uploadUrl;
  }
}
