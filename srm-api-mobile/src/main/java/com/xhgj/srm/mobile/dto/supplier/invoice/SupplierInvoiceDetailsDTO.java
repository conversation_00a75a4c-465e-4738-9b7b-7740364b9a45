package com.xhgj.srm.mobile.dto.supplier.invoice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.InvoiceTypeEnum;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import com.xhgj.srm.mobile.dto.FileDTO;
import com.xhgj.srm.util.PlatformUtil;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/** Created by Geng Shy on 2023/8/20 */
@Data
public class SupplierInvoiceDetailsDTO {

  @ApiModelProperty("发票列表")
  private List<SupplierInvoiceDetailsInvoiceDTO> invoices;

  @ApiModelProperty("订单列表")
  private List<SupplierInvoiceDetailsOrderDTO> orders;

  @ApiModelProperty("审核状态")
  private String dataState;

  @ApiModelProperty("发票价税合计")
  private String invoiceTotalAmountIncludingTax;

  @ApiModelProperty("订单价税合计")
  private String orderTotalAmountIncludingTax;

  @ApiModelProperty("驳回理由")
  private String rejection;

  @ApiModelProperty("物流公司")
  private String logisticsCompany;

  @ApiModelProperty("物流单号")
  private String logisticsNum;

  @Data
  public static class SupplierInvoiceDetailsInvoiceDTO {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("发票类型")
    private InvoiceTypeEnum invoiceType;

    @ApiModelProperty("发票号")
    private String invoiceNum;

    @ApiModelProperty("发票代码")
    private String invoiceCode;

    @ApiModelProperty("开票时间")
    private Long invoiceTime;

    @ApiModelProperty("合计金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("合计税额")
    private BigDecimal totalTaxAmount;

    @ApiModelProperty("价税合计")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty("发票附件")
    private FileDTO file;

    @ApiModelProperty("验真类型")
    private String verificationType;

    @ApiModelProperty("校验码")
    private String checkCode;

    public static List<SupplierInvoiceDetailsInvoiceDTO> buildList(
        List<OrderSupplierInvoice> orderSupplierInvoices) {
      ArrayList<SupplierInvoiceDetailsInvoiceDTO> result = new ArrayList<>();
      if (CollUtil.isEmpty(orderSupplierInvoices)) {
        return result;
      }
      for (OrderSupplierInvoice orderSupplierInvoice : orderSupplierInvoices) {
        SupplierInvoiceDetailsInvoiceDTO invoice = new SupplierInvoiceDetailsInvoiceDTO();
        invoice.setId(orderSupplierInvoice.getId());
        invoice.setInvoiceType(orderSupplierInvoice.getInvoiceType()!=null?
            InvoiceTypeEnum.fromKey(orderSupplierInvoice.getInvoiceType()):null);
        invoice.setInvoiceNum(orderSupplierInvoice.getInvoiceNum());
        invoice.setInvoiceCode(orderSupplierInvoice.getInvoiceCode());
        invoice.setInvoiceTime(orderSupplierInvoice.getInvoiceTime());
        invoice.setTotalAmount(orderSupplierInvoice.getTotalAmount());
        invoice.setTotalTaxAmount(orderSupplierInvoice.getTotalTaxAmount());
        invoice.setTotalAmountIncludingTax(orderSupplierInvoice.getTotalAmountIncludingTax());
        invoice.setVerificationType(orderSupplierInvoice.getVerificationType());
        invoice.setCheckCode(orderSupplierInvoice.getCheckCode());
        result.add(invoice);
      }
      return result;
    }
  }

  @Data
  public static class SupplierInvoiceDetailsOrderDTO {

    @ApiModelProperty("订单id")
    private String id;

    @ApiModelProperty("客户订单号")
    private String orderNo;

    @ApiModelProperty("客户名称")
    private String customer;

    @ApiModelProperty("下单平台")
    private String platform;

    @ApiModelProperty("下单时间")
    private Long orderTime;

    @ApiModelProperty("合计金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("合计税额")
    private BigDecimal totalTaxAmount;

    @ApiModelProperty("价税合计")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty("最终结算金额")
    private BigDecimal finalPrice;

    @ApiModelProperty("签收凭证")
    private String signVoucherState;

    @ApiModelProperty("回款进度 0-未回款 1-部分回款 2-全部回款")
    private String returnState;

    public static List<SupplierInvoiceDetailsOrderDTO> buildList(List<OrderAmount> orderAmounts) {
      ArrayList<SupplierInvoiceDetailsOrderDTO> result = new ArrayList<>();
      if (CollUtil.isEmpty(orderAmounts)) {
        return result;
      }
      for (OrderAmount orderAmount : orderAmounts) {
        SupplierInvoiceDetailsOrderDTO supplierInvoiceOrder = new SupplierInvoiceDetailsOrderDTO();
        Order order = orderAmount.getOrder();
        supplierInvoiceOrder.setId(order.getId());
        supplierInvoiceOrder.setOrderNo(order.getOrderNo());
        supplierInvoiceOrder.setCustomer(order.getCustomer());
        supplierInvoiceOrder.setPlatform(
            StrUtil.emptyIfNull(PlatformUtil.getPlatformNameByCode(order.getType())));
        supplierInvoiceOrder.setOrderTime(order.getCreateTime());
        supplierInvoiceOrder.setTotalAmount(orderAmount.getTotalAmount());
        supplierInvoiceOrder.setTotalTaxAmount(orderAmount.getTotalTaxAmount());
        supplierInvoiceOrder.setTotalAmountIncludingTax(orderAmount.getTotalAmountIncludingTax());
        supplierInvoiceOrder.setFinalPrice(orderAmount.getFinalPrice());
        supplierInvoiceOrder.setSignVoucherState(orderAmount.getOrderAcceptState());
        supplierInvoiceOrder.setReturnState(order.getCustomerReturnProgress());
        result.add(supplierInvoiceOrder);
      }
      return result;
    }
  }
}
