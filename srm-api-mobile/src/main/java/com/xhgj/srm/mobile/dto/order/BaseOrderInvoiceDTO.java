package com.xhgj.srm.mobile.dto.order;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public abstract class BaseOrderInvoiceDTO {

  @ApiModelProperty("订单id")
  private String id;

  @ApiModelProperty("客户订单号")
  private String orderNo;

  @ApiModelProperty("下单平台")
  private String platform;

  @ApiModelProperty("下单金额")
  private BigDecimal price;

  @ApiModelProperty("客户名称")
  private String customer;

  @ApiModelProperty("申请人")
  private String enterPriseName;

  @ApiModelProperty("申请时间")
  private String applicationTime;

  @ApiModelProperty("下单时间")
  private String createTime;

  @ApiModelProperty("开票状态")
  private String invoicingState;

  @ApiModelProperty("开票申请单号")
  private String invoiceApplicationNumber;
}
