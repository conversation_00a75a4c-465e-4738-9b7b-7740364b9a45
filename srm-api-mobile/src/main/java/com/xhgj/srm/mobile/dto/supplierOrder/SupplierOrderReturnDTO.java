package com.xhgj.srm.mobile.dto.supplierOrder;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> @ClassName SupplierOrderShipDTO
 */
@Data
public class SupplierOrderReturnDTO {
  @ApiModelProperty("退货单id")
  private String id;

  @ApiModelProperty("退货单编号")
  private String returnNumber;

  @ApiModelProperty("退货时间")
  private String returnTime;

  @ApiModelProperty("退款金额")
  private BigDecimal returnPrice;

  @ApiModelProperty("状态 （3--ERP 审核 3--ERP 驳回 5--退货中 6- 已退货 7 --已撤销）")
  private String state;

  @ApiModelProperty("物流公司")
  private String logisticsCompany;

  @ApiModelProperty("物流单号")
  private String trackNum;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("物料明细")
  private List<ReturnProductDTO> returnProductDTOList;

  public SupplierOrderReturnDTO(
      SupplierOrderToForm supplierOrderToForm, List<ReturnProductDTO> returnProductDTOList) {
    this.id = supplierOrderToForm.getId();
    this.returnNumber = StrUtil.emptyIfNull(supplierOrderToForm.getNumbers());
    this.returnTime =
        ObjectUtil.isNotEmpty(supplierOrderToForm.getTime()) && supplierOrderToForm.getTime() > 0
            ? DateUtil.format(
                new Date(supplierOrderToForm.getTime()), DatePattern.NORM_DATETIME_PATTERN)
            : "";
    this.returnPrice =
        ObjectUtil.defaultIfNull(supplierOrderToForm.getReturnPrice(), new BigDecimal(0));
    this.state = StrUtil.emptyIfNull(supplierOrderToForm.getStatus());
    this.returnProductDTOList = returnProductDTOList;
    this.logisticsCompany = StrUtil.emptyIfNull(supplierOrderToForm.getLogisticsCompany());
    this.trackNum = StrUtil.emptyIfNull(supplierOrderToForm.getTrackNum());
    this.remark = StrUtil.emptyIfNull(supplierOrderToForm.getRemark());
  }
}
