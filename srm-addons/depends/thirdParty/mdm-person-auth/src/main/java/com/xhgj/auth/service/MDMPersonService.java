package com.xhgj.auth.service;

import com.xhgj.auth.form.req.LoginReq;
import com.xhgj.auth.vo.OrganizationDto;
import com.xhgj.auth.vo.PersonDomain;

/**
 * <AUTHOR>
 */
public interface MDMPersonService {

  /**
   * 启用人员状态
   * @param mobile
   * @param abbreviation
   */
  void enablePersonStatus(String mobile, String abbreviation);

  /**
   * 判断是否为默认密码
   */
  boolean isDefaultPassword(LoginReq form);

  OrganizationDto getOrgByCode(String code);

  PersonDomain getPersonByJobNo(String jobNo);
}
