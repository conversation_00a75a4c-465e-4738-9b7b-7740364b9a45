package com.xhgj.auth.api.interceptor;

import com.xhiot.boot.forest.interceptor.base.BaseInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * MDMPerson服务 API拦截器
 */
@Component
@Slf4j
public class MD<PERSON>ersonInterceptor extends BaseInterceptor {

  @Override
  protected String getSystemName() {
    return "mdm-person";
  }

  @Override
  protected String getBaseUrl() {
    return "";
  }
}
