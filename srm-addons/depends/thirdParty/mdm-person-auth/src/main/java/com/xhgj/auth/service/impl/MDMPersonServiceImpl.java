package com.xhgj.auth.service.impl;

import com.dtflys.forest.http.ForestResponse;
import com.xhgj.auth.api.MDMPersonApi;
import com.xhgj.auth.form.req.LoginReq;
import com.xhgj.auth.service.MDMPersonService;
import com.xhgj.auth.vo.OrganizationDto;
import com.xhgj.auth.vo.PersonDomain;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class MDMPersonServiceImpl implements MDMPersonService {

  private static final String MSG_SUCCESS_EN = "success";

  private static final String MSG_SUCCESS_CN = "操作成功";

  private static final int SUCCESS_CODE = 0;

  private boolean checkFail(String msg, int code) {
    if (MSG_SUCCESS_EN.equals(msg) || MSG_SUCCESS_CN.equals(msg) || code == SUCCESS_CODE) {
      return false;
    }
    return true;
  }

  @Resource
  MDMPersonApi mdmPersonApi;

  @Override
  public void enablePersonStatus(String mobile, String abbreviation) {
    ForestResponse<ResultBean<String>> forestResponse = mdmPersonApi.enablePersonStatus(mobile,
        abbreviation);
    ResultBean<String> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("mdm-person登出失败");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      throw new CheckException(result.getMsg());
    }
  }

  @Override
  public boolean isDefaultPassword(LoginReq form) {
    ForestResponse<ResultBean<Boolean>> forestResponse = mdmPersonApi.isDefaultPassword(form);
    ResultBean<Boolean> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("mdm-auth判断是否为默认密码失败");
    }
    return result.getData();
  }

  @Override
  public OrganizationDto getOrgByCode(String code) {
    ForestResponse<ResultBean<OrganizationDto>> forestResponse = mdmPersonApi.getOrgByCode(code);
    ResultBean<OrganizationDto> result = forestResponse.getResult();
    if (result == null || result.getData() == null) {
      throw new CheckException("mdm-person根据组织编码获取组织信息失败");
    }
    return result.getData();
  }
  @Override
  public PersonDomain getPersonByJobNo(String jobNo) {
    ForestResponse<ResultBean<PersonDomain>> forestResponse = mdmPersonApi.getPersonByJobNo(jobNo);
    ResultBean<PersonDomain> result = forestResponse.getResult();
    if (result == null || result.getData() == null) {
      throw new CheckException("mdm-person根据工号精确查询人员信息失败");
    }
    return result.getData();
  }

}
