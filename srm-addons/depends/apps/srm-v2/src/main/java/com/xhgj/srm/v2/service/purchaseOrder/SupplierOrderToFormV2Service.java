package com.xhgj.srm.v2.service.purchaseOrder;

import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import java.util.List;

/**
 * @Author: fanghuanxu
 * @Date: 2025/5/14 10:50
 */
public interface SupplierOrderToFormV2Service {

  /**
   * 判断是否存在该类型单据
   * @param typeList 单据类型
   * @param supplierOrderId 订单 id
   * @param excludeStatus 排除单据状态
   * @return
   */
  boolean existByTypeAndSupplierOrderIdAndExcludeStatusNotIn(List<SupplierOrderFormType> typeList,
      String supplierOrderId, List<String> excludeStatus);

  /**
   * 根据单子类型和供应商订单 id 获得对应的单据
   *
   * @param type 单据类型 必传
   * @param supplierOrderId 供应商订单 id 必传
   */
  List<SupplierOrderToFormV2> getByTypeAndSupplierOrderId(SupplierOrderFormType type, String supplierOrderId);

  /**
   * 创建采购订单关联单据
   * @param supplierOrderId
   * @param type
   * @return
   */
  SupplierOrderToFormV2 createSupplierOrderForm(String supplierOrderId, SupplierOrderFormType type);
}
