package com.xhgj.srm.v2.helper;/**
 * @since 2025/5/9 14:46
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.xhgj.srm.v2.form.feida.SupplierOrderProcessForm;
import com.xhgj.srm.v2.form.feida.SupplierOrderProcessForm.SupplierOrderDetailProcessForm;
import com.xhgj.srm.v2.form.feida.SupplierOrderProcessForm.SupplierOrderFileProcessForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 *<AUTHOR>
 *@date 2025/5/9 14:46:55
 *@description
 */
@Slf4j
public class PurchaseOrderFeidaProcessCompareHelper {
  /**
   * 比较新旧对象差异，生成修改字段描述
   * @param newProcessForm 新表单对象
   * @param originProcessForm 原表单对象
   * @return 修改字段描述字符串，用顿号分隔
   */
  public String compareProcessForms(SupplierOrderProcessForm newProcessForm,
      SupplierOrderProcessForm originProcessForm) {
    // 0. 定义实例
    FeidaProcessCompareHelper feidaProcessCompareHelper = new FeidaProcessCompareHelper();
    // 1. 定义需要忽略的字段路径模式
    Set<String> ignoredPatterns = new HashSet<>();
    ignoredPatterns.add("type");
    ignoredPatterns.add("modifiedFields");
    ignoredPatterns.add("fileProcessForms");
    // 3. 创建字段路径到友好名称的映射
    Map<String, String> fieldNameMapping = new HashMap<>();
    // 为基本字段创建映射
    setupBaseFieldsMapping(fieldNameMapping);
    // 为明细字段创建映射
    setupDetailFieldsMapping(fieldNameMapping);
    String updateFields = feidaProcessCompareHelper.compareProcessForms(newProcessForm, originProcessForm, ignoredPatterns, fieldNameMapping);
    List<String> specialCompare = this.compareFileLists(newProcessForm.getFileProcessForms(),
        originProcessForm.getFileProcessForms());
    if (CollUtil.isNotEmpty(specialCompare)) {
      updateFields = String.join(",", specialCompare);
    }
    return updateFields;
  }

  /**
   * 设置基本字段的名称映射
   */
  private void setupBaseFieldsMapping(Map<String, String> fieldNameMapping) {
    // 使用反射获取SupplierOrderProcessForm类中的ApiModelProperty注解
    Field[] fields = SupplierOrderProcessForm.class.getDeclaredFields();
    for (Field field : fields) {
      if (List.class.isAssignableFrom(field.getType())) {
        continue;  // 跳过列表字段
      }

      ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
      if (annotation != null) {
        fieldNameMapping.put(field.getName(), annotation.value());
      }
    }
  }
  /**
   * 设置明细字段的名称映射
   */
  private void setupDetailFieldsMapping(Map<String, String> fieldNameMapping) {
    // 映射明细列表的变更为"明细"
    fieldNameMapping.put("detailProcessForms", "明细");
    fieldNameMapping.put("detailProcessForms[*]", "明细");

    // 映射明细中的各个字段
    Field[] detailFields = SupplierOrderDetailProcessForm.class.getDeclaredFields();
    for (Field field : detailFields) {
      ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
      if (annotation != null) {
        // 用明细字段的友好名称
        fieldNameMapping.put("detailProcessForms[*]." + field.getName(), annotation.value());
      }
    }
  }


//  /**
//   * 比较两个对象的基本字段
//   */
//  private List<String> compareBaseFields(SupplierOrderProcessForm newObj, SupplierOrderProcessForm originObj, List<String> ignoreFields) {
//    List<String> modifiedFields = new ArrayList<>();
//    Class<?> clazz = SupplierOrderProcessForm.class;
//
//    // 获取类的所有声明字段
//    Field[] fields = clazz.getDeclaredFields();
//    for (Field field : fields) {
//      // 跳过列表类型字段和type、modifiedFields字段
//      if (List.class.isAssignableFrom(field.getType()) || ignoreFields.contains(field.getName())) {
//        continue;
//      }
//      field.setAccessible(true);
//      try {
//        Object newValue = field.get(newObj);
//        Object originValue = field.get(originObj);
//
//        // 比较值是否相等
//        if (!Objects.equals(newValue, originValue)) {
//          ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
//          if (annotation != null) {
//            modifiedFields.add(annotation.value());
//          }
//        }
//      } catch (IllegalAccessException e) {
//        log.error("比较字段值出错", e);
//      }
//    }
//
//    return modifiedFields;
//  }
//
//  /**
//   * 比较两个订单明细列表
//   */
//  private List<String> compareDetailLists(List<SupplierOrderDetailProcessForm> newList, List<SupplierOrderDetailProcessForm> originList) {
//    Set<String> modifiedFields = new HashSet<>();
//
//    // 创建原始明细的Map，以lineId为key
//    Map<String, SupplierOrderDetailProcessForm> originMap = new HashMap<>();
//    for (SupplierOrderDetailProcessForm detail : originList) {
//      originMap.put(detail.getLineId(), detail);
//    }
//
//    // 比较每个新明细
//    for (SupplierOrderDetailProcessForm newDetail : newList) {
//      String lineId = newDetail.getLineId();
//      SupplierOrderDetailProcessForm originDetail = originMap.get(lineId);
//
//      if (originDetail == null) {
//        // 新增明细项
//        modifiedFields.add("明细");
//        continue;
//      }
//
//      // 比较明细对象的字段
//      List<String> detailChanges = compareDetailFields(newDetail, originDetail, new ArrayList<>());
//      if (!detailChanges.isEmpty()) {
//        modifiedFields.add("明细");
//        // 可选：添加具体的明细字段变更
//        modifiedFields.addAll(detailChanges);
//      }
//
//      // 从Map中移除已比较的明细
//      originMap.remove(lineId);
//    }
//
//    // 有删除的明细
//    if (!originMap.isEmpty()) {
//      modifiedFields.add("明细");
//    }
//
//    return new ArrayList<>(modifiedFields);
//  }
//  /**
//   * 比较两个明细对象的字段
//   */
//  private List<String> compareDetailFields(SupplierOrderDetailProcessForm newDetail,
//      SupplierOrderDetailProcessForm originDetail, List<String> ignoreFields) {
//    List<String> modifiedFields = new ArrayList<>();
//    Class<?> clazz = SupplierOrderDetailProcessForm.class;
//
//    Field[] fields = clazz.getDeclaredFields();
//    for (Field field : fields) {
//      if (ignoreFields.contains(field.getName())) {
//        continue;
//      }
//      field.setAccessible(true);
//      try {
//        Object newValue = field.get(newDetail);
//        Object originValue = field.get(originDetail);
//
//        if (!Objects.equals(newValue, originValue)) {
//          ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
//          if (annotation != null) {
//            modifiedFields.add(annotation.value());
//          }
//        }
//      } catch (IllegalAccessException e) {
//        log.error("比较明细字段值出错", e);
//      }
//    }
//
//    return modifiedFields;
//  }

  /**
   * 比较两个附件列表
   */
  private List<String> compareFileLists(List<SupplierOrderFileProcessForm> newList, List<SupplierOrderFileProcessForm> originList) {
    // 简化处理：只要数量不同，或者有任何文件不同，就认为"附件"有变化
    if (newList.size() != originList.size()) {
      return Collections.singletonList("附件");
    }

    // 对附件进行排序，按文件名比较
    newList.sort(Comparator.comparing(SupplierOrderFileProcessForm::getFileName));
    originList.sort(Comparator.comparing(SupplierOrderFileProcessForm::getFileName));

    for (int i = 0; i < newList.size(); i++) {
      SupplierOrderFileProcessForm newFile = newList.get(i);
      SupplierOrderFileProcessForm originFile = originList.get(i);

      if (!Objects.equals(newFile.getFileName(), originFile.getFileName()) ||
          !Objects.equals(newFile.getFileUrl(), originFile.getFileUrl()) ||
          !Objects.equals(newFile.getRelationType(), originFile.getRelationType())) {
        return Collections.singletonList("附件");
      }
    }

    return Collections.emptyList();
  }


}
