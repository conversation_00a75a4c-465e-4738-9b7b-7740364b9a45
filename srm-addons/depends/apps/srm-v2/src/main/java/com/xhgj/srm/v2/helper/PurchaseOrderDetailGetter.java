package com.xhgj.srm.v2.helper;/**
 * @since 2025/4/28 17:56
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.v2.dto.InputInvoiceOrderWithDetailV2;
import com.xhgj.srm.v2.dto.PurchaseOrderInvoiceRelationV2;
import com.xhgj.srm.v2.provider.ShareInputInvoiceProvider;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/4/28 17:56:27
 *@description
 */
public class PurchaseOrderDetailGetter {

  private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;

  private SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;

  private ShareInputInvoiceProvider shareInputInvoiceProvider;

  public PurchaseOrderDetailGetter(SupplierOrderToFormV2Repository supplierOrderToFormV2Repository,
      SupplierOrderDetailV2Repository supplierOrderDetailV2Repository,
      ShareInputInvoiceProvider shareInputInvoiceProvider) {
    this.supplierOrderToFormV2Repository = supplierOrderToFormV2Repository;
    this.supplierOrderDetailV2Repository = supplierOrderDetailV2Repository;
    this.shareInputInvoiceProvider = shareInputInvoiceProvider;
  }

  /**
   * 获取不含税价
   *
   * @param price 税价
   * @return 不含税价
   */
  public String getNakedPrice(BigDecimal price, BigDecimal taxRate) {
    if (price == null || taxRate == null) {
      return BigDecimal.ZERO.toPlainString();
    }
    BigDecimal nakedPrice =
        price.divide(
            BigDecimal.ONE.add(taxRate),
            10,
            RoundingMode.HALF_UP);
    return nakedPrice.toPlainString();
  }

  /**
   * 获取税额
   *
   * @param nakedPrice 不含税价
   * @return 税额
   */
  public String getTaxPrice(BigDecimal nakedPrice, BigDecimal taxRate) {
    return BigDecimalUtil.setScaleBigDecimalHalfUp(NumberUtil.mul(taxRate, nakedPrice), 2)
        .stripTrailingZeros().toPlainString();
  }

  public List<PurchaseOrderInvoiceRelationV2> getInvoiceNumber(String supplierOrderId) {
    List<PurchaseOrderInvoiceRelationV2> list = new ArrayList<>();
    List<SupplierOrderToFormV2> supplierOrderToFormList =
        supplierOrderToFormV2Repository.getAllByTypeAndSupplierOrderIdAndStateOrderByTimeAsc(
            SupplierOrderFormType.WAREHOUSING.getType(), supplierOrderId, Constants.STATE_OK);
    if(CollUtil.isNotEmpty(supplierOrderToFormList)){
      for (SupplierOrderToFormV2 supplierOrderToForm : supplierOrderToFormList){
        List<SupplierOrderDetailV2> shipProductDTOList =
            supplierOrderDetailV2Repository
                .getAllByOrderToFormIdAndStateOrderBySortNumAsc(supplierOrderToForm.getId(),Constants.STATE_OK);
        for (SupplierOrderDetailV2 supplierOrderDetail : shipProductDTOList){
          List<InputInvoiceOrderWithDetailV2> orderInvoiceRelationListByDetailIds =
              shareInputInvoiceProvider.getOrderInvoiceRelationListByDetailIdsRef(Collections.singletonList(supplierOrderDetail.getId()));
          List<PurchaseOrderInvoiceRelationV2> purchaseOrderInvoiceRelationList = orderInvoiceRelationListByDetailIds.stream().map(
              item -> new PurchaseOrderInvoiceRelationV2(
                  item.getInputInvoiceOrder())).collect(Collectors.toList());
          list.addAll(purchaseOrderInvoiceRelationList);
        }
      }
    }
    return list.stream().distinct().collect(Collectors.toList());
  }

  public BigDecimal getInvoiceOpenNum(String purchaseOrderId) {
    List<SupplierOrderToFormV2> supplierOrderToForms =
        supplierOrderToFormV2Repository.findBySupplierOrderIdAndTypeAndState(purchaseOrderId,
            SupplierOrderFormType.WAREHOUSING.getKey(), Constants.STATE_OK);
    return CollUtil.emptyIfNull(supplierOrderToForms).stream().map(
        supplierOrderToForm -> supplierOrderDetailV2Repository.getAllByOrderToFormIdAndStateOrderBySortNumAsc(
                supplierOrderToForm.getId(), Constants.STATE_OK).stream()
            .map(SupplierOrderDetailV2::getInvoicedNum).filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add)).reduce(BigDecimal.ZERO, BigDecimal::add);
  }
}

