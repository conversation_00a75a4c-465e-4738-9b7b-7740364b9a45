package com.xhgj.srm.v2.service.purchaseOrder;/**
 * @since 2025/4/28 9:40
 */

import javax.validation.constraints.NotBlank;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

/**
 *<AUTHOR>
 *@date 2025/4/28 09:40:33
 *@description
 */
public interface PurchaseOrderV2AdminService {
  /**
   * 采购订单导入
   */
  void importPurchaseOrder(MultipartFile file, String userId);

  /**
   * 采购订单入库单导入
   */
  void inboundDelivery(MultipartFile file, String userId);

  /**
   * 采购订单退库单导入
   */
  void outboundDelivery(MultipartFile file, String userId);

  /**
   * 删除异常订单
   *
   * @param supplierOrderIds 供应商订单ID
   */
  void deleteAnomalyOrder( List<String> supplierOrderIds);

}
