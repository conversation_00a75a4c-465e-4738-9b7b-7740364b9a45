package com.xhgj.srm.v2.vo.purchaseOrder;/**
 * @since 2025/5/6 17:04
 */

import com.xhgj.srm.common.enums.supplierorder.SupplierOrderSyncStatus;
import com.xhgj.srm.jpa.entity.SupplierOrderSync;
import lombok.Data;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/5/6 17:04:20
 *@description
 */
@Data
public class SupplierOrderSyncVO {
  /**
   * 创建时间
   */
  private Long createTime;

  /**
   * 创建人
   */
  private String createManName;

  /**
   * 修改人
   */
  private String updateManName;

  /**
   * 上次修改时间
   */
  private Long updateTime;

  /**
   * 上次审核时间
   */
  private Long auditTime;

  /**
   * 同步记录
   */
  private List<SupplierOrderSyncOne> syncList;

  @Data
  public static class SupplierOrderSyncOne{
    /**
     * 唯一id
     */
    private String id;

    /**
     * 目标系统
     */
    private String target;

    /**
     * 同步方式
     */
    private String syncType;

    /**
     * 同步结果
     * @see com.xhgj.srm.common.enums.supplierorder.SupplierOrderSyncStatus
     */
    private Byte status;

    /**
     * 同步结果描述
     */
    private String statusDesc;
    /**
     * 同步时间
     */
    private Long createTime;
    /**
     * 完成时间
     */
    private Long finishTime;
    /**
     * 操作人
     */
    private String createManName;
    /**
     * 请求URL
     */
    private String url;
    /**
     * 请求头
     */
    private String header;
    /**
     * 请求参数
     */
    private String req;
    /**
     * 返回参数
     */
    private String res;

    public SupplierOrderSyncOne(SupplierOrderSync sync) {
      this.id = sync.getId();
      this.target = sync.getTarget();
      this.status = sync.getStatus();
      this.createTime = sync.getCreateTime();
      this.finishTime = sync.getSuccessTime();
      this.createManName = sync.getCreateManName();
      this.url = sync.getUrl();
      this.header = sync.getHeader();
      this.req = sync.getReq();
      this.res = sync.getRes();
      this.syncType = sync.getSyncType();
    }

    public String getStatusDesc() {
      return SupplierOrderSyncStatus.getDescByCode(this.status);
    }
  }



}
