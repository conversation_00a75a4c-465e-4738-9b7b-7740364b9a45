package com.xhgj.srm.v2.service.impl.purchaseOrder;/**
 * @since 2025/4/28 9:40
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.component.LockUtils;
import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.enums.WarehouseEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormExecutionStatusEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormReviewStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.utils.MissionUtil;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.common.utils.supplierOrderForm.SupplierOrderFormCodeGenerator;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderOutBoundDeliveryStatistics;
import com.xhgj.srm.jpa.entity.BaseSupplierOrderDetail;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.jpa.repository.InventoryRepository;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.dispatcher.MissionDispatcher;
import com.xhgj.srm.request.dto.hZero.process.StartProcessVo;
import com.xhgj.srm.request.dto.hZero.process.SupplierOrderReturnProcessForm.ReturnDetail;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalParams;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult.ReturnMessage;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapResult.UpdatePurchaseOrderRETURNDTO;
import com.xhgj.srm.request.service.third.hZero.HZeroService;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.v2.constants.PurchaseOrderV2Lock;
import com.xhgj.srm.v2.dao.SupplierOrderDetailV2Dao;
import com.xhgj.srm.v2.dao.SupplierOrderToFormV2Dao;
import com.xhgj.srm.v2.dto.InputInvoiceOrderWithDetailV2;
import com.xhgj.srm.v2.dto.PurchaseOrderInvoiceRelationV2;
import com.xhgj.srm.v2.dto.PurchaseOrderProductDetailedReturnV2VO;
import com.xhgj.srm.v2.dto.PurchaseOrderReturnV2VO;
import com.xhgj.srm.v2.dto.RetreatWarehousePageV2DTO;
import com.xhgj.srm.v2.dto.RetreatWarehouseV2DTO;
import com.xhgj.srm.v2.factory.SapV2Factory;
import com.xhgj.srm.v2.form.OutBoundDeliveryV2Prams;
import com.xhgj.srm.v2.form.PurchaseOrderReturnV2Form;
import com.xhgj.srm.v2.form.PurchaseOrderReturnV2Form.ProductDetail;
import com.xhgj.srm.v2.provider.PermissionTypeProvider;
import com.xhgj.srm.v2.provider.ShareInputInvoiceProvider;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderV2Repository;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2BaseService;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2ReturnService;
import com.xhgj.srm.v2.service.purchaseOrder.SupplierOrderDetailV2Service;
import com.xhgj.srm.v2.service.purchaseOrder.SupplierOrderToFormV2Service;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.config.BootConfig;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import com.xhiot.boot.mvc.base.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PurchaseOrderV2ReturnServiceImpl implements PurchaseOrderV2ReturnService {
  @Resource private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;
  @Resource private SupplierOrderV2Repository supplierOrderV2Repository;
  @Resource private RedissonClient redissonClient;
  @Resource private PlatformTransactionManager transactionManager;
  @Resource private InventoryLocationRepository inventoryLocationRepository;
  @Resource private ShareInputInvoiceProvider shareInputInvoiceProvider;
  @Resource private SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;
  @Resource private PurchaseOrderV2BaseService purchaseOrderV2BaseService;
  @Resource private SupplierOrderToFormV2Service supplierOrderToFormV2Service;
  @Resource private SupplierOrderDetailV2Service supplierOrderDetailV2Service;
  @Autowired private SupplierRepository supplierRepository;
  @Resource private InventoryRepository inventoryRepository;
  @Resource private SAPService sapService;
  @Resource private SupplierOrderDetailV2Dao supplierOrderDetailV2Dao;
  @Resource private LockUtils lockUtils;
  @Autowired private SapV2Factory sapV2Factory;
  @Autowired private BootConfig bootConfig;
  @Resource private HZeroService heroService;
  @Resource private PermissionTypeProvider permissionTypeProvider;
  @Resource private SupplierOrderToFormV2Dao supplierOrderToFormDao;
  @Resource private MissionRepository missionRepository;
  @Resource private MissionUtil missionUtil;
  @Resource private MissionDispatcher missionDispatcher;

  @Override
  public void returnReversal(String orderToFormId) {
    SupplierOrderToFormV2 v2ReturnForm = supplierOrderToFormV2Repository.findById(orderToFormId).orElseThrow(
        () -> CheckException.noFindException(SupplierOrderToForm.class, orderToFormId));
    if (!StrUtil.equals(SupplierOrderFormType.RETURN.getType(), v2ReturnForm.getType())) {
      throw new CheckException("单据类型错误");
    }
    String supplierOrderId = v2ReturnForm.getSupplierOrderId();
    SupplierOrderV2 supplierOrderV2 = supplierOrderV2Repository.findById(supplierOrderId)
        .orElseThrow(() -> new CheckException("采购订单不存在"));
    // 单据明细
    List<SupplierOrderDetailV2> supplierOrderDetails =
        supplierOrderDetailV2Repository.getAllByOrderToFormIdAndStateOrderBySortNumAsc(
            orderToFormId, Constants.STATE_OK);
    List<String> v2DetailIds = supplierOrderDetails.stream().map(SupplierOrderDetailV2::getId)
        .collect(Collectors.toList());
    List<RLock> rLocks = lockUtils.lockAll(supplierOrderId,
        PurchaseOrderV2Lock.PURCHASE_ORDER_RETURN_LOCK);
    List<RLock> rLocks2 = lockUtils.lockAll(supplierOrderId,
        PurchaseOrderV2Lock.PURCHASE_ORDER_LOCK);
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(status -> {
        // 1. 锁内强校验
        checkReturnReversal(v2ReturnForm, supplierOrderV2, v2DetailIds);
        // 2. 处理冲销 && 3. 采购订单状态变更
        v2ReturnForm.setStatus(SupplierOrderFormStatus.REVERSAL.getStatus());
        returnReversal(v2ReturnForm, supplierOrderV2, supplierOrderDetails);
        // 4. 调用 SAP 逻辑
        ReceiptOrReturnReversalParams reversalParams =
            sapV2Factory.create032ReversalParamsForReturn(supplierOrderV2, v2ReturnForm);
        ReceiptOrReturnReversalResult returnReversal =
            sapService.sapReceiptOrReturnReversal(reversalParams);
        String reversalNo = returnReversal.getReturnX().getBelnr();
        v2ReturnForm.setSapReversalNo(reversalNo);
        // 单据状态变成已冲销
        v2ReturnForm.setStatus(SupplierOrderFormStatus.REVERSAL.getStatus());
        // SAP 冲销单号赋值
        supplierOrderToFormV2Repository.save(v2ReturnForm);
        return null;
      });
    } catch (Exception e) {
      log.error("冲销单据失败", e);
      throw e;
    } finally {
      lockUtils.unlockAllLocks(rLocks);
      lockUtils.unlockAllLocks(rLocks2);
    }
  }




  @Override
  public void deleteOutBoundProductDetail(String id, String sapRwoId) {
    // 采购订单id
    List<RLock> rLocks = lockUtils.lockAll(id,
        PurchaseOrderV2Lock.PURCHASE_ORDER_RETURN_LOCK);
    List<RLock> rLocks2 = lockUtils.lockAll(id,
        PurchaseOrderV2Lock.PURCHASE_ORDER_LOCK);
    // 开启事务
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(status -> {
        //退库单对应的订单明细
        SupplierOrderDetailV2 supplierOrderDetail = supplierOrderDetailV2Repository.findById(sapRwoId)
            .orElseThrow(() -> CheckException.noFindException(SupplierOrderDetailV2.class, sapRwoId));
        //退库单对应的订单
        SupplierOrderV2 supplierOrder = supplierOrderV2Repository.findById(id)
            .orElseThrow(() -> CheckException.noFindException(SupplierOrderV2.class, id));
        String inWareHouseId = supplierOrderDetail.getInWareHouseId();
        SupplierOrderDetailV2 detailed = supplierOrderDetail.getDetailed();
        if (detailed != null) {
          //对应的入库单明细
          SupplierOrderDetailV2 wareHouseDetailed =
              supplierOrderDetailV2Repository.getFirstByOrderToFormIdAndDetailedIdAndState(inWareHouseId, detailed.getId(),Constants.STATE_OK);
          if (wareHouseDetailed != null) {
            //退库数量减去本次删除的退库数量
            wareHouseDetailed.setReturnQty(NumberUtil.sub(wareHouseDetailed.getReturnQty(),
                supplierOrderDetail.getStockOutputQty()));
            wareHouseDetailed.setStockOutputQty(NumberUtil.sub(wareHouseDetailed.getStockOutputQty(),
                supplierOrderDetail.getStockOutputQty()));
            //加回可开票数量=删除的退库数量
            wareHouseDetailed.setInvoicableNum(
                NumberUtil.add(NumberUtil.toBigDecimal(wareHouseDetailed.getInvoicableNum()),
                    supplierOrderDetail.getStockOutputQty()));
            supplierOrderDetailV2Repository.save(wareHouseDetailed);
          }
          // 实际交货数量（入库数量-退库数量）
          detailed.setSettleQty(
              NumberUtil.sub(detailed.getStockInputQty(), detailed.getStockOutputQty()));
          detailed.setStockOutputQty(NumberUtil.sub(detailed.getStockOutputQty(),supplierOrderDetail.getStockOutputQty()));
          // 总入库数量增加
          supplierOrder.setTotalStockInputQty(NumberUtil.add(supplierOrder.getTotalStockInputQty(),
              supplierOrderDetail.getStockOutputQty()));
          supplierOrder.setCancelReturnPrice(NumberUtil.sub(supplierOrder.getCancelReturnPrice(),
              NumberUtil.mul(supplierOrderDetail.getStockOutputQty(),
                  supplierOrderDetail.getReturnPrice())));
          supplierOrder.setFinalPrice(NumberUtil.add(NumberUtil.toBigDecimal(supplierOrder.getFinalPrice()),
              NumberUtil.mul(supplierOrderDetail.getStockOutputQty(),
                  supplierOrderDetail.getReturnPrice())));
          // 退货状态? 待入库数量 字段y意思可能变更
          //退货状态 查询订单物料明细看是否有退货数量
          BigDecimal totalStockOutputQty = getTotalStockOutputQty(supplierOrder.getId());
          if (NumberUtil.isGreater(totalStockOutputQty, BigDecimal.ZERO)) {
            supplierOrder.setOrderReturnState(false);
          }
          // TODO 处理 订单物料退货数量
          supplierOrderDetail.setReturnQty(BigDecimal.ZERO);
          supplierOrderDetailV2Repository.save(supplierOrderDetail);
          // 待入库数量（已经发货的数量 - 采购入库数量 - 退库数量）
          detailed.setWaitStockInputQty(NumberUtil.sub(detailed.getShipQty(), detailed.getStockInputQty(),
              detailed.getStockOutputQty()));
          supplierOrderDetailV2Repository.save(detailed);

          // 更新入库进度
          purchaseOrderV2BaseService.setStockProgress(supplierOrder);
        }
        List<SupplierOrderDetailV2> orderDetailList =
            supplierOrderDetailV2Repository.findByOrderToFormIdAndState(
                supplierOrderDetail.getOrderToFormId(), Constants.STATE_OK);
        supplierOrderDetail.setState(Constants.STATE_NO);
        supplierOrderDetailV2Repository.save(supplierOrderDetail);
        if (orderDetailList.size() <= 1) {
          supplierOrderToFormV2Repository.findById(supplierOrderDetail.getOrderToFormId())
              .ifPresent(supplierOrderToForm -> {
                supplierOrderToForm.setState(Constants.STATE_NO);
                supplierOrderToFormV2Repository.save(supplierOrderToForm);
              });
        }
        return null;
      });
    } catch (Exception e) {
      throw e;
    } finally {
      lockUtils.unlockAllLocks(rLocks);
      lockUtils.unlockAllLocks(rLocks2);
    }

  }

  @Override
  public void deleteOutBound(String outBoundId) {
    SupplierOrderToFormV2 outBoundOrder = supplierOrderToFormV2Repository.findById(outBoundId)
        .orElseThrow(() -> CheckException.noFindException(SupplierOrderToFormV2.class, outBoundId));
    List<SupplierOrderDetailV2> byOrderToFormIdAndState =
        supplierOrderDetailV2Repository.findByOrderToFormIdAndState(outBoundOrder.getId(),
            Constants.STATE_OK);
    if (CollUtil.isNotEmpty(byOrderToFormIdAndState)) {
      for (SupplierOrderDetailV2 supplierOrderDetailV2 : byOrderToFormIdAndState) {
        this.deleteOutBoundProductDetail(outBoundOrder.getSupplierOrderId(),
            supplierOrderDetailV2.getId());
      }
    }
    outBoundOrder.setState(Constants.STATE_NO);
    supplierOrderToFormV2Repository.save(outBoundOrder);
  }

  public BigDecimal getTotalStockOutputQty(String supplierOrderId) {
    List<SupplierOrderToFormV2> supplierOrderToForms =
        supplierOrderToFormV2Repository.findBySupplierOrderIdAndTypeAndState(supplierOrderId,
            SupplierOrderFormType.DETAILED.getType(), Constants.STATE_OK);
    if (CollUtil.isEmpty(supplierOrderToForms)) {
      return BigDecimal.ZERO;
    }
    BigDecimal totalStockOutputQty = BigDecimal.ZERO;
    for (SupplierOrderToFormV2 supplierOrderToForm : supplierOrderToForms) {
      List<SupplierOrderDetailV2> supplierOrderDetails =
          supplierOrderDetailV2Repository.findByOrderToFormIdAndState(supplierOrderToForm.getId(),
              Constants.STATE_OK);
      for (SupplierOrderDetailV2 supplierOrderDetail : CollUtil.emptyIfNull(supplierOrderDetails)) {
        BigDecimal stockInputQty = supplierOrderDetail.getStockOutputQty();
        if (stockInputQty != null && NumberUtil.isGreater(stockInputQty, BigDecimal.ZERO)) {
          totalStockOutputQty = totalStockOutputQty.add(stockInputQty);
        }
      }
    }
    return totalStockOutputQty;
  }

  @Override
  public List<PurchaseOrderReturnV2VO> purchaseOrderReturn(String id) {
    if (StrUtil.isBlank(id)) {
      throw new CheckException("采购订单 id 不能为空");
    }
    SupplierOrderV2 supplierOrder = supplierOrderV2Repository.findById(id)
        .orElseThrow(() -> CheckException.noFindException(SupplierOrderV2.class, id));
    return CollUtil.emptyIfNull(
        supplierOrderToFormV2Service.getByTypeAndSupplierOrderId(SupplierOrderFormType.RETURN,
            supplierOrder.getId())).stream().map(supplierOrderToForm -> {
      PurchaseOrderReturnV2VO vo = new PurchaseOrderReturnV2VO();
      vo.setId(supplierOrderToForm.getId());
      vo.setTime(supplierOrderToForm.getTime());
      vo.setProductVoucher(supplierOrderToForm.getProductVoucher());
      vo.setProductVoucherYear(supplierOrderToForm.getProductVoucherYear());
      vo.setSapReversalNo(supplierOrderToForm.getSapReversalNo());
      vo.setReturnReason(supplierOrderToForm.getReturnReason());
      vo.setLogisticsCompany(supplierOrderToForm.getLogisticsCompany());
      vo.setLogisticsCode(supplierOrderToForm.getLogisticsCode());
      vo.setTrackNum(supplierOrderToForm.getTrackNum());
      // 设置退货的仓库执行状态
      vo.setExecutionStatus(supplierOrderToForm.getExecutionStatus());
      // 设置退货的仓库执行状态值
      Optional.ofNullable(
              SupplierOrderFormExecutionStatusEnum.fromKey(supplierOrderToForm.getExecutionStatus()))
          .ifPresent(
              executionStatusEnum -> vo.setExecutionStatusValue(executionStatusEnum.getValue()));
      vo.setReturnWarehouse(supplierOrderToForm.getWarehouseCode());
      vo.setReturnWarehouseName(supplierOrderToForm.getWarehouseName());
      vo.setConsignee(supplierOrderToForm.getConsignee());
      vo.setReceiveAddress(supplierOrderToForm.getReceiveAddress());
      //是否需要开红票
      vo.setNeedRedTicket(
          StrUtil.emptyToDefault(supplierOrderToForm.getNeedRedTicket(), Constants.STATE_NO));
      vo.setSapReturnNumber(StrUtil.emptyIfNull(supplierOrderToForm.getSapReturnNumber()));
      vo.setStatus(supplierOrderToForm.getStatus());
      // v2
      vo.setFormCode(supplierOrderToForm.getFormCode());
      vo.setReviewTime(supplierOrderToForm.getReviewTime());
      vo.setReviewId(supplierOrderToForm.getReviewId());
      vo.setReviewReason(supplierOrderToForm.getReviewReason());
      vo.setReviewStatus(supplierOrderToForm.getReviewStatus());
      List<PurchaseOrderProductDetailedReturnV2VO> shipProductDTOList =
          supplierOrderDetailV2Service.getByOrderToFormId(supplierOrderToForm.getId()).stream()
              .map(supplierOrderDetail -> {
                String orderDetailId = supplierOrderDetail.getId();
                List<InputInvoiceOrderWithDetailV2> inputInvoiceOrderWithDetails =
                    shareInputInvoiceProvider.getOrderInvoiceRelationListByDetailIdsRef(
                        Collections.singletonList(orderDetailId));
                List<PurchaseOrderInvoiceRelationV2> purchaseOrderInvoiceRelationList =
                    inputInvoiceOrderWithDetails.stream().map(
                            item -> new PurchaseOrderInvoiceRelationV2(item.getInputInvoiceOrder()))
                        .collect(Collectors.toList());
                PurchaseOrderProductDetailedReturnV2VO productDetailedReturnVO =
                    new PurchaseOrderProductDetailedReturnV2VO(supplierOrderDetail);
                //关联发票号
                productDetailedReturnVO.setPurchaseOrderInvoiceRelationList(
                    purchaseOrderInvoiceRelationList);
                return productDetailedReturnVO;
              }).collect(Collectors.toList());
      vo.setReturnProductDTOList(shipProductDTOList);
      return vo;
    }).collect(Collectors.toList());
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void addReturnOrder(PurchaseOrderReturnV2Form param, User user) {
    SupplierOrderV2 supplierOrder = supplierOrderV2Repository.findById(param.getId())
        .orElseThrow(() -> CheckException.noFindException(SupplierOrderV2.class, param.getId()));
    Supplier supplier = supplierRepository.findById(supplierOrder.getSupplierId())
        .orElseThrow(() -> new CheckException("供应商信息不存在！"));
    Boolean oneTimeSupplier = supplier.isOneTimeSupplier();
    if (!oneTimeSupplier) {
      String invoicingParty = supplierOrder.getInvoicingParty();
      supplierRepository.findFirstByEnterpriseNameAndState(invoicingParty, Constants.STATE_OK)
              .orElseThrow(() -> new CheckException("供应商信息不存在！"));
    }
    List<String> collectDetailIds = param.getProductDetailList().stream().map(ProductDetail::getId)
        .collect(Collectors.toList());
    List<SupplierOrderDetailV2> openOrderDetailList =
        supplierOrderDetailV2Repository.findAllById(collectDetailIds);
    Map<String, SupplierOrderDetailV2> id2OpenOrderDetail = openOrderDetailList.stream()
        .collect(Collectors.toMap(BaseSupplierOrderDetail::getId, v -> v, (k1, k2) -> k1));
    // 1. check
    checkAddReturnOrder(param, supplierOrder, openOrderDetailList, id2OpenOrderDetail);
    // 2. 处理退货
    param.setReturnReason(
        generateReturnReason(param.getReturnReason(), param.getReturnReasonDetails()));
    // 新增退货单
    SupplierOrderToFormV2 returnOrderForm =
        supplierOrderToFormV2Service.createSupplierOrderForm(param.getId(),
            SupplierOrderFormType.RETURN);
    returnOrderForm.setLogisticsCompany(param.getLogisticsCompany());
    returnOrderForm.setLogisticsCode(param.getLogisticsCode());
    returnOrderForm.setCode(param.getCode());
    returnOrderForm.setTrackNum(param.getTrackNum());
    returnOrderForm.setStatus(SupplierOrderFormStatus.RETURN.getStatus());
    returnOrderForm.setReturnReason(param.getReturnReason());
    returnOrderForm.setReturnWarehouse(param.getReturnWarehouse());
    returnOrderForm.setWarehouseCode(param.getReturnWarehouse());
    returnOrderForm.setWarehouseName(param.getReturnWarehouseName());
    returnOrderForm.setConsignee(param.getConsignee());
    returnOrderForm.setReceiveAddress(param.getReceiveAddress());
    //是否需要开红票
    returnOrderForm.setNeedRedTicket(param.getNeedRedTicket());
    returnOrderForm.setSapReturnNumber(supplierOrder.getCode());
    returnOrderForm.setPostingDate(param.getPostingDate());
    returnOrderForm.setReviewStatus(SupplierOrderFormReviewStatus.FEI_DA_AUDIT.getCode());
    returnOrderForm.setFormCode(SupplierOrderFormCodeGenerator.INSTANCE.generate(redissonClient,
        SupplierOrderFormType.RETURN));
    supplierOrderToFormV2Repository.save(returnOrderForm);
    List<ProductDetail> productDetailList = param.getProductDetailList();
    // 记录每个物料的最初退库数量
    Map<String, BigDecimal> originDetailToOutput = new HashMap<>();
    int index = 1;
    List<ReturnDetail> details = new ArrayList<>();
    for (ProductDetail productDetail : productDetailList) {
      SupplierOrderDetailV2 openOrderDetail =
          id2OpenOrderDetail.get(productDetail.getId());
      if (openOrderDetail == null) {
        continue;
      }
      originDetailToOutput.putIfAbsent(openOrderDetail.getId(),
          openOrderDetail.getStockOutputQty());
      SupplierOrderDetailV2 orderDetailBase =
          supplierOrderDetailV2Repository.findById(openOrderDetail.getDetailedId()).orElse(null);
      if (orderDetailBase == null) {
        continue;
      }
      SupplierOrderDetailV2 returnDetailV2 =
          saveSupplierOrderDetail(returnOrderForm, productDetail, openOrderDetail, orderDetailBase,
              supplierOrder.getId(), param.getNeedRedTicket(), supplierOrder.getOrderType(),
              index++);
      openOrderDetail.setInvoicableNum(
          openOrderDetail.getInvoicableNum().subtract(productDetail.getReturnNum()));
      // 使用最初获得的退库数量。防止同时退库同一物料，退库数量叠加
      BigDecimal baseOutputQty =
          Optional.ofNullable(originDetailToOutput.get(openOrderDetail.getId()))
              .orElse(BigDecimal.ZERO);
      openOrderDetail.setReturnQty(
          (BigDecimalUtil.setScaleBigDecimalHalfUp(baseOutputQty.add(productDetail.getReturnNum()),
              3)));
      openOrderDetail.setStockOutputQty(
          (BigDecimalUtil.setScaleBigDecimalHalfUp(baseOutputQty.add(productDetail.getReturnNum()),
              3)));
      openOrderDetail.setPurchaseOrderId(supplierOrder.getId());
      supplierOrderDetailV2Repository.save(openOrderDetail);
      //入库单id
      String orderToFormId = openOrderDetail.getOrderToFormId();
      SupplierOrderToFormV2 supplierOrderToForm =
          supplierOrderToFormV2Repository.findById(orderToFormId).orElse(null);
      if (supplierOrderToForm != null) {
        supplierOrderToForm.setReturnPrice(supplierOrderToForm.getReturnPrice()
            .subtract(openOrderDetail.getPrice().multiply(productDetail.getReturnNum())));
        supplierOrderToForm.setNum(
            supplierOrderToForm.getNum() == null ? productDetail.getReturnNum()
                : supplierOrderToForm.getNum().subtract(productDetail.getReturnNum()));
        supplierOrderToFormV2Repository.save(supplierOrderToForm);
      }
      details.add(buildProcessDetail(returnDetailV2, openOrderDetail));
    }
    supplierOrder.setOrderReturnState(true);
    supplierOrderV2Repository.save(supplierOrder);
    StartProcessVo startProcessVo =
        heroService.startSupplierOrderReturnProcess(supplierOrder, returnOrderForm, details, user);
    returnOrderForm.setReviewId(startProcessVo.getInstanceId());
    returnOrderForm.setReviewStatus(SupplierOrderFormReviewStatus.FEI_DA_AUDIT.getCode());
    returnOrderForm.setExecutionStatus(SupplierOrderFormExecutionStatusEnum.INITIAL.getKey());
    supplierOrderToFormV2Repository.save(returnOrderForm);
  }

  @Override
  public void auditCallBack(ApprovalResult approvalResult, SupplierOrderFormReviewStatus status) {
    String instanceId = approvalResult.getProcessInstanceId();
    String remark = approvalResult.getRemark();
    long finishTime = approvalResult.getFinishTime();
    SupplierOrderToFormV2 returnOrderToForm =
        supplierOrderToFormV2Repository.findFirstByReviewIdAndState(instanceId, Constants.STATE_OK)
            .orElseThrow(
                () -> CheckException.noFindException(SupplierOrderToForm.class, instanceId));
    String supplierOrderId = returnOrderToForm.getSupplierOrderId();
    SupplierOrderV2 supplierOrder = supplierOrderV2Repository.findById(supplierOrderId)
        .orElseThrow(() -> CheckException.noFindException(SupplierOrderV2.class, supplierOrderId));
    List<RLock> rLocks =
        lockUtils.lockAll(supplierOrder.getId(), PurchaseOrderV2Lock.PURCHASE_ORDER_RETURN_LOCK);
    List<RLock> rLocks2 =
        lockUtils.lockAll(supplierOrder.getId(), PurchaseOrderV2Lock.PURCHASE_ORDER_LOCK);
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(transactionStatus -> {
        // 1. 退库单状态
        returnOrderToForm.setReviewStatus(status.getCode());
        returnOrderToForm.setReviewTime(finishTime);
        returnOrderToForm.setReviewReason(remark);
        supplierOrderToFormV2Repository.saveAndFlush(returnOrderToForm);
        if (status == SupplierOrderFormReviewStatus.FEI_DA_REJECT) {
          return null;
        }
        // 2. 采购订单退货数据
        BigDecimal totalReturnNum = BigDecimal.ZERO;
        BigDecimal totalReturnAmount = BigDecimal.ZERO;
        // 退货明细
        List<SupplierOrderDetailV2> returnDetails =
            supplierOrderDetailV2Repository.getAllByOrderToFormIdAndStateOrderBySortNumAsc(
                returnOrderToForm.getId(), Constants.STATE_OK);
        for (SupplierOrderDetailV2 returnDetail : returnDetails) {
          BigDecimal curReturnNum = returnDetail.getStockOutputQty();
          totalReturnNum = totalReturnNum.add(curReturnNum);
          totalReturnAmount = totalReturnAmount.add(returnDetail.getPrice().multiply(curReturnNum));
          returnDetail.setInvoicableNum(curReturnNum);
          // 物料明细行
          SupplierOrderDetailV2 orderDetailBase = returnDetail.getDetailed();
          if (orderDetailBase == null) {
            throw new CheckException("数据异常物料明细" + returnDetail.getDetailedId() + "不存在");
          }
          orderDetailBase.setReturnQty(BigDecimalUtil.setScaleBigDecimalHalfUp(
              orderDetailBase.getReturnQty() == null ? curReturnNum
                  : orderDetailBase.getReturnQty().add(curReturnNum), 3));
          orderDetailBase.setStockOutputQty(BigDecimalUtil.setScaleBigDecimalHalfUp(
              orderDetailBase.getStockOutputQty() == null ? curReturnNum
                  : orderDetailBase.getStockOutputQty().add(curReturnNum), 3));
          orderDetailBase.setSettleQty(BigDecimalUtil.setScaleBigDecimalHalfUp(
              orderDetailBase.getSettleQty().subtract(curReturnNum), 3));
          orderDetailBase.setPurchaseOrderId(supplierOrder.getId());
          supplierOrderDetailV2Repository.save(orderDetailBase);
        }
        supplierOrderDetailV2Repository.saveAll(returnDetails);
        // 采购订单
        BigDecimal oldTotalProgress = new BigDecimal(
            StrUtil.subAfter(supplierOrder.getStockProgress(), CharUtil.SLASH, true));
        BigDecimal newTotalProgress = new BigDecimal(
            StrUtil.subBefore(supplierOrder.getStockProgress(), CharUtil.SLASH, true));
        supplierOrder.makeAndSetStockProgress(NumberUtil.sub(newTotalProgress, totalReturnNum),
            NumberUtil.sub(oldTotalProgress, totalReturnNum));
        supplierOrder.setTotalStockInputQty(
            NumberUtil.sub(supplierOrder.getTotalStockInputQty(), totalReturnNum));
        if (supplierOrder.getFinalPrice() != null
            && supplierOrder.getFinalPrice().compareTo(BigDecimal.ZERO) > 0) {
          supplierOrder.setFinalPrice(
              NumberUtil.sub(supplierOrder.getFinalPrice(), totalReturnAmount));
        }
        supplierOrder.setCancelReturnPrice(
            NumberUtil.add(supplierOrder.getCancelReturnPrice(), totalReturnAmount));
        supplierOrderV2Repository.save(supplierOrder);
        return null;
      });
    } catch (Exception e) {
      log.error("处理审核回调失败", e);
      throw e;
    } finally {
      lockUtils.unlockAllLocks(rLocks);
      lockUtils.unlockAllLocks(rLocks2);
    }
    // todo v.0.4.0 异步调用sap
    String traceId = MDC.get("TRACE_ID");
    CompletableFuture.runAsync(() -> {
      MDC.put("TRACE_ID", traceId);
      returnOrderSyncSapCallback(returnOrderToForm.getId(),supplierOrder.getCode());
    });
  }

  private static ReturnDetail buildProcessDetail(SupplierOrderDetailV2 returnDetailV2,
      SupplierOrderDetailV2 openOrderDetail) {
    ReturnDetail processDetailItem = new ReturnDetail();
    SupplierOrderProductV2 productV2 = returnDetailV2.getSupplierOrderProduct();
    processDetailItem.setProductCode(productV2.getCode());
    processDetailItem.setBrand(productV2.getBrand());
    processDetailItem.setProductName(productV2.getName());
    processDetailItem.setSpecification(productV2.getSpecification());
    processDetailItem.setModel(productV2.getModel());
    processDetailItem.setUnit(productV2.getUnit());
    processDetailItem.setBatchNo(returnDetailV2.getBatchNo());
    processDetailItem.setLedgerSubject(productV2.getLedgerSubject());
    processDetailItem.setInWareHouseName(returnDetailV2.getInWareHouseName());
    processDetailItem.setStockOutputQty(String.valueOf(returnDetailV2.getStockOutputQty()));
    processDetailItem.setStockInputQty(String.valueOf(openOrderDetail.getNum()));
    processDetailItem.setReturnPrice(String.valueOf(returnDetailV2.getReturnPrice()));
    processDetailItem.setReturnAmount(String.valueOf(returnDetailV2.getReturnAmount()));
    return processDetailItem;
  }

  /**
   * 校验新增退库单参数
   */
  private void checkAddReturnOrder(PurchaseOrderReturnV2Form param, SupplierOrderV2 supplierOrder,
      List<SupplierOrderDetailV2> openOrderDetailList,
      Map<String, SupplierOrderDetailV2> id2OpenOrderDetail) {
    // 物料行开票状态
    List<String> collectDetailIds = param.getProductDetailList().stream().map(ProductDetail::getId)
        .collect(Collectors.toList());
    List<InputInvoiceOrderWithDetailV2> invoiceDetails =
        shareInputInvoiceProvider.getOrderInvoiceRelationListByDetailIdsRef(collectDetailIds);
    boolean hasIngOrReject = invoiceDetails.stream().anyMatch(
        item -> StrUtil.equalsAny(item.getInputInvoiceOrder().getInvoiceState(),
            Constants.ORDER_INVOICE_STATE_ING, Constants.ORDER_INVOICE_STATE_REJECT));
    if (hasIngOrReject) {
      throw new CheckException("有审核中/驳回的发票，请检查");
    }
    // 开票数量要么都>0 要么都=0
    Set<String> greatZero = openOrderDetailList.stream().filter(
            item -> item.getInvoicedNum() != null
                && item.getInvoicedNum().compareTo(BigDecimal.ZERO) > 0)
        .map(SupplierOrderDetailV2::getId).collect(Collectors.toSet());
    Set<String> equZero = openOrderDetailList.stream()
        .filter(item -> NumberUtil.null2Zero(item.getInvoicedNum()).compareTo(BigDecimal.ZERO) == 0)
        .map(SupplierOrderDetailV2::getId).collect(Collectors.toSet());
    if (!greatZero.isEmpty() && !equZero.isEmpty()) {
      throw new CheckException("物料行开票状态不一致，请检查");
    }
    // 审批状态必须是审核通过的入库单
    List<String> inWareHouseIds =
        param.getProductDetailList().stream().map(ProductDetail::getInWareHouseId).distinct()
            .collect(Collectors.toList());
    boolean allAgree = supplierOrderToFormV2Repository.findAllById(inWareHouseIds).stream()
        .allMatch(
            form -> SupplierOrderFormReviewStatus.NORMAL.getCode().equals(form.getReviewStatus()));
    if (!allAgree) {
      throw new CheckException("存在入库单未审核通过，请检查");
    }
    // 无开票数量，开红票只能是否
    if (StrUtil.equals(param.getNeedRedTicket(), Constants.NEED_RED_TICKET)
        && greatZero.isEmpty()) {
      throw new CheckException("无开票数量，不允许开红票");
    }
    // 仓库 relation 必填
    boolean directShipment =
        WarehouseEnum.HAI_NING_DIRECT_SALES.getCode().equals(param.getReturnWarehouse());
    if (!directShipment) {
      if (StrUtil.equalsAny(param.getReturnWarehouse(),
          WarehouseEnum.FINISHED_PRODUCTS.getCode(),
          WarehouseEnum.HAI_NING_FINISHED_PRODUCTS.getCode(),
          "1008","1009","6001")
          && StrUtil.hasBlank(param.getLogisticsCompany(), param.getTrackNum())) {
        throw new CheckException("物流公司和物流单号不能为空");
      }
      if (StrUtil.equalsAny(param.getReturnWarehouse(), WarehouseEnum.FINISHED_PRODUCTS.getCode(),
          WarehouseEnum.HAI_NING_FINISHED_PRODUCTS.getCode()) && StrUtil.hasBlank(
          param.getReceiveAddress(), param.getConsignee())) {
        throw new CheckException("收件人和收件地址不能为空");
      }
    }
    // 校验库存
    // 科目分配类别
    String assignmentCategoryCode =
        openOrderDetailList.get(0).getSupplierOrderProduct().getAssignmentCategoryCode();
    if (StrUtil.isNotBlank(assignmentCategoryCode)) {
      if (StrUtil.isNotBlank(param.getReturnWarehouse()) || StrUtil.isNotBlank(
          param.getReturnWarehouseName())) {
        throw new CheckException("当前科目分配类别不允许选择仓库");
      }
      return;
    }
    if (StrUtil.hasBlank(param.getReturnWarehouse(), param.getReturnWarehouseName())) {
      throw new CheckException("仓库不能为空");
    }
    String groupCode = supplierOrder.getGroupCode();
    String warehouse = param.getReturnWarehouse();
    for (ProductDetail productDetail : param.getProductDetailList()) {
      SupplierOrderDetailV2 openOrderDetail = id2OpenOrderDetail.get(productDetail.getId());
      if (openOrderDetail == null) {
        continue;
      }
      SupplierOrderProductV2 productV2 = openOrderDetail.getSupplierOrderProduct();
      String productCode =
          Optional.ofNullable(productV2).map(SupplierOrderProductV2::getCode).orElse(null);
      String batchNo = StrUtil.equals(PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey(),
          supplierOrder.getOrderType()) ? productDetail.getBatchNumber()
          : openOrderDetail.getBatchNo();
      String projectType = openOrderDetail.getProjectType();
      if (StrUtil.hasBlank(productCode, batchNo)) {
        throw new CheckException("存在物料编码/批次号为空，请检查");
      }
      BigDecimal stockNum =
          inventoryRepository.findFirstByGroupCodeAndProductCodeAndWarehouseAndBatchNoAndState(
              groupCode, productCode, warehouse, batchNo, Constants.STATE_OK).map(inventory -> {
            if (StrUtil.equalsAny(projectType, Constants.PROJECT_TYPE_STANDARD, Constants.PROJECT_TYPE_WW)) {
              return inventory.getInventoryTotalNumber();
            } else {
              return inventory.getConsignmentInventoryNumber();
            }
          }).orElse(BigDecimal.ZERO);
      if (NumberUtil.isGreater(productDetail.getReturnNum(), stockNum)) {
        throw new CheckException(
            StrUtil.format("第{}行，库存数量不足，请核实！", productDetail.getRowNo()));
      }
    }
  }

  private SupplierOrderDetailV2 saveSupplierOrderDetail(SupplierOrderToFormV2 returnOrderForm,
      ProductDetail productDetail, SupplierOrderDetailV2 openOrderDetail,
      SupplierOrderDetailV2 orderDetailBase,String purchaseOrderId,String needRedTicket,
      String orderType, int index) {
    SupplierOrderDetailV2 supplierOrderDetail = new SupplierOrderDetailV2();
    supplierOrderDetail.setTaxRate(openOrderDetail.getTaxRate());
    supplierOrderDetail.setProductRate(openOrderDetail.getProductRate());
    supplierOrderDetail.setOrderToFormId(returnOrderForm.getId());
    supplierOrderDetail.setState(Constants.STATE_OK);
    supplierOrderDetail.setCreateTime(System.currentTimeMillis());
    supplierOrderDetail.setOrderProductId(openOrderDetail.getOrderProductId());
    supplierOrderDetail.setSupplierOrderProduct(openOrderDetail.getSupplierOrderProduct());
    // 明细id
    supplierOrderDetail.setSortNum(openOrderDetail.getSortNum());
    supplierOrderDetail.setDetailedId(orderDetailBase.getId());
    supplierOrderDetail.setStockOutputQty(productDetail.getReturnNum());
    supplierOrderDetail.setInWareHouseId(productDetail.getInWareHouseId());
    supplierOrderDetail.setInWareHouseName(productDetail.getInWareHouseName());
    supplierOrderDetail.setReturnQty(productDetail.getReturnNum());
    supplierOrderDetail.setBatchNo(
        StrUtil.equals(PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey(), orderType)
            ? productDetail.getBatchNumber() : openOrderDetail.getBatchNo());
    supplierOrderDetail.setStockOutputQty(productDetail.getReturnNum());
    BigDecimal subtract =
        openOrderDetail.getStockInputQty().subtract(NumberUtil.add(productDetail.getReturnNum(),
            openOrderDetail.getStockOutputQty()));
    if (NumberUtil.isLess(subtract,BigDecimal.ZERO)) {
      throw new CheckException("退库数量超过可退货数量");
    }
    supplierOrderDetail.setStockInputQty(subtract);
    supplierOrderDetail.setTotalPrice(openOrderDetail.getPrice().multiply(productDetail.getReturnNum()));
    supplierOrderDetail.setPrice(orderDetailBase.getPrice());
    //退库单价
    supplierOrderDetail.setReturnPrice(productDetail.getReturnPrice());
    //本次退库金额=行退库单价*本次退库数量
    supplierOrderDetail.setReturnAmount(
        productDetail.getReturnAmount() != null
            ? productDetail.getReturnAmount()
            : BigDecimalUtil.setScaleBigDecimalHalfUp(
                NumberUtil.mul(productDetail.getReturnNum(), productDetail.getReturnPrice()), 2)
    );
    //退库单添加采购订单id
    supplierOrderDetail.setPurchaseOrderId(purchaseOrderId);
    if (StrUtil.equals(needRedTicket, Constants.NEED_RED_TICKET)) {
      supplierOrderDetail.setOpenRedInvoice(true);
    }
    supplierOrderDetail.setIndex(String.valueOf(index));
    supplierOrderDetail.setWarehouse(returnOrderForm.getWarehouseCode());
    supplierOrderDetail.setWarehouseName(returnOrderForm.getWarehouseName());
    supplierOrderDetailV2Repository.save(supplierOrderDetail);
    return supplierOrderDetail;
  }

  private String generateReturnReason(String returnReason, String returnReasonDetails) {
    if (StrUtil.isNotBlank(returnReason) && StrUtil.isNotBlank(returnReasonDetails)) {
      return "【" + returnReason + "】 " + returnReasonDetails;
    }
    if (StrUtil.isBlank(returnReason) && StrUtil.isBlank(returnReasonDetails)) {
      return StrUtil.EMPTY;
    }
    if (StrUtil.isNotBlank(returnReason) && StrUtil.isBlank(returnReasonDetails)) {
      return "【" + returnReason + "】";
    }
    throw new CheckException("入参不合法");
  }

  /**
   * 退库冲销校验
   * @param v2ReturnForm
   * @param supplierOrderV2
   * @param v2DetailIds
   */
  private void checkReturnReversal(SupplierOrderToFormV2 v2ReturnForm, SupplierOrderV2 supplierOrderV2,
      List<String> v2DetailIds) {
    if (SupplierOrderFormStatus.REVERSAL.getStatus().equals(v2ReturnForm.getStatus())) {
      throw new CheckException("单据已经冲销，无法重复操作");
    }
    if (StrUtil.isNotBlank(v2ReturnForm.getSapReversalNo())) {
      throw new CheckException("单据已被冲销，无需重复操作");
    }
    if (StrUtil.isBlank(v2ReturnForm.getProductVoucher())) {
      throw new CheckException("无物料凭证号，无法冲销");
    }
    // 库位管理
    inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(
            supplierOrderV2.getGroupCode(), v2ReturnForm.getWarehouseCode(), Constants.STATE_OK)
        .ifPresent(inventoryLocation -> {
          String allowReversal = inventoryLocation.getInboundReturnReversal();
          if (StrUtil.equals(allowReversal, Constants.STATE_NO)) {
            throw new CheckException("此仓库不允许在SRM操作冲销，请联系仓管人员去SAP操作");
          }});
    boolean warehousePending = StrUtil.equals(v2ReturnForm.getExecutionStatus(),
        SupplierOrderFormExecutionStatusEnum.PENDING_EXECUTION.getKey());
    if (warehousePending) {
      throw new CheckException("此退货单仓库正在执行，无法冲销");
    }
    boolean hasUnOffset = shareInputInvoiceProvider.getOrderInvoiceRelationListByDetailIdsRef(
            v2DetailIds)
        .stream().anyMatch(relation ->
          !Constants.ORDER_INVOICE_STATE_OFFSET.equals(
              relation.getInputInvoiceOrder().getInvoiceState()));
    if (hasUnOffset) {
      throw new CheckException("有未冲销的发票，请先冲销发票");
    }
  }

  private void returnReversal(
      SupplierOrderToFormV2 v2ReturnForm, SupplierOrderV2 supplierOrderV2,
      List<SupplierOrderDetailV2> supplierOrderDetails) {
    Assert.notEmpty(supplierOrderDetails);
    Assert.notNull(v2ReturnForm);
    // 冲销需要更新采购明细行的退库数量减少
    for (SupplierOrderDetailV2 supplierOrderDetail : supplierOrderDetails) {
      String inWareHouseId = supplierOrderDetail.getInWareHouseId();
      supplierOrderDetail.setInvoicableNum(
          NumberUtil.add(supplierOrderDetail.getInvoicableNum(),
              supplierOrderDetail.getStockOutputQty()));
      // 入库单的orderToForm回退num
      supplierOrderToFormV2Repository.findById(inWareHouseId).ifPresent(inOrderForm -> {
        inOrderForm.setNum(inOrderForm.getNum().add(supplierOrderDetail.getStockOutputQty()));
        inOrderForm.setReturnPrice(
            inOrderForm.getReturnPrice().subtract(supplierOrderDetail.getReturnAmount()));
        supplierOrderToFormV2Repository.save(inOrderForm);
      });
      SupplierOrderDetailV2 detailed =
          supplierOrderDetailV2Repository.findById(supplierOrderDetail.getDetailedId()).orElse(null);
      if (detailed == null) {
        continue;
      }

      String detailedId = detailed.getId();
      // 退库数量减少
      detailed.setStockOutputQty(
          NumberUtil.sub(detailed.getStockOutputQty(), supplierOrderDetail.getStockOutputQty()));
      detailed.setReturnQty(
          NumberUtil.sub(detailed.getReturnQty(), supplierOrderDetail.getStockOutputQty()));
      // 入库单的退库数量减少（通过关联的入库单 id 和明细行 id 获取到行数据）
      SupplierOrderDetailV2 wareHouseDetailed =
          supplierOrderDetailV2Repository.getFirstByOrderToFormIdAndDetailedIdAndState(inWareHouseId,detailedId,Constants.STATE_OK);

      if (wareHouseDetailed != null) {
        wareHouseDetailed.setReturnQty(NumberUtil.sub(wareHouseDetailed.getReturnQty(),
            supplierOrderDetail.getStockOutputQty()));
        wareHouseDetailed.setStockOutputQty(NumberUtil.sub(wareHouseDetailed.getStockOutputQty(),
            supplierOrderDetail.getStockOutputQty()));
        // 加回可开票数量=冲销的退库单数量
        wareHouseDetailed.setInvoicableNum(
            NumberUtil.add(NumberUtil.toBigDecimal(wareHouseDetailed.getInvoicableNum()),
                supplierOrderDetail.getStockOutputQty()));
        supplierOrderDetailV2Repository.save(wareHouseDetailed);
      }
      // 实际交货数量（入库数量-退库数量）
      detailed.setSettleQty(
          NumberUtil.sub(detailed.getStockInputQty(), detailed.getStockOutputQty()));
      // 总入库数量增加
      supplierOrderV2.setTotalStockInputQty(NumberUtil.add(supplierOrderV2.getTotalStockInputQty(),
          supplierOrderDetail.getStockOutputQty()));
      supplierOrderV2.setCancelReturnPrice(NumberUtil.sub(supplierOrderV2.getCancelReturnPrice(),
          NumberUtil.mul(supplierOrderDetail.getStockOutputQty(),
              supplierOrderDetail.getReturnPrice())));
      supplierOrderV2.setFinalPrice(NumberUtil.add(supplierOrderV2.getFinalPrice(),
          NumberUtil.mul(supplierOrderDetail.getStockOutputQty(),
              supplierOrderDetail.getReturnPrice())));
      // 更新入库进度
      purchaseOrderV2BaseService.setStockProgress(supplierOrderV2);
      // 待入库数量（已经发货的数量 - 采购入库数量 - 退库数量）
      /* v2不使用
      detailed.setWaitStockInputQty(
          NumberUtil.sub(detailed.getShipQty(), detailed.getStockInputQty(),
              detailed.getStockOutputQty()));*/
      supplierOrderDetailV2Repository.save(detailed);
      supplierOrderDetailV2Repository.save(supplierOrderDetail);
      // 3. 采购订单状态变更
      // 判断订单状态 如果不存在发货单和入库单需要将该订单状态改为 待履约
      if (!supplierOrderToFormV2Service.existByTypeAndSupplierOrderIdAndExcludeStatusNotIn(
          ListUtil.toList(SupplierOrderFormType.WAREHOUSING, SupplierOrderFormType.DELIVER),
          supplierOrderV2.getId(), ListUtil.toList(SupplierOrderFormStatus.REVERSAL.getStatus(),
              SupplierOrderFormStatus.REVOKE.getStatus()))) {
        supplierOrderV2.setOrderState(SupplierOrderState.WAIT.getOrderState());
      }
      //入库单冲销订单状态改成完成
      if (StrUtil.subBefore(supplierOrderV2.getStockProgress(), '/', true).trim()
          .equals(StrUtil.subAfter(supplierOrderV2.getStockProgress(), '/', true).trim())) {
        supplierOrderV2.setOrderState(SupplierOrderState.COMPLETE.getOrderState());
      }
      supplierOrderV2Repository.save(supplierOrderV2);
    }
  }

  private void returnOrderSyncSapCallback(String returnOrderId,String orderNo) {
    try {
      returnOrderSyncSap(returnOrderId, null);
    } catch (Exception e) {
      String env = bootConfig.getEnv();
      DingUtils.sendMsgByWarningRobot(
          "【" + env + "环境 " + bootConfig.getAppName() + "】 【" + orderNo + "】调用SAP失败：" + "请求参数：" + returnOrderId
              + "异常信息：" + e.getMessage() + " ，请及时处理！", env);
    }
  }
  /**
   * 退库单同步SAP
   */
  @Override
  public void returnOrderSyncSap(String returnOrderId, Long returnTime) {
    // 退库单记录
    SupplierOrderToFormV2 v2ReturnForm = supplierOrderToFormV2Repository.findById(returnOrderId)
        .orElseThrow(() -> CheckException.noFindException(SupplierOrderToFormV2.class, returnOrderId));
    if (Objects.nonNull(returnTime)) {
      v2ReturnForm.setPostingDate(returnTime);
    }
    //退库单对应的订单
    SupplierOrderV2 supplierOrderV2 = supplierOrderV2Repository.findById(v2ReturnForm.getSupplierOrderId())
        .orElseThrow(() -> CheckException.noFindException(SupplierOrderV2.class, returnOrderId));
    // 审核状态
    if (!Objects.equals(SupplierOrderFormReviewStatus.NORMAL.getCode(),v2ReturnForm.getReviewStatus())) {
      throw new CheckException("当前退库单状态非审核通过状态！");
    }
     String returnWarehouse = v2ReturnForm.getWarehouseCode();
     // 库位管理 用来判断是否涉及WMS
     String allowReversal = inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(
             supplierOrderV2.getGroupCode(), returnWarehouse, Constants.STATE_OK)
         .map(InventoryLocation::getIsWms)
         .orElse(Constants.STATE_NO);
     // 获取退库单明细
     List<SupplierOrderDetailV2> supplierOrderDetails =
         supplierOrderDetailV2Repository.findByOrderToFormIdAndState(v2ReturnForm.getId(),
             Constants.STATE_OK);
     String inWareHouseId = supplierOrderDetails.get(0).getInWareHouseId();
     SupplierOrderDetailV2 detailed = supplierOrderDetails.get(0).getDetailed();
     // 入库单明细
     SupplierOrderDetailV2 wareHouseDetailed =
         supplierOrderDetailV2Repository.getFirstByOrderToFormIdAndDetailedIdAndState(inWareHouseId,detailed.getId(),Constants.STATE_OK);
     BigDecimal invoicedNum = wareHouseDetailed.getInvoicedNum();
     String executionStatus;
     if (Objects.equals(Constants.STATE_OK,allowReversal)) {
       // 涉及WMS
       if (NumberUtil.isGreater(invoicedNum,BigDecimal.ZERO)) {
         //若仓库涉及WMS的采购订单，且入库单有已开票数量，调用SAP021创建退换货订单接口，更新仓库执行状态为未审批未执行 注：无料号的要有总账科目
         UpdatePurchaseOrderSapParam updatePurchaseOrderSapParam =
             sapV2Factory.createBaseMM021ParamForReturnOrder(supplierOrderV2, v2ReturnForm,
                 supplierOrderDetails);
         UpdatePurchaseOrderRETURNDTO updatePurchaseOrderRETURNDTO =
             sapService.sapPurchaseOrderWithAlarm(updatePurchaseOrderSapParam,
                 supplierOrderV2.getCode());
         v2ReturnForm.setSapReturnNumber(updatePurchaseOrderRETURNDTO.getEbeln());
       } else {
         // 退库单审核通过后，若仓库涉及WMS的采购订单，且入库单无已开票数量，调用SAP031创建退库物料凭证接口（传递非厂直发标识），更新仓库执行状态为未审批未执行
         sapService.sapMaterialVoucherWithLockGroup(
             sapV2Factory.createMM031Param(supplierOrderV2, v2ReturnForm, supplierOrderDetails,
                 "X"));
       }
       executionStatus = SupplierOrderFormExecutionStatusEnum.PENDING_EXECUTION.getKey();
     } else {
       // 不涉及WMS
       if (NumberUtil.isGreater(invoicedNum,BigDecimal.ZERO)) {
         //退库单审核通过后，若仓库不涉及WMS的采购订单，且入库单有已开票数量，调用SAP075一键退换货订单接口，保存退货凭证号凭证时间，更新仓库执行状态为无需仓库执行 注：无料号的要有总账科目
         MM_075Result result = sapService.sapDirectReturnWithError(
             sapV2Factory.createMM075Param(supplierOrderV2, supplierOrderDetails, v2ReturnForm));
         v2ReturnForm.setProductVoucher(result.getReturnMessages().get(0).getProductVoucher());
         //sap退库单采购订单号
         v2ReturnForm.setSapReturnNumber(result.getReturnMessages().get(0).getEBELN());
       } else {
         // 退库单审核通过后，若仓库不涉及WMS的采购订单，且入库单无已开票数量，调用SAP031创建退库物料凭证接口，保存退货凭证号凭证时间，更新仓库执行状态为无需仓库执行
         ReceiptVoucherSynchronizationResult result =
             sapService.sapMaterialVoucherWithLockGroup(
                 sapV2Factory.createMM031Param(supplierOrderV2, v2ReturnForm, supplierOrderDetails,
                     ""));
         List<ReturnMessage> returnMessages = result.getReturnMessages();
         for (ReturnMessage returnMessage : returnMessages) {
           String lineItem = returnMessage.getLineItem();
           String documentNumber = returnMessage.getDocumentNumber();
           String purchaseOrderLineItems = returnMessage.getPurchaseOrderLineItems();
           v2ReturnForm.setProductVoucher(documentNumber);
           if (StrUtil.isNotBlank(purchaseOrderLineItems)) {
             Integer purchaseOrderLineItems1 = Integer.valueOf(purchaseOrderLineItems);
             Optional<SupplierOrderDetailV2> first =
                 supplierOrderDetails.stream().filter(supplierOrderDetail -> {
                   return Objects.equals(supplierOrderDetail.getSortNum(), purchaseOrderLineItems1);
                 }).findFirst();
             first.ifPresent(supplierOrderDetail -> {
               supplierOrderDetail.setBatchNo(returnMessage.getCharge());
               supplierOrderDetail.setSapRowId(lineItem);
               supplierOrderDetail.setSortNum(purchaseOrderLineItems1);
               supplierOrderDetailV2Repository.save(supplierOrderDetail);
             });
           }
         }
       }
       executionStatus = SupplierOrderFormExecutionStatusEnum.NO_NEED_EXECUTION.getKey();
       // 保存退货凭证时间
     }
     v2ReturnForm.setExecutionStatus(executionStatus);
     supplierOrderToFormV2Repository.save(v2ReturnForm);
  }

  @Override
  public void deleteRejectReturnOrder(String orderToFormId) {
    SupplierOrderToFormV2 v2ReturnForm = supplierOrderToFormV2Repository.findById(orderToFormId)
        .orElseThrow(
            () -> CheckException.noFindException(SupplierOrderToForm.class, orderToFormId));
    if (!StrUtil.equals(SupplierOrderFormType.RETURN.getType(), v2ReturnForm.getType())) {
      throw new CheckException("单据类型错误");
    }
    String supplierOrderId = v2ReturnForm.getSupplierOrderId();
    SupplierOrderV2 supplierOrderV2 = supplierOrderV2Repository.findById(supplierOrderId)
        .orElseThrow(() -> new CheckException("采购订单不存在"));
    // 单据明细
    List<SupplierOrderDetailV2> supplierOrderDetails =
        supplierOrderDetailV2Repository.getAllByOrderToFormIdAndStateOrderBySortNumAsc(
            orderToFormId, Constants.STATE_OK);
    List<RLock> rLocks = lockUtils.lockAll(new HashSet<>(Collections.singleton(supplierOrderId)),
        PurchaseOrderV2Lock.PURCHASE_ORDER_RETURN_LOCK);
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(status -> {
        if (!SupplierOrderFormReviewStatus.FEI_DA_REJECT.getCode().equals(v2ReturnForm.getReviewStatus())) {
          throw new CheckException("仅支持驳回的退库单删除");
        }
        // 1. 删除退库单
        v2ReturnForm.setState(Constants.STATE_NO);
        supplierOrderToFormV2Repository.save(v2ReturnForm);
        // 订单退货状态
        supplierOrderV2.setOrderReturnState(
            supplierOrderToFormV2Repository.countByTypeAndSupplierOrderIdAndState(
                SupplierOrderFormType.RETURN.getType(), supplierOrderV2.getId(), Constants.STATE_OK)
                > 0);
        supplierOrderV2Repository.save(supplierOrderV2);
        // 2. 删除退库单明细
        List<SupplierOrderDetailV2> wareHouseDetails = new ArrayList<>();
        for (SupplierOrderDetailV2 returnDetail : supplierOrderDetails) {
          returnDetail.setState(Constants.STATE_NO);
          SupplierOrderDetailV2 wareHouseDetailed =
              supplierOrderDetailV2Repository.getFirstByOrderToFormIdAndDetailedIdAndState(
                  returnDetail.getInWareHouseId(), returnDetail.getDetailedId(),
                  Constants.STATE_OK);
          if (wareHouseDetailed != null) {
            //退库数量减去本次删除的退库数量
            wareHouseDetailed.setReturnQty(NumberUtil.sub(wareHouseDetailed.getReturnQty(),
                returnDetail.getStockOutputQty()));
            wareHouseDetailed.setStockOutputQty(NumberUtil.sub(wareHouseDetailed.getStockOutputQty(),
                returnDetail.getStockOutputQty()));
            //加回可开票数量=删除的退库数量
            wareHouseDetailed.setInvoicableNum(
                NumberUtil.add(NumberUtil.toBigDecimal(wareHouseDetailed.getInvoicableNum()),
                    returnDetail.getStockOutputQty()));
            wareHouseDetails.add(wareHouseDetailed);
          }
        }
        supplierOrderDetailV2Repository.saveAll(supplierOrderDetails);
        if (!wareHouseDetails.isEmpty()) {
          supplierOrderDetailV2Repository.saveAll(wareHouseDetails);
        }
        return null;
      });
    } catch (Exception e) {
      log.error("删除驳回的退库单失败", e);
      throw e;
    } finally {
      lockUtils.unlockAllLocks(rLocks);
    }
  }

  @Override
  public void returnReversal(String sapVoucherNo, String sapReversalVoucherNo, User user) {
    // 退库单记录
    SupplierOrderToFormV2 returnForm =
        supplierOrderToFormV2Repository.findFirstByProductVoucherAndState(sapVoucherNo,
            Constants.STATE_OK);
    String supplierOrderId = returnForm.getSupplierOrderId();
    SupplierOrderV2 supplierOrderV2 = supplierOrderV2Repository.findById(supplierOrderId)
        .orElseThrow(() -> new CheckException("采购订单不存在"));
    List<RLock> rLocks = lockUtils.lockAll(supplierOrderId,
        PurchaseOrderV2Lock.PURCHASE_ORDER_RETURN_LOCK);
    List<RLock> rLocks2 = lockUtils.lockAll(supplierOrderId,
        PurchaseOrderV2Lock.PURCHASE_ORDER_LOCK);
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(transactionStatus -> {
        if (!StrUtil.equals(SupplierOrderFormType.RETURN.getType(),
                    returnForm.getType())) {
          throw new CheckException("退库单不存在");
        }
        if (SupplierOrderFormReviewStatus.NORMAL.getCode().equals(returnForm.getReviewStatus())) {
          throw new CheckException("退库单审核状态未通过");
        }
        List<SupplierOrderDetailV2> supplierOrderDetails =
            supplierOrderDetailV2Repository.getAllByOrderToFormIdAndStateOrderBySortNumAsc(
                returnForm.getId(), Constants.STATE_OK);
        // 退库单状态
        returnForm.setSapReversalNo(sapReversalVoucherNo);
        returnForm.setStatus(SupplierOrderFormStatus.REVERSAL.getStatus());
        // 处理后置影响
        returnReversal(returnForm, supplierOrderV2, supplierOrderDetails);
        return null;
      });
    } catch (Exception e) {
      log.error("处理审核回调失败", e);
      throw e;
    } finally {
      lockUtils.unlockAllLocks(rLocks);
      lockUtils.unlockAllLocks(rLocks2);
    }
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.CANCELLATION_FORM_PAGE_V2)
  public PageResult<RetreatWarehouseV2DTO> outBoundDeliveryPageRef(OutBoundDeliveryV2Prams param,
      User user) {
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    } else {
      // 用户权限下采购名称集合
      userNameList = permissionTypeProvider.getConcatNumUserNameList(user.getId(),
          Constants.USER_PERMISSION_SUPPLIER_ORDER,
          ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
    PageResult<RetreatWarehousePageV2DTO> pageResult =
        supplierOrderToFormDao.outBoundDeliveryPageRef(param.toQueryMap(userNameList, purchaseId, createMan));
    List<RetreatWarehousePageV2DTO> content = pageResult.getContent();
    List<String> detailIds =
        content.stream().map(RetreatWarehousePageV2DTO::getDetailId).distinct().collect(Collectors.toList());
    List<InputInvoiceOrderWithDetailV2> inputInvoiceOrderWithDetailList =
        shareInputInvoiceProvider.getOrderInvoiceRelationListByDetailIdsRef(detailIds);
    List<RetreatWarehouseV2DTO> result = content.stream().map(returnDto -> {
      RetreatWarehouseV2DTO retreatWarehouseDTO = new RetreatWarehouseV2DTO(returnDto);
      // 根据detailId过滤出相应的inputInvoice
      List<InputInvoiceOrderWithDetailV2> filterInputInvoiceOrderWithDetail = inputInvoiceOrderWithDetailList.stream()
          .filter(item -> item.getDistinctDetailIds().contains(returnDto.getDetailId()))
          .collect(Collectors.toList());
      List<PurchaseOrderInvoiceRelationV2> inputInvoiceOrders = filterInputInvoiceOrderWithDetail.stream()
          .map(item -> new PurchaseOrderInvoiceRelationV2(item.getInputInvoiceOrder())).distinct()
          .collect(Collectors.toList());
      retreatWarehouseDTO.setPurchaseOrderInvoiceRelationList(inputInvoiceOrders);
      return retreatWarehouseDTO;
    }).collect(Collectors.toList());
    return new PageResult<>(result, pageResult.getTotalCount(), pageResult.getTotalPages(),
        pageResult.getPageNo(), pageResult.getPageSize());
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.CANCELLATION_FORM_PAGE_V2)
  public PurchaseOrderOutBoundDeliveryStatistics outBoundDeliveryStatistics(
      OutBoundDeliveryV2Prams param, User user) {
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)
        || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    } else {
      // 用户权限下采购名称集合
      userNameList = permissionTypeProvider.getConcatNumUserNameList(user.getId(),
          Constants.USER_PERMISSION_SUPPLIER_ORDER,
          ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
    return supplierOrderToFormDao.outBoundDeliveryStatistics2(param.toQueryMap(userNameList, purchaseId, createMan));
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.CANCELLATION_FORM_PAGE_V2)
  public void exportOutBoundDelivery(User user, OutBoundDeliveryV2Prams param) {
    checkOrderExportPermission(user.getId());
    param.setPageNo(1);
    param.setPageSize(Integer.MAX_VALUE);
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    } else {
      // 用户权限下采购名称集合
      userNameList =
          permissionTypeProvider.getConcatNumUserNameList(
              user.getId(),
              Constants.USER_PERMISSION_SUPPLIER_ORDER,
              ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
    Map<String, Object> queryMap =
        param.toQueryMap(userNameList, purchaseId, createMan);
    queryMap.put("version", ShardingContext.getVersion());
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(user.getCode()),
        "导出-2.0退库单",
        user.getId(),
        Constants.PLATFORM_TYPE_AFTER,
        null,
        ""
    );
    missionRepository.saveAndFlush(mission);
    missionDispatcher.doDispatch(mission.getId(), JSON.toJSONString(queryMap),
        MissionTypeEnum.BATCH_TASK_EXPORT_RETURN_STORAGE_ORDER_V2);
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.CANCELLATION_FORM_PAGE_V2)
  public Long getExportOutBoundCount(OutBoundDeliveryV2Prams param, User user) {
    List<String> ids = param.getIds();
    if (CollUtil.isNotEmpty(ids)) {
      return (long) ids.size();
    }
    PageResult<RetreatWarehouseV2DTO> pageResult =
        this.outBoundDeliveryPageRef(param, user);
    return pageResult.getTotalCount();
  }

  private void checkOrderExportPermission(String id) {
    String permissionCode = permissionTypeProvider.getUserPermissionCodeByUserIdAndType(id,
        Constants.USER_PERMISSION_EXPORT_WAREHOUSE_RETURN);
    permissionCode = StrUtil.blankToDefault(permissionCode,Constants.NOT_EXPORT_IMPORT_KEY);
    if (StrUtil.equals(permissionCode,Constants.NOT_EXPORT_IMPORT_KEY)) {
      throw new CheckException("您没有导出入库单/退库单的权限！");
    }
  }
}
