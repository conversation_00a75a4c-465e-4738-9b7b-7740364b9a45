package com.xhgj.srm.v2.service.purchaseOrder;/**
 * @since 2025/4/28 15:28
 */

import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderProductStatistics;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.v2.dto.UpdateProductDetailParamV2;
import com.xhgj.srm.v2.dto.purchaseOrder.SupplierOrderCountV2DTO;
import com.xhgj.srm.v2.form.purchaseOrder.product.PurchaseOrderProductExport;
import com.xhgj.srm.v2.form.purchaseOrder.product.PurchaseOrderProductTableHeaderV2Query;
import com.xhgj.srm.v2.form.purchaseOrder.product.PurchaseOrderProductV2QueryForm;
import com.xhgj.srm.v2.vo.PurchaseOrderProductV2DetailedVO;
import com.xhgj.srm.v2.vo.purchaseOrder.PurchaseOrderProductV2ListVO;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/4/28 15:28:38
 *@description
 */
public interface PurchaseOrderV2ProductService {

  /**
   * 根据条件查询采购订单物料列表
   * @param form
   * @param user
   * @return
   */
  PageResult<PurchaseOrderProductV2ListVO> getPagePurchaseOrderProductPageRef(PurchaseOrderProductV2QueryForm form, User user);

  /**
   * 获得采购订单物料状态 对应的数量
   * @param form
   * @param user
   * @return
   */
  SupplierOrderCountV2DTO getOrderProductCount(PurchaseOrderProductV2QueryForm form, User user);

  /**
   * 查询采购订单物料统计信息
   * @param form
   * @param user
   * @return
   */
  PurchaseOrderProductStatistics getPagePurchaseOrderStatisticsForProduct(PurchaseOrderProductV2QueryForm form, User user);

  /**
   * 采购订单物料表头筛选
   * @param param
   * @param user
   * @return
   */
  List<Object> getProductListByTableHeaderRef(PurchaseOrderProductTableHeaderV2Query param, User user);

  void updateProductDetail(UpdateProductDetailParamV2 param);

  /**
   * 导出采购订单物料
   * @param export
   */
  void exportPurchaseOrderProduct(PurchaseOrderProductExport export, User user);

  List<PurchaseOrderProductV2DetailedVO> getPurchaseOrderProductDetailed(String id);
}
