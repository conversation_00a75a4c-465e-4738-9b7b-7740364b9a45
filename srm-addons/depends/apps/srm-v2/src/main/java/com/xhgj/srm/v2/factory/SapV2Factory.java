package com.xhgj.srm.v2.factory;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalParams.DATADTO.HEADDTO;
/**
 * @since 2025/4/23 14:02
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_FileRelationType;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.enums.SettleCurrency;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderSyncStatus;
import com.xhgj.srm.common.utils.SAPToolUtils;
import com.xhgj.srm.common.utils.TimeStampUtil;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrderSync;
import com.xhgj.srm.jpa.entity.v2.PurchaseApplyForOrderV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.repository.SupplierOrderSyncRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param.PurchaseOrderReturnDATADTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param.PurchaseOrderReturnDATADTO.PurchaseOrderReturnHEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_084Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.PurchaseApplyForOrderUpdateParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalParams;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam.DataInfo;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam.Head;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam.Item;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO.ITEMDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO.ITEMDTO.WWDTO;
import com.xhgj.srm.v2.dto.purchaseOrder.SupplierOrderOriginData;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form.EntrustProduct;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form.ProductDetailAdd;
import com.xhgj.srm.v2.helper.PurchaseOrderSapCompareUtil;
import com.xhgj.srm.v2.repository.PurchaseApplyForOrderV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/4/23 14:02:05
 *@description 构建SAP相关入参工厂 v2版本
 */
@Component
public class SapV2Factory {

  @Resource
  SupplierRepository supplierRepository;
  @Resource
  FileDao fileDao;
  @Resource
  SrmConfig srmConfig;
  @Resource
  SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;

  @Resource
  InventoryLocationRepository inventoryLocationRepository;
  @Resource
  SupplierOrderSyncRepository supplierOrderSyncRepository;
  @Resource
  SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;
  @Resource
  PurchaseApplyForOrderV2Repository purchaseApplyForOrderV2Repository;

  /**
   * 创建053取消
   */
  public List<PurchaseApplyForOrderUpdateParam> create053CancelPurchaseParam(
      List<PurchaseApplyForOrderV2> forms, String state) {
    List<PurchaseApplyForOrderUpdateParam> res = new ArrayList<>();
    for (PurchaseApplyForOrderV2 form : forms) {
      PurchaseApplyForOrderUpdateParam build =
          PurchaseApplyForOrderUpdateParam.builder()
              .applyForNo(form.getApplyForOrderNo())
              .productRowId(form.getRowId())
              .cancelState(state)
              .purchaseManId(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES)
              .PurchaseDepartmentErpCode("@")
              .purchaseMan(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES).build();
      res.add(build);
    }
    return res;
  }
  /**
   * 删除订单明细数据
   * @param supplierOrderId
   */
  public void delSupplierOrderInfo(String supplierOrderId) {
    if (StrUtil.isBlank(supplierOrderId)) {
      return;
    }
    // 删除 订单明细，物料数据
    // 按供应商订单 ID 和类型和状态查找
    List<SupplierOrderToFormV2> supplierOrderToForms =
        supplierOrderToFormV2Repository.findBySupplierOrderIdAndTypeAndState(
            supplierOrderId, SupplierOrderFormType.DETAILED.getType(), Constants.STATE_OK);
    for (SupplierOrderToFormV2 supplierOrderToForm : supplierOrderToForms) {
      // 逻辑删除
      supplierOrderToForm.setState(Constants.STATE_DELETE);
      supplierOrderToFormV2Repository.save(supplierOrderToForm);
      List<SupplierOrderDetailV2> byOrderToFormIdAndState =
          supplierOrderDetailV2Repository.findByOrderToFormIdAndState(
              supplierOrderToForm.getId(), Constants.STATE_OK);
      for (SupplierOrderDetailV2 supplierOrderDetail : byOrderToFormIdAndState) {
        if(StrUtil.isNotEmpty(supplierOrderDetail.getPurchaseApplyForOrderId())){
          PurchaseApplyForOrderV2 purchaseApplyForOrder =
              purchaseApplyForOrderV2Repository
                  .findById(supplierOrderDetail.getPurchaseApplyForOrderId())
                  .orElse(null);
          if(purchaseApplyForOrder!=null){
            purchaseApplyForOrder.setOrderGoodsNumber(purchaseApplyForOrder.getOrderGoodsNumber().subtract(supplierOrderDetail.getNum()));
            purchaseApplyForOrder.updateOrderGoodsStateCheckLock();
            purchaseApplyForOrderV2Repository.save(purchaseApplyForOrder);
          }
        }
        supplierOrderDetail.setState(Constants.STATE_DELETE);
        supplierOrderDetailV2Repository.save(supplierOrderDetail);
      }
    }
  }


  /**
   * 创建053采购申请修改入参
   */
  public List<PurchaseApplyForOrderUpdateParam> createPurchaseApplyForOrderUpdateParam(
      List<PurchaseApplyForOrderV2> forms, String reviewMark, Boolean success) {
    List<PurchaseApplyForOrderUpdateParam> res = new ArrayList<>();
    for (PurchaseApplyForOrderV2 form : forms) {
      PurchaseApplyForOrderUpdateParam one = new PurchaseApplyForOrderUpdateParam();
      one.setPurchaseManId(form.getPurchaseManNumber());
      one.setPurchaseMan(form.getPurchaseMan());
      one.setCancelState(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES);
      one.setPurchaseDepartmentErpCode(form.getPurchaseDepartment());
      one.setApplyForNo(form.getApplyForOrderNo());
      one.setProductRowId(form.getRowId());
      one.setApplyForCount(
          Optional.ofNullable(form.getApplyForNumber()).orElse(BigDecimal.ZERO).stripTrailingZeros()
              .toPlainString());
      one.setApplyForRemark(form.getApplicationFormRemarks());
      one.setApplyForResult(success);
      one.setApplyForOpinion(reviewMark);
      res.add(one);
    }
    return res;
  }

  public UpdatePurchaseOrderSapParam createMM021ParamForSupplierOrder(SupplierOrderV2 supplierOrder,
      List<SupplierOrderDetailV2> supplierOrderDetails,
      List<SupplierOrderProductV2> supplierOrderProducts,
      Map<String, PurchaseApplyForOrderV2> purchaseApplyForOrderMap, PurchaseOrderAddV2Form form,
      SupplierOrderOriginData orderOriginData) {
    UpdatePurchaseOrderSapParam newSapParam =
        createBaseMM021ParamForSupplierOrder(supplierOrder, supplierOrderDetails,
            supplierOrderProducts, purchaseApplyForOrderMap, form);
    SupplierOrderV2 originSupplierOrder = orderOriginData.getOriginSupplierOrder();
    // 查询上次审核成功的SAP日志
    SupplierOrderSync lastSuccessSap =
        supplierOrderSyncRepository.findFirstByStateAndTargetAndSupplierOrderIdAndReviewStatusOrderByCreateTimeDesc(
            Constants.STATE_OK, "SAP", supplierOrder.getId(),
            SupplierOrderSyncStatus.SUCCESS.getCode());
    if (lastSuccessSap != null) {
      UpdatePurchaseOrderSapParam originSapParam =
          createBaseMM021ParamForSupplierOrder(originSupplierOrder,
              orderOriginData.getOriginSupplierOrderDetailList(),
              orderOriginData.getOriginSupplierOrderProductList(), purchaseApplyForOrderMap, form);
      // 比较两个param字段，如果出现一致的则设值为@
      // 1. 定义各类型的ID字段映射
      Map<Class<?>, List<String>> idFieldMap = new HashMap<>();

      // 订单项通过行号(ebelp)标识
      idFieldMap.put(UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO.ITEMDTO.class,
          Collections.singletonList("ebelp"));

      // 组件通过行号+组件物料编码标识
      idFieldMap.put(UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO.ITEMDTO.WWDTO.class,
          Arrays.asList("ebelp", "wlzj"));

      // 2. 定义需要忽略的字段
      Set<String> ignoredFields = new HashSet<>();
      ignoredFields.add("data.head.zsp");  // 忽略自动审批标志
      ignoredFields.add("data.head.zcdoa"); // 忽略传递OA标志
      ignoredFields.add("data.head.ebeln"); // 忽略采购订单号
      ignoredFields.add("data.head.waers"); // 忽略币种
      // ITEMDTO的标识字段
      ignoredFields.add("data.head.item[*].ebelp");  // 忽略所有项目行号字段

      // WWDTO的标识字段
      ignoredFields.add("data.head.item[*].ww[*].ebelp");  // 忽略组件的行号字段
      ignoredFields.add("data.head.item[*].ww[*].wlzj");  // 忽略组件的物料编码字段

      // 3. 执行比较
      PurchaseOrderSapCompareUtil.compareAndMark(newSapParam, originSapParam, "@",
          Constants_Sap.DEFAULT_ATTRIBUTE_VALUES, idFieldMap, ignoredFields);
    }
    return newSapParam;
  }

  public UpdatePurchaseOrderSapParam createBaseMM021ParamForSupplierOrder(
      SupplierOrderV2 supplierOrder,
      List<SupplierOrderDetailV2> supplierOrderDetails,
      List<SupplierOrderProductV2> supplierOrderProducts,
      Map<String,PurchaseApplyForOrderV2> purchaseApplyForOrderMap,
      PurchaseOrderAddV2Form form
  ) {
    Supplier supplier = supplierRepository.findById(supplierOrder.getSupplierId())
        .orElseThrow(() -> new CheckException("未找到对应的供应商"));
    Supplier invoiceSupplier =
        supplierRepository.findFirstByEnterpriseNameAndState(supplierOrder.getInvoicingParty(),
            Constants.STATE_OK).orElseThrow(() -> new CheckException("未找到对应的开票方"));
    UpdatePurchaseOrderSapParam result = new UpdatePurchaseOrderSapParam();
    UpdatePurchaseOrderDATADTO data = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO head = new UpdatePurchaseOrderHEADDTO();
    List<ITEMDTO> itemdtos = new ArrayList<>();
    head.setEbeln(supplierOrder.getCode());
    head.setBsart(supplierOrder.getOrderType());
    head.setEkorg(supplierOrder.getGroupCode());
    head.setEkgrp(supplierOrder.getPurchaseDeptCode());
    head.setBukrs(supplierOrder.getGroupCode());
    File file = fileDao.getFirstFileByRIdAndRType(supplierOrder.getId(), Constants_FileRelationType.ORDER_CONTRACT);
    // 抬头文本传值优化。SRM2.0新增采购订单调用021接口传递抬头文本，未上传合同附件的传值说明
    StringBuilder remark = new StringBuilder();
    // 如果合同附件存在
    if (file != null) {
      remark.append("【点击查看合同：").append(srmConfig.getContractFileH5Url()).append("V2").append("?id=").append(supplierOrder.getId()).append("】");
    }
    // 如果备注存在
    if (StrUtil.isNotBlank(supplierOrder.getMark())) {
      remark.append(supplierOrder.getMark());
    }
    if (file == null) {
      // 如果有值则补充逗号
      if (StrUtil.isNotBlank(remark.toString())) {
        remark.append("，");
      }
      remark.append("未上传合同附件");
    }
    head.setZttwb(remark.toString());
    // 凭证日期 若为退换货订单则传递退换货订单创建日期字段
    // Long 转 str
    head.setBedat(DateUtil.format(new Date(supplierOrder.getCreateTime()), DatePattern.PURE_DATE_PATTERN));
    head.setLifnr(supplier.getMdmCode());
    head.setLifn2(invoiceSupplier.getMdmCode());
    head.setCountry("CN");
    head.setSpras(null);
    head.setZterm_01(StrUtil.isNotEmpty(form.getPaymentTermsStr()) ? form.getPaymentTermsStr() : StrUtil.EMPTY);
    //如果有项目类别是标准&&关联采购申请是空，调用MM021接口时，ZZSP_01主管审批传递X,默认传空
    //如果有项目类别是标准&&关联采购申请是空&&是否免费为“否”&&关联销售订单号是空，调用MM021接口时，ZZSP_01主管审批传递X
    boolean supervisorApproval = false;
    if (StrUtil.equals(form.getOrderType(), PurchaseOrderTypeEnum.SAP.getKey())
        && CollUtil.isNotEmpty(form.getProductList())) {
      supervisorApproval = form.getProductList().stream().anyMatch(
          item -> StrUtil.isEmpty(item.getPurchaseApplyForOrderId()) && StrUtil.equals(
              item.getFreeState(), Constants.STATE_NO)&&StrUtil.isEmpty(item.getSalesOrderNo()));
    }
    head.setZzsp_01(supervisorApproval ? Constants_Sap.CONFIRM_IDENTIFICATION : StrUtil.EMPTY);
    if(Constants.SUPPLIER_TYPE_PROVISIONAL.equals(supplier.getSupType())){
      head.setName1(supplier.getEnterpriseName());
      head.setCity1("一次性供应商");
    }else {
      head.setName1(null);
      head.setCity1(null);
    }
    //20250210 创建、修改采购订单时，如果WAERS货币码是JPY，汇率传递订单汇率*100
    head.setWkurs(StrUtil.equals(SettleCurrency.PRE004.getKey(), supplierOrder.getMoneyCode())
        ? NumberUtil.mul(NumberUtil.toBigDecimal(supplierOrder.getOrderRate()), BigDecimal.valueOf(100)).toString()
        : supplierOrder.getOrderRate());
    final String payment_iterm = "0001";
    head.setZterm(payment_iterm);
    head.setWaers(supplierOrder.getMoneyCode());
    head.setSRMID(null);
    // 整单创建和修改 OA传N 是否自动审批传空
    head.setZsp("");
    head.setZcdoa("N");
    head.setBednr(supplierOrder.getPurchaseCode());
    head.setAfnam(supplierOrder.getPurchaseMan().substring(4, supplierOrder.getPurchaseMan().length()));
    head.setKufix("X");
    for (int i = 0; i < supplierOrderDetails.size(); i++) {
      SupplierOrderProductV2 supplierOrderProduct = supplierOrderProducts.get(i);
      SupplierOrderDetailV2 supplierOrderDetail = supplierOrderDetails.get(i);
      List<ProductDetailAdd> productList = form.getProductList();
      ProductDetailAdd productDetailAdd = productList.get(i);
      PurchaseApplyForOrderV2 purchaseApplyForOrder =
          purchaseApplyForOrderMap.get(supplierOrderDetail.getPurchaseApplyForOrderId());
      ITEMDTO itemdto = new ITEMDTO();
      //默认赋值空的参数
      itemdto.setBanfn(StrUtil.EMPTY);
      itemdto.setBnfpo(StrUtil.EMPTY);
      itemdto.setCharX(StrUtil.EMPTY);
      itemdto.setElikz(StrUtil.EMPTY);
      itemdto.setKnttp(StrUtil.EMPTY);
      itemdto.setKostl(StrUtil.EMPTY);
      itemdto.setLoekz(StrUtil.EMPTY);
      itemdto.setMatkl(StrUtil.EMPTY);
      itemdto.setZgsje(StrUtil.EMPTY);
      itemdto.setZjsj(StrUtil.EMPTY);
      itemdto.setZyyje(StrUtil.EMPTY);
      itemdto.setZzfjf(StrUtil.EMPTY);
      itemdto.setZzkhddh_01(StrUtil.EMPTY);
      itemdto.setZZPOSNR(StrUtil.EMPTY);
      itemdto.setZZVBELN(StrUtil.EMPTY);
      itemdto.setZzwlsl(StrUtil.EMPTY);
      itemdto.setZzxmbh_01(StrUtil.EMPTY);
      itemdto.setRetpo(StrUtil.EMPTY);
      itemdto.setZzf1(StrUtil.EMPTY);
      itemdto.setEbelp(supplierOrderDetail.getSortNum().toString());
      itemdto.setMatnr(supplierOrderProduct.getCode());
      itemdto.setTxz01(supplierOrderProduct.getName());
      itemdto.setMeins(supplierOrderProduct.getUnitCode());
      itemdto.setMenge(supplierOrderDetail.getNum().stripTrailingZeros().toPlainString());
      itemdto.setWerks(supplierOrder.getGroupCode());
      itemdto.setLgort(supplierOrderDetail.getWarehouse());
      itemdto.setAplfz(supplierOrderDetail.getPurchaseDeliverTime() == null ? StrUtil.EMPTY
          : DateUtils.formatTimeStampToPureDate(supplierOrderDetail.getPurchaseDeliverTime()));
      itemdto.setMatkl(supplierOrderProduct.getItemGroupCode());
      itemdto.setMwskz(Constants.TAX_RATE_TYPE_NUM.get(supplierOrderDetail.getTaxRate()));
      Pair<BigDecimal, Integer> convertSapPrice =
          SAPToolUtils.convertSapPrice(supplierOrderDetail.getPrice(), 2);
      itemdto.setNetpr(convertSapPrice.getKey().stripTrailingZeros().toPlainString());
      itemdto.setPeinh(convertSapPrice.getValue().toString());
      itemdto.setBprme(supplierOrderProduct.getUnitCode());
      if(supplierOrderDetail.getTariffAmount() !=null){
        itemdto.setZgsje(supplierOrderDetail.getTariffAmount().stripTrailingZeros().toPlainString());
      }
      if(supplierOrderDetail.getFreight() !=null){
        itemdto.setZyyje(supplierOrderDetail.getFreight().toString());
      }
      if(supplierOrderDetail.getIncidentalAmount() !=null){
        itemdto.setZzf1(supplierOrderDetail.getIncidentalAmount().toString());
      }
      itemdto.setZjsj(BooleanUtil.isTrue(supplierOrder.getSelfState()) ? "0"
          : NumberUtil.toBigDecimal(supplierOrderDetail.getSettlementPrice()).stripTrailingZeros().toPlainString());
      //寄售转自有订单传对应批号
      if (StrUtil.equals(supplierOrder.getOrderType(),
          PurchaseOrderTypeEnum.CONSIGNMENT_TO_OWNED.getKey())) {
        itemdto.setCharX(supplierOrderDetail.getBatchNo());
      }
      itemdto.setZmfbs(StrUtil.equals(supplierOrderDetail.getFreeState(), SimpleBooleanEnum.YES.getKey()) ?
          Constants_Sap.CONFIRM_IDENTIFICATION : "");
      itemdto.setPstyp(supplierOrderDetail.getProjectType());
      itemdto.setKnttp(StrUtil.emptyIfNull(supplierOrderProduct.getAssignmentCategoryCode()));
      itemdto.setKostl(StrUtil.emptyIfNull(supplierOrderProduct.getCostCenterCode()));
      if (purchaseApplyForOrder != null) {
        itemdto.setBanfn(purchaseApplyForOrder.getApplyForOrderNo());
        itemdto.setBnfpo(purchaseApplyForOrder.getRowId());
      }
      //关税供应商和运费供应商没值的时候传空
      itemdto.setYfgys(StrUtil.EMPTY);
      itemdto.setGsgys(StrUtil.EMPTY);
      itemdto.setZfgys(StrUtil.EMPTY);
      String freightSupplierId = supplierOrderDetail.getFreightSupplierId();
      if (StrUtil.isNotBlank(freightSupplierId)) {
        supplierRepository.findById(freightSupplierId).ifPresent(
            supplierTemp -> itemdto.setYfgys(supplierTemp.getMdmCode()));
      }
      String tariffSupplierId = supplierOrderDetail.getTariffSupplierId();
      if (StrUtil.isNotBlank(tariffSupplierId)) {
        supplierRepository.findById(tariffSupplierId).ifPresent(
            supplierTemp -> itemdto.setGsgys(supplierTemp.getMdmCode()));
      }
      //6.8.2杂费供应商
      String incidentalSupplierId = supplierOrderDetail.getIncidentalSupplierId();
      if (StrUtil.isNotBlank(incidentalSupplierId)) {
        supplierRepository.findById(incidentalSupplierId).ifPresent(
            supplierTemp -> itemdto.setZfgys(supplierTemp.getMdmCode()));
      }
      // 获取项目编号 - 从 supplierOrderDetail 中获取
      itemdto.setZzxmbh_01(StrUtil.emptyIfNull(supplierOrderDetail.getProjectNo()));
      itemdto.setZzkhddh_01(purchaseApplyForOrder == null ? StrUtil.EMPTY : purchaseApplyForOrder.getCustomerOrderNumber());
      itemdto.setZzfjf(NumberUtil.null2Zero(supplierOrderDetail.getSurcharge()).stripTrailingZeros().toPlainString());
      itemdto.setZzwlsl(NumberUtil.null2Zero(supplierOrderDetail.getProductRate()).stripTrailingZeros().toPlainString());
      String salesOrderNo = supplierOrderDetail.getSalesOrderNo();
      // 根据 -分割
      if (StrUtil.isNotEmpty(salesOrderNo)) {
        String[] split = salesOrderNo.split("-");
        if (split.length > 1) {
          itemdto.setZZPOSNR(split[1]);
          itemdto.setZZVBELN(split[0]);
        } else {
          itemdto.setZZPOSNR(salesOrderNo);
          itemdto.setZZVBELN(StrUtil.EMPTY);
        }
      } else {
        itemdto.setZZPOSNR(StrUtil.EMPTY);
        itemdto.setZZVBELN(StrUtil.EMPTY);
      }
      // 设置资产相关信息，如果字段为空则默认传EMPTY
      // 资产卡片编码
      itemdto.setAnln1(StrUtil.emptyIfNull(supplierOrderProduct.getProfileCardCode()));
      // 订单编码
      itemdto.setAufnr(StrUtil.emptyIfNull(supplierOrderProduct.getOrderCode()));
      // 总账科目编码
      itemdto.setSakto(StrUtil.emptyIfNull(supplierOrderProduct.getLedgerSubjectCode()));
      // 委外
      List<WWDTO> wwdtos = new ArrayList<>();
      if (CollUtil.isNotEmpty(productDetailAdd.getEntrustProductList())) {
        for (EntrustProduct entrustProduct : productDetailAdd.getEntrustProductList()) {
          WWDTO wwdto = new WWDTO();
          wwdto.setEbelp(supplierOrderDetail.getSortNum().toString());
          wwdto.setWlzj(entrustProduct.getProductCode());
          wwdto.setWerks(supplierOrder.getGroupCode());
          wwdto.setMenge(entrustProduct.getNum().stripTrailingZeros().toPlainString());
          wwdto.setErfme(entrustProduct.getComponentUnit());
          if (entrustProduct.getComponentDemandDate() != null) {
            wwdto.setBedat(DateUtil.format(new Date(entrustProduct.getComponentDemandDate()), DatePattern.PURE_DATE_PATTERN));
          }
          wwdto.setPostp(entrustProduct.getLineItemCategory());
          wwdto.setDismm(entrustProduct.getMrpType());
          wwdtos.add(wwdto);
        }
      }
      // 设置委外产品
      if (!wwdtos.isEmpty()) {
        itemdto.setWw(wwdtos);
      }
      itemdtos.add(itemdto);
    }
    head.setItem(itemdtos);
    data.setHead(head);
    result.setData(data);
    return result;
  }

  public MM_084Param createMM084Param(SupplierOrderV2 supplierOrder, String reason) {
    MM_084Param param = new MM_084Param();
    param.setSupplierOrder(supplierOrder.getCode());
    param.setReviewStatus("S");
    param.setReviewReason(reason);
    return param;
  }

  public ReceiptVoucherSynchronizationParam createMM031Param(
      SupplierOrderV2 supplierOrder,
      SupplierOrderToFormV2 returnOrderToForm,
      List<SupplierOrderDetailV2> supplierOrderDetails,
      String zxkbj) {
    Assert.notNull(supplierOrder);
    Assert.notNull(returnOrderToForm);
    Assert.notEmpty(supplierOrderDetails);
    ReceiptVoucherSynchronizationParam param = new ReceiptVoucherSynchronizationParam();
    Head head = new Head();
    head.setMaterialPostingDate(
        TimeStampUtil.convertTimestampToFormat(returnOrderToForm.getPostingDate()));
    head.setVoucherDate(
        TimeStampUtil.convertTimestampToFormat(returnOrderToForm.getCreateTime()));
    head.setSrmId(returnOrderToForm.getId());
    head.setZxkbj(zxkbj);
    head.setMovementType("122");
    head.setTextRemark(returnOrderToForm.getReturnReason());
    head.setLogisticsCompany(returnOrderToForm.getLogisticsCode());
    head.setLogisticsNumber(returnOrderToForm.getTrackNum());

    //入库调用SAP接口增加收件人和收件地址
    head.setOperator(supplierOrder.getPurchaseMan().substring(4,
        supplierOrder.getPurchaseMan().length()));
    head.setConsignee(supplierOrder.getReceiveMan());
    head.setConsigneeAddress(supplierOrder.getReceiveAddress());
    List<Item> items = new ArrayList<>();
    for (SupplierOrderDetailV2 supplierOrderDetail : CollUtil.emptyIfNull(supplierOrderDetails)) {
      Item item = new Item();
      item.setPurchaseOrderNumber(supplierOrder.getCode());
      item.setPurchaseOrderLineItemNo(supplierOrderDetail.getSortNum() == null ? "" :
          supplierOrderDetail.getSortNum().toString());
      SupplierOrderDetailV2 detailed = supplierOrderDetail.getDetailed();
      if (detailed == null) {
        throw new CheckException("数据异常，前联系管理员！");
      }
      SupplierOrderProductV2 product = detailed.getSupplierOrderProduct();
      if (product == null) {
        throw new CheckException("数据异常，前联系管理员！");
      }
      item.setBaseUnitOfMeasure(product.getUnitCode());
      item.setSakto(product.getLedgerSubjectCode());
      item.setMaterialNumber(product.getCode());
      item.setQuantity(BigDecimalUtil.formatForStandard(supplierOrderDetail.getStockOutputQty()).toPlainString());
      item.setFactoryCode(supplierOrder.getGroupCode());
      item.setWarehouseLocation(StrUtil.emptyToDefault(returnOrderToForm.getWarehouseCode(),"6000"));
      // 获取入库单ID
      String inWareHouseId = supplierOrderDetail.getInWareHouseId();
      SupplierOrderToFormV2 inWareHouseSupplierOrderToForm =
          supplierOrderToFormV2Repository.findById(inWareHouseId).orElse(null);
      // 入库单明细
      SupplierOrderDetailV2 wareHouseDetailed =
          supplierOrderDetailV2Repository.getFirstByOrderToFormIdAndDetailedIdAndState(inWareHouseId,detailed.getId(),Constants.STATE_OK);
      item.setReferenceMaterialDocumentNumber(inWareHouseSupplierOrderToForm.getProductVoucher());
      item.setReferenceMaterialDocumentLineNumber(wareHouseDetailed.getSapRowId());
      items.add(item);
    }
    head.setItems(items);
    DataInfo dataInfo = new DataInfo();
    dataInfo.setHead(head);
    param.setData(dataInfo);
    return param;

  }

  public MM_075Param createMM075Param(
      SupplierOrderV2 supplierOrderV2,
      List<SupplierOrderDetailV2> supplierOrderDetailV2List,
      SupplierOrderToFormV2 supplierOrderToFormV2) {
    MM_075Param mm075Param = new MM_075Param();
    Supplier supplier = supplierRepository.findById(supplierOrderV2.getSupplierId())
        .orElseThrow(() -> new CheckException("供应商信息不存在！"));
    PurchaseOrderReturnDATADTO purchaseOrderReturnDATADTO = new PurchaseOrderReturnDATADTO();
    PurchaseOrderReturnHEADDTO purchaseOrderReturnHEADDTO = new PurchaseOrderReturnHEADDTO();
    purchaseOrderReturnHEADDTO.setEbeln("21"+RandomUtil.randomNumbers(8));
    purchaseOrderReturnHEADDTO.setBsart(StrUtil.equals(supplierOrderV2.getOrderType(),
        PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey())? PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey():"Z050");
    purchaseOrderReturnHEADDTO.setEkorg(supplierOrderV2.getGroupCode());
    purchaseOrderReturnHEADDTO.setEkgrp(supplierOrderV2.getPurchaseDeptCode());
    purchaseOrderReturnHEADDTO.setBukrs(supplierOrderV2.getGroupCode());
    if(Constants.SUPPLIER_TYPE_PROVISIONAL.equals(supplier.getSupType())){
      purchaseOrderReturnHEADDTO.setName1(supplier.getEnterpriseName());
      purchaseOrderReturnHEADDTO.setCity1("一次性供应商");
      purchaseOrderReturnHEADDTO.setCountry("CN");
    }

    Boolean oneTimeSupplier = supplier.isOneTimeSupplier();
    Supplier supplierInvoice = supplier;
    if (!oneTimeSupplier) {
      String invoicingParty = supplierOrderV2.getInvoicingParty();
      supplierInvoice =
          supplierRepository.findFirstByEnterpriseNameAndState(invoicingParty, Constants.STATE_OK)
              .orElseThrow(() -> new CheckException("供应商信息不存在！"));
    }
    purchaseOrderReturnHEADDTO.setZttwb(supplierOrderToFormV2.getReturnReason());
    purchaseOrderReturnHEADDTO.setBedat(DateUtil.format(new Date(),DatePattern.PURE_DATE_PATTERN));
    purchaseOrderReturnHEADDTO.setLifnr(supplier.getMdmCode());
    purchaseOrderReturnHEADDTO.setLifn2(supplierInvoice.getMdmCode());
    purchaseOrderReturnHEADDTO.setWkurs(supplierOrderV2.getOrderRate());
    final String payment_iterm = "0001";
    purchaseOrderReturnHEADDTO.setZterm(payment_iterm);
    purchaseOrderReturnHEADDTO.setWaers(supplierOrderV2.getMoneyCode());
    //增加申请人和工号
    purchaseOrderReturnHEADDTO.setZycgdh(supplierOrderV2.getCode());
    purchaseOrderReturnHEADDTO.setAfnam(supplierOrderV2.getPurchaseMan().substring(4,
        supplierOrderV2.getPurchaseMan().length()));
    purchaseOrderReturnHEADDTO.setBednr(supplierOrderV2.getPurchaseCode());
    List<PurchaseOrderReturnHEADDTO.ITEMDTO> itemdtoList = new ArrayList<>();

    for (SupplierOrderDetailV2 productDetail : supplierOrderDetailV2List) {
      SupplierOrderProductV2 product = productDetail.getSupplierOrderProduct();
      SupplierOrderDetailV2 detailed = productDetail.getDetailed();
      BigDecimal price = detailed.getPrice();
      Pair<BigDecimal, Integer> convertSapPrice = SAPToolUtils.convertSapPrice(price, 2);
      //构建sap参数 MM_075
      PurchaseOrderReturnHEADDTO.ITEMDTO itemReturndto = new PurchaseOrderReturnHEADDTO.ITEMDTO();
      itemReturndto.setEbelp(productDetail.getSortNum().toString());
      itemReturndto.setMatnr(product.getCode());
      itemReturndto.setTxz01(product.getName());
      itemReturndto.setMeins(product.getUnitCode());
      itemReturndto.setBprme(product.getUnitCode());
      itemReturndto.setPstyp(product.getLineItemCategory());
      itemReturndto.setKnttp(product.getAssignmentCategoryCode());
      itemReturndto.setAnln1(product.getProfileCardCode());
      itemReturndto.setKostl(product.getCostCenterCode());
      itemReturndto.setAufnr(product.getOrderCode());
      itemReturndto.setSakto(product.getLedgerSubjectCode());

      itemReturndto.setMenge(
          productDetail.getStockOutputQty().stripTrailingZeros().toPlainString());
      itemReturndto.setWerks(supplierOrderV2.getGroupCode());
      itemReturndto.setLgort(StrUtil.emptyToDefault(supplierOrderToFormV2.getWarehouseCode(), "6000"));
      itemReturndto.setCharX(productDetail.getBatchNo());
      itemReturndto.setMwskz(
          Constants.TAX_RATE_TYPE_NUM.get(detailed.getTaxRate().stripTrailingZeros()));

      itemReturndto.setNetpr(convertSapPrice.getKey().toPlainString());
      itemReturndto.setPeinh(convertSapPrice.getValue().toString());
      itemReturndto.setRetpo("X");
      itemReturndto.setAplfz(DateUtil.format(DateUtil.date(), "yyyyMMdd"));
      itemdtoList.add(itemReturndto);
    }
    purchaseOrderReturnHEADDTO.setItem(itemdtoList);
    purchaseOrderReturnDATADTO.setHead(purchaseOrderReturnHEADDTO);
    mm075Param.setData(purchaseOrderReturnDATADTO);
    return mm075Param;
  }

  public UpdatePurchaseOrderSapParam createBaseMM021ParamForReturnOrder(
      SupplierOrderV2 supplierOrderV2,
      SupplierOrderToFormV2 supplierOrderToFormV2,
      List<SupplierOrderDetailV2> supplierOrderDetailV2List) {
    Supplier supplier = supplierRepository.findById(supplierOrderV2.getSupplierId())
        .orElseThrow(() -> new CheckException("供应商信息不存在！"));
    Boolean oneTimeSupplier = supplier.isOneTimeSupplier();
    Supplier supplierInvoice = supplier;
    if (!oneTimeSupplier) {
      String invoicingParty = supplierOrderV2.getInvoicingParty();
      supplierInvoice =
          supplierRepository.findFirstByEnterpriseNameAndState(invoicingParty, Constants.STATE_OK)
              .orElseThrow(() -> new CheckException("供应商信息不存在！"));
    }
    UpdatePurchaseOrderSapParam param = new UpdatePurchaseOrderSapParam();
    UpdatePurchaseOrderDATADTO datadto = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO headdto = new UpdatePurchaseOrderHEADDTO();
    headdto.setSpras(StrUtil.EMPTY);
    headdto.setName1(StrUtil.EMPTY);
    headdto.setCity1(StrUtil.EMPTY);
    headdto.setCountry(StrUtil.EMPTY);
    headdto.setZterm_01(StrUtil.EMPTY);
    headdto.setZzsp_01(StrUtil.EMPTY);
    headdto.setEbeln("21"+RandomUtil.randomNumbers(8));
    headdto.setSRMID(supplierOrderToFormV2.getId());
    headdto.setBsart(StrUtil.equals(supplierOrderV2.getOrderType(),
        PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey())? PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey():"Z050");
    headdto.setEkorg(supplierOrderV2.getGroupCode());
    headdto.setEkgrp(supplierOrderV2.getPurchaseDeptCode());
    headdto.setBukrs(supplierOrderV2.getGroupCode());
    if(Constants.SUPPLIER_TYPE_PROVISIONAL.equals(supplier.getSupType())){
      headdto.setName1(supplier.getEnterpriseName());
      headdto.setCity1("一次性供应商");
      headdto.setCountry("CN");
    }
    headdto.setZttwb(supplierOrderToFormV2.getReviewReason());
    headdto.setBedat(TimeStampUtil.convertTimestampToFormat(supplierOrderToFormV2.getCreateTime()));
    headdto.setLifnr(supplier.getMdmCode());
    headdto.setLifn2(supplierInvoice.getMdmCode());
    headdto.setZycgdh(supplierOrderV2.getCode());
    //20250210 创建、修改采购订单时，如果WAERS货币码是JPY，汇率传递订单汇率*100
    headdto.setWkurs(StrUtil.equals(SettleCurrency.PRE004.getKey(), supplierOrderV2.getMoneyCode())
        ? NumberUtil.mul(NumberUtil.toBigDecimal(supplierOrderV2.getOrderRate()), BigDecimal.valueOf(100)).toString()
        : supplierOrderV2.getOrderRate());
    final String payment_iterm = "0001";
    headdto.setZterm(payment_iterm);
    headdto.setWaers(supplierOrderV2.getMoneyCode());
    if (BooleanUtil.isTrue(supplierOrderV2.getSelfState())) {
      headdto.setZsp("X");
    }else{
      headdto.setZsp("");
    }
    headdto.setZcdoa("N");
    headdto.setAfnam(supplierOrderV2.getPurchaseMan().substring(4,
        supplierOrderV2.getPurchaseMan().length()));
    headdto.setBednr(supplierOrderV2.getPurchaseCode());
    headdto.setZthlx(Constants_Sap.RETURN_ORDER_TYPE_ORIGINAL);
    List<ITEMDTO> itemdtos = new ArrayList<>();
    headdto.setKufix(Constants_Sap.CONFIRM_IDENTIFICATION);
    for (SupplierOrderDetailV2 orderDetail : supplierOrderDetailV2List) {
      SupplierOrderProductV2 productV2 = orderDetail.getSupplierOrderProduct();
      SupplierOrderDetailV2 detailed = orderDetail.getDetailed();
      ITEMDTO itemdto = new ITEMDTO();
      itemdto.setCharX(StrUtil.EMPTY);
      itemdto.setElikz(StrUtil.EMPTY);
      itemdto.setKnttp(StrUtil.EMPTY);
      itemdto.setLoekz(StrUtil.EMPTY);
      itemdto.setMatkl(StrUtil.EMPTY);
      itemdto.setRetpo(StrUtil.EMPTY);
      itemdto.setZZPOSNR(StrUtil.EMPTY);
      itemdto.setZZVBELN(StrUtil.EMPTY);
      itemdto.setZgsje(StrUtil.EMPTY);
      itemdto.setZyyje(StrUtil.EMPTY);
      itemdto.setZzf1(StrUtil.EMPTY);
      itemdto.setBanfn(StrUtil.EMPTY);
      itemdto.setBnfpo(StrUtil.EMPTY);
      itemdto.setZjsj(StrUtil.EMPTY);
      itemdto.setZzfjf(StrUtil.EMPTY);
      itemdto.setZzkhddh_01(StrUtil.EMPTY);
      itemdto.setZzwlsl(StrUtil.EMPTY);
      itemdto.setZzxmbh_01(StrUtil.EMPTY);
      itemdto.setEbelp(orderDetail.getSortNum().toString());
      itemdto.setMatnr(productV2.getCode());
      itemdto.setTxz01(productV2.getName());
      itemdto.setMeins(productV2.getUnitCode());
      itemdto.setMenge(BigDecimalUtil.formatForStandard(orderDetail.getStockOutputQty()).toPlainString());
      itemdto.setWerks(supplierOrderV2.getGroupCode());
      itemdto.setLgort(StrUtil.emptyToDefault(supplierOrderToFormV2.getWarehouseCode(), "6000"));
      itemdto.setAplfz(DateUtils.formatTimeStampToStr(System.currentTimeMillis(),
          DatePattern.PURE_DATE_PATTERN));
      itemdto.setMwskz(Constants.TAX_RATE_TYPE_NUM.get(orderDetail.getTaxRate()));
      BigDecimal price = orderDetail.getPrice();
      Pair<BigDecimal, Integer> convertSapPrice = SAPToolUtils.convertSapPrice(price, 2);
      itemdto.setNetpr(convertSapPrice.getKey().toPlainString());
      itemdto.setPeinh(convertSapPrice.getValue().toString());
      itemdto.setBprme(productV2.getUnitCode());
      itemdto.setZsfkhp("X");
      itemdto.setCharX(orderDetail.getBatchNo());
      itemdto.setRetpo("X");
      itemdto.setZmfbs(StrUtil.equals(orderDetail.getFreeState(), SimpleBooleanEnum.YES.getKey()) ?
          Constants_Sap.CONFIRM_IDENTIFICATION : "");
      itemdto.setPstyp(orderDetail.getProjectType());
      itemdto.setKnttp(StrUtil.emptyIfNull(productV2.getAssignmentCategoryCode()));
      itemdto.setAnln1(productV2.getProfileCardCode());
      itemdto.setKostl(productV2.getCostCenterCode());
      itemdto.setAufnr(productV2.getOrderCode());
      itemdto.setSakto(productV2.getLedgerSubjectCode());
      itemdtos.add(itemdto);
    }
    headdto.setItem(itemdtos);
    datadto.setHead(headdto);
    param.setData(datadto);
    return param;
  }

  public ReceiptVoucherSynchronizationParam createMM031ForWarehouseCreate(SupplierOrderV2 supplierOrder, SupplierOrderToFormV2 inForm, List<SupplierOrderDetailV2> inDetails) {
    ReceiptVoucherSynchronizationParam param = new ReceiptVoucherSynchronizationParam();
    Head head = new Head();
    head.setMaterialPostingDate(
        TimeStampUtil.convertTimestampToFormat(inForm.getPostingDate()));
    head.setVoucherDate(
        TimeStampUtil.convertTimestampToFormat(inForm.getCreateTime()));
    String movement_type = "101";
    head.setMovementType(movement_type);
    head.setLogisticsCompany(inForm.getLogisticsCode());
    head.setLogisticsNumber(inForm.getTrackNum());
    //入库调用SAP接口增加收件人和收件地址
    head.setOperator(inForm.getCreateUserName());
    head.setConsignee(supplierOrder.getReceiveMan());
    head.setConsigneeAddress(supplierOrder.getReceiveAddress());
    head.setConsigneeMobile(supplierOrder.getReceiveMobile());
    List<Item> items = new ArrayList<>();
    for (SupplierOrderDetailV2 inDetail : CollUtil.emptyIfNull(inDetails)) {
      Item item = new Item();
      item.setPurchaseOrderNumber(supplierOrder.getCode());
      item.setPurchaseOrderLineItemNo(inDetail.getSortNum() == null ? "" : inDetail.getSortNum().toString());
      SupplierOrderProductV2 product = inDetail.getSupplierOrderProduct();
      item.setBaseUnitOfMeasure(product.getUnitCode());
      item.setMaterialNumber(product.getCode());
      item.setQuantity(BigDecimalUtil.formatForStandard(inDetail.getStockInputQty()).stripTrailingZeros().toPlainString());
      item.setFactoryCode(supplierOrder.getGroupCode());
      item.setWarehouseLocation(inDetail.getWarehouse());
      items.add(item);
    }
    head.setItems(items);
    DataInfo dataInfo = new DataInfo();
    dataInfo.setHead(head);
    param.setData(dataInfo);
    return param;
  }

  public ReceiptVoucherSynchronizationParam createMM031ForWarehouseUpdate(SupplierOrderV2 supplierOrder, SupplierOrderToFormV2 inForm, List<SupplierOrderDetailV2> inDetails) {
    ReceiptVoucherSynchronizationParam param = new ReceiptVoucherSynchronizationParam();
    Head head = new Head();
    head.setLogisticsCompany(inForm.getLogisticsCode());
    head.setLogisticsNumber(inForm.getTrackNum());
    head.setProductVoucherNo(inForm.getProductVoucher());
    if (inForm.getTime() == null) {
      throw new CheckException("入库年度数据为空，请联系管理员！");
    }
    LocalDateTime localDateTime = LocalDateTimeUtil.of(inForm.getTime(), ZoneId.systemDefault());
    head.setAccountingYear(String.valueOf(localDateTime.getYear()));
    List<Item> items = new ArrayList<>();
    for (SupplierOrderDetailV2 supplierOrderDetail : CollUtil.emptyIfNull(inDetails)) {
      Item item = new Item();
      item.setPurchaseOrderNumber(supplierOrder.getCode());
      item.setPurchaseOrderLineItemNo(supplierOrderDetail.getSortNum() == null ? "" :
          supplierOrderDetail.getSortNum().toString());
      items.add(item);
    }
    head.setItems(items);
    DataInfo dataInfo = new DataInfo();
    dataInfo.setHead(head);
    param.setData(dataInfo);
    return param;
  }

  /**
   * 创建032入库单冲销入参
   * @param supplierOrder
   * @param inform
   * @return
   */
  public ReceiptOrReturnReversalParams create032ReversalParamsForWarehouse(SupplierOrderV2 supplierOrder, SupplierOrderToFormV2 inform) {
    ReceiptOrReturnReversalParams res = new ReceiptOrReturnReversalParams();
    ReceiptOrReturnReversalParams.DATADTO data = new ReceiptOrReturnReversalParams.DATADTO();
    ReceiptOrReturnReversalParams.DATADTO.HEADDTO headdto = new HEADDTO();
    headdto.setBelnr(inform.getProductVoucher());
    headdto.setZxkbj("");
    inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(
            supplierOrder.getGroupCode(), inform.getWarehouseCode(), Constants.STATE_OK)
        .ifPresent(inventoryLocation -> {
          if (inventoryLocation.hasMovement()) {
            headdto.setZxkbj("X");
          } else {
            headdto.setZxkbj("");
          }
        });
    DateTime date = DateTime.of(inform.getPostingDate());
    headdto.setBudat(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN));
    headdto.setBwart("102");
    headdto.setBldat(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
    int year = DateUtil.year(new Date(inform.getPostingDate()));
    headdto.setGjahr(Convert.toStr(year));
    data.setHead(headdto);
    res.setData(data);
    return res;
  }

  /**
   * 创建032退库单冲销入参
   * @param supplierOrder
   * @param returnForm
   * @return
   */
  public ReceiptOrReturnReversalParams create032ReversalParamsForReturn(SupplierOrderV2 supplierOrder,
      SupplierOrderToFormV2 returnForm) {
    ReceiptOrReturnReversalParams res = new ReceiptOrReturnReversalParams();
    ReceiptOrReturnReversalParams.DATADTO data = new ReceiptOrReturnReversalParams.DATADTO();
    ReceiptOrReturnReversalParams.DATADTO.HEADDTO headdto = new HEADDTO();
    headdto.setBelnr(returnForm.getProductVoucher());
    headdto.setZxkbj("");
    inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(
            supplierOrder.getGroupCode(), returnForm.getWarehouseCode(), Constants.STATE_OK)
        .ifPresent(inventoryLocation -> {
          if (inventoryLocation.hasMovement()) {
            headdto.setZxkbj("X");
          } else {
            headdto.setZxkbj("");
          }
        });
    DateTime date = DateTime.of(returnForm.getPostingDate());
    headdto.setBudat(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN));
    headdto.setBwart("102");
    headdto.setBldat(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
    int year = DateUtil.year(new Date(returnForm.getPostingDate()));
    headdto.setGjahr(Convert.toStr(year));
    data.setHead(headdto);
    res.setData(data);
    return res;
  }
}
