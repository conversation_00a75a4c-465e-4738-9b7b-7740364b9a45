package com.xhgj.srm.unified.dto;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.unified.enmus.UnifiedCombinationLogicEnums;
import com.xhgj.srm.unified.enmus.UnifiedOperatorEnums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jboss.jandex.ClassType;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class UnifiedChildForm {

  /**
   * 统一查询列表
   */
  private List<UnifiedFilter> filters;

  /**
   * 组合条件逻辑关系
   */
  private UnifiedCombinationLogicEnums logicOperator;

  public UnifiedChildForm(UnifiedCombinationLogicEnums logicOperator) {
    this.logicOperator = logicOperator;
    this.filters = new ArrayList<>();
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class UnifiedFilter{
    /**
     * 统一查询字段
     */
    private String field;

    /**
     * 统一查询值
     */
    private Object value;

    /**
     * 统一查询值操作符
     */
    private UnifiedOperatorEnums operator;

    /**
     * 类型
     */
    private String classTypeStr;

    public Class<?> getClassType() {
      try {
        return Class.forName(classTypeStr);
      } catch (ClassNotFoundException e) {
        return String.class;
      }
    }

    public static UnifiedFilter appendTextContains(String field, Map<String, Object> queryMap, Class<?> clazz) {
      Object value = queryMap.get(field);
      if (value == null) {
        return null;
      }
      // 字符串或者 chat[]
      if (String.class == clazz || value instanceof String) {
        String str = Convert.toStr(value);
        if (StrUtil.isBlank(str)) {
          return null;
        }
      }
      return new UnifiedFilter(field, value, UnifiedOperatorEnums.TEXT_CONTAINS, clazz.getName());
    }


    public static UnifiedFilter appendTextContains(String field, Map<String, Object> queryMap) {
      Object value = queryMap.get(field);
      if (value == null) {
        return null;
      }
      return appendTextContains(field, queryMap, value.getClass());
    }


  }

}
