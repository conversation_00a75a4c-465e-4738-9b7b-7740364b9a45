package com.xhgj.srm.notice.service.impl;

import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.PlatformRemindConfig;
import com.xhgj.srm.jpa.entity.PlatformRemindLog;
import com.xhgj.srm.jpa.repository.PlatformRemindConfigRepository;
import com.xhgj.srm.jpa.repository.PlatformRemindLogRepository;
import com.xhgj.srm.notice.enmus.PlatformRemindEnums;
import com.xhgj.srm.notice.factory.MapStructFactory;
import com.xhgj.srm.notice.form.PlatformRemindConfigSaveForm;
import com.xhgj.srm.notice.service.PlatformRemindService;
import com.xhgj.srm.notice.vo.PlatformRemindConfigVO;
import com.xhgj.srm.notice.vo.PlatformRemindLogLastTime;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PlatformRemindServiceImpl implements PlatformRemindService {

  @Resource
  PlatformRemindConfigRepository platformRemindConfigRepository;
  @Resource
  PlatformRemindLogRepository platformRemindLogRepository;

  @Override
  public List<PlatformRemindConfigVO> getPlatformRemindConfig(String platformId) {
    List<PlatformRemindConfig> platformRemindConfigs =
        platformRemindConfigRepository.findAllByPlatformIdAndState(platformId, Constants.STATE_OK);
    Map<Byte, PlatformRemindConfig> type2PlatformRemindConfig = platformRemindConfigs.stream().collect(
        Collectors.toMap(PlatformRemindConfig::getRemindType, Function.identity(), (v1, v2) -> v2));
    return Arrays.stream(PlatformRemindEnums.values()).map(item -> {
      PlatformRemindConfig platformRemindConfig = type2PlatformRemindConfig.get(item.getKey());
      if (platformRemindConfig == null) {
        return PlatformRemindConfigVO.createDefault(platformId, item.getKey());
      }
      PlatformRemindConfigVO vo = MapStructFactory.INSTANCE.toPlatformRemindConfigVO(platformRemindConfig);
      vo.setRemindTypeValue(PlatformRemindEnums.getValueByKey(vo.getRemindType()));
      return vo;
    }).collect(Collectors.toList());
  }

  @Override
  public void savePlatformRemindConfig(PlatformRemindConfigSaveForm form) {
    List<PlatformRemindConfig> platformRemindConfigs =
        platformRemindConfigRepository.findAllByPlatformIdAndState(form.getPlatformId(), Constants.STATE_OK);
    Map<Byte, PlatformRemindConfig> type2PlatformRemindConfig = platformRemindConfigs.stream().collect(
        Collectors.toMap(PlatformRemindConfig::getRemindType, Function.identity(), (v1, v2) -> v2));
    PlatformRemindConfig platformRemindConfig = type2PlatformRemindConfig.get(form.getRemindType());
    if (platformRemindConfig == null) {
      platformRemindConfig = MapStructFactory.INSTANCE.toPlatformRemindConfig(form);
      platformRemindConfig.setCreateTime(System.currentTimeMillis());
      platformRemindConfig.setUpdateTime(System.currentTimeMillis());
      platformRemindConfig.setState(Constants.STATE_OK);
      platformRemindConfig.setExtra(JSON.toJSONString(new Object()));
    } else {
      MapStructFactory.INSTANCE.updatePlatformRemindConfig(form, platformRemindConfig);
      platformRemindConfig.setUpdateTime(System.currentTimeMillis());
    }
    platformRemindConfigRepository.save(platformRemindConfig);
  }

  @Override
  public List<PlatformRemindConfig> getRemindList(Byte type) {
    List<PlatformRemindConfig> all =
        platformRemindConfigRepository.findAllByRemindTypeAndState(type, Constants.STATE_OK);
    // 根据platformId去重
    return new ArrayList<>(all.stream().collect(Collectors.toMap(PlatformRemindConfig::getPlatformId, Function.identity(),
        (v1, v2) -> v1)).values());
  }

  @Override
  public List<PlatformRemindLogLastTime> getLastRemindTime(String platformId, List<String> supplierIds,Byte remindType) {
    List<PlatformRemindLog> platformRemindLog = platformRemindLogRepository.findLatestRemindLogs(platformId, supplierIds, remindType);
    return platformRemindLog.stream().map(item -> {
      PlatformRemindLogLastTime vo = new PlatformRemindLogLastTime();
      vo.setSupplierId(item.getSupplierId());
      vo.setPlatformId(item.getPlatformId());
      vo.setLastTime(item.getSendTime());
      return vo;
    }).collect(Collectors.toList());
  }

  @Override
  public void saveRemindLog(String platformId, String supplierId, Byte remindType, String msg, List<String> mobiles) {
    PlatformRemindLog platformRemindLog = new PlatformRemindLog();
    platformRemindLog.setPlatformId(platformId);
    platformRemindLog.setSupplierId(supplierId);
    platformRemindLog.setRemindType(remindType);
    platformRemindLog.setSendTime(System.currentTimeMillis());
    platformRemindLog.setMobiles(JSON.toJSONString(mobiles));
    platformRemindLog.setMessage(msg);
    platformRemindLogRepository.save(platformRemindLog);
  }
}

