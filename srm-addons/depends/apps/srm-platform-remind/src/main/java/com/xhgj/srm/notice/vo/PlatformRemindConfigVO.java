package com.xhgj.srm.notice.vo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.notice.enmus.PlatformRemindEnums;
import lombok.Data;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 平台提醒配置VO
 */
@Data
public class PlatformRemindConfigVO {
  /**
   * 平台id
   */
  private String platformId;

  /**
   * 提醒类型
   * {@link com.xhgj.srm.notice.enmus.PlatformRemindEnums}
   */
  private Byte remindType;

  /**
   * 提醒类型
   * {@link com.xhgj.srm.notice.enmus.PlatformRemindEnums}
   */
  private String remindTypeValue;

  /**
   * 是否启用
   */
  private Boolean enabled;

  /**
   * 阈值-小时
   */
  private BigDecimal thresholdHours;

  /**
   * 频率-天
   */
  private Integer frequencyDays;

  /**
   * 额外配置
   */
  private String extra;

  /**
   * 创建时间
   */
  private Long createTime;

  /**
   * 更新时间
   */
  private Long updateTime;

  public Map<String, Object> getExtraMap() {
    if (StrUtil.isBlank(extra)) {
      return new HashMap<>();
    }
    return JSON.parseObject(extra);
  }

  public static PlatformRemindConfigVO createDefault(String platformId, Byte remindType) {
    PlatformRemindConfigVO vo = new PlatformRemindConfigVO();
    vo.setPlatformId(platformId);
    vo.setRemindType(remindType);
    vo.setRemindTypeValue(PlatformRemindEnums.getValueByKey(remindType));
    vo.setEnabled(false);
    vo.setThresholdHours(new BigDecimal("24"));
    vo.setFrequencyDays(1);
    vo.setExtra("{}");
    vo.setCreateTime(0L);
    vo.setUpdateTime(0L);
    return vo;
  }
}
