package com.xhgj.srm.notice.service;

import com.xhgj.srm.jpa.entity.PlatformRemindConfig;
import com.xhgj.srm.notice.form.PlatformRemindConfigSaveForm;
import com.xhgj.srm.notice.vo.PlatformRemindConfigVO;
import com.xhgj.srm.notice.vo.PlatformRemindLogLastTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public interface PlatformRemindService {
  /**
   * 根据平台id获取提醒配置
   */
  List<PlatformRemindConfigVO> getPlatformRemindConfig(String platformId);

  /**
   * 保存提醒配置
   */
  void savePlatformRemindConfig(PlatformRemindConfigSaveForm form);

  /**
   * 根据消息种类获取提醒列表
   * @return
   */
  List<PlatformRemindConfig> getRemindList(Byte type);

  /**
   * 获取最后提醒时间
   * @param platformId
   * @param supplierIds
   * @param remindType
   * @return
   */
  List<PlatformRemindLogLastTime> getLastRemindTime(String platformId, List<String> supplierIds,Byte remindType);

  /**
   * 保存提醒日志
   * @param platformId
   * @param supplierId
   * @param remindType
   * @param msg
   * @param mobiles
   */
  void saveRemindLog(String platformId, String supplierId, Byte remindType, String msg, List<String> mobiles);
}
