package com.xhgj.srm.cacheStore.service;

import com.xhgj.srm.cacheStore.form.CacheStoreSaveForm;
import com.xhgj.srm.cacheStore.vo.CacheStoreVO;

/**
 * <AUTHOR>
 */
public interface CacheStoreService {

  /**
   * 新增或更新缓存
   */
  void saveCache(CacheStoreSaveForm storeSaveForm);

  /**
   * 新增同时删除旧的
   */
  void saveCacheAndDeleteOld(CacheStoreSaveForm storeSaveForm);

  /**
   * 删除缓存
   * @param userId
   */
  void deleteCache(String userId, String cacheType);

  /**
   * 获取最新缓存
   * @param userId
   * @param cacheType
   * @return
   */
  CacheStoreVO getCache(String userId, String cacheType);
}
