package com.xhgj.srm.registration.dto;

import com.xhgj.srm.jpa.entity.File;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class RegistrationFileDTO {

    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("附件名")
    private String name;
    @ApiModelProperty("附件路径")
    private String url;
    @ApiModelProperty("前缀路径")
    private String baseUrl;
    @ApiModelProperty("文件类型")
    private String type;

    public RegistrationFileDTO(File file){
        this.id = file.getId();
        this.name = StringUtils.emptyIfNull(file.getDescription());
        this.url = StringUtils.emptyIfNull(file.getUrl());
    }

  public RegistrationFileDTO(File file, String baseUrl){
    this(file);
    this.baseUrl = baseUrl;
  }

}
