package com.xhgj.srm.registration.dto.entryregistration;

import com.xhgj.srm.jpa.entity.Platform;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/** 入住报备分页返回实体类 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EntryRegistrationDTO {

  /** 报备单ID */
  @ApiModelProperty(value = "报备单id")
  private String id;

  /** 报备单号 */
  @ApiModelProperty(value = "报备单号")
  private String registrationNumber;

  /** 审核状态Key */
  @ApiModelProperty(value = "审核状态Key")
  private String registrationStatusKey;

  /** 审核状态 */
  @ApiModelProperty(value = "审核状态")
  private String registrationStatus;

  /** 落地商名称 */
  @ApiModelProperty(value = "落地商名称")
  private String partnerName;

  /** 业务员名称 */
  @ApiModelProperty(value = "业务员名称")
  private String salesmanName;

  /** 业务员名称 */
  @ApiModelProperty(value = "业务员所属公司")
  private String salesmanGroup;

  /** 报备时间。 */
  @ApiModelProperty(value = "报备时间")
  private Long registrationTime;

  /** 报备单是否完善*/
  @ApiModelProperty(value = "报备单是否完善")
  private Boolean isComplete;

  /**
   * 供应商审核状态，此报备单审核通过之后该属性为null表示无需审核
   * {@link com.xhgj.srm.common.enums.AssessStateEnum}
   */
  @ApiModelProperty(value = "供应商审核状态")
  private String supplierAuditStatus;
  /**
   * 关联的合同数量
   */
  @ApiModelProperty(value = "关联的合同数量")
  private Integer relationContractCount;


  /**
   * 下单平台
   */
  @ApiModelProperty(value = "下单平台")
  private List<PlatformForEntry> platformList;


  @ApiModelProperty(value = "项目大类名称")
  private String projectCategory;


  @Data
  @AllArgsConstructor
  public static class PlatformForEntry {
    private String platformCode;
    private String platformName;

    public PlatformForEntry(Platform platform) {
      this.platformCode = platform.getCode();
      this.platformName = platform.getName();
    }
  }
}
