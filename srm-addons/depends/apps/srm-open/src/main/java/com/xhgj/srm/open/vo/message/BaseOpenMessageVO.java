package com.xhgj.srm.open.vo.message;
import com.xhgj.srm.open.enums.OpenMessageTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BaseOpenMessageVO {

  /**
   * 消息id
   */
  private String id;

  /**
   * 消息类型
   */
  private Byte type;

  /**
   * 消息类型名称
   */
  private String typeName;

  /**
   * 创建时间
   */
  private Long createTime;

  /**
   * 根据type返回typeName
   * @return
   */
  public String getTypeName() {
    if (type == null) {
      return null;
    }
    return OpenMessageTypeEnum.getValue(type);
  }
}
