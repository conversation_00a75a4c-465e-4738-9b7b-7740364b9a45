package com.xhgj.srm.open.form.login;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * open登录表单
 */
@ApiModel(description = "open登录表单")
@Data
public class LoginForm {

  /**
   * 用户名
   */
  @ApiModelProperty(value = "用户名", required = true, dataType = "string")
  @NotBlank(message = "用户名不能为空")
  private String name;

  /**
   * 密码
   */
  @ApiModelProperty(value = "密码", required = true, dataType = "string")
  @NotBlank(message = "密码不能为空")
  private String pwd;
}
