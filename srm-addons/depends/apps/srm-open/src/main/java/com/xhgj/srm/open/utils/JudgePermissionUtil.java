package com.xhgj.srm.open.utils;

import cn.hutool.core.util.ObjectUtil;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhiot.boot.core.common.exception.CheckException;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class JudgePermissionUtil {

  @Resource
  OpenSecurityUtil openSecurityUtil;

  /**
   * 判断当前登录用户是否有权限查看该订单
   * @param supplierId
   */
  public void judgeSupplierUserPermission(String supplierId, String errorMessage) {
    SupplierUser supplierUser = openSecurityUtil.getOpenUserDetails().supplierUser();
    if (!ObjectUtil.equals(supplierId, supplierUser.getSupplierId())) {
      throw new CheckException(errorMessage);
    }
  }
}
