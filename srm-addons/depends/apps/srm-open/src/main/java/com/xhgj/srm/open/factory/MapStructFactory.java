package com.xhgj.srm.open.factory;/**
 * @since 2024/12/5 17:13
 */
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderCancel;
import com.xhgj.srm.jpa.entity.OrderDelivery;
import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhgj.srm.map.domain.BaseMapStruct;
import com.xhgj.srm.open.dto.order.OpenOrderCancelDto;
import com.xhgj.srm.open.dto.order.OpenOrderReturnDto;
import com.xhgj.srm.open.dto.order.OpenOrderShipDto;
import com.xhgj.srm.open.entity.OpenMessage;
import com.xhgj.srm.open.entity.OrderEntity;
import com.xhgj.srm.open.form.message.OpenMessageSaveForm;
import com.xhgj.srm.open.vo.message.AuditOpenMessageVO;
import com.xhgj.srm.open.vo.message.CancelOrderOpenMessageVO;
import com.xhgj.srm.open.vo.message.OrderOpenMessageVO;
import com.xhgj.srm.open.vo.message.ReturnOrderOpenMessageVO;
import com.xhgj.srm.open.vo.order.OpenOrderVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 *<AUTHOR>
 *@date 2024/12/5 17:13:45
 *@description
 */
@Mapper
public interface MapStructFactory extends BaseMapStruct {
  MapStructFactory INSTANCE = Mappers.getMapper(MapStructFactory.class);

  /**
   * openMessage to OrderOpenMessageVO
   * @param openMessage
   * @return
   */
  @Mapping(target = "type", source = "messageType")
  OrderOpenMessageVO toOrderOpenMessageVO(OpenMessage openMessage);

  /**
   * openMessage to ReturnOrderOpenMessageVO
   * @param openMessage
   * @return
   */
  @Mapping(target = "type", source = "messageType")
  ReturnOrderOpenMessageVO toReturnOrderOpenMessageVO(OpenMessage openMessage);

  /**
   * openMessage to AuditOpenMessageVO
   * @param openMessage
   * @return
   */
  @Mapping(target = "auditResult", source = "result")
  @Mapping(target = "type", source = "messageType")
  AuditOpenMessageVO toAuditOpenMessageVO(OpenMessage openMessage);

  /**
   * openMessage to CancelOrderOpenMessageVO
   * @param openMessage
   * @return
   */
  @Mapping(target = "type", source = "messageType")
  CancelOrderOpenMessageVO toCancelOrderOpenMessageVO(OpenMessage openMessage);

  /**
   * openMessageSaveForm to OpenMessage
   * @param openMessageSaveForm
   * @return
   */
  OpenMessage toOpenMessage(OpenMessageSaveForm openMessageSaveForm);

  /**
   * update OpenMessage
   * @param source
   * @param target
   */
  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  void updateOpenMessage(OpenMessageSaveForm source, @MappingTarget OpenMessage target);

  /**
   * update OpenOrderVO
   */
  @BeanMapping(
      nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  @Mapping(target = "platform", source = "platform", ignore = true)
  @Mapping(target = "orderDetails", source = "orderDetails", ignore = true)
  @Mapping(target = "invoiceTemplate", source = "invoiceTemplate", ignore = true)
  void updateOpenOrderVO(OrderEntity source, @MappingTarget OpenOrderVO target);

  /**
   * update OrderEntity
   * @param source
   * @param target
   */
  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  void updateOrderEntity(Order source, @MappingTarget OrderEntity target);

  /**
   * OrderDelivery to OpenOrderShipDto
   * @param orderDelivery
   * @return
   */
  OpenOrderShipDto toOpenOrderShipDto(OrderDelivery orderDelivery);

  /**
   * OrderReturn to OpenOrderReturnDto
   * @param orderReturn
   * @return
   */
  OpenOrderReturnDto toOpenOrderReturnDto(OrderReturn orderReturn);

  /**
   * OrderCancel to OpenOrderCancelDto
   * @param orderCancel
   * @return
   */
  OpenOrderCancelDto toOpenOrderCancelDto(OrderCancel orderCancel);
}
