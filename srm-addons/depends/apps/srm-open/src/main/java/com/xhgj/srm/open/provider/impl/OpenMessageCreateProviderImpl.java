package com.xhgj.srm.open.provider.impl;
import com.xhgj.srm.open.enums.OpenMessageTypeEnum;
import com.xhgj.srm.open.form.message.OpenMessageSaveForm;
import com.xhgj.srm.open.provider.OpenMessageCreateProvider;
import com.xhgj.srm.open.repository.OpenMessageRepository;
import com.xhgj.srm.open.service.OpenMessageService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class OpenMessageCreateProviderImpl implements OpenMessageCreateProvider {
  @Resource
  private OpenMessageService openMessageService;
  @Resource
  private OpenMessageRepository openMessageRepository;

  @Override
  public void createOrderMessage(String orderId, String supplierId) {
    OpenMessageSaveForm openMessageSaveForm = new OpenMessageSaveForm();
    openMessageSaveForm.setOrderId(orderId);
    openMessageSaveForm.setSupplierId(supplierId);
    openMessageSaveForm.setMessageType(OpenMessageTypeEnum.ORDER_CREATE.getKey());
    openMessageService.saveOpenMessage(openMessageSaveForm);
  }

  @Override
  public void withdrawOrderMessage(String orderId, String supplierId) {
    OpenMessageSaveForm openMessageSaveForm = new OpenMessageSaveForm();
    openMessageSaveForm.setOrderId(orderId);
    openMessageSaveForm.setSupplierId(supplierId);
    openMessageSaveForm.setMessageType(OpenMessageTypeEnum.ORDER_RECALL.getKey());
    openMessageService.saveOpenMessage(openMessageSaveForm);
  }

  @Override
  public void cancelOrderMessage(String orderId, String cancelId, String supplierId) {
    OpenMessageSaveForm openMessageSaveForm = new OpenMessageSaveForm();
    openMessageSaveForm.setOrderId(orderId);
    openMessageSaveForm.setCancelOrderId(cancelId);
    openMessageSaveForm.setSupplierId(supplierId);
    openMessageSaveForm.setMessageType(OpenMessageTypeEnum.ORDER_CANCEL.getKey());
    openMessageService.saveOpenMessage(openMessageSaveForm);
  }

  @Override
  public void returnOrderMessage(String orderId, String returnId, String supplierId) {
    OpenMessageSaveForm openMessageSaveForm = new OpenMessageSaveForm();
    openMessageSaveForm.setOrderId(orderId);
    openMessageSaveForm.setReturnOrderId(returnId);
    openMessageSaveForm.setSupplierId(supplierId);
    openMessageSaveForm.setMessageType(OpenMessageTypeEnum.ORDER_RETURN.getKey());
    openMessageService.saveOpenMessage(openMessageSaveForm);
  }

  @Override
  public void auditProductMessage(String code, Byte result, String supplierId) {
    OpenMessageSaveForm openMessageSaveForm = new OpenMessageSaveForm();
    openMessageSaveForm.setCode(code);
    openMessageSaveForm.setSupplierId(supplierId);
    openMessageSaveForm.setMessageType(OpenMessageTypeEnum.PRODUCT_AUDIT.getKey());
    openMessageSaveForm.setResult(result);
    openMessageService.saveOpenMessage(openMessageSaveForm);
  }

  @Override
  public void auditBrandMessage(String code, Byte result, String supplierId) {
    OpenMessageSaveForm openMessageSaveForm = new OpenMessageSaveForm();
    openMessageSaveForm.setCode(code);
    openMessageSaveForm.setSupplierId(supplierId);
    openMessageSaveForm.setMessageType(OpenMessageTypeEnum.BRAND_AUDIT.getKey());
    openMessageSaveForm.setResult(result);
    openMessageService.saveOpenMessage(openMessageSaveForm);
  }

  @Override
  public void rejectOrderReceiptMessage(String orderId, String supplierId) {
    OpenMessageSaveForm openMessageSaveForm = new OpenMessageSaveForm();
    openMessageSaveForm.setOrderId(orderId);
    openMessageSaveForm.setSupplierId(supplierId);
    openMessageSaveForm.setMessageType(OpenMessageTypeEnum.ORDER_SIGN_REJECT.getKey());
    openMessageService.saveOpenMessage(openMessageSaveForm);
  }
}

