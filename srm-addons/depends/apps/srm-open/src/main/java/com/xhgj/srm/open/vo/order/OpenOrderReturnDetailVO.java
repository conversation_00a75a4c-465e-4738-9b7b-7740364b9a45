package com.xhgj.srm.open.vo.order;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.OrderReturnDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 退货单明细详情VO对象
 */
@Data
public class OpenOrderReturnDetailVO {
  @ApiModelProperty("物料编码")
  private String productCode;

  @ApiModelProperty("品牌")
  private String brand;

  @ApiModelProperty("商品名称")
  private String productName;

  @ApiModelProperty("规格型号")
  private String manuCode;

  @ApiModelProperty("退货数量")
  private BigDecimal returnNum;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("单价")
  private BigDecimal productPrice;

  @ApiModelProperty("发货明细id")
  private String deliveryDetailId;

  public OpenOrderReturnDetailVO(OrderReturnDetail orderReturnDetail) {
    this.productCode = StrUtil.emptyIfNull(orderReturnDetail.getCode());
    this.brand = StrUtil.emptyIfNull(orderReturnDetail.getBrand());
    this.productName = StrUtil.emptyIfNull(orderReturnDetail.getName());
    this.manuCode = StrUtil.emptyIfNull(orderReturnDetail.getModel());
    this.returnNum = BigDecimalUtil.formatForStandard(orderReturnDetail.getReturnNum());
    this.unit = StrUtil.emptyIfNull(orderReturnDetail.getUnit());
    this.productPrice = orderReturnDetail.getPrice();
    this.deliveryDetailId = orderReturnDetail.getDeliveryDetailId();

  }
}