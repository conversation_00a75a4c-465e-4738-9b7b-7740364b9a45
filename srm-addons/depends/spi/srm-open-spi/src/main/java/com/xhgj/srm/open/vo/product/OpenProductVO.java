package com.xhgj.srm.open.vo.product;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.xhgj.srm.open.dto.product.OpenExternalLinkAndLabelDTO;
import com.xhgj.srm.open.dto.product.OpenPlatformProjectDTO;
import com.xhgj.srm.open.dto.product.OpenProductFieldDTO;
import com.xhgj.srm.open.dto.product.OpenProductFileDTO;
import com.xhgj.srm.open.dto.product.OpenProductPicDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OpenProductVO {

  @ApiModelProperty("商品名称")
  @NotEmpty(message = "商品名称必填")
  private String name;

  @ApiModelProperty("市场价")
  private String marketPrice;

  @ApiModelProperty("厂家型号")
  @NotEmpty(message = "型号规格必填")
  private String model;

  @ApiModelProperty("基本单位")
  @NotEmpty(message = "基本单位必填")
  private String basicUnit;

  @ApiModelProperty("毛重")
  @NotEmpty(message = "毛重必填")
  private String grossWeight;

  @ApiModelProperty("净重")
  private String netWeight;

  @ApiModelProperty("长")
  private String length;

  @ApiModelProperty("宽")
  private String width;

  @ApiModelProperty("高")
  private String height;

  @ApiModelProperty("图文详情")
  private String info;

  @ApiModelProperty("描述")
  @NotNull(message = "描述不能为 null")
  private String remark;

  @ApiModelProperty("品牌Id")
  @NotEmpty(message = "品牌 Id 必填")
  private String brandMdmId;

  @ApiModelProperty("类目Id")
  @NotEmpty(message = "类目 Id 必填")
  private String cateMdmId;

  @ApiModelProperty("物料编码")
  private String code;

  //  v1.1.0

  @ApiModelProperty("条形码")
  private String barCode;

  @ApiModelProperty("体积")
  private String volume;
  // 3.3.0
  @ApiModelProperty("货主编码")
  @NotEmpty(message = "货主必填")
  private String shipperCode;

  @ApiModelProperty("发货日（期），传空代表【详询客服】")
  private String deliveryDay;

  @ApiModelProperty("是否包邮：1 包邮、0 不包邮")
  private String isFreeShipping;

  /** 对应数据库字段：是否停产 */
  @ApiModelProperty("是否退市，1是，0否")
  private String isDelisting;

  @ApiModelProperty("采购价")
  private String supplyPrice;

  @ApiModelProperty("测试报告")
  private List<OpenProductFileDTO> testReportFileList;

  @ApiModelProperty("质量证明")
  private List<OpenProductFileDTO> certificateOfQuality;

  @ApiModelProperty("附件（宣传资料）")
  private List<OpenProductFileDTO> fileList;

  @ApiModelProperty("起订量")
  @Positive(message = "起订量必须为正整数！")
  @NotNull(message = "请输入起订量")
  @Max(value = 9999999999L, message = "起订量长度限制 10！")
  private Long moq;

  @ApiModelProperty("助记码")
  private String mnemonicCode;
  @ApiModelProperty("外部链接和外部链接标签")
  private List<OpenExternalLinkVO> externalLinks;

  @ApiModelProperty("是否含检测费：0：不含; 1：含第三方检测费; 2:含出厂检测费")
  private String testingFee;

  @ApiModelProperty("是否含安装费")
  private String packingExpense;

  @ApiModelProperty("图片关系")
  private String pictureRelationship;

  @ApiModelProperty("srm物料类型")
  private String srmProductType;

  @ApiModelProperty("调拨价是否含运费")
  private String isTransferPriceFreightIncluded;

  @ApiModelProperty("图片")
  private List<OpenProductPicDTO> mainUrl;

  @ApiModelProperty("类目名称")
  private String cateName;

  @ApiModelProperty("类目编码集合")
  private List<String> cateCodeList;

  @ApiModelProperty("品牌名称")
  private String brandName;

  @ApiModelProperty("品牌英文名")
  private String brandNameEn;

  @ApiModelProperty("品牌中文名")
  private String brandNameCn;

  @ApiModelProperty("mdmId")
  private String productMdmId;

  @ApiModelProperty("货主名称")
  private String shipperName;

  @ApiModelProperty("单位名称")
  private String basicUnitName;

  @ApiModelProperty("拓展属性列表")
  private List<OpenProductFieldDTO> fieldList;

  @ApiModelProperty("该物料是否正在审核中")
  private String isAssessing;
  @ApiModelProperty("税收分类编码")
  private String taxCategoryCode;
  @ApiModelProperty("税收分类名称")
  private String taxCategoryName;
  /**
   * 税收分类税率
   */
  @ApiModelProperty("税收分类税率")
  private String taxCategoryRate;

  /**
   * 税收分类简称
   */
  @ApiModelProperty("税收分类简称")
  @JSONField(name = "taxCategoryAbbr", alternateNames = {"abbreviation"})
  private String taxCategoryAbbr;

  @ApiModelProperty("项目型字段")
  private List<OpenPlatformProjectDTO> projectValueInfoList;

  @ApiModelProperty("详情图")
  private List<String> detailPicList;
}
