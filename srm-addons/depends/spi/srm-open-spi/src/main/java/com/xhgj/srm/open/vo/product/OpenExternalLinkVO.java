package com.xhgj.srm.open.vo.product;/**
 * @since 2025/1/7 16:48
 */

import com.xhgj.srm.common.enums.product.ProductExternalLinkType;
import com.xhgj.srm.common.enums.product.ProductExternalPlatform;
import com.xhgj.srm.common.enums.product.ProductExternalStore;
import com.xhgj.srm.common.enums.product.ProductExternalType;
import lombok.Data;
import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/1/7 16:48:02
 *@description
 */
@Data
public class OpenExternalLinkVO {
  /**
   * id
   */
  private String id;

  /**
   * 物料id
   */
  private String productId;


  /**
   *外部链接地址
   */
  private String externalLink;

  /**
   *外部链接类型
   * @see com.xhgj.srm.common.enums.product.ProductExternalLinkType
   */
  private String linkType;


  /**
   *外部链接类型
   * @see com.xhgj.srm.common.enums.product.ProductExternalLinkType
   */
  private String linkTypeValue;


  /**
   * 平台类型
   * @see com.xhgj.srm.common.enums.product.ProductExternalPlatform
   */
  private String platformType;

  /**
   * 平台类型
   * @see com.xhgj.srm.common.enums.product.ProductExternalPlatform
   */
  private String platformTypeValue;

  /**
   * 店铺类型
   * @see com.xhgj.srm.common.enums.product.ProductExternalStore
   */
  private String storeType;

  /**
   * 店铺类型
   * @see com.xhgj.srm.common.enums.product.ProductExternalStore
   */
  private String storeTypeValue;

  /**
   * 外链数量，计价单位转换
   */
  private Integer externalNum;

  /**
   * 咸亨数量，计价单位转换
   */
  private Integer internalNum;

  /**
   * 参考价格
   */
  private BigDecimal externalPrice;

  /**
   * 佐证文件
   */
  private String externalFile;

  /**
   * 类型，0默认为外部链接 1为价格佐证
   * @see com.xhgj.srm.common.enums.product.ProductExternalType
   */
  private Byte type;

  /**
   * 类型，0默认为外部链接 1为价格佐证
   * @see com.xhgj.srm.common.enums.product.ProductExternalType
   */
  private String typeValue;


  public String getLinkTypeValue() {
    return ProductExternalLinkType.getNameByCode(linkType);
  }

  public String getPlatformTypeValue() {
    return ProductExternalPlatform.getNameByCode(platformType);
  }

  public String getStoreTypeValue() {
    return ProductExternalStore.getNameByCode(storeType);
  }

  public String getTypeValue() {
    return ProductExternalType.getNameByCode(type);
  }
}
