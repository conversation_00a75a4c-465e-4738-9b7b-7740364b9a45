package com.xhgj.srm.v2.repository;

import com.xhgj.srm.jpa.entity.v2.PurchaseOrderPaymentTermsV2;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;

import java.util.List;

public interface PurchaseOrderPaymentTermsV2Repository extends
    BootBaseRepository<PurchaseOrderPaymentTermsV2, String> {

  List<PurchaseOrderPaymentTermsV2> findAllByPurchaseOrderIdAndState(String purchaseOrderId, String state);
}
