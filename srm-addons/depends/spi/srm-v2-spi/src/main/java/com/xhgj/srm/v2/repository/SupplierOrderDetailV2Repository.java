package com.xhgj.srm.v2.repository;

import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.data.jpa.repository.Query;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/28 15:22
 */
public interface SupplierOrderDetailV2Repository extends BootBaseRepository<SupplierOrderDetailV2,String> {

  /**
   * 通过 erp id 获得该行的订单明细
   * @param erpId erpId 必传
   * @param orderToFormId 关联的表单 id 必传
   * @param state 数据状态 必传
   */
  SupplierOrderDetailV2 getFirstByErpIdAndOrderToFormIdAndState(String erpId,String orderToFormId,String state);


  /**
   * 通过关联的表单 id 获得详情
   * @param orderToFormId 关联的表单 id 必传
   * @param state 数据状态 id 必传
   */
  List<SupplierOrderDetailV2> getAllByOrderToFormIdAndStateOrderBySortNumAsc(String orderToFormId,String state);

  /**
   * 根据订单详情表单id查询
   * @param orderToFormId 订单关联发货表数据id
   * @return 供应商订单发货明细集合
   */
  List<SupplierOrderDetailV2> findByOrderToFormIdAndState(String orderToFormId, String state);

  /**
   * 根据订单详情表单id查询
   * @param orderToFormIds 订单关联发货表数据id
   * @return 供应商订单发货明细集合
   */
  List<SupplierOrderDetailV2> findByOrderToFormIdInAndState(List<String> orderToFormIds, String state);

  /**
   * @param purchaseApplyForOrderId 采购申请单id
   * @param state 数据状态
   */
  List<SupplierOrderDetailV2> findAllByPurchaseApplyForOrderIdAndState(String purchaseApplyForOrderId,
      String state);

  /**
   * @param purchaseApplyForOrderId 采购申请单id
   */
  List<SupplierOrderDetailV2> findAllByPurchaseApplyForOrderId(String purchaseApplyForOrderId);

  /**
   * @param entrustDetailId 委外加工id
   * @param state 数据状态
   */
  List<SupplierOrderDetailV2> findAllByEntrustDetailIdAndState(String entrustDetailId, String state);


  /**
   * 根据订单详情表单id查询
   * @param orderToFormId 订单关联发货表数据id
   * @return 供应商订单发货明细集合
   */
  SupplierOrderDetailV2 findByOrderToFormIdAndStateAndSortNum(String orderToFormId,
      String state,Integer SortNum);



  /**
   * 根据单据 id 获取详情数量
   * @param orderToFormId 单据 id 必传
   * @param state 数据状态
   * @return
   */
  long countByOrderToFormIdAndState(String orderToFormId, String state);

  SupplierOrderDetailV2 getFirstByOrderToFormIdAndDetailedIdAndState(String orderToFormId,
      String detailedId, String state);

  /**
   * 通过sapRowId 获得该行的订单明细
   * @param sapRowId sapRowId 必传
   * @param orderToFormId 关联的表单 id 必传
   * @param state 数据状态 必传
   */
  SupplierOrderDetailV2 getFirstBySapRowIdAndOrderToFormIdAndState(String sapRowId,
      String orderToFormId,String state);


  /**
   * @description: 获取所有详情通过关联的表单id
   * @param detailedFormIds 关联的表单 ids
   * @param state 数据状态
   */
  List<SupplierOrderDetailV2> findAllByOrderToFormIdInAndState(List<String> detailedFormIds,
      String state);

  List<SupplierOrderDetailV2> findAllByDetailedIdAndState(String detailId, String state);

  List<SupplierOrderDetailV2> findAllByDetailedIdInAndState(List<String> detailIds, String state);

  @Query(value = "select so as supplierOrder, sod as supplierOrderDetail " +
      "from SupplierOrderDetailV2 sod " +
      "left join SupplierOrderToFormV2 sof on sod.orderToFormId = sof.id " +
      "left join SupplierOrderV2 so on sof.supplierOrderId = so.id " +
      "where sod.state = '1' and so.state in ('1','2') and sof.state = '1' " +
      "and so.id in :orderIds and sof.type = :orderFormType")
  List<SupplierOrder2DetailProjectionV2> getDetailsByOrderIds2(List<String> orderIds,
      String orderFormType);

  /**
   * 根据采购申请单id查询
   * @param purchaseApplyForOrderIds
   * @param stateOk
   * @return
   */
  List<SupplierOrderDetailV2> findAllByPurchaseApplyForOrderIdInAndState(List<String> purchaseApplyForOrderIds, String stateOk);


  // 接口定义
  interface SupplierOrder2DetailProjectionV2 {
    SupplierOrderV2 getSupplierOrder();
    SupplierOrderDetailV2 getSupplierOrderDetail();
  }

  /**
   * 查询委外组件清单
   */
  List<SupplierOrderDetailV2> findAllByEntrustDetailIdInAndState(List<String> supplierdetailIds, String state);

  /**
   * 查询根据productId
   */
  List<SupplierOrderDetailV2> findAllByOrderProductIdAndState(String productId, String state);

  /**
   * 根据订单id和表单类型查询
   * @return
   */
  List<SupplierOrderDetailV2> findAllByPurchaseOrderIdAndOrderToFormTypeAndState(String purchaseOrderId, String orderToFormType, String state);
}
