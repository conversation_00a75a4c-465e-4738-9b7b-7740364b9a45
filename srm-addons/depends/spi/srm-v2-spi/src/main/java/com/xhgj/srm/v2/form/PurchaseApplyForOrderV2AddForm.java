package com.xhgj.srm.v2.form;/**
 * @since 2025/4/18 13:08
 */

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/4/18 13:08:05
 *@description 采购申请新增V2
 */
@Data
public class PurchaseApplyForOrderV2AddForm {

  /**
   * 用于区分不同的item
   */
  @ApiModelProperty(hidden = true)
  private Integer index;

  /**
   * 采购申请号 <-> 采购申请编号
   */
  @Size(min = 10, max = 10)
  @JSONField(name = "APPLY_FOR_ORDER_NO")
  @NotBlank(message = "采购申请单号必填")
  private String applyForOrderNo;

  /**
   * 采购申请类型 <-> 凭证类型
   */
  @JSONField(name = "APPLY_FOR_TYPE")
  @NotBlank(message = "采购申请类型必填")
  private String applyForType;

  /**
   * 创建时间 <-> 请求日期
   */
  @JSONField(name = "CREATE_TIME")
  @NotNull(message = "创建时间不能为空")
  private Long createTime;

  /**
   * 取消状态 <-> 关闭
   */
  @JSONField(name = "CANCEL_STATUS")
  private String cancelStatus;

  /**
   * 申请人 <-> 创建人
   */
  @JSONField(name = "APPLY_FOR_USER_CODE")
  private String applyForUserCode;

  /**
   * 申请人 <-> 创建人
   */
  @JSONField(name = "APPLY_FOR_USER_NAME")
  private String applyForUserName;

  public String getApplyForUserCode() {
    // 转小写
    if (StrUtil.isBlank(applyForUserCode)) {
      return applyForUserCode;
    }
    return applyForUserCode.toLowerCase();
  }

  /**
   * 采购组织 <-> 采购组织
   */
  @JSONField(name = "PURCHASING_ORGANIZATION")
  @NotBlank(message = "采购组织必填")
  private String purchasingOrganization;

  /**
   * 销售组织 <-> 销售组织
   */
  @JSONField(name = "SALES_ORGANIZATION")
  private String salesOrganization;

  /**
   * 采购部门 <-> 采购组
   */
  @JSONField(name = "PURCHASE_DEPARTMENT")
  @NotBlank(message = "采购部门必填")
  private String purchaseDepartment;

  /**
   * 业务员 <-> 业务员
   */
  @JSONField(name = "SALESMAN")
  private String salesman;

  /**
   * 申请单备注 <-> 采购申请整单备注
   */
  @JSONField(name = "APPLICATION_FORM_REMARKS")
  private String applicationFormRemarks;

  /**
   * 发货方式 <-> 发货方式
   */
  @JSONField(name = "DELIVERY_TYPE")
  private String deliveryType;

  /**
   * 售达方 <-> 客户名称
   */
  @JSONField(name = "SOLD_TO_PARTY")
  private String soldToParty;

  /**
   * 收货人 <-> 收件人姓名
   */
  @JSONField(name = "CONSIGNEE")
  private String consignee;

  /**
   * 联系方式 <-> 收件人电话号码
   */
  @JSONField(name = "CONTACT_INFORMATION")
  private String contactInformation;

  /**
   * 收货地址 <-> 收货地址
   */
  @JSONField(name = "DELIVERY_ADDRESS")
  private String deliveryAddress;

  /**
   * 物料序号 <-> 采购申请行项目号
   */
  @NotBlank(message = "物料序号必填")
  @JSONField(name = "ROW_ID")
  private String rowId;

  /**
   * 资产卡片 <-> 资产卡片
   */
  @JSONField(name = "ASSET_CARD")
  private String assetCard;

  /**
   * 资产卡片名称 <-> 资产卡片名称
   */
  @JSONField(name = "ASSET_CARD_NAME")
  private String assetCardName;

  /**
   * 物料编码 <-> 物料编码
   */
  @JSONField(name = "PRODUCT_CODE")
  private String productCode;

  /**
   * 物料名称 <-> 物料名称
   */
  @JSONField(name = "PRODUCT_NAME")
  @NotBlank(message = "物料名称必填")
  private String productName;

  /**
   * 品牌 <-> 品牌
   */
  @JSONField(name = "BRAND")
  private String brand;

  /**
   * 规格 <-> 规格
   */
  @JSONField(name = "SPECIFICATION")
  private String specification;

  /**
   * 型号 <-> 型号
   */
  @JSONField(name = "MODEL")
  private String model;

  /**
   * 单位 <-> 计量单位
   */
  @JSONField(name = "UNIT")
  private String unit;

  /**
   * 物料描述 <-> 物料备注
   */
  @JSONField(name = "MATERIAL_DESCRIPTION")
  private String materialDescription;

  /**
   * 物料行备注 <-> 采购申请行备注
   */
  @JSONField(name = "MATERIAL_LINE_REMARKS")
  private String materialLineRemarks;

  /**
   * 申请数量 <-> 申请数量
   */
  @JSONField(name = "QUANTITY")
  @NotNull(message = "申请数量必填")
  private BigDecimal quantity;

  /**
   * 销售单价 <-> 销售价
   */
  @JSONField(name = "SALES_UNIT_PRICE")
  private BigDecimal salesUnitPrice;

  /**
   * 销售需求数量 <-> 销售需求数量
   */
  @JSONField(name = "SALES_DEMAND_QUANTITY")
  private BigDecimal salesDemandQuantity;

  /**
   * MPM参考结算价 <-> 结算价
   */
  @JSONField(name = "MPM_REFERENCE_SETTLEMENT_PRICE")
  private BigDecimal mpmReferenceSettlementPrice;

  /**
   * 仓库 <-> 库存地点
   */
  @JSONField(name = "WAREHOUSE")
  private String warehouse;

  /**
   * 计划需求日期 <-> 需求日期
   */
  @JSONField(name = "PLAN_DEMAND_DATE")
  private Long planDemandDate;

  /**
   * 采购员工号 <-> 采购员ID
   */
  @JSONField(name = "PURCHASING_EMPLOYEE_NUMBER")
  private String purchasingEmployeeNumber;

  public String getPurchasingEmployeeNumber() {
    // 转小写
    if (StrUtil.isBlank(purchasingEmployeeNumber)) {
      return purchasingEmployeeNumber;
    }
    return purchasingEmployeeNumber.toLowerCase();
  }

  /**
   * 采购员姓名 <-> 采购人员
   */
  @JSONField(name = "PURCHASER_NAME")
  private String purchaserName;

  /**
   * 科目分配类别编码 <-> 科目分配类别
   */
  @JSONField(name = "SUBJECT_CATEGORY_CODE")
  private String subjectAllocationCategoryCode;

  /**
   * 科目分配类别名称 <-> 科目分配类别描述
   */
  @JSONField(name = "SUBJECT_CATEGORY_NAME")
  private String subjectAllocationCategoryName;

  /**
   * 项目类别编码 <-> 项目类别
   */
  @JSONField(name = "PROJECT_CATEGORY_CODE")
  private String projectCategoryCode;

  /**
   * 项目类别名称 <-> 项目类型描述
   */
  @JSONField(name = "PROJECT_CATEGORY_NAME")
  private String projectCategoryName;

  /**
   * 总账科目编码 <-> 总账科目
   */
  @JSONField(name = "GENERAL_LEDGER_ACCOUNT_CODE")
  private String generalLedgerAccountCode;

  /**
   * 总账科目名称 <-> 总账科目描述
   */
  @JSONField(name = "GENERAL_LEDGER_ACCOUNT_NAME")
  private String generalLedgerAccountName;

  /**
   * 成本中心编码 <-> 成本中心
   */
  @JSONField(name = "COST_CENTER_CODE")
  private String costCenterCode;

  /**
   * 成本中心名称 <-> 成本中心描述
   */
  @JSONField(name = "COST_CENTER_NAME")
  private String costCenterName;

  /**
   * 订单 <-> 订单
   */
  @JSONField(name = "ORDER_NO")
  private String orderNo;

  /**
   * 订单描述 <-> 订单描述
   */
  @JSONField(name = "ORDER_DESCRIPTION")
  private String orderDescription;

  /**
   * 交货日期 <-> 交货日期
   */
  @JSONField(name = "DELIVERY_DATE")
  private Long deliveryDate;

  /**
   * 物料组编码 <-> 物料组
   */
  @JSONField(name = "PRODUCT_GROUP_CODE")
  private String productGroupCode;

  /**
   * 物料组名称 <-> 物料组描述
   */
  @JSONField(name = "PRODUCT_GROUP_NAME")
  private String productGroupName;

  /**
   * 固定的供应商
   */
  @JSONField(name = "FIXED_SUPPLIER")
  private String fixedSupplier;

  /**
   * 采购信息记录
   */
  @JSONField(name = "PURCHASE_INFO_RECORD")
  private String purchaseInfoRecord;

  /**
   * 销售订单号
   */
  @JSONField(name = "SALE_ORDER_NO")
  private String saleOrderNo;

  /**
   * 销售订单物料行ID
   */
  @JSONField(name = "SALE_ORDER_PRODUCT_ROW_ID")
  private String saleOrderProductRowId;

  @ApiModelProperty("是否急单:Y/N")
  @JSONField(name = "IS_WORRY_ORDER")
  private String isWorryOrder;

  /**
   * 验收单模板下载(预留字段)
   */
  @ApiModelProperty("验收单模板下载")
  @JSONField(name = "ACCEPTANCE_TEMPLATE_DOWNLOAD")
  private String acceptanceTemplateDownload;

  /**
   * 委外申请明细 列表
   */
  @JSONField(name = "OUTSOURCING_APPLY_DETAIL_LIST")
  @Valid
  private List<OutsourcingApplyDetail> outsourcingApplyDetailList;

  /**
   * 委外申请明细
   */
  @Data
  public static final class OutsourcingApplyDetail {
    /**
     * 项目编号 <-> 组件项目
     */
    @JSONField(name = "PROJECT_NO")
    private String projectNo;

    /**
     * 组件物料编码 <-> 组件物料
     */
    @JSONField(name = "COMPONENT_PRODUCT_CODE")
    private String componentProductCode;

    /**
     * 物料名称 <-> 组件物料名称
     */
    @JSONField(name = "COMPONENT_PRODUCT_NAME")
    private String componentProductName;

    /**
     * 品牌 <-> 组件品牌
     */
    @JSONField(name = "COMPONENT_BRAND")
    private String componentBrand;

    /**
     * 规格 <-> 组件规格
     */
    @JSONField(name = "COMPONENT_SPECIFICATION")
    private String componentSpecification;

    /**
     * 型号 <-> 组件型号
     */
    @JSONField(name = "COMPONENT_MODEL")
    private String componentModel;

    /**
     * 需求数量 <-> 需求数量
     */
    @JSONField(name = "COMPONENT_QUANTITY")
    private BigDecimal componentQuantity;

    /**
     * 单位 <-> 单位
     */
    @JSONField(name = "COMPONENT_UNIT")
    private String componentUnit;

    /**
     * 工厂 <-> 组件工厂
     */
    @JSONField(name = "COMPONENT_FACTORY")
    private String componentFactory;

    /**
     * 需求日期 <-> 需求日期
     */
    @JSONField(name = "COMPONENT_DEMAND_DATE")
    private Long componentDemandDate;

    /**
     * 行项目类别 <-> 行项目类别
     */
    @JSONField(name = "LINE_ITEM_CATEGORY")
    private String lineItemCategory;

    /**
     * MRP类型  <-> MRP类型
     */
    @JSONField(name = "MRP_TYPE")
    private String mrpType;

    /**
     * 已使用数量 <-> 提货数量
     */
    @JSONField(name = "USED_QUANTITY")
    private BigDecimal usedQuantity;
  }

}
