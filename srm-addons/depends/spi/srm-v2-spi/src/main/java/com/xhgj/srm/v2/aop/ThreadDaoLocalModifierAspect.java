package com.xhgj.srm.v2.aop;/**
 * @since 2025/4/17 19:18
 */

import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;

/**
 *<AUTHOR>
 *@date 2025/4/17 19:18:11
 *@description
 */
@Aspect
@Component
@Slf4j
public class ThreadDaoLocalModifierAspect {

  /**
   * 记录当前线程是否由此切面设置了版本信息以及设置的嵌套深度
   */
  private static final ThreadLocal<Integer> ASPECT_DEPTH = new ThreadLocal<>();

  // 1. 切点：你自己 v2 包里的接口
  @Pointcut("execution(* com.xhgj.srm.v2.repository..*.*(..))")
  private void v2RepoInterface() {}

  // 2. 切点：Spring Data JPA 实现类，但仅限 v2 包
  @Pointcut(
      "execution(* org.springframework.data.repository.CrudRepository+.*(..))"
          + " && within(com.xhgj.srm.v2.repository..*)"
  )
  private void v2RepoImpl() {}

  // 3. 还包括你自己的 DAO 包
  @Pointcut("execution(* com.xhgj.srm.v2.dao..*.*(..))")
  private void v2Dao() {}


  @Before("v2RepoInterface() || v2RepoImpl() || v2Dao()")
  public void modifyThreadLocalBeforeExecution() {
    // 只有上下文没值才设置
    Integer depth = ASPECT_DEPTH.get();
    if (ShardingContext.getVersion() == null || depth != null) {
      if (depth == null) {
        depth = 0;
      }
      log.info("ThreadDaoLocalModifierAspect modifyThreadLocalBeforeExecution");
      ShardingContext.setVersion(VersionEnum.V2);
      // 嵌套深度加1
      ASPECT_DEPTH.set(depth + 1);
    }
  }

  @After("v2RepoInterface() || v2RepoImpl() || v2Dao()")
  public void cleanupThreadLocal() {
    Integer depth = ASPECT_DEPTH.get();
    if (depth != null) {
      depth = depth - 1;
      // 只有最外层方法执行完才清理ThreadLocal
      if (depth == 0) {
        log.info("ThreadDaoLocalModifierAspect cleanupThreadLocal");
        ShardingContext.clear();
        ASPECT_DEPTH.remove();
      } else {
        ASPECT_DEPTH.set(depth);
      }
    }
  }
}
