package com.xhgj.srm.v2.dto;

import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.common.map.TypeAwareMap;
import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * WarehouseEntryListParamV2
 */
@Data
public class WarehouseEntryListParamV2 implements BaseDefaultSearchSchemeForm {
  @ApiModelProperty("勾选的数据行")
  private List<String> ids;
  @ApiModelProperty("搜索方案 id")
  private String schemeId;
  @ApiModelProperty(value = "采购订单号")
  private String orderCode;
  @ApiModelProperty(value = "SAP物料凭证号")
  private String sapProductVoucherNo;
  @ApiModelProperty(value = "批号")
  private String batchNo;
  @ApiModelProperty(value = "已开票数量")
  private BigDecimal invoiceQuantity;
  @ApiModelProperty(value = "已开票数量操作符")
  private LogicalOperatorsEnums invoiceQuantityOperator;
  @ApiModelProperty(value = "关联发票号")
  private String invoiceNo;
  @ApiModelProperty(value = "入库时间起")
  private Long startTime;
  @ApiModelProperty(value = "入库时间止")
  private Long endTime;
  @ApiModelProperty(value = "快递单号")
  private String expressNo;
  @ApiModelProperty(value = "物流公司")
  private String expressCompany;
  @ApiModelProperty(value = "物料编码")
  private String productCode;
  @ApiModelProperty(value = "品牌")
  private String brand;
  @ApiModelProperty(value = "物料名称")
  private String productName;
  @ApiModelProperty(value = "规格型号")
  private String specification;
  @ApiModelProperty(value = "单价")
  private BigDecimal unitPrice;
  @ApiModelProperty(value = "单价操作符")
  private LogicalOperatorsEnums unitPriceOperators;
  @ApiModelProperty(value = "退库数量")
  private BigDecimal returnQuantity;
  @ApiModelProperty(value = "单退库数量操作符")
  private LogicalOperatorsEnums returnQuantityOperator;
  @ApiModelProperty(value = "冲销状态")
  private String reversalStatus;
  @ApiModelProperty("供应商名称")
  private String supplierName;
  @ApiModelProperty("采购员")
  private String purchaseMan;
  @ApiModelProperty("采购部门")
  private String purchaseDept;
  @ApiModelProperty("未开票数量")
  private BigDecimal unInvoiceNum;
  @ApiModelProperty(value = "未开票数量操作符")
  private LogicalOperatorsEnums unInvoiceNumOperator;
  @ApiModelProperty(value = "仓库")
  private String warehouseV2;
  @ApiModelProperty(value = "订单类型")
  private String orderType;
  @ApiModelProperty("含税金额")
  private BigDecimal totalPriceAndTax;
  @ApiModelProperty(value = "含税金额操作符")
  private LogicalOperatorsEnums totalPriceAndTaxOperator;
  @ApiModelProperty("已开票金额")
  private BigDecimal invoicedAmount;
  @ApiModelProperty(value = "已开票金额操作符")
  private LogicalOperatorsEnums invoicedAmountOperator;
  @ApiModelProperty("未开票金额")
  private BigDecimal unInvoicedAmount;
  @ApiModelProperty(value = "未开票金额操作符")
  private LogicalOperatorsEnums unInvoicedAmountOperator;
  @ApiModelProperty("采购申请单号")
  private String purchaseApplyCode;
  @ApiModelProperty("项目编码")
  private String projectNo;
  @ApiModelProperty("业务员")
  private String salesman;
  @ApiModelProperty("销售订单号")
  private String saleOrderNo;

  @ApiModelProperty("结算单价")
  private BigDecimal settlementPrice;

  @ApiModelProperty(value = "结算单价操作符")
  private LogicalOperatorsEnums settlementPriceOperator;

  @ApiModelProperty("可开票数量")
  private BigDecimal invoiceAbleNum;

  @ApiModelProperty(value = "可开票数量操作符")
  private LogicalOperatorsEnums invoiceAbleNumOperator;

  private Integer pageNo;
  private Integer pageSize;
  private String userId;
  /**
   * 用户组
   */
  private String userGroup;

  /**
   * 过账日期/退库日期
   *
   */
  @ApiModelProperty("过账日期-开始")
  private Long postingDateStart;

  @ApiModelProperty("过账日期-结束")
  private Long postingDateEnd;

  /**
   * form状态
   * @see  com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus
   */
  @ApiModelProperty("审批状态")
  private Byte auditStatus;

  /**
   * 入库单号
   */
  @ApiModelProperty("入库单号")
  private String formCode;

  public Integer getPageNo() {
    if (pageNo == null) {
      return 1;
    }
    return pageNo;
  }

  public Integer getPageSize() {
    if (pageSize == null) {
      return 10;
    }
    return pageSize;
  }

  public Map<String,Object> toQueryMap(List<String> userNameList, String purchaseId,
      String createMan) {
    Map<String, Object> queryMap = new TypeAwareMap<>();
    queryMap.put("ids", ids);
    queryMap.put("userNameList", userNameList);
    queryMap.put("purchaseId", purchaseId);
    queryMap.put("createMan", createMan);
    queryMap.put("warehouseV2", warehouseV2);
    queryMap.put("orderType", orderType);
    queryMap.put("orderCode", orderCode);
    queryMap.put("sapProductVoucherNo", sapProductVoucherNo);
    queryMap.put("invoiceNo", invoiceNo);
    queryMap.put("batchNo", batchNo);
    // invoiceQuantityOperator
    queryMap.put("invoiceQuantityOperator", invoiceQuantityOperator);
    queryMap.put("invoiceQuantity", invoiceQuantity);
    queryMap.put("startTime", startTime);
    queryMap.put("endTime", endTime);
    queryMap.put("expressNo", expressNo);
    queryMap.put("expressCompany", expressCompany);
    queryMap.put("productCode", productCode);
    queryMap.put("brand", brand);
    queryMap.put("productName", productName);
    queryMap.put("specification", specification);
    // priceOperator
    queryMap.put("priceOperator", unitPriceOperators);
    queryMap.put("price", unitPrice);
    // returnQuantityOperator
    queryMap.put("returnQuantityOperator", returnQuantityOperator);
    queryMap.put("returnQuantity", returnQuantity);
    // unInvoiceNumOperator
    queryMap.put("unInvoiceNumOperator", unInvoiceNumOperator);
    queryMap.put("unInvoiceNum", unInvoiceNum);
    // writeOffState
    queryMap.put("writeOffState", reversalStatus);
    // supplierName
    queryMap.put("supplierName", supplierName);
    // purchaseMan
    queryMap.put("purchaseMan", purchaseMan);
    // purchaseDept
    queryMap.put("purchaseDept", purchaseDept);
    // totalPriceAndTaxOperator
    queryMap.put("totalPriceAndTaxOperator", totalPriceAndTaxOperator);
    queryMap.put("totalPriceAndTax", totalPriceAndTax);
    // invoicedAmountOperator
    queryMap.put("invoicedAmountOperator", invoicedAmountOperator);
    queryMap.put("invoicedAmount", invoicedAmount);
    // unInvoicedAmountOperator
    queryMap.put("unInvoicedAmountOperator", unInvoicedAmountOperator);
    queryMap.put("unInvoicedAmount", unInvoicedAmount);
    // purchaseApplyCode
    queryMap.put("purchaseApplyCode", purchaseApplyCode);
    // projectNo
    queryMap.put("projectNo", projectNo);
    // salesman
    queryMap.put("salesman", salesman);
    // saleOrderNo
    queryMap.put("saleOrderNo", saleOrderNo);
    // settlementPriceOperator
    queryMap.put("settlementPriceOperator", settlementPriceOperator);
    queryMap.put("settlementPrice", settlementPrice);
    // invoiceAbleNumOperator
    queryMap.put("invoiceAbleNumOperator", invoiceAbleNumOperator);
    queryMap.put("invoiceAbleNum", invoiceAbleNum);
    queryMap.put("pageNo", getPageNo());
    queryMap.put("pageSize", getPageSize());
    queryMap.put("userGroup", userGroup);
    queryMap.put("postingDateStart", postingDateStart);
    queryMap.put("postingDateEnd", postingDateEnd);
    queryMap.put("auditStatus", auditStatus);
    queryMap.put("formCode", formCode);
    return queryMap;
  }


}
