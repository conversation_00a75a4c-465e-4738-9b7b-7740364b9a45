package com.xhgj.srm.v2.form;

import lombok.Data;
import java.util.List;

/**
 * sap入库申请单
 */
@Data
public class PurchaseOrderWarehouseApplyV2AddFormSap extends PurchaseOrderWarehouseApplyV2AddForm{

  /**
   * 物料凭证
   */
  private String productVoucher;
  /**
   * 物料凭证年度
   */
  private String productVoucherYear;

  /**
   * 物料信息
   */
  private List<PurchaseOrderWarehouseApplyV2AddFormSapDetail> productInfoList;

  @Data
  public static class PurchaseOrderWarehouseApplyV2AddFormSapDetail {

    /**
     * 采购订单行项目号
     */
    private String purchaseOrderRowId;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 入库单SAP物料行id / 退库单SAP物料凭证行项目
     */
    private String warehousingRowId;

  }

}
