package com.xhgj.srm.v2.dto;

import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * PurchaseOrderInvoiceRelationV2
 */
@Data

public class PurchaseOrderInvoiceRelationV2 {
  @ApiModelProperty("发票id")
  private String orderInvoiceRelationId;

  @ApiModelProperty("发票号")
  private String invoiceNums;

  @ApiModelProperty("发票状态")
  private String invoiceState;

  @ApiModelProperty("源单类型")
  private String orderSource;

  @ApiModelProperty("是否来源后台 0否 1是")
  private String  manageFlag;

  public PurchaseOrderInvoiceRelationV2(InputInvoiceOrder orderInvoiceRelation) {
    this.orderInvoiceRelationId = orderInvoiceRelation.getId();
    this.invoiceNums = orderInvoiceRelation.getInvoiceNums();
    this.invoiceState = orderInvoiceRelation.getInvoiceState();
    this.orderSource = orderInvoiceRelation.getOrderSource();
    this.manageFlag = orderInvoiceRelation.getManageFlag();
  }
}
