<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>srm-boot</artifactId>
    <groupId>com.xhgj</groupId>
    <version>3.0.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>srm-request</artifactId>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.xhiot.xhiot-boot.modules-core</groupId>
      <artifactId>boot-request-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhgj.xhgj-cloud.dock</groupId>
      <artifactId>dock-provider-support</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-jpa</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-entry-registration-ddd</artifactId>
      <version>3.0.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-forest</artifactId>
      <version>3.0.0-SNAPSHOT</version>
    </dependency>

  </dependencies>

</project>