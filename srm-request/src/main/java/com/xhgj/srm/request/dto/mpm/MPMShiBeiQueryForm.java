package com.xhgj.srm.request.dto.mpm;/**
 * @since 2025/1/14 17:17
 */

import com.xhgj.srm.common.enums.product.ProductExternalPlatform;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 *<AUTHOR>
 *@date 2025/1/14 17:17:43
 *@description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MPMShiBeiQueryForm {
  /**
   * 电商商品链接
   */
  @NotBlank(message = "电商商品链接不能为空")
  private String url;

  /**
   * 外部单位
   */
  @NotNull(message = "外部单位不能为空")
  private BigDecimal externalNum;

  /**
   * 内部单位
   */
  @NotNull(message = "内部单位不能为空")
  private BigDecimal internalNum;

  /**
   * 市场价
   */
  @NotNull(message = "市场价不能为空")
  private BigDecimal marketPrice;

  /**
   * 平台类型
   */
  @NotNull(message = "平台类型必填")
  private String platformType;

  /**
   * 转换为外部的查询参数
   * @return
   */
  public Map<String, Object> toQueryMap() {
    Map<String, Object> map = new HashMap<>();
    map.put("url", url);
    map.put("externalUnit", externalNum);
    map.put("insideUnit", internalNum);
    map.put("marketPrice", marketPrice);
    map.put("platformType", ProductExternalPlatform.getMpmCodeByCode(platformType));
    return map;
  }
}
