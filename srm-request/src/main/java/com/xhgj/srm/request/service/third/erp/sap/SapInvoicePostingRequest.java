package com.xhgj.srm.request.service.third.erp.sap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.request.enums.SAPMethod;
import com.xhgj.srm.request.service.third.erp.sap.dto.InvoicePostingParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.InvoicePostingResult;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SapInvoicePostingRequest extends BaseSapRequest{

  /**
   * 发票过账
   * @throws RuntimeException 请求异常、json转换异常时抛出
   * @deprecated 请使用 {@link com.xhgj.srm.request.service.third.sap.impl.SAPServiceImpl#transferItemsWithAlarm(InvoicePostingParam, String)} 方法
   *
   */
  @Deprecated
  public InvoicePostingResult transferItems(InvoicePostingParam invoicePostingParam) throws RuntimeException {
    InvoicePostingResult result;
    try {
      String responseBody = postSap(SAPMethod.ZFM_FICO_008, invoicePostingParam);
      result = JSON.parseObject(responseBody,
          new TypeReference<InvoicePostingResult>() {});
    } catch (CheckException e){
      throw e;
    } catch (Exception e) {
      log.error("调用sap接口失败", e);
      throw new RuntimeException("调用sap接口失败", e);
    }
    return result;
  }
}
