package com.xhgj.srm.request.utils;/**
 * @since 2025/5/9 16:52
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONValidator;
import com.dtflys.forest.http.ForestBody;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.http.ForestURL;
import com.dtflys.forest.http.body.NameValueRequestBody;
import com.dtflys.forest.http.body.ObjectRequestBody;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderSyncStatus;
import com.xhgj.srm.jpa.entity.SupplierOrderSync;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.repository.SupplierOrderSyncRepository;
import com.xhgj.srm.request.dto.hZero.process.StartProcessVo;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/5/9 16:52:13
 *@description 重推操作记录
 */
@Component
public class RePushExecutor {

  @Resource
  SupplierOrderSyncRepository supplierOrderSyncRepository;


  /**
   * 执行重推操作并自动处理上下文
   *
   * @param type 同步类型
   * @param syncMode 同步模式
   * @param target 目标系统
   * @param payload 推送内容
   * @param sourceObj 源对象
   * @param user 用户
   * @param action 要执行的核心操作
   * @param <T> 源对象类型
   * @throws Exception 如果执行过程中发生异常
   */
  public <T> void execute(
      String type,
      String syncMode,
      String target,
      String payload,
      T sourceObj,
      User user,
      Runnable action) {

    try {
      // 设置记录标志
      RePushContext.setRecordFlag(true);

      // 创建同步记录
      SupplierOrderSync supplierOrderSync = this.createSupplierOrderSync(type, syncMode, target, payload, sourceObj, user);

      // 保存同步记录
      supplierOrderSyncRepository.saveAndFlush(supplierOrderSync);

      // 设置上下文
      RePushContext.setRePushContext(supplierOrderSync);

      // 执行主要操作
      action.run();
      // 设置成功状态
      supplierOrderSync.setStatus(SupplierOrderSyncStatus.SUCCESS.getCode());

    } catch (Exception e) {
      if (RePushContext.getRePushContext() != null) {
        RePushContext.getRePushContext().setStatus(SupplierOrderSyncStatus.FAIL.getCode());
      }
      throw e;
    } finally {
      // 更新同步记录
      this.updateSupplierOrderSync(
          RePushContext.getRePushContext(),
          RePushContext.getForestRequest(),
          RePushContext.getForestResponse()
      );

      // 清理上下文
      RePushContext.clear();
    }
  }

  /**
   * 带返回值的执行方法
   */
  public <T, R> R executeWithResult(
      String type,
      String syncMode,
      String target,
      String payload,
      T sourceObj,
      User user,
      Supplier<R> action) {

    R result = null;
    try {
      RePushContext.setRecordFlag(true);
      SupplierOrderSync supplierOrderSync = this.createSupplierOrderSync(
          type, syncMode, target, payload, sourceObj, user);
      supplierOrderSyncRepository.saveAndFlush(supplierOrderSync);
      RePushContext.setRePushContext(supplierOrderSync);

      // 执行并获取结果
      result = action.get();
      // 设置成功状态
      supplierOrderSync.setStatus(SupplierOrderSyncStatus.SUCCESS.getCode());
      // 设置结果
      if (result instanceof StartProcessVo) {
        StartProcessVo startProcessVo = (StartProcessVo) result;
        supplierOrderSync.setReviewId(startProcessVo.getInstanceId());
      }
      return result;

    } catch (Exception e) {
      if (RePushContext.getRePushContext() != null) {
        RePushContext.getRePushContext().setStatus(SupplierOrderSyncStatus.FAIL.getCode());
      }
      throw e;
    } finally {
      this.updateSupplierOrderSync(
          RePushContext.getRePushContext(),
          RePushContext.getForestRequest(),
          RePushContext.getForestResponse()
      );
      RePushContext.clear();
    }
  }

  /**
   * 生成采购订单同步记录日志
   */
  public <T> SupplierOrderSync createSupplierOrderSync(
      String type,
      String syncMode,
      String target,
      String req,
      T unique,
      User user) {
    SupplierOrderSync supplierOrderSync = new SupplierOrderSync();
    supplierOrderSync.setType(type);
    if (unique instanceof SupplierOrderV2) {
      SupplierOrderV2 supplierOrderV2 = (SupplierOrderV2) unique;
      supplierOrderSync.setSupplierOrderId(supplierOrderV2.getId());
      supplierOrderSync.setSupplierOrderCode(supplierOrderV2.getCode());
    } else {
      // 其他类型的处理逻辑
      supplierOrderSync.setSupplierOrderId(unique.toString());
    }
    supplierOrderSync.setTarget(target);
    supplierOrderSync.setCreateTime(System.currentTimeMillis());
    supplierOrderSync.setSuccessTime(null);
    supplierOrderSync.setSyncType(syncMode);
    if (user != null) {
      supplierOrderSync.setCreateMan(user.getId());
      supplierOrderSync.setCreateManName(user.getRealName());
    } else {
      supplierOrderSync.setCreateMan(null);
      supplierOrderSync.setCreateManName("系统");
    }
    supplierOrderSync.setStatus(SupplierOrderSyncStatus.SYNCING.getCode());
    supplierOrderSync.setReviewStatus(SupplierOrderSyncStatus.SYNCING.getCode());
    supplierOrderSync.setReviewId(null);
    supplierOrderSync.setReviewTime(null);
    supplierOrderSync.setReviewReason(null);
    supplierOrderSync.setState(Constants.STATE_OK);
    supplierOrderSync.setReq(req);
    supplierOrderSync.setRes(null);
    supplierOrderSync.setUrl(null);
    supplierOrderSync.setHeader(null);
    return supplierOrderSync;
  }

  private void updateSupplierOrderSync(SupplierOrderSync supplierOrderSync, ForestRequest forestRequest, ForestResponse forestResponse) {
    if (supplierOrderSync == null) {
      return;
    }
    try {
      if (forestRequest != null) {
        ForestURL forestURL = forestRequest.url();
        String scheme = forestURL.getScheme();
        String authority = forestURL.getAuthority();
        String path = forestURL.getPath();
        String queryString = forestRequest.getQueryString();
        String ref = forestURL.getRef();
        URI originUri = new URI(
            // Scheme
            scheme,
            // Host
            decodeValue(authority),
            // 处理路径
            decodeValue(path),
            decodeValue(queryString),
            decodeValue(ref)
        );
        supplierOrderSync.setUrl(originUri.toString());
        Set<Entry<String, String>> entries = forestRequest.headers().entrySet();
        Map<String, String> headers = new HashMap<>();
        for (Entry<String, String> entry : entries) {
          headers.put(entry.getKey(), entry.getValue());
        }
        supplierOrderSync.setHeader(JSON.toJSONString(headers));
        supplierOrderSync.setReq(JSON.toJSONString(this.getRequestForm(forestRequest)));
      }
      if (forestResponse != null) {
        Object result = forestResponse.getResult();
        if (result instanceof String) {
          // 如果不是JSON格式则不保存
          if (JSONValidator.from(Convert.toStr(forestResponse.getResult())).validate()) {
            supplierOrderSync.setRes(Convert.toStr(forestResponse.getResult()));
          } else {
            supplierOrderSync.setRes(null);
          }
        } else {
          supplierOrderSync.setRes(JSON.toJSONString(forestResponse.getResult()));
        }
      }
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    } finally {
      supplierOrderSync.setSuccessTime(System.currentTimeMillis());
      supplierOrderSyncRepository.saveAndFlush(supplierOrderSync);
    }
  }

  private String decodeValue(String value) {
    if (StrUtil.isBlank(value)) {
      return value;
    }
    try {
      // 尝试解码，如果已经是解码状态，可能会抛出异常
      return URLDecoder.decode(value, "UTF-8");
    } catch (Exception e) {
      // 解码失败则返回原始值
      return value;
    }
  }

  private Object getRequestForm(ForestRequest request) {
    Object form = null;
    ForestBody body = request.getBody();
    // 获取 ObjectRequestBody 列表
    List<ObjectRequestBody> objectItems = body.getObjectItems();
    // 获取 NameValueRequestBody 列表
    List<NameValueRequestBody> nameValueItems = body.getNameValueItems();
    // 优先处理 ObjectRequestBody
    if (CollUtil.isNotEmpty(objectItems)) {
      form = objectItems.stream()
          .map(ObjectRequestBody::getObject)
          .findFirst()
          .orElse(null);
    }
    // 处理 NameValueRequestBody
    else if (CollUtil.isNotEmpty(nameValueItems)) {
      // 将 NameValueRequestBody 转换为 Map 格式
      form = nameValueItems.stream()
          .filter(item -> item.getName() != null && item.getValue() != null) // 过滤掉null键值对
          .collect(Collectors.toMap(
              NameValueRequestBody::getName,
              NameValueRequestBody::getValue,
              (oldValue, newValue) -> newValue // 冲突时使用新值
          ));
    }else{
      // 从请求体中获取不到参数，尝试从请求参数中获取
      form = request.getQuery();
    }
    return form;
  }
}
