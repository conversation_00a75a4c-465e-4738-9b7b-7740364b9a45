package com.xhgj.srm.request.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/12/20 14:19
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateReturnParams extends BaseSupplierOrderERP {

  /** 单据 id */
  @JSONField(name = "OrderId")
  private String orderId;

  /** 承运商编码 */
  @JSONField(name = "Logistics")
  private String logistics;
  /** 物流单号 */
  @JSONField(name = "LogisticsNo")
  private String logisticsNo;
  /** 物料 */
  @JSONField(name = "ProductDetail")
  private List<RowAndNum> productDetail;

  /** 退货原因 */
  @JSONField(name = "ReturnReason")
  private String returnReason;

}
