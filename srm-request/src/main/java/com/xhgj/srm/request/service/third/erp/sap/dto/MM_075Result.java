package com.xhgj.srm.request.service.third.erp.sap.dto;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;
import lombok.Data;

@Data
public class MM_075Result {

  @JSONField(name = "RETURN")
  private List<ReturnMessage> returnMessages;

  @Data
  public static class ReturnMessage {
    @JSONField(name = "EBELN")
    private String EBELN;
    /**
     * 物料凭证号
     * @deprecated 文档里发现不存在这个字段
     */
    @JSONField(name = "BELNR")
    @Deprecated
    private String documentNumber;
    /**
     * 采购订单号
     */
    @JSONField(name = "TYPE")
    private String type;
    /**
     * 消息类型
     */
    @JSONField(name = "MESSAGE")
    private String message;

    @JSONField(name = "MBLNR")
    private String productVoucher;

    /**
     * 消息文本
     */
    @JSONField(name = "MSG")
    private String msg;
  }
}
