package com.xhgj.srm.request.service.third.erp.sap.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_087Result.ReturnMessage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * MM_088Result
 */
@Data
public class MM_088Result {
  /**
   * 消息类型: S 成功,E 错误,W 警告,I 信息,A 中断
   */
  @JSONField(name = "TYPE")
  private String type;

  /**
   * 消息内容
   */
  @JSONField(name = "MESSAGE")
  private String msg;

  @JSONField(name = "DATA")
  private List<MM_088Result.ReturnMessage> data;

  @Data
  public static class ReturnMessage {

    /**
     * 组件工厂
     */
    @ApiModelProperty("组件工厂")
    @JSONField(name = "WERKS")
    private String wreks;

    /**
     * 组件级别
     */
    @ApiModelProperty("组件级别")
    @JSONField(name = "STUFE")
    private String stufe;

    /**
     * 组件项目
     */
    @ApiModelProperty("组件项目")
    @JSONField(name = "POSNR")
    private String posnr;

    /**
     * 组件物料
     */
    @ApiModelProperty("组件物料")
    @JSONField(name = "DOBJT")
    private String dobjt;

    /**
     * 组件物料名称
     */
    @ApiModelProperty("组件物料名称")
    @JSONField(name = "OJTXP")
    private String ojtxp;

    /**
     * 品牌描述
     */
    @ApiModelProperty("品牌描述")
    @JSONField(name = "ZPPMS")
    private String zppms;


    /**
     * 规格
     */
    @ApiModelProperty("规格")
    @JSONField(name = "ZZGG")
    private String zzgg;

    /**
     * 型号
     */
    @ApiModelProperty("型号")
    @JSONField(name = "ZZXH")
    private String zzxh;

    /**
     * 是否虚拟件
     */
    @ApiModelProperty("是否虚拟件")
    @JSONField(name = "ZXNJ")
    private String zxnj;

    /**
     * 组件数量
     */
    @ApiModelProperty("组件数量")
    @JSONField(name = "MENGE")
    private String menge;

    /**
     * 组件单位
     */
    @ApiModelProperty("组件单位")
    @JSONField(name = "MEINS")
    private String meins;

    /**
     * BOM预留字段1
     */
    @ApiModelProperty("BOM预留字段1")
    @JSONField(name = "ZZCKPYL1")
    private String zzckpyl1;

    /**
     * BOM预留字段2
     */
    @ApiModelProperty("BOM预留字段2")
    @JSONField(name = "ZZCKPYL2")
    private String zzckpyl2;

    /**
     * BOM预留字段3
     */
    @ApiModelProperty("BOM预留字段3")
    @JSONField(name = "ZZCKPYL3")
    private String zzckpyl3;

    /**
     * BOM预留字段4
     */
    @ApiModelProperty("BOM预留字段4")
    @JSONField(name = "ZZCKPYL4")
    private String zzckpyl4;

    /**
     * BOM预留字段5
     */
    @ApiModelProperty("BOM预留字段5")
    @JSONField(name = "ZZCKPYL5")
    private String zzckpyl5;


  }


}
