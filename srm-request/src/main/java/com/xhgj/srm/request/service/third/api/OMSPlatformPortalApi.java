package com.xhgj.srm.request.service.third.api;

import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Query;
import com.dtflys.forest.annotation.Retry;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.request.service.third.api.interceptor.OMSPlatformPortalInterceptor;
import com.xhgj.srm.request.vo.BaseXhgjRes;
import com.xhgj.srm.request.vo.oms.OmsOrderCustomerInfo;
import com.xhiot.boot.forest.annotation.BaseRequest;

@BaseRequest(interceptor = OMSPlatformPortalInterceptor.class)
public interface OMSPlatformPortalApi {
  /**
   * 获取履约订单信息
   */
  @Get( url = "/dock/SrmOrder/getSrmOrderInfo",
      contentType = "application/json",
      dataType = "json",
      headers = {"Api-Name: 获取履约订单客户信息"}
  )
  @Retry(maxRetryCount = "0", maxRetryInterval = "100")
  ForestResponse<BaseXhgjRes<OmsOrderCustomerInfo>> getOrderCustomerInfo(
      @Query("dockingOrderNo") String orderNo,
      @Query("dockingOrderType") String type
  );
}
