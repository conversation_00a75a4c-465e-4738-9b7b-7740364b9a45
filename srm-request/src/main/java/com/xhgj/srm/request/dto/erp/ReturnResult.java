package com.xhgj.srm.request.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/12/20 15:02
 */
@NoArgsConstructor
@Data
public class ReturnResult {

  /** 是否成功 */
  @JSONField(name = "success")
  private Boolean success;
  /** 返回信息 */
  @JSONField(name = "mes")
  private String mes;
  /** 返回数据 */
  @JSONField(name = "data")
  private List<OrderReturnData> returnData;

  @NoArgsConstructor
  @Data
  public static class OrderReturnData {

    /** 采购退货单单据Id */
    @JSONField(name = "FId")
    private Integer fId;

    /** 采购退货单编码 */
    @JSONField(name = "FNumber")
    private String fNumber;

    @JSONField(name = "EntryInfo")
    private List<EntryInfoDTO> entryInfo;

    @NoArgsConstructor
    @Data
    public static class EntryInfoDTO {

      /** 采购退货单明细行id */
      @JSONField(name = "RowId")
      private Integer rowId;
      /** 采购退货单明细行实退数量 */
      @JSONField(name = "Num")
      private BigDecimal num;
      /** 明细行 id */
      @JSONField(name = "PooRowId")
      private Integer pooRowId;
    }
  }
}
