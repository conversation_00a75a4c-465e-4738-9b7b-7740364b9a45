package com.xhgj.srm.request.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Created by <PERSON>g Shy on 2023/11/13
 */
@Data
public class UpdateErpOrderRateParams extends BaseSupplierOrderERP {

  @JSONField(name = "OrderData")
  private OrderData orderData;

  @Data
  public static class OrderData {

    /**
     * 采购订单号
     */
    @JSONField(name = "OrderNo")
    private String orderNo;
    @JSONField(name = "Entry")
    private List<Entry> entries;

    @Data
    public static class Entry {

      /**
       * 物料行id
       */
      @JSONField(name = "EntryId")
      private Integer entryId;
      /**
       * 含税单价
       */
      @JSONField(name = "Taxprice")
      private BigDecimal taxPrice;
      /**
       * 税率
       */
      @JSONField(name = "TaxRate")
      private BigDecimal taxRate;
    }
  }
}
