package com.xhgj.srm.request.config;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023-06-04 15:00
 */
@Configuration
@ConfigurationProperties(prefix = "third.mpm")
@Data
@EqualsAndHashCode(callSuper = true)
public class XhgjMPMConfig extends AbstractXhgjConfig {}
