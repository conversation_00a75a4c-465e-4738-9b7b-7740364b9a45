package com.xhgj.srm.request.service.third.erp.sap.dto;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class PushPurchaseInvoiceParam {

  /**
   * 国家代码：供應商所在国家代碼
   */
  @JSONField(name = "BUKRS")
  private String countryCode;

  /**
   * 打开发票时分配给发票的唯一标识符
   */
  @JSONField(name = "LIFRE")
  private String openInvoiceCode;

  // todo 目前不知道是否还在使用的等待测试测
  /**
   * 最早开具的发票的开具日期
   */
  @JSONField(name = "BLDAT")
  private Long firstOpenInvoiceDate;

  /**
   * 过账日期
   */
  @JSONField(name = "BUDAT")
  private Long transferItemsDate;

  /**
   * 增值税单总金额
   */
  @JSONField(name = "RMWWR")
  private BigDecimal taxPriceAmount;

  /**
   * 发票单税额合计
   */
  @JSONField(name = "WMWST")
  private BigDecimal taxAmount;

  /**
   * 货币代码
   */
  @JSONField(name = "WAERS")
  private String currencyCode;

  /**
   * 按照汇率计算的货币兑换比率
   */
  @JSONField(name = "KURSF")
  private BigDecimal exchangeRate;

  /**
   * 销售税率
   */
  @JSONField(name = "MWSKZ")
  private Integer taxRate;
}
