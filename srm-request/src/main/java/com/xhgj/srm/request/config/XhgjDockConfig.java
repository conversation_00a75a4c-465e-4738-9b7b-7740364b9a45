package com.xhgj.srm.request.config;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2022/8/8 19:59
 */
@Configuration
@ConfigurationProperties(prefix = "third.xhgj.dock")
@Data
@EqualsAndHashCode(callSuper = true)
public class XhgjDockConfig extends AbstractXhgjConfig {}
