package com.xhgj.srm.request.utils;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.util.URLUtil;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import org.apache.commons.compress.utils.IOUtils;

/**
 * <AUTHOR>
 * @since 2023-04-17 14:10
 */
public class AccurateUtils {
  /**
   * 构建 getAccurate 的 image pdf接口参数
   * @param bytes 数组
   * @return
   */
  public static String buildImageOrPdfParams(byte[] bytes) {
    return URLUtil.encode(Base64Encoder.encode(bytes), StandardCharsets.UTF_8);
  }

  /**
   * 构建 getAccurate 的 image pdf接口参数
   * @param inputStream
   * @throws IOException
   */
  public static String buildImageOrPdfParams(InputStream inputStream) throws IOException {
    return buildImageOrPdfParams(IOUtils.toByteArray(inputStream));
  }
}
