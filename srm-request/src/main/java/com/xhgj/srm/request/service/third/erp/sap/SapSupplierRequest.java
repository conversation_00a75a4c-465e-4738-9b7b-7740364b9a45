package com.xhgj.srm.request.service.third.erp.sap;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.common.enums.SupplierBlockRangeEnum;
import com.xhgj.srm.request.enums.SAPMethod;
import com.xhgj.srm.request.service.third.erp.sap.dto.SupplierAddOrUpdateResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.SupplierAddOrUpdateParam.Item1;
import com.xhgj.srm.request.service.third.erp.sap.dto.SupplierAddOrUpdateParam.Item2;
import com.xhiot.boot.core.common.exception.CheckException;
import java.util.Collections;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * created by <PERSON><PERSON> on 2019/5/16
 */
@Component
@Slf4j
public class SapSupplierRequest extends BaseSapRequest {

  /**
   * @param supplierMdmCode 供应商mdm编码
   * @param supplierBlockRangeEnum 供应商黑名单范围
   * @throws RuntimeException 请求异常、json转换异常时抛出
   */
  public boolean blacklistingSuppliers(String supplierMdmCode, String groupErpCode,
      SupplierBlockRangeEnum supplierBlockRangeEnum) throws RuntimeException{
    HashMap<String, Object> param = new HashMap<>();
    if (SupplierBlockRangeEnum.PROHIBIT_ACCOUNTING == supplierBlockRangeEnum) {
      Item1 item1 = Item1.buildByBlock(supplierMdmCode, groupErpCode);
      Item2 item2 = Item2.buildByBlock(supplierMdmCode, groupErpCode);
      item2.setProcurementFreeze(StrUtil.EMPTY);
      param.put(Constants_Sap.ITEM_ONE, Collections.unmodifiableList(ListUtil.of(item1)));
      param.put(Constants_Sap.ITEM_TWO, Collections.unmodifiableList(ListUtil.of(item2)));
    }
    if (SupplierBlockRangeEnum.PROHIBIT_MAKING_ORDERS == supplierBlockRangeEnum) {
      Item1 item1 = Item1.buildByBlock(supplierMdmCode, groupErpCode);
      item1.setProcurementFreeze(StrUtil.EMPTY);
      Item2 item2 = Item2.buildByBlock(supplierMdmCode, groupErpCode);
      param.put(Constants_Sap.ITEM_ONE, Collections.unmodifiableList(ListUtil.of(item1)));
      param.put(Constants_Sap.ITEM_TWO, Collections.unmodifiableList(ListUtil.of(item2)));
    }
    if (SupplierBlockRangeEnum.PROHIBIT_ACCOUNTING_AND_ORDER == supplierBlockRangeEnum) {
      Item1 item1 = Item1.buildByBlock(supplierMdmCode, groupErpCode);
      Item2 item2 = Item2.buildByBlock(supplierMdmCode, groupErpCode);
      param.put(Constants_Sap.ITEM_ONE, Collections.unmodifiableList(ListUtil.of(item1)));
      param.put(Constants_Sap.ITEM_TWO, Collections.unmodifiableList(ListUtil.of(item2)));
    }
    //移除黑名单
    if (supplierBlockRangeEnum == null) {
      Item1 item1 = Item1.buildByBlock(supplierMdmCode, groupErpCode);
      item1.setProcurementFreeze(StrUtil.EMPTY);
      Item2 item2 = Item2.buildByBlock(supplierMdmCode, groupErpCode);
      item2.setProcurementFreeze(StrUtil.EMPTY);
      param.put(Constants_Sap.ITEM_ONE, Collections.unmodifiableList(ListUtil.of(item1)));
      param.put(Constants_Sap.ITEM_TWO, Collections.unmodifiableList(ListUtil.of(item2)));
    }
    SupplierAddOrUpdateResult supplierAddOrUpdateResult;
    try {
      String responseBody = postSap(SAPMethod.ZFM_SRM_010, param);
      supplierAddOrUpdateResult =
          JSON.parseObject(responseBody, new TypeReference<SupplierAddOrUpdateResult>() {});
    } catch (CheckException e){
      throw e;
    } catch (Exception e) {
      log.error("调用sap接口失败", e);
      throw new CheckException("调用sap接口失败", e);
    }
    return supplierAddOrUpdateResult.isSuccess();
  }
}
