package com.xhgj.srm.request.service.third.api.interceptor;
import com.xhgj.srm.common.config.SrmConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * OMS API拦截器
 */
@Component
@Slf4j
public class OMSPlatformInterceptor extends OMSInterceptor {

  /**
   * 注入platformUrl
   * @see SrmConfig#getPlatformUrl()
   */
  @Value("${srm.platform-url:default}")
  private String platformUrl;


  @Override
  protected String getBaseUrl() {
    return platformUrl;
  }
}
