package com.xhgj.srm.request.service.third.api.interceptor;/**
 * @since 2025/2/25 18:02
 */

import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.request.utils.RePushContext;
import com.xhiot.boot.forest.interceptor.hand.HandInterceptor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class FeiDaInterceptor extends HandInterceptor {

  @Override
  protected String getSystemName() {
    return "飞搭";
  }

  @Override
  public boolean beforeExecute(ForestRequest request) {
    RePushContext.setForestRequest(request);
    return super.beforeExecute(request);
  }

  @Override
  public void afterExecute(ForestRequest request, ForestResponse response) {
    RePushContext.setForestResponse(response);
    super.afterExecute(request, response);
  }
}

