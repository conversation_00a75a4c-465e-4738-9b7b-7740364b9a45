package com.xhgj.srm.request.dto.partner;

import com.xhgj.srm.common.utils.compare.CompareIgnore;
import com.xhgj.srm.jpa.entity.Financial;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModelProperty.AccessMode;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/11 14:18
 */
@Data
@NoArgsConstructor
public class SupFinancialDTO {

  @ApiModelProperty("银行 id")
  private String id;

  @ApiModelProperty("开户银行")
  private String bankName;

  @ApiModelProperty("银行账号")
  private String bankNum;

  @ApiModelProperty("账户名称")
  private String bankAccount;

  @ApiModelProperty("开户行地址")
  private String bankAddress;

  @ApiModelProperty("联行号")
  private String bankCode;

  @ApiModelProperty("swiftCode")
  private String swiftCode;

  @ApiModelProperty(value = "开户许可证", notes = "仅国内供应商")
  private String accountUrl;

  @ApiModelProperty(value = "原始 id", accessMode = AccessMode.READ_ONLY)
  @CompareIgnore
  private String originId;

  @ApiModelProperty(value = "创建时间", accessMode = AccessMode.READ_ONLY)
  @CompareIgnore
  private Long createTime;

  public SupFinancialDTO(Financial financial) {
    this.id = financial.getId();
    this.bankName = financial.getBankName();
    this.bankNum = financial.getBankNum();
    this.bankAccount = financial.getBankAccount();
    this.bankAddress = financial.getBankAddress();
    this.bankCode = financial.getBankCode();
    this.swiftCode = financial.getSwiftCode();
    this.accountUrl = financial.getAccountUrl();
    this.createTime = financial.getCreateTime();
    this.originId = financial.getOriginId();
  }
}
