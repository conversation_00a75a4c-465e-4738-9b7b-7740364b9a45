package com.xhgj.srm.request.service.third.erp.sap.dto;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Getter;

/**
 * Created by Geng Shy on 2023/12/12
 */
@Getter
public class SapRequestBody<T> {

  @JSONField(name = "HEAD")
  private Map<String, Object> header;
  @JSONField(name = "ITEM")
  private List<T> item = new ArrayList<>();

  public SapRequestBody(Map<String, Object> header, T item) {
    this.header = header;
    this.item.add(item);
  }

  public SapRequestBody(Map<String, Object> header, List<T> item) {
    this.header = header;
    this.item.addAll(item);
  }

  public void setHead(Map<String, Object> map) {
    header = map;
  }

  public void setItem(T t) {
    item.add(t);
  }

  public void setItem(List<T> t) {
    item.addAll(t);
  }
}
