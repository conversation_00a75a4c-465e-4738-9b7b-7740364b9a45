package com.xhgj.srm.request.enums;

/**
 * SAP 方法枚举，可直接用于请求头 if_id
 * <AUTHOR>
 * @since 2024/6/27 21:13
 */
public enum SAPMethod {
  /** 直销库退货 */
  ZFM_MM_075,
  /** 预付款申请 */
  ZFM_MM_034,
  /** 提款申请 */
  ZFM_MM_066,
  /** 发票过账 */
  ZFM_FICO_008,
  /** 查询sap付款状态 */
  ZFM_SRM_FI_018,
  /** 更新采购申请单 */
  ZFM_MM_053,
  /** 同步物料凭证 */
  ZFM_MM_031,
  /** 供应商主数据的创建（包含扩展视图）、修改 */
  ZFM_SRM_010,
  /** 采购信息记录创建、修改 */
  ZFM_SRM_014,
  /** SRM->SAP 物料凭证冲销接口 */
  ZFM_MM_032,
  /** SRM采购订单同步SAP */
  ZFM_MM_021,
  /**
   * 库存查询接口
   */
  ZFM_MM_080,
  /**
   * sap调拨凭证过账接口
   */
  ZFM_MM_076,
  /**
   * sap组装拆卸单创建修改接口
   */
  ZFM_MM_077,
  /**
   * sap组装拆卸单状态变更同步
   */
  ZFM_MM_078,
  /**
   * sap组装拆卸信息过账接口
   */
  ZFM_MM_079,
  /**
   * 084采购订单更新审核状态
   */
  ZFM_MM_084,

  /**
   * 086接口订单查询
   */
  ZFM_MM_086,

  /**
   * 087接口资料卡片查询
   */
  ZFM_MM_087,

  /**
   * 088接口查询bom清单
   */
  ZFM_MM_088
  ;
}
