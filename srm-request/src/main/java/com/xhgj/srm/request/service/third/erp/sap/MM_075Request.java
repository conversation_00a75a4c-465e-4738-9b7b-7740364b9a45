package com.xhgj.srm.request.service.third.erp.sap;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.request.enums.SAPMethod;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Result;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.Date;

@Slf4j
@Component
public class MM_075Request extends BaseSapRequest {

  /**
   * 直销库退货
   *
   * @param param
   * @return
   * @throws RuntimeException
   * @deprecated
   */
  @Deprecated
  public MM_075Result directReturn(MM_075Param param) throws RuntimeException {
    Assert.notNull(param);
    MM_075Result result;
    try {
      String responseBody = postSap(SAPMethod.ZFM_MM_075, param);
      result = JSON.parseObject(responseBody, new TypeReference<MM_075Result>() {});
      if (StrUtil.equals(result.getReturnMessages().get(0).getType(), Constants_Sap.SUCCESS_TYPE)) {
        return result;
      } else {
        throw new CheckException("调用sap接口失败:" + JSON.toJSONString(result));
      }
    }catch (CheckException e){
      throw e;
    }catch (Exception e) {
      log.error("调用sap接口失败", e);
      throw new CheckException(
          "SAP网络异常请求失败，请联系管理员处理。请求时间"
              + DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
    }
  }
}
