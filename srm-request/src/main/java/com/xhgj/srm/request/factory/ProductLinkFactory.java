package com.xhgj.srm.request.factory;/**
 * @since 2025/1/15 18:05
 */

import cn.hutool.core.convert.Convert;
import com.xhgj.srm.common.enums.product.ProductExternalLinkType;
import com.xhgj.srm.common.enums.product.ProductExternalPlatform;
import com.xhgj.srm.common.enums.product.ProductExternalStore;
import com.xhgj.srm.common.enums.product.ProductExternalType;
import com.xhgj.srm.jpa.entity.ProductExternalLink;
import com.xhgj.srm.jpa.entity.ProductExternalLink.ExternalFile;
import com.xhgj.srm.request.dto.mpm.externalLink.ProductChildLinkParam;
import com.xhgj.srm.request.dto.mpm.externalLink.ProductPriceEvidenceDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


public class ProductLinkFactory {

  /**
   * 构建商品外部链接
   * @param productExternalLinks
   * @return
   */
  public List<ProductChildLinkParam> buildProductChildLink(List<ProductExternalLink> productExternalLinks) {
    List<ProductExternalLink> filters = productExternalLinks.stream()
        .filter(item -> ProductExternalType.EXTERNAL_LINK.getCode() == item.getType())
        .collect(Collectors.toList());
    return filters.stream().map(item -> {
      ProductChildLinkParam productChildLinkParam = new ProductChildLinkParam();
//      productChildLinkParam.setId(item.getId());
      productChildLinkParam.setShopType(ProductExternalStore.getMpmCodeByCode(item.getStoreType()));
      productChildLinkParam.setPlatformType(
          ProductExternalPlatform.getMpmCodeByCode(item.getPlatformType()));
      productChildLinkParam.setPrice(item.getExternalPrice());
      productChildLinkParam.setInsideUnit(Convert.toStr(item.getInternalNum()));
      productChildLinkParam.setExternalUnit(Convert.toStr(item.getExternalNum()));
      productChildLinkParam.setLink(item.getExternalLink());
      productChildLinkParam.setType(ProductExternalLinkType.getMpmCodeByCode(item.getLinkType()));
      return productChildLinkParam;
    }).collect(Collectors.toList());
  }

  /**
   * 构建商品价格佐证
   * @param productExternalLinks
   * @return
   */
  public List<ProductPriceEvidenceDTO> buildProductPriceEvidence(List<ProductExternalLink> productExternalLinks) {
    List<ProductExternalLink> filters = productExternalLinks.stream()
        .filter(item -> ProductExternalType.PRICE_PROOF.getCode() == item.getType())
        .collect(Collectors.toList());
    return filters.stream().map(item -> {
      ProductPriceEvidenceDTO productPriceEvidenceDTO = new ProductPriceEvidenceDTO();
//      productPriceEvidenceDTO.setId(item.getId());
      productPriceEvidenceDTO.setPrice(item.getExternalPrice());
      productPriceEvidenceDTO.setType(ProductExternalLinkType.getMpmCodeByCode(item.getLinkType()));
      productPriceEvidenceDTO.setFileList(new ArrayList<>());
      List<ExternalFile> externalFiles = item.makeExternalFiles();
      externalFiles.forEach(externalFile -> {
        ProductPriceEvidenceDTO.ProductPriceEvidenceFileDTO productPriceEvidenceFileDTO = new ProductPriceEvidenceDTO.ProductPriceEvidenceFileDTO();
        productPriceEvidenceFileDTO.setName(externalFile.getName());
        productPriceEvidenceFileDTO.setUrl(externalFile.getUrl());
        productPriceEvidenceFileDTO.setBaseUrl(externalFile.getBaseUrl());
        productPriceEvidenceDTO.getFileList().add(productPriceEvidenceFileDTO);
      });
      return productPriceEvidenceDTO;
    }).collect(Collectors.toList());
  }
}
