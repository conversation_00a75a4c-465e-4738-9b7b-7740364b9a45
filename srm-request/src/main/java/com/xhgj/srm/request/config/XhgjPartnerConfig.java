package com.xhgj.srm.request.config;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2022/8/8 14:45
 */
@Configuration
@ConfigurationProperties(prefix = "third.xhgj.partner")
@Data
@EqualsAndHashCode(callSuper = true)
public class XhgjPartnerConfig extends AbstractXhgjConfig {}
