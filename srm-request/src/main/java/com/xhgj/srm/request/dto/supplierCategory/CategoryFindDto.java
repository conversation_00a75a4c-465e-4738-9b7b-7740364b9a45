package com.xhgj.srm.request.dto.supplierCategory;/**
 * @since 2024/12/17 9:58
 */

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2024/12/17 09:58:09
 *@description
 */
@Data
public class CategoryFindDto {
  /**
   * 编码
   */
  private String code;

  /**
   * 名称
   */
  private String name;

  /**
   * 名称
   */
  private List<String> names;

  /**
   * 路径
   */
  private List<String> path;

  public String makeNamesString() {
    if (CollUtil.isEmpty(names)) {
      return "";
    }
    return String.join("/", names);
  }

  public String makePathString() {
    if (CollUtil.isEmpty(path)) {
      return "";
    }
    return String.join("/", path);
  }
}
