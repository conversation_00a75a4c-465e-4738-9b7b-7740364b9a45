package com.xhgj.srm.request.dto.hZero.process;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.List;

/**
 * @Author: fanghuanxu
 * @Date: 2025/5/15 16:32
 * @Description: 采购订单退货流程表单
 */
@Data
public class SupplierOrderReturnProcessForm {

  /**
   * 退库单号
   */
  @JSONField(name = "singleNumber")
  private String formCode;

  /**
   * 采购订单号
   */
  @JSONField(name = "orderNumber")
  private String code;

  /**
   * 供应商名称
   */
  @JSONField(name = "nameOfSupplier")
  private String supplierName;

  /**
   * 采购员
   */
  @JSONField(name = "buyers")
  private String purchaseMan;

  /**
   * 采购部门
   */
  @JSONField(name = "procurementDepartment")
  private String purchaseDept;

  /**
   * 退库时间
   */
  @JSONField(name = "retiredTime")
  private String postingDate;

  /**
   * 退库原因
   */
  @JSONField(name = "reasonsForWithdrawal")
  private String returnReason;

  /**
   * 退库仓库
   */
  @JSONField(name = "retirementWarehouse")
  private String warehouseName;

  /**
   * 物流公司
   */
  @JSONField(name = "logisticsCompany")
  private String logisticsCompany;

  /**
   * 快递单号
   */
  @JSONField(name = "expressSingleNumber")
  private String trackNum;

  /**
   * 收件人
   */
  @JSONField(name = "recipient")
  private String consignee;

  /**
   * 收件地址
   */
  @JSONField(name = "receiptAddress")
  private String receiveAddress;

  /**
   * 是否需要开红票
   */
  @JSONField(name = "redTicket")
  private String needRedTicket;

  /**
   * 采购组织名称
   */
  @JSONField(name = "nameOfProcurement")
  private String groupName;

  /**
   * 采购组织编码
   */
  @JSONField(name = "organizationCode")
  private String groupCode;

  @JSONField(name = "XHGJ_SRM0601")
  private List<ReturnDetail> details;

  @Data
  public static class ReturnDetail {

    /**
     * 物料编码
     */
    @JSONField(name = "materialCode")
    private String productCode;

    /**
     * 品牌
     */
    @JSONField(name = "brand")
    private String brand;

    /**
     * 物料名称
     */
    @JSONField(name = "materialName")
    private String productName;

    /**
     * 规格
     */
    @JSONField(name = "specifications")
    private String specification;

    /**
     * 型号
     */
    @JSONField(name = "model")
    private String model;

    /**
     * 单位
     */
    @JSONField(name = "unit")
    private String unit;

    /**
     * 批号
     */
    @JSONField(name = "batchNumber")
    private String batchNo;

    /**
     * 总帐科目
     */
    @JSONField(name = "ledgerSubjects")
    private String ledgerSubject;

    /**
     * 入库单序号
     */
    @JSONField(name = "orderNumber")
    private String inWareHouseName;

    /**
     * 退库数量
     */
    @JSONField(name = "numberOfWithdrawals")
    private String stockOutputQty;

    /**
     * 对应入库单入库数量
     */
    @JSONField(name = "singleDeposits")
    private String stockInputQty;

    /**
     * 退库单价
     */
    @JSONField(name = "refundUnitPrice")
    private String returnPrice;

    /**
     * 退库金额
     */
    @JSONField(name = "amountOfWithdrawal")
    private String returnAmount;
  }
}
