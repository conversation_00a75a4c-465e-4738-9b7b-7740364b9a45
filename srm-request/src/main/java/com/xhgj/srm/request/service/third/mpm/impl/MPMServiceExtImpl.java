package com.xhgj.srm.request.service.third.mpm.impl;/**
 * @since 2025/2/18 15:46
 */

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component
public class MPMServiceExtImpl {
  @Resource
  MPMService mpmService;



  /**
   * 获取产品单位编码
   * @param productCode
   * @return
   */
  public String getProductUnitCode(String productCode) {
    if (StrUtil.isBlank(productCode)) {
      return null;
    }
    TypeReference<JSONObject> typeRef = new TypeReference<JSONObject>() {};
    JSONObject jsonObject = mpmService.getProductDetail(productCode, typeRef);
    return jsonObject.getOrDefault("basicUnit", "").toString();
  }
}
