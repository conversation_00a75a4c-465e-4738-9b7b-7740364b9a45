package com.xhgj.srm.request.service.third.data.warehouse;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.xhgj.srm.request.config.ScConfig;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Created by Geng Shy on 2023/8/2
 */
@Slf4j
@Component
public class DataWarehouseRequest {

  private final String dataWarehouseBaseUrl;

  public DataWarehouseRequest(ScConfig scConfig) {
    this.dataWarehouseBaseUrl = scConfig.getServiceUrl();
  }

  /**
   * 获取全部银行网点
   */
  private static final String BASE_URL = "/bank-branches";
  private static final String QUERY_ALL_BANK_BRANCH = BASE_URL + "/page";

  private static final String QUERY_INTERBANK_NUM = BASE_URL + "/interbank-num/";

  /**
   * 通过银行网点名称分页查询银行网点信息
   *
   * @param branchName 网点名称
   */
  public String pageQueryBankBranches(String branchName, String interBankNum, String branchNameOrInterBankNum, int pageNo, int pageSize) {
    if (branchName == null) {
      branchName = "";
    }
    if (interBankNum == null) {
      interBankNum = "";
    }
    if (branchNameOrInterBankNum == null) {
      branchNameOrInterBankNum = "";
    }
    String urlString = buildFullUrl(
        QUERY_ALL_BANK_BRANCH + "/" + pageNo + "/" + pageSize + "?branchName=" + branchName
            + "&interBankNum=" + interBankNum + "&branchNameOrInterBankNum=" + branchNameOrInterBankNum);
    String result;
    try {
      result = HttpUtil.get(urlString);
    } catch (Exception e) {
      log.error("调用【数仓银行网点信息查询】出现未知异常");
      log.error(e.getMessage());
      throw new RuntimeException();
    }
    log.info("调用【数仓银行网点信息查询】出参：{}", result);
    return result;
  }

  /**
   * 通过网点名称精确查找联行号
   * @param branchName
   * @return
   */
  public String queryInterBankNum(String branchName) {
    String urlString = URLUtil.encode(buildFullUrl(QUERY_INTERBANK_NUM + branchName));
    String result;
    try {
      result = HttpUtil.get(urlString);
    } catch (Exception e) {
      log.error("调用【数仓银行网点联行号查询】出现未知异常");
      log.error(e.getMessage());
      throw new RuntimeException();
    }
    log.info("调用【数仓银行网点信息查询】出参：{}", result);
    return result;
  }


  /**
   * 构建完整接口路径
   *
   * @param url 接口路径（半路径）
   */
  String buildFullUrl(String url) {
    String serviceUrl = dataWarehouseBaseUrl;
    if (StrUtil.isBlank(serviceUrl)) {
      throw new CheckException("数仓服务地址未配置，请联系管理员处理！");
    }
    return StrUtil.removeSuffix(serviceUrl, "/") + StrUtil.addPrefixIfNot(url, "/");
  }
}
