package com.xhgj.srm.request.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

/**  ERP采购订单物流信息链接修改对象 <AUTHOR> @ClassName SrmUpdateLogisitcaDTO */
@Data
@Builder
public class UpdateLogisitcaDTO {

  /** 数据中心id */
  @JSONField(name = "DbId")
  private String dbId;
  /** 登录名 */
  @JSONField(name = "UserName")
  private String userName;
  /** 密码 */
  @JSONField(name = "PassWord")
  private String passWord;
  /** 采购订单单据id */
  @JSONField(name = "OrderId")
  private String orderId;
  /** 物流链接 */
  @JSONField(name = "LogisticsNo")
  private String logisticsNo;
}
