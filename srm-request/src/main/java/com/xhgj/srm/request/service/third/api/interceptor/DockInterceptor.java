package com.xhgj.srm.request.service.third.api.interceptor;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.forest.interceptor.base.BaseInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DockInterceptor extends BaseInterceptor {

  /**
   * @see SrmConfig#getDockUrl()
   */
  @Value("${srm.dock-url:default}")
  private String dockUrl;


  @Override
  protected String getSystemName() {
    return "Dock";
  }

  @Override
  protected String getBaseUrl() {
    if (StrUtil.isBlank(dockUrl) || "default".equals(dockUrl)) {
      throw new CheckException("Dock" + "地址未配置，请联系管理员处理！");
    }
    return dockUrl;
  }
}
