package com.xhgj.srm.request.dto.mpm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MpmBrandDTO {

  @ApiModelProperty(value = "品牌mpmId")
  private String brandMpmId;

  @ApiModelProperty(value = "品牌名称(中文)")
  private String brandNameCn;

  @ApiModelProperty(value = "品牌名称(英文)")
  private String brandNameEn;

  @ApiModelProperty(value = "品牌logo地址")
  private String brandLogoUrl;

  @ApiModelProperty(value = "描述信息")
  private String desc;

  @ApiModelProperty(value = "经营形式(1_品牌方 2_集货商)")
  private String manageType;


}
