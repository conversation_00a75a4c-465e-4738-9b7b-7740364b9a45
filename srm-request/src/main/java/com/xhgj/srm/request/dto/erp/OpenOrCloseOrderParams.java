package com.xhgj.srm.request.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/12/14 16:27
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class OpenOrCloseOrderParams extends BaseSupplierOrderERP{

  /** 操作类型 {@link com.xhgj.srm.request.enums.OpenOrCloseOrderType} */
  @JSONField(name = "type")
  private Integer type;

  /** Id 集合 */
  @JSONField(name = "Ids")
  private List<OpenOrCloseOrderIdsDTO> ids;

  @NoArgsConstructor
  @AllArgsConstructor
  @Data
  @Builder
  public static class OpenOrCloseOrderIdsDTO {

    /** 单据 id */
    @J<PERSON>NField(name = "Id")
    private String id;

    /** 明细 id 集合 */
    @JSONField(name = "entryIds")
    private List<String> entryIds;

    /**
     * 构建 OpenOrCloseOrderIdsDTO
     * @param id erp 单据 id
     * @param entryIds erp 明细行 id
     */
    public static OpenOrCloseOrderIdsDTO build(String id, List<String> entryIds) {
      return OpenOrCloseOrderIdsDTO.builder().id(id).entryIds(entryIds).build();
    }
  }
}
