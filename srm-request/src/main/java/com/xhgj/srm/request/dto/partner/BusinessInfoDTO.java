package com.xhgj.srm.request.dto.partner;

import com.xhgj.srm.jpa.entity.BaseSupplierInGroup.PartnershipTypeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/22 19:35
 */
@Data
public class BusinessInfoDTO {

  @ApiModelProperty("企业名称")
  private String companyName;

  @ApiModelProperty(value = "企业简称")
  private String abbreviation;

  @ApiModelProperty("统一社会信用代码")
  private String creditCode;

  @ApiModelProperty("区域")
  private String region;

  @ApiModelProperty("法人")
  private String legalPersonName;

  @ApiModelProperty("经营状态")
  private String regStatus;

  @ApiModelProperty("成立时间")
  private Long estiblishTime;

  @ApiModelProperty("经营开始时间")
  private Long fromTime;

  @ApiModelProperty("经营结束时间")
  private Long toTime;

  @ApiModelProperty("注册资本")
  private String regCapital;

  @ApiModelProperty("人员规模")
  private String staffNumRange;

  @ApiModelProperty("实缴资本")
  private String actualCapital;

  @ApiModelProperty("参保人数")
  private String socialStaffNum;

  @ApiModelProperty("纳税人识别号")
  private String taxNumber;

  @ApiModelProperty("曾用名")
  private String historyNames;

  @ApiModelProperty("纳税人资质")
  private String taxQualification;

  @ApiModelProperty("企业类型")
  private String companyOrgType;

  @ApiModelProperty("行业")
  private String industry;

  @ApiModelProperty("英文名")
  private String property3;

  @ApiModelProperty("登记机关")
  private String regInstitute;

  @ApiModelProperty("注册号")
  private String regNumber;

  @ApiModelProperty("组织机构代码")
  private String orgNumber;

  @ApiModelProperty("注册地址")
  private String regLocation;

  @ApiModelProperty("核准时间")
  private Long approvedTime;

  @ApiModelProperty("经营范围")
  private String businessScope;

  @ApiModelProperty("省份")
  private String province;

  @ApiModelProperty("城市")
  private String city;

  @ApiModelProperty("更新天眼查工商信息的时间")
  private Long updateTime;

  @ApiModelProperty("财务信息")
  private SupFinancialDTO financials = new SupFinancialDTO();


  @ApiModelProperty("供应商性质数组")
  private List<String> enterpriseNatures;

  @ApiModelProperty("营业执照")
  private SupplierFileDTO license;

  @ApiModelProperty("付款方式")
  private String payType;

  /**
   * 合作性质
   */
  private List<PartnershipTypeDTO> partnershipTypes;

  @ApiModelProperty("其他付款方式")
  private String payTypeOther;

  @ApiModelProperty(value = "网络备案信息")
  private List<PartnerIcpDTO> parterIcpList;

  @ApiModelProperty(value = "网络备案时间")
  private String websiteRegistrationSyncTime;

}
