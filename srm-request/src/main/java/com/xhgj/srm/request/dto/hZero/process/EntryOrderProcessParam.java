package com.xhgj.srm.request.dto.hZero.process;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.BooleanEnum;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationCompanyAttributeEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationCooperationTypeEnum;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: fanghuanxu
 * @Date: 2025/2/26 15:58
 * @Description: 入驻报备流程表单json报文参数
 */
@Data
public class EntryOrderProcessParam {

  /**
   * 合作类型
   */
  @JSONField(name = "jt03")
  private String typeOfCooperation;

  public String getTypeOfCooperation() {
    return Optional.ofNullable(EntryRegistrationCooperationTypeEnum.valueOfByKey(typeOfCooperation))
        .map(EntryRegistrationCooperationTypeEnum::getDescription).orElse(null);
  }

  /**
   * 合作单位名称
   */
  @JSONField(name = "jt05")
  private String partnerName;

  /**
   * 合作时间（开始）
   */
  @JSONField(name = "jt06")
  private Long cooperationStartTime;

  public String getCooperationStartTime() {
    return DateUtils.formatTimeStampToNormalDate(cooperationStartTime);
  }

  /**
   * 合作时间（结束）
   */
  @JSONField(name = "jt07")
  private Long cooperationEndTime;

  public String getCooperationEndTime() {
    return DateUtils.formatTimeStampToNormalDate(cooperationEndTime);
  }

  /**
   * 合作联系人
   */
  @JSONField(name = "jt08")
  private String cooperationContactName;

  /**
   * 联系电话
   */
  @JSONField(name = "jt09")
  private String cooperationContactPhone;

  /**
   * 职务
   */
  @JSONField(name = "jt10")
  private String position;

  /**
   * 合作品牌/合作品类
   */
  @JSONField(name = "jt13")
  private String cooperationBrand;

  /**
   * 合作区域
   */
  @JSONField(name = "jt14")
  private String cooperationRegion;

  /**
   * 合作比例
   */
  @JSONField(name = "jt15")
  private String discountRatio;

  /**
   * 保证金
   */
  @JSONField(name = "jt16")
  private BigDecimal deposit;

  public String getDeposit() {
    return deposit == null ? StrUtil.EMPTY : deposit.stripTrailingZeros().toPlainString() + "元";
  }

  /**
   * 账期
   */
  @JSONField(name = "jt17")
  private Integer accountPeriod;

  private Boolean backToBack;

  public String getAccountPeriod() {
    return BooleanUtil.isTrue(backToBack) ? "背靠背"
        : accountPeriod == null ? StrUtil.EMPTY : String.format("%s天", accountPeriod);
  }

  /**
   * 仓储
   */
  @JSONField(name = "jt19")
  private String storage;

  public String getStorage() {
    return Optional.ofNullable(BooleanEnum.fromKey(storage)).map(BooleanEnum::getDescription)
        .orElse(StrUtil.EMPTY);
  }

  /**
   * 仓库地址
   */
  @JSONField(name = "jt20")
  private String storageAddress;

  /**
   * 仓库面积
   */
  @JSONField(name = "jt21")
  private BigDecimal storageArea;

  public String getStorageArea() {
    return storageArea == null ? StrUtil.EMPTY
        : storageArea.stripTrailingZeros().toPlainString() + "㎡";
  }

  /**
   * 保底金额
   */
  @JSONField(name = "jt22")
  private BigDecimal guaranteedAmount;

  public String getGuaranteedAmount() {
    return guaranteedAmount == null ? StrUtil.EMPTY
        : guaranteedAmount.stripTrailingZeros().toPlainString() + "万元";
  }

  /**
   * 违约金
   */
  @JSONField(name = "jt23")
  private String penalty;

  public String getPenalty() {
    if (StrUtil.isNotBlank(penalty)) {
      String[] split = penalty.split(",");
      if (split.length != 2) {
        throw new CheckException("违约金格式错误");
      }
      // {}万元-实际订单金额*{}%的方式计算
      return StrUtil.format("{}万元-实际订单金额*{}%的方式计算", split[0], split[1]);
    }
    return StrUtil.EMPTY;
  }

  /**
   * 付款方式
   */
  @JSONField(name = "jt25")
  private String paymentType;

  /**
   * 付款方式后输入框 (月份、其他)
   */
  private String paymentTypeInput;

  public String getPaymentType() {
    PayTypeSAPEnums payTypeSAPEnums = PayTypeSAPEnums.fromKey(paymentType);
    String paymentTypeDesc = "";
    if (PayTypeSAPEnums.TRANSFER.equals(payTypeSAPEnums)) {
      paymentTypeDesc = payTypeSAPEnums.getName();
    } else if (PayTypeSAPEnums.OHTER.equals(payTypeSAPEnums)) {
      paymentTypeDesc = payTypeSAPEnums.getName() + "，" + paymentTypeInput;
    } else {
      paymentTypeDesc = payTypeSAPEnums.getName() + String.format("，%s个月", paymentTypeInput);
    }
    return paymentTypeDesc;
  }

  /**
   * 付款比例
   */
  @JSONField(name = "jt26")
  private String paymentRatio;

  public String getPaymentRatio() {
    return StrUtil.isBlank(paymentRatio) ? StrUtil.EMPTY
        : String.format("按货%s%%付款", paymentRatio);
  }

  /**
   * 其他备注
   */
  @JSONField(name = "jt27")
  private String otherRemarks;

  /**
   * 联系地址
   */
  @JSONField(name = "jt29")
  private String contactAddress;

  /**
   * 补充附件
   */
  @JSONField(name = "jt30")
  private String supplementaryAttachment1;

  @JSONField(name = "jt35")
  private String supplementaryAttachment2;

  @JSONField(name = "jt36")
  private String supplementaryAttachment3;

  @JSONField(name = "jt37")
  private String supplementaryAttachment4;

  @JSONField(name = "jt38")
  private String supplementaryAttachment5;

  /**
   * 准入说明
   */
  @JSONField(name = "jt31")
  private String notes;

  /**
   * 项目大类
   */
  @JSONField(name = "jt32")
  private String projectCategory;

  /**
   * 项目全称
   */
  @JSONField(name = "jt33")
  private String projectName;

  /**
   * 平台名称
   */
  @JSONField(name = "jt34")
  private String platformNames;

  /**
   * 一对一，为符合接口要求使用List
   */
  @JSONField(name = "XHGJ_JT0301")
  private List<EntryLandingMerchant> entryLandingMerchant;

  /**
   * 电商供应商信息
   */
  @Data
  @NoArgsConstructor
  public static class EntryLandingMerchant {

    /**
     * 企业名称
     */
    @JSONField(name = "jt01")
    private String enterpriseName;

    /**
     * 统一社会信用代码
     */
    @JSONField(name = "jt02")
    private String uscc;

    /**
     * 公司属性
     */
    @JSONField(name = "jt03")
    private String companyAttributeType;

    public String getCompanyAttributeType() {
      if (StrUtil.isNotBlank(companyAttributeType)) {
        List<String> companyAttributes =
            Stream.of(StrUtil.split(companyAttributeType, ",")).map(key -> {
              EntryRegistrationCompanyAttributeEnum companyAttributeEnum =
                  EntryRegistrationCompanyAttributeEnum.fromKey(key).orElse(null);
              if (companyAttributeEnum != null) {
                return companyAttributeEnum.getDescription();
              }
              return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
        return StrUtil.join(",", companyAttributes);
      }
      return null;
    }

    /**
     * 法定代表人
     */
    @JSONField(name = "jt04")
    private String corporate;

    /**
     * 行业
     */
    @JSONField(name = "jt05")
    private String industry;

    /**
     * 区域
     */
    @JSONField(name = "jt06")
    private String region;

    /**
     * 注册地址
     */
    @JSONField(name = "jt07")
    private String regAddress;

    /**
     * 账户名称
     */
    @JSONField(name = "jt08")
    private String accountName;

    /**
     * 电话
     */
    @JSONField(name = "jt09")
    private String phone;

    /**
     * 实际经营地址
     */
    @JSONField(name = "jt10")
    private String bankAddress;

    /**
     * 开户行
     */
    @JSONField(name = "jt11")
    private String bankName;

    /**
     * 银行账户
     */
    @JSONField(name = "jt12")
    private String bankAccountNumber;

    /**
     * 银行联行号
     */
    @JSONField(name = "jt13")
    private String bankCode;

    /**
     * 账户使用人
     */
    @JSONField(name = "jt14")
    private String accountUser;

    /**
     * 手机号
     */
    @JSONField(name = "jt15")
    private String accountUserPhone;

    /**
     * 落地商邮箱
     */
    @JSONField(name = "jt16")
    private String emailAddress;

    /**
     * 票种
     */
    @JSONField(name = "jt17")
    private String invoiceType;

    public String getInvoiceType() {
      return Constants.INVOICETYPE.get(invoiceType);
    }

    /**
     * 税率
     */
    @JSONField(name = "jt18")
    private BigDecimal taxRate;

    public String getTaxRate() {
      return taxRate == null ? StrUtil.EMPTY : taxRate.stripTrailingZeros().toPlainString() + "%";
    }

    /**
     * 营业执照
     */
    @JSONField(name = "jt19")
    private String businessLicenseAttachment;

    /**
     * 法定代表人身份证正反面
     */
    @JSONField(name = "jt20")
    private String corporateIdCardAttachment1;

    /**
     * 法定代表人身份证正反面
     */
    @JSONField(name = "jt25")
    private String corporateIdCardAttachment2;

    /**
     * 产品资质书
     */
    @JSONField(name = "jt21")
    private String productQualificationAttachment1;

    @JSONField(name = "jt26")
    private String productQualificationAttachment2;

    @JSONField(name = "jt27")
    private String productQualificationAttachment3;

    @JSONField(name = "jt28")
    private String productQualificationAttachment4;

    @JSONField(name = "jt29")
    private String productQualificationAttachment5;

    @JSONField(name = "jt30")
    private String productQualificationAttachment6;

    @JSONField(name = "jt31")
    private String productQualificationAttachment7;
  }
}
