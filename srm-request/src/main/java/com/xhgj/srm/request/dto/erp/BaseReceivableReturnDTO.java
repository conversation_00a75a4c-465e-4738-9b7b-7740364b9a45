package com.xhgj.srm.request.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/** <AUTHOR> @ClassName ReceivableQueryDTO */
@Data
public abstract class BaseReceivableReturnDTO {

  /** 项目编号 */
  @JSONField(name = "ProjectNo")
  private String projectNo;
  /** 项目名称 */
  @JSONField(name = "ProjectName")
  private String projectName;
  /** 客户编码 */
  @JSONField(name = "CustomerNo")
  private String customerNo;
  /** 客户名称 */
  @JSONField(name = "CustomerName")
  private String customerName;
  /** 销售员编码 */
  @JSONField(name = "SalesmanNo")
  private String salesmanNo;
  /** 销售员名称 */
  @JSONField(name = "SalesmanName")
  private String salesmanName;
  /** 价税合计/回款日期 */
  @JSONField(name = "Amount")
  private String amount;
}
