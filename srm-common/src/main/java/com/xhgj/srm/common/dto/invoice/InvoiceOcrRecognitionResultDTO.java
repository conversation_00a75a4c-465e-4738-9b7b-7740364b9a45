package com.xhgj.srm.common.dto.invoice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.BooleanEnum;
import com.xhgj.srm.common.enums.InvoiceTypeEnum;
import com.xhiot.boot.core.common.exception.CheckException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/9/7
 */
@Data
public class InvoiceOcrRecognitionResultDTO {

  /**
   * 返回信息
   */
  private String message;
  private Result result;
  private int cost_time;
  /**
   * 响应code
   */
  private int code;

  @Data
  public static class Product {

    private String value;
    private String key;
    private String description;
  }

  /**
   * 合合信息识别出来的发票字段的格式，
   * 属性key为识别出来的发票字段，属性value为字段的值。
   */
  @Data
  public static class Item {

    /**
     * 字段值
     */
    private String value;
    /**
     * 描述
     */
    private String description;
    /**
     * 在图片上或者文件上的位置
     */
    private List<Integer> position;
    /**
     * 识别到的字段名称
     */
    private String key;
    /**
     * 无意义字段，合合官方文档中也没提起这个字段。
     */
    private int confidence;
  }

  @Data
  public static class Result {

    private int image_angle;
    /**
     * 发票类型的描述
     */
    private String type_description;
    private int rotated_image_width;
    private int rotated_image_height;
    private String kind;
    /**
     * 商品列表，目前业务中不涉及。
     */
    private List<List<Product>> product_list;
    /**
     * 识别出来的发票信息字段的集合
     */
    private List<Item> item_list;
    private String kind_description;
    /**
     * 发票类型
     */
    private String type;

    /**
     * 购方名称
     */
    public String purchaserName;

    /**
     * 销方名称
     */
    public String sellerName;

    /**
     * 获取购买方名称
     */
    public String getPurchaserName() {
      Map<String, String> itemMap = CollUtil.emptyIfNull(item_list).stream()
          .collect(Collectors.toMap(Item::getKey, Item::getValue));
      return itemMap.get("vat_invoice_payer_name");
    }

    /**
     * 获取销售方名称
     */
    public String getSellerName() {
      Map<String, String> itemMap = CollUtil.emptyIfNull(item_list).stream()
          .collect(Collectors.toMap(Item::getKey, Item::getValue));
      return itemMap.get("vat_invoice_seller_name");
    }

    /**
     * 获取购买方名称 - 括号进行处理
     */
    public String getPurchaserNameReplace() {
      String tempName = getPurchaserName();
      if (StrUtil.isBlank(tempName)) {
        return tempName;
      }
      tempName = StrUtil.replace(tempName, "(", "（");
      tempName = StrUtil.replace(tempName, ")", "）");
      return tempName;
    }

    /**
     * 获取销售方名称 - 括号进行处理
     */
    public String getSellerNameReplace() {
      String tempName = getSellerName();
      if (StrUtil.isBlank(tempName)) {
        return tempName;
      }
      tempName = StrUtil.replace(tempName, "(", "（");
      tempName = StrUtil.replace(tempName, ")", "）");
      return tempName;
    }
  }

  public String getInvoiceNumber() {
    checkResult();
    List<Item> itemList = this.getResult().getItem_list();
    Map<String, String> itemMap = CollUtil.emptyIfNull(itemList).stream()
        .collect(Collectors.toMap(Item::getKey, Item::getValue));
    return itemMap.get("vat_invoice_haoma");
  }

  private void checkResult() {
    if (this.getResult() == null || this.getResult().getItem_list() == null) {
      throw new CheckException("发票识别结果为空");
    }
  }
  public InvoiceIdentifyResultDTO buildVoObject(String fileBaseUrl, String fileUrl) {
    checkResult();
    List<Item> itemList = this.getResult().getItem_list();
    Map<String, String> itemMap = CollUtil.emptyIfNull(itemList).stream()
        .collect(Collectors.toMap(Item::getKey, Item::getValue));
    InvoiceIdentifyResultDTO vo = new InvoiceIdentifyResultDTO();
    vo.setInvoiceCode(itemMap.get("vat_invoice_daima"));
    vo.setInvoiceNum(itemMap.get("vat_invoice_haoma"));
    String totalStr = itemMap.get("vat_invoice_total_print");
    String totalTaxStr = itemMap.get("vat_invoice_tax_total");
    // 自己新加的字段
    String totalAmountIncludingTaxStr = itemMap.get("customize_total");
    BigDecimal totalPrice = BigDecimal.ZERO;
    // 先判断是否为数字类型
    if (!NumberUtil.isNumber(totalStr)) {
//      throw new CheckException("打印合计信息未能识别");
      // 从金额明细中累加
      String priceListStr = itemMap.get("vat_invoice_price_list");
      if (StrUtil.isBlank(priceListStr)) {
        throw new CheckException("金额明细未能识别");
      }
      // 根据换行符分割
      ArrayList<String> priceList = CollUtil.toList(priceListStr.split("\n"));
      // 判断每个price如果是非数字类型，则抛出异常
      for (String price : priceList) {
        if (!NumberUtil.isNumber(price)) {
          throw new CheckException("打印合计信息未能识别");
        }
        totalPrice = NumberUtil.add(totalPrice, new BigDecimal(price));
      }
    }else{
      totalPrice = new BigDecimal(totalStr);
    }
    if (!NumberUtil.isNumber(totalTaxStr)) {
      // 和产品确认过，如果价税合计有问题，那么默认为0
      totalTaxStr = "0";
       // throw new CheckException("税额合计信息未能识别");
    }
    BigDecimal totalTaxPrice = new BigDecimal(totalTaxStr);
    vo.setTotalAmount(totalPrice.toPlainString());
    vo.setTotalTaxAmount(totalTaxPrice.toPlainString());
    BigDecimal invoiceTotal =
        NumberUtil.add(totalPrice, totalTaxPrice);
    if (StrUtil.isNotBlank(totalAmountIncludingTaxStr)) {
      invoiceTotal = new BigDecimal(totalAmountIncludingTaxStr);
    }
    itemMap.put("vat_invoice_total_cover_tax_digits", invoiceTotal.toString());
    //价税合计取合计金额和合计税额之和
    vo.setTotalAmountIncludingTax(itemMap.get("vat_invoice_total_cover_tax_digits"));
    Date date = Convert.toDate(itemMap.get("vat_invoice_issue_date"));
    if (date != null) {
      vo.setInvoiceTime(date.getTime());
    }
    vo.setBaseUrl(fileBaseUrl);
    vo.setUrl(fileUrl);
    vo.setRemarks(itemMap.get("vat_invoice_total_note"));
    InvoiceTypeEnum invoiceTypeEnum = InvoiceTypeEnum.fromIdentifier(this.getResult().getType());
    if (invoiceTypeEnum == null) {
      throw new CheckException(
          "不支持的发票类型：" + this.getResult().getType() + "，发票代码：" + vo.getInvoiceCode());
    }
    vo.setInvoiceType(invoiceTypeEnum);
    //校验码
    vo.setCheckCode(itemMap.get("vat_invoice_correct_code"));
    String issuingInvoicesOnBehalfOfOthers = itemMap.get("vat_invoice_dai_kai_flag");
    BooleanEnum booleanEnum = BooleanEnum.fromDesc(issuingInvoicesOnBehalfOfOthers);
    if (booleanEnum!=null&&Objects.equals(booleanEnum.getKey(), Constants.YES)) {
      vo.setSellerName(extractCompanyName(vo.getRemarks()));
    }else {
      vo.setSellerName(itemMap.get("vat_invoice_seller_name"));
    }
    vo.setPayerName(itemMap.get("vat_invoice_payer_name"));
    return vo;
  }


  private static String extractCompanyName(String fullString) {
    // 代开发票备注实例：“代开企业税号:92120111L62381913Q 代开企业名称：天津市西青区华鑫荣机加工厂”
    // 查找"代开企业名称"及其后的冒号的位置
    String needFindStr = "代开企业名称";
    int startIndex = fullString.indexOf(needFindStr);
    if (startIndex == -1) {
      throw new CheckException("代开发票发票备注格式不在预期内");
    }
    String substringAfterColon = fullString.substring(startIndex + needFindStr.length());
    // 如果截取到的字符串不为空且长度大于0，则删除第一个字符（即冒号）
    if (StrUtil.isNotBlank(substringAfterColon)) {
      return substringAfterColon.substring(1).trim();
    }
    // 如果没有找到，返回空字符串或抛出异常
    throw new CheckException("代开发票提取销方信息异常");
  }

  /**
   * 获取购买方纳税人识别号
   */
  public String getPayerTaxIdentificationNumber() {
    checkResult();
    List<Item> itemList = this.getResult().getItem_list();
    Map<String, String> itemMap = CollUtil.emptyIfNull(itemList).stream()
        .collect(Collectors.toMap(Item::getKey, Item::getValue));
    return itemMap.get("vat_invoice_rate_payer_id");
  }
}
