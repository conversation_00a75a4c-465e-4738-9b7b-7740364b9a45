package com.xhgj.srm.common.enums.supplierorder;/**
 * @since 2025/5/20 11:02
 */

/**
 *<AUTHOR>
 *@date 2025/5/20 11:02:02
 *@description
 */
public enum SupplierOrderFormReviewStatus {
  /**
   * 飞搭审核中
   */
  FEI_DA_AUDIT((byte) 11, "审核中"),
  /**
   * 飞搭审核驳回
   */
  FEI_DA_REJECT((byte) -1, "审核拒绝"),
  /**
   * NORMAL 状态 (飞搭审核通过/正常状态)
   */
  NORMAL((byte) 22, "审核通过");

  private final Byte code;

  private final String desc;

  SupplierOrderFormReviewStatus(Byte code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public Byte getCode() {
    return code;
  }

  public String getDesc() {
    return desc;
  }
  /**
   * 根据code获取描述
   */
  public static String getDescByCode(Byte code) {
    for (SupplierOrderFormReviewStatus status : SupplierOrderFormReviewStatus.values()) {
      if (status.getCode().equals(code)) {
        return status.getDesc();
      }
    }
    return null;
  }
}
