package com.xhgj.srm.common.enums.transferOrder;/**
 * @since 2025/2/25 11:14
 */

/**
 *<AUTHOR>
 *@date 2025/2/25 11:14:35
 *@description
 */
public enum TransferOrderType {
  /**
   * 标准
   */
  STANDARD((byte) 1, "标准"),
  /**
   * 寄售
   */
  CONSIGNMENT((byte) 2, "寄售")
  ;

  private Byte code;

  private String name;

  TransferOrderType(Byte code, String name) {
    this.code = code;
    this.name = name;
  }

  public static String getNameByCode(Byte code) {
    for (TransferOrderType value : TransferOrderType.values()) {
      if (value.getCode().equals(code)) {
        return value.getName();
      }
    }
    return null;
  }

  public Byte getCode() {
    return code;
  }

  public String getName() {
    return name;
  }
}
