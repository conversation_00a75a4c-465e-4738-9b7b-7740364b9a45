package com.xhgj.srm.common.event.impl;

import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.event.SrmEvent;
import com.xhgj.srm.common.event.SrmEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 事件 发布器
 */
@Component
@Slf4j
public class SrmSpringEventPublisher implements SrmEventPublisher {
    final
    ApplicationEventPublisher applicationEventPublisher;

    public SrmSpringEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

  @Override
  public void publish(SrmEvent event) {
    log.info("publish event:" + JSON.toJSONString(event));
    applicationEventPublisher.publishEvent(event);
  }

  @Override
  public void asyncPublish(SrmEvent event) {
    log.info("publish sync event:" + JSON.toJSONString(event));
    applicationEventPublisher.publishEvent(event);
  }
}
