package com.xhgj.srm.common.vo.transferOrder;/**
 * @since 2025/2/25 14:03
 */

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.transferOrder.TransferOrderStatus;
import com.xhgj.srm.common.enums.transferOrder.TransferOrderType;
import lombok.Data;
import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/2/25 14:03:37
 *@description 调拨单列表VO
 */
@Data
public class TransferOrderListVO {
  /**
   * 调拨单id
   */
  private String id;

  /**
   * 调拨单号
   */
  private String code;

  /**
   * 单据类型
   */
  private Byte type;

  /**
   * 单据类型value
   */
  private String typeValue;

  /**
   * 调拨状态
   */
  private Byte status;

  /**
   * 调拨状态value
   */
  private String statusValue;

  /**
   * 物料编码
   */
  private String productCode;

  /**
   * 品牌
   */
  private String brand;

  /**
   * 物料名称
   */
  private String productName;

  /**
   * 规格型号
   */
  private String model;

  /**
   * 单位
   */
  private String unit;

  /**
   * 单位编码
   */
  private String unitCode;

  /**
   * 调拨数量
   */
  private BigDecimal num;

  /**
   * 调出仓库
   */
  private String warehouseOut;

  /**
   * 调出仓库名称
   */
  private String warehouseOutName;

  /**
   * 调入仓库
   */
  private String warehouseIn;

  /**
   * 调入仓库名称
   */
  private String warehouseInName;

  /**
   * 批次
   */
  private String batchNo;

  /**
   * 创建人 code
   */
  private String createManCode;

  /**
   * 创建人 name
   */
  private String createManName;

  /**
   * 创建人mix
   */
  private String createManMix;

  /**
   * 创建时间
   */
  private Long createTime;

  /**
   * 审核人
   */
  private String reviewer;

  /**
   * 审核时间
   */
  private Long reviewTime;

  /**
   * 仓库执行员
   */
  private String warehouseOperator;

  /**
   * 仓库执行时间
   */
  private Long warehouseTime;

  /**
   * sap物料凭证号
   */
  private String productVoucher;

  /**
   * sap物料凭证号年份
   */
  private String productVoucherYear;

  /**
   * 供应商名称
   */
  private String supplierName;

  /**
   * 单据类型value
   * @return
   */
  public String getTypeValue() {
    return TransferOrderType.getNameByCode(this.type);
  }

  /**
   * 调拨状态value
   * @return
   */
  public String getStatusValue() {
    return TransferOrderStatus.getNameByCode(this.status);
  }

  /**
   * 创建人mix
   * @return
   */
  public String getCreateManMix() {
    if (StrUtil.isNotBlank(createManCode)
        && createManCode.length() > 4
        && StrUtil.isNotBlank(createManName)
    ) {
      return createManCode.substring(createManCode.length() - 4) + createManName;
    }
    return null;
  }
}
