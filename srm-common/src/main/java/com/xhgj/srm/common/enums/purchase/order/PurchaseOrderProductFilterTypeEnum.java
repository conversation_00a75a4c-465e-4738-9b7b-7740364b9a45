package com.xhgj.srm.common.enums.purchase.order;

import com.xhiot.boot.core.common.util.dict.BootDictEnum;

/**
 * 采购订单筛选物料类型
 */
public enum PurchaseOrderProductFilterTypeEnum implements BootDictEnum<String, String> {

  /**
   * 筛选类型: 1.供应商名称，2.是否预付款，3.是否上传合同，
   * 4.业务员，5.跟单员，6.采购员，7.物料编码，8.品牌，9.规格型号，10.仓库
   */
  SUPPLIER_NAME("1", "供应商名称", "so.c_supplier_name"),
  PRE_PAY("2", "是否预付款",
      "CAST( EXISTS (SELECT 1 FROM `t_payment_apply_detail` pad "
          + " JOIN `t_payment_apply_record` record ON pad.payment_apply_record_id = record.id "
          + " WHERE pad.`c_supplier_order_no` = so.c_code "
          + " and record.c_state = '1' and record.c_apply_type = '1') "
          + " AS CHAR)"),
  UPLOAD_CONTRACT("3", "是否上传合同",
      "CAST(( SELECT COUNT( * ) FROM t_file WHERE c_relationId = so.id AND c_relationType = '101' ) AS CHAR)"),
  SALES_MAN("4", "业务员", "sop.c_salesman"),
  FOLLOWUP_PERSON_NAME("5", "跟单员", "sop.c_follow_up_person_name"),
  PURCHASE_MAN("6", "采购员", "so.c_purchase_man"), PRODUCT_CODE("7", "物料编码", "sop.c_code"),
  BRAND("8", "品牌", "sop.c_brand"),
  MANU_CODE("9", "规格型号", "sop.c_manu_code"),
  WARE_HOUSE("10", "仓库", "sod.c_warehouse_name"),
  SCP("11", "是否走scp", "CAST(so.c_scp AS CHAR)"),
  PURCHASE_DEPARTMENT("12", "采购部门", "so.c_purchase_dept"),
  MAKE_MAN_NAME("13", "制单员", "sop.c_make_man_name"),
  LOSS("14", "是否亏本", "so.c_loss"),
  IS_WORRY_ORDER("15", "是否急单", "pafo.c_is_worry_order"),
  // 订单状态
  ORDER_STATUS("16", "订单状态", "so.c_order_state"),
  // 订单类型
  ORDER_TYPE("17", "订单类型", "so.c_order_type"),
  // 供应商开票
  SUPPLIER_INVOICE("18", "供应商开票", "so.c_supplier_open_invoice_state"),
  // 采购申请类型
  PURCHASE_APPLY_TYPE("19", "采购申请类型", "pafo.c_apply_for_type"),
  // 规格
  SPECIFICATION("20", "规格", "sop.c_specification"),
  // 型号
  MODEL("21", "型号", "sop.c_model"),
  ;


  private final String type;
  private final String desc;
  private final String value;

  PurchaseOrderProductFilterTypeEnum(String type, String desc, String value) {
    this.type = type;
    this.desc = desc;
    this.value = value;
  }

  public static PurchaseOrderProductFilterTypeEnum getByDesc(String desc) {
    for (PurchaseOrderProductFilterTypeEnum e : PurchaseOrderProductFilterTypeEnum.values()) {
      if (e.desc.equals(desc)) {
        return e;
      }
    }
    return null;
  }

  public static PurchaseOrderProductFilterTypeEnum getByType(String type) {
    for (PurchaseOrderProductFilterTypeEnum e : PurchaseOrderProductFilterTypeEnum.values()) {
      if (e.type.equals(type)) {
        return e;
      }
    }
    return null;
  }

  @Override
  public String getKey() {
    return type;
  }

  @Override
  public String getValue() {
    return value;
  }

  public String getDesc() {
    return desc;
  }
}
