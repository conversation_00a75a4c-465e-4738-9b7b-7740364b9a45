package com.xhgj.srm.common.enums.purchase.order;


import com.xhiot.boot.core.common.util.dict.BootDictEnum;

/**
 * 采购申请筛选类型
 */
public enum PurchaseApplyFilterTypeEnum implements BootDictEnum<String, String> {

  /** 筛选类型: 1.业务员，2.跟单员，3.采购员，4.物料编码，5.品牌，6.规格类型 */

  SALESMAN("1", "业务员", "c_salesman"),
  MERCHANDISER("2", "跟单员", "c_follow_man_name"),
  PURCHASE_MAN("3", "采购员", "c_purchase_man"),
  PRODUCT_CODE("4", "物料编码", "c_product_code"),
  BRAND("5", "品牌", "c_brand"),
  MODEL("6", "规格类型", "c_model"),
  URGENT_ORDER("7", "是否急单", "c_is_worry_order"),
  STRAIGHT_HAIR("8", "是否直发", "c_direct_shipment"),
  PURCHASE_APPLY_GOODS("9", "采购申请", "c_apply_for_type"),
  PURCHASE_DEPT_TYPE("10", "采购部门", "purchase_department");


  private final String type;
  private final String desc;
  private final String value;
  PurchaseApplyFilterTypeEnum(String type, String desc, String value) {
    this.type = type;
    this.desc = desc;
    this.value = value;
  }
  public static PurchaseApplyFilterTypeEnum getByDesc(String desc) {
    for (PurchaseApplyFilterTypeEnum e : PurchaseApplyFilterTypeEnum.values()) {
      if (e.desc.equals(desc)) {
        return e;
      }
    }
    return null;
  }

  public static PurchaseApplyFilterTypeEnum getByType(String type) {
    for (PurchaseApplyFilterTypeEnum e : PurchaseApplyFilterTypeEnum.values()) {
      if (e.type.equals(type)) {
        return e;
      }
    }
    return null;
  }
  @Override
  public String getKey() {
    return type;
  }

  @Override
  public String getValue() {
    return value;
  }
  public String getDesc() {
    return desc;
  }
}
