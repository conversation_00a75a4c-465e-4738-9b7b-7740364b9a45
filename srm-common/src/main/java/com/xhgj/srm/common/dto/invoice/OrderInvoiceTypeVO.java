package com.xhgj.srm.common.dto.invoice;

import com.xhgj.srm.common.enums.OrderInvoiceTypeEnum;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class OrderInvoiceTypeVO {

  @ApiModelProperty("是否后台生成标识")
  private Boolean manageFlag;
  @ApiModelProperty("发票单类型")
  private OrderInvoiceTypeEnum orderInvoiceRelationType;
  @ApiModelProperty("采购订单类型")
  private PurchaseOrderTypeEnum orderType;
  @ApiModelProperty("旧订单标识")
  private String oldOrderFlag;

  public OrderInvoiceTypeVO(Boolean manageFlag, OrderInvoiceTypeEnum orderInvoiceRelationType,
      PurchaseOrderTypeEnum orderType, String oldOrderFlag) {
    this.manageFlag = manageFlag;
    this.orderInvoiceRelationType = orderInvoiceRelationType;
    this.orderType = orderType;
    this.oldOrderFlag = oldOrderFlag;
  }
}