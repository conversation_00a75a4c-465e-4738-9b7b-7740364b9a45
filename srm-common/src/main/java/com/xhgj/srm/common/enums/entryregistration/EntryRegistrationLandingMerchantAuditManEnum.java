package com.xhgj.srm.common.enums.entryregistration;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.Getter;

@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum EntryRegistrationLandingMerchantAuditManEnum {
  FENG_JIA_LE("冯家乐"), HOU_XIA_LIN("侯夏琳"), QU_YI("屈艺");



  private final String name;

  EntryRegistrationLandingMerchantAuditManEnum(String name) {
    this.name = name;
  }

}
