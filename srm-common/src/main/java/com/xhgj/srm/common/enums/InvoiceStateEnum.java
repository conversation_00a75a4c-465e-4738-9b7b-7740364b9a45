package com.xhgj.srm.common.enums;

public enum InvoiceStateEnum {
  /** 发票状态 1 审核中 2 暂存 3 通过 4 驳回 */

  UNDER_REVIEW("1", "审核中"),
  TEMPORARY ("2", "暂存"),
  PASS("3", "通过"),
  REJECT("4", "驳回"),
  REVERSAL("7", "已冲销");

  private final String code;
  private final String name;

  InvoiceStateEnum(String code, String name) {
    this.code = code;
    this.name = name;
  }

  public String getCode() {
    return code;
  }

  public String getName() {
    return name;
  }

  public static InvoiceStateEnum fromCode(String code) {
    for (InvoiceStateEnum type : values()) {
      if (type.getCode().equals(code)) {
        return type;
      }
    }
    return null;
  }

  public static InvoiceStateEnum fromName(String name) {
    for (InvoiceStateEnum type : values()) {
      if (type.getName().equals(name)) {
        return type;
      }
    }
    return null;
  }
}
