package com.xhgj.srm.common.enums;

public enum PaymentAuditStateEnum {
  /**
   * 审核中
   */
  PROCESSING("1", "审核中"),
  /**
   * 驳回
   */
  REJECTED("3", "驳回"),
  /**
   * 放弃
   */
  ABANDONED("4", "放弃"),
  /**
   * 通过
   */
  APPROVED("2", "通过");

  private String key;
  private String name;

  PaymentAuditStateEnum(String key, String name) {
    this.key = key;
    this.name = name;
  }

  public String getKey() {
    return key;
  }

  public String getName() {
    return name;
  }

  public static PaymentAuditStateEnum fromKey(String key) {
    for (PaymentAuditStateEnum status : values()) {
      if (status.getKey().equals(key)) {
        return status;
      }
    }
    return null;
  }

  public static PaymentAuditStateEnum fromName(String name) {
    for (PaymentAuditStateEnum status : values()) {
      if (status.getName().equals(name)) {
        return status;
      }
    }
    return null;
  }

}
