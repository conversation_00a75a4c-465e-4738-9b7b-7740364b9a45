package com.xhgj.srm.common.enums.contract;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Objects;
import lombok.Getter;

/**
 * 落地商v3版本合同ocr识别提取关键字
 * 每个枚举常量后面的行注释为合合信息识别提取到的信息示例
 */
@Getter
public enum LandingMerchantContractKeywordEnum {
  CONTRACT_NUM("contractNum", "合同编号", "合同编号"), //XH-GJGW-20231212001
  OUR_CONTRACTING_SUBJECT("ourContractingSubject", "甲方", "我方签约主体"), //咸亨国际科技股份有限公司
  ENTERPRISE_NAME("enterpriseName", "乙方（供应商）", "对方签约主体"), //上海宝岛服饰有限公司
  TYPE_OF_COOPERATION("typeOfCooperation", "合作类型：", "合作类型"), //项目报备 （项目报备/品牌合作/区域合作）
  COOPERATION_BRAND("cooperationBrand", "合作产品品类：", "合作品牌"), //咸亨中标全品类（除厂控品牌）
  COOPERATION_PERIOD("cooperationPeriod", "合作期限", "合作期限"), //【 2019 】年【11 】月【12 】日至【2024 】年【11 】月 【20 】日
  COOPERATION_RATIO("cooperationRatio", "商品结算价格比例", "合作比例"), //91 %
//  MINIMUM_GUARANTEE_AMOUNT("minimumGuaranteeAmount", "保底金金额(万元)", "保底金金额"), //10
//  INITIAL_AMOUNT_OF_LIQUIDATED_DAMAGES
//      ("initialAmountOfLiquidatedDamages", "甲方有权按照差额（计算方式：（）万元）", "违约金起始金额"), //[ 2 ]万元
//  GUARANTEED_AMOUNT_DEFAULT
//      ("guaranteedAmountDefault", "保底金额违约金比例", "保底金额违约金比例"), //[ 13 ]%
  SMALL_DEPOSIT("smallDeposit", "履约保证金小写", "保证金"), //1000000 元
  INVOICE_TYPE("invoiceType", "发票类型", "发票类型"), //增值税专用发票
  TAX_RATE("taxRate", "税率", "税率"); // 13%

  private final String code;
  private final String keyword;
  private final String name;

  LandingMerchantContractKeywordEnum(String code, String keyword, String name) {
    this.code = code;
    this.keyword = keyword;
    this.name = name;
  }
  /**
   * 处理识别到的信息
   */
  public static String getAfterProcessing(LandingMerchantContractKeywordEnum contractKeywordEnum,
      String text) {
    if (StrUtil.isNotBlank(text)) {
      text = StrUtil.removeAll(text, " ");
    }
    Objects.requireNonNull(contractKeywordEnum);
    if (StrUtil.isBlank(text)) {
      return text;
    }
    String[] removes = {"元", "万", "万元", "[", "]", "【", "】", "/", "%", "／", "％", " ", "［", "］", "【",
        "】"};
    switch (contractKeywordEnum) {
      case TYPE_OF_COOPERATION:
        return StrUtil.sub(text, 0, 4);
      case COOPERATION_RATIO:
        String cooperationRatio = StrUtil.removeAny(text, removes);
        try {
          //合作比例需要用1减去合同提取出来的比例
          BigDecimal ratio = new BigDecimal(cooperationRatio);
          BigDecimal max_ratio = new BigDecimal("100");
          return NumberUtil.sub(max_ratio, ratio).stripTrailingZeros().toPlainString();
        } catch (Exception e) {
          return cooperationRatio;
        }
/*      case INITIAL_AMOUNT_OF_LIQUIDATED_DAMAGES:
        return StrUtil.removeAny(text, removes);
      case GUARANTEED_AMOUNT_DEFAULT:
        return StrUtil.removeAny(text, removes);*/
      case INVOICE_TYPE:
        return StrUtil.removeAll(StrUtil.removeAny(text, removes), "发票");
      case SMALL_DEPOSIT:
        return StrUtil.removeAny(text, removes);
      case TAX_RATE:
        return StrUtil.removeAny(text, removes);
      default:
        return text;
    }
  }


  public static LandingMerchantContractKeywordEnum throwIfNotFindByKeyword(String keyword) {
    for (LandingMerchantContractKeywordEnum value : LandingMerchantContractKeywordEnum.values()) {
      if (value.getKeyword().equals(keyword)) {
        return value;
      }
    }
    throw new CheckException("未找到合同识别关键字：" + keyword);
  }
  public static LandingMerchantContractKeywordEnum throwIfNotFindByCode(String code) {
    for (LandingMerchantContractKeywordEnum value : LandingMerchantContractKeywordEnum.values()) {
      if (value.getCode().equals(code)) {
        return value;
      }
    }
    throw new CheckException("未找到合同识别代码：" + code);
  }

}
