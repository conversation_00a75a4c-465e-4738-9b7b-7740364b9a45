package com.xhgj.srm.common.enums;

public enum DataStatusEnum {
    // 使用枚举常量定义数据状态，并可以附带构造参数和字段
    DELETED("0", "删除"),
    NORMAL("1", "正常"),
    LOCKED("2", "锁定");

    // 定义枚举的字段
    private final String code;
    private final String description;

    // 枚举的构造方法默认为private，防止外部创建枚举实例
    DataStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
    public static DataStatusEnum fromCode(String code) {
      for (DataStatusEnum status : DataStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No constant with code " + code + " found");
    }
}