package com.xhgj.srm.common.utils;

import cn.hutool.core.lang.Assert;
import java.lang.ref.WeakReference;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created by <PERSON><PERSON> on 2023/8/24
 * 防止多个供应商账号操作共享数据而产生的数据不一致的情况。
 * 相比直接使用synchronized关键字等，降低了锁粒度，提高了并发能力。
 */
public class LockUtil {

  private LockUtil() {
  }

  /**
   * 监视器容器
   */
  private static final Map<String, WeakReference<MonitorObject>> MONITOR_MAP = new ConcurrentHashMap<>();
  private static final ScheduledExecutorService CLEANUP_SCHEDULER = Executors.newScheduledThreadPool(1);

  static {
    // 定期清理不再使用的MonitorObject
    CLEANUP_SCHEDULER.scheduleAtFixedRate(LockUtil::cleanupUnusedLocks, 5, 5, TimeUnit.MINUTES);
  }

  private static void cleanupUnusedLocks() {
    MONITOR_MAP.entrySet().removeIf(entry -> ((WeakReference<MonitorObject>) entry.getValue()).get() == null);
  }


  /**
   * 获取监视器对象。
   *
   * @param id 监视器唯一标识
   * @return 监视器对象。
   */
  public static synchronized MonitorObject getLockObject(final String id) {
    Assert.notBlank(id);
    MonitorObject lock;
    WeakReference<MonitorObject> ref = MONITOR_MAP.get(id);
    if (ref != null && (lock = ref.get()) != null) {
      return lock;
    }
    lock = new MonitorObject(id);
    MONITOR_MAP.put(id, new WeakReference<>(lock));
    return lock;
  }



  /**
   * 监视器对象
   */
  public static class MonitorObject {

    private final String id;

    private MonitorObject(String id) {
      this.id = id;
    }

    public String getId() {
      return id;
    }
  }
}
