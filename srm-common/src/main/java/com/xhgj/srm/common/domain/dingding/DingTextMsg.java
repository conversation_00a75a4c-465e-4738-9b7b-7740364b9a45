package com.xhgj.srm.common.domain.dingding;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-02-17 10:04
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DingTextMsg {
  private String msgtype;
  private At at;
  private Text text;

  @Builder
  @AllArgsConstructor
  @NoArgsConstructor
  @Data
  public static class At {
    private List<String> atMobiles;
    private List<String> atUserIds;

    @JSONField(name = "isAtAll")
    private Boolean isAtAll;
  }

  @Builder
  @AllArgsConstructor
  @NoArgsConstructor
  @Data
  public static class Text {
    private String content;
  }
}
