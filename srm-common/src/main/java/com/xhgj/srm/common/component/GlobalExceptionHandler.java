package com.xhgj.srm.common.component;/**
 * @since 2025/6/11 16:07
 */

import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 *<AUTHOR>
 *@date 2025/6/11 16:07:43
 *@description
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

  @ExceptionHandler(CheckException.class)
  public ResultBean<String> handleCheckException(CheckException e) {
    ResultBean<String> resultBean = new ResultBean<>();
    resultBean.setMsg(e.getMessage());
    resultBean.setCode(ResultBean.FAIL);
    return resultBean;
  }
}
