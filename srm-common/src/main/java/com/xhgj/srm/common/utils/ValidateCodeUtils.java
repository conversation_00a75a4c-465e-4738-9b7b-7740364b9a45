package com.xhgj.srm.common.utils;

import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 *
 */
@Configuration
@Component
public class ValidateCodeUtils {


	public static final String getRandomCode4(){
		return getRandomNumberCode(4);
	}

	private static final String getRandomNumberCode(int count){
		 StringBuffer sb = new StringBuffer();
		 String str = "0123456789";
		 Random r = new Random();
		 for(int i=0;i<count;i++){
			 int num = r.nextInt(str.length());
			 sb.append(str.charAt(num));
			 str = str.replace((str.charAt(num)+""), "");
		 }
		 return sb.toString();
	}

	public static final String REGEX_MOBILE = "^((13[0-9])|(14[0-9])|(15([0-9]))|(17[0-9])|(18[0-9]))\\d{8}$";

	public static final String REGEX_NUMBER = "^[0-9]*$";

	//yyyy-mm-dd
	public static String regex = "(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)";
	//excel日期格式  为一串数字
	public static String regex2 = "\\d+(\\.\\d+)?";

  /**
   * @description: 检查是否仅包含零字符（即全零）
   * @param: str
   **/
  public static boolean checkOnlyContainsZero(String str) {
    return str.matches("^0+$");
  }

  /**
	 * @param regex
	 *            正则表达式字符串
	 * @param str
	 *            要匹配的字符串
	 * @return 如果str 符合 regex的正则表达式格式,返回true, 否则返回 false;
	 */
	public boolean match(String regex, String str) {
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(str);
		return matcher.matches();
	}

	public static boolean checkDate(String checkValue, int iFlag) {
		boolean valid = true;
		DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
		Date d = null;
		if (checkValue != null && !checkValue.equals("")) {
			if (iFlag == 0) {
				dateFormat = new SimpleDateFormat("yyyy/MM/dd");
			}else if (iFlag == 2) {
				dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			}
		} else {
			valid = false;
		}
		try {
			d = dateFormat.parse(checkValue);
		} catch (Exception e) {
			valid = false;
		}
		String eL = "^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s(((0?[0-9])|([1-2][0-9]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$";
		Pattern p = Pattern.compile(eL);
		Matcher m = p.matcher(checkValue);
		boolean b = m.matches();
		if (!b) {
			valid = false;
		}
		return valid;
	}

	public static String getFormatDate(Date date, int iFlag) {
		SimpleDateFormat simpleDateFormat;
		if (iFlag == 0) {
			simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
		} else if (iFlag == 1) {
			simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		} else if (iFlag == 2) {
			simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		} else if (iFlag == 3) {
			simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
		} else if (iFlag == 4) {
			simpleDateFormat = new SimpleDateFormat("yyyy-MM");
		} else if (iFlag == 5) {
			simpleDateFormat = new SimpleDateFormat("yyyy-MM HH");
		} else if (iFlag == 6) {
			simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
		}else if (iFlag == 7) {
			simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
		}else {
			simpleDateFormat = new SimpleDateFormat("yyyy-MM HH:mm");
		}
		return simpleDateFormat.format(date);
	}

	/**
	 * 验证特殊字符
	 * <AUTHOR>
	 * @date 2021/8/23 17:14
	 */
	public boolean isConformToFormat(String str){
		if(str.contains("\\") || str.contains("\"")){
			return true;
		}
		return false;
	}

	public void isConformValue(String value, String info, Boolean validateIsNull){
		if(validateIsNull && StringUtils.isNullOrEmpty(value)){
			throw new CheckException(info+"不能为空！");
		}
		if(isConformToFormat(value)){
			throw new CheckException(info+"携带非法符号\\或\"");
		}
		if(value.length() >= 100){
			throw new CheckException(info+"长度超过100个字符");
		}
	}

}
