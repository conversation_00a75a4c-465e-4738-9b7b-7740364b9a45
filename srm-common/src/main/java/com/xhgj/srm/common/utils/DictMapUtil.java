package com.xhgj.srm.common.utils;

import com.xhiot.boot.core.common.util.StringUtils;
import java.util.Map;

/**
 * 字典 map（key 必须为 String）工具类
 *
 * <AUTHOR>
 * @since 2022/7/12 16:31
 */
public class DictMapUtil {

  /**
   * 校验 key 是否是非法的字典键
   *
   * @param dictMap 字典 map
   * @param key 键，空值认为是合法
   */
  public static boolean isInvalidDictMapKeyAcceptEmpty(Map<String, ?> dictMap, String key) {
    return isInvalidDictMapKey(dictMap, key, true);
  }

  /**
   * 校验 key 是否是合法的字典键
   *
   * @param dictMap 字典 map
   * @param key 键，空值认为是合法
   */
  public static boolean isValidDictMapKeyAcceptEmpty(Map<String, ?> dictMap, String key) {
    return isValidDictMapKey(dictMap, key, true);
  }

  /**
   * 校验 key 是否是非法的字典键
   *
   * @param dictMap 字典 map
   * @param key 键
   * @param acceptKeyEmpty 是否允许键位空值，若为 true，则 key 为空时返回 false
   */
  public static boolean isInvalidDictMapKey(
      Map<String, ?> dictMap, String key, boolean acceptKeyEmpty) {
    return !isValidDictMapKey(dictMap, key, acceptKeyEmpty);
  }

  /**
   * 校验 key 是否是合法的字典键
   *
   * @param dictMap 字典 map
   * @param key 键
   * @param acceptKeyEmpty 是否允许键为空值，若为 true，则 key 为空时返回 true
   */
  public static boolean isValidDictMapKey(
      Map<String, ?> dictMap, String key, boolean acceptKeyEmpty) {
    if (StringUtils.isNullOrEmpty(key)) {
      return acceptKeyEmpty;
    } else {
      return dictMap.containsKey(key);
    }
  }
}
