package com.xhgj.srm.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-05-13 9:05
 */
@Getter
public enum LogicalOperatorsEnums {
  EQUALS("="), LESS("<"), GREATER(">"),NO_EQUALS("!=")
  ;

  private final String symbol;

  LogicalOperatorsEnums(String symbol) {
    this.symbol = symbol;
  }


  public static String fromSymbol(String input) {
    for (LogicalOperatorsEnums operator : LogicalOperatorsEnums.values()) {
      if (operator.name().equals(input)) {
        return operator.symbol;
      }
    }
    return null;
  }
}
