package com.xhgj.srm.common.map;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.util.TypeUtils;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class TypeAwareMap<K, V> extends HashMap<K, V> {

  // 注册自定义的序列化器
  static {
    // 这里将自定义的序列化器绑定到 TypeAwareMap 类上
    SerializeConfig.getGlobalInstance().put(TypeAwareMap.class, new TypeAwareMapSerializer());
    ParserConfig.getGlobalInstance().putDeserializer(TypeAwareMap.class, new TypeAwareMapDeserializer());
  }

  private final Map<K, Class<?>> typeMap = new HashMap<>();

  public static TypeAwareMap<String,Object> loadMap(Map<String,Object> loadMap) {
    if (loadMap == null) {
      return new TypeAwareMap<>();
    }
    return load(loadMap);
  }

  public static TypeAwareMap<String,Object> loadMap(Object loadMapObj) {
    if (loadMapObj == null) {
      return new TypeAwareMap<>();
    }
    // 如果是字符串，先解析为 JSON 对象
    if (loadMapObj instanceof String) {
      return load(JSONObject.parseObject((String) loadMapObj));
    }
    // 如果是复杂对象，先转为 JSON 字符串再解析
    return load(JSON.parseObject(JSON.toJSONString(loadMapObj)));
  }

  private static TypeAwareMap<String, Object> load(Map<String, Object> loadMap) {
    // 从 parsedData 中提取 'type' 部分和 'data' 部分
    Map<String, String> typeMap = (Map<String, String>) loadMap.getOrDefault("type", new HashMap<>());
    Map<String, Object> dataMap = (Map<String, Object>) loadMap.getOrDefault("data", new HashMap<>());

    TypeAwareMap<String, Object> map = new TypeAwareMap<>();

    // 遍历 'data' 部分，根据 'typeMap' 转换数据
    for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
      String key = entry.getKey();
      String className = typeMap.get(key);  // 获取对应的类名
      if (StrUtil.isBlank(className)) {
        map.put(key, entry.getValue());
        continue;
      }
      try {
        // 使用反射将 className 转为类
        Object value = null;
        Class<?> clazz = Class.forName(className);
        if (clazz.isEnum()) {
          // 转换为枚举类型
          value = EnumUtil.fromString((Class<Enum>) clazz, Convert.toStr(entry.getValue()));
        } else {
          // 其他类型使用 Convert.convert 方法进行转换
          value = Convert.convert(clazz, entry.getValue());
        }
        // 将转换后的值放入 map 中
        map.put(key, value);
      } catch (ClassNotFoundException e) {
        e.printStackTrace();
        map.put(key, entry.getValue());
      }
    }
    return map;
  }

  private Map<K, Class<?>> getTypeMap() {
    return typeMap;
  }

  // 重写 put 方法，保存类型信息
  @Override
  public V put(K key, V value) {
    // 将 value 和其类型封装成一个 ValueWithType 对象
    // 保存类型信息
    if (value != null) {
      typeMap.put(key, value.getClass());
    }
    return super.put(key, value);
  }

  @Override
  public V get(Object key) {
    return super.get(key);
  }

  @Override
  public V remove(Object key) {
    typeMap.remove(key);
    return super.remove(key);
  }

  // Clear both maps
  @Override
  public void clear() {
    typeMap.clear();
    super.clear();
  }

  // 自定义序列化：先序列化 data，再序列化 typeMap
  public static class TypeAwareMapSerializer implements ObjectSerializer {

    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
      TypeAwareMap<?, ?> map = (TypeAwareMap<?, ?>) object;

      // 序列化 typeMap: 将 typeMap 中的 Class<?> 转为类名字符串
      Map<String, String> typeMapAsString = new HashMap<>();
      for (Map.Entry<?, Class<?>> entry : map.getTypeMap().entrySet()) {
        // 将 Class<?> 对象转为字符串（类名）
        typeMapAsString.put(entry.getKey().toString(), entry.getValue().getName());
      }
      // 序列化 map 中的实际内容
      Map<Object, Object> dataMap = new HashMap<>();
      for (Map.Entry<?, ?> entry : map.entrySet()) {
        dataMap.put(entry.getKey(), entry.getValue());
      }
      Map<String, Object> resultMap = new HashMap<>();
      resultMap.put("type", typeMapAsString);
      resultMap.put("data", dataMap);
      serializer.write(resultMap);
    }
  }

  // 自定义反序列化：恢复 typeMap 和 data
  public static class TypeAwareMapDeserializer implements ObjectDeserializer {

    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
      // 解析整个 JSON 对象为一个 Map
      Map<String, Object> parsedData = parser.parseObject(Map.class);

      // 从 parsedData 中提取 'type' 部分和 'data' 部分
      Map<String, String> typeMap = (Map<String, String>) parsedData.getOrDefault("type", new HashMap<>());
      Map<String, Object> dataMap = (Map<String, Object>) parsedData.getOrDefault("data", new HashMap<>());

      TypeAwareMap<String, Object> map = new TypeAwareMap<>();

      // 遍历 'data' 部分，根据 'typeMap' 转换数据
      for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
        String key = entry.getKey();
        String className = typeMap.get(key);  // 获取对应的类名
        if (StrUtil.isBlank(className)) {
          map.put(key, entry.getValue());
          continue;
        }
        try {
          // 使用反射将 className 转为类
          Object value = null;
          Class<?> clazz = Class.forName(className);
          if (clazz.isEnum()) {
            // 转换为枚举类型
            value = EnumUtil.fromString((Class<Enum>) clazz, Convert.toStr(entry.getValue()));
          } else {
            // 其他类型使用 Convert.convert 方法进行转换
            value = Convert.convert(clazz, entry.getValue());
          }
          // 将转换后的值放入 map 中
          map.put(key, value);
        } catch (ClassNotFoundException e) {
          e.printStackTrace();
          map.put(key, entry.getValue());
        }
      }

      return (T) map;
    }

    @Override
    public int getFastMatchToken() {
      return 0;
    }
  }
}

