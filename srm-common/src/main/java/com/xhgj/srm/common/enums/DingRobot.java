package com.xhgj.srm.common.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhiot.boot.core.common.util.StringUtils;
import java.util.Collections;
import java.util.List;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/10/26 14:45
 */
@Getter
public enum DingRobot {
  WARNING(
      "信息化系统报警群",
      "小亨",
      "2f3c1a4d18e515cfeb942978fe5b6b6f8a8ac77ef3e90d7096a74d71bcdbefcd",
      Collections.singletonList("请及时处理")),
  WARNING_TEST(
      "信息化系统报警群【测试环境】",
      "小亨【测试】",
      "efc0140ff4657ec241a6dc283ccf70ed2fd52c646840916dd7d9fb9941e39666",
      Collections.singletonList("请及时处理")),
  ;

  /** 钉钉群名 */
  private final String groupName;
  /** 机器人昵称 */
  private final String name;

  /** token */
  private final String accessToken;

  /**
   * 基于钉钉群聊机器人的安全设置，目前我们都使用【自定义关键词】（最多配置十个）的配置<br>
   * 若发送的消息体内不含关键词内容，则无法发送消息
   */
  private final List<String> msgKeyList;
  /**
   * 初始化钉钉机器人
   *
   * @param groupName 钉钉群名，若传空则设置为【未知】
   * @param name 机器人昵称，若传空则设置为【未知】
   * @param accessToken 访问钉钉接口使用的 token，必填，用于识别机器人
   * @param msgKeyList 自定义关键词，不能为空
   */
  DingRobot(String groupName, String name, String accessToken, List<String> msgKeyList) {
    Assert.notEmpty(accessToken);
    CollUtil.removeBlank(msgKeyList);
    Assert.notEmpty(msgKeyList);
    this.groupName = groupName;
    this.name = StrUtil.emptyToDefault(name, "未知");
    this.accessToken = StrUtil.emptyToDefault(accessToken, "未知");
    this.msgKeyList = msgKeyList;
  }

  /**
   * 校验消息体是否符合该机器人的配置
   *
   * @param msg 消息
   * @return 合法返回 true，反之 false
   */
  public boolean validateMsg(String msg) {
    if (StringUtils.isNullOrEmpty(msg)) {
      return false;
    }
    return CollUtil.emptyIfNull(this.msgKeyList).stream().anyMatch(msg::contains);
  }
}
