package com.xhgj.srm.common.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Geng Shy on 2023/12/12
 */
public class ListUtil {

  /**
   * 拆分集合
   *
   * @param originalList 源集合
   * @param size 拆分长度
   * @param <T> 类型
   * @return 拆分后的小集合 List<List<T>>
   */
  public static <T> List<List<T>> splitList(List<T> originalList, int size) {
    List<List<T>> subLists = new ArrayList<>();
    if (originalList == null || originalList.isEmpty()) {
      return subLists;
    }
    int totalSize = originalList.size();
    for (int i = 0; i < totalSize; i += size) {
      subLists.add(new ArrayList<>(originalList.subList(i, Math.min(i + size, totalSize))));
    }
    return subLists;
  }
}
