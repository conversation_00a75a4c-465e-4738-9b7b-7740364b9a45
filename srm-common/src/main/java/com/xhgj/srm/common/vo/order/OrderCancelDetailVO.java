package com.xhgj.srm.common.vo.order;/**
 * @since 2025/3/19 18:14
 */

import lombok.Data;
import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/3/19 18:14:48
 *@description
 */
@Data
public class OrderCancelDetailVO {

  /**
   * 主键id
   */
  private String id;

  /**
   * 关联取消单id
   */
  private String cancelId;

  /**
   * 关联订单id
   */
  private String orderId;

  /**
   *  物料编码
   */
  private String code;

  /**
   * 物料品牌
   */
  private String brand;

  /**
   * 物料名称
   */
  private String name;

  /**
   * 物料规格
   */
  private String model;

  /**
   * 物料单位
   */
  private String unit;

  /**
   * 物料价格
   */
  private BigDecimal price;

  /**
   * 物料数量
   */
  private BigDecimal num;

  /**
   * 创建时间
   */
  private Long createTime;
}
