package com.xhgj.srm.common.enums.landingContract;/**
 * @since 2024/11/28 10:24
 */

/**
 *<AUTHOR>
 *@date 2024/11/28 10:24:36
 *@description 绑品类型
 */
public enum BundleType {
  /**
   * 品牌绑定
   */
  BRAND((byte) 0, "品牌绑定"),
  /**
   * 区域绑定
   */
  AREA((byte) 1, "区域绑定"),
  /**
   * 客户单位绑定
   */
  CUSTOMER_ORG((byte) 2, "客户单位绑定")
  ;

  private Byte code;

  private String desc;

  BundleType(Byte code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public static BundleType getEnum(Byte code) {
    for (BundleType value : BundleType.values()) {
      if (value.getCode().equals(code)) {
        return value;
      }
    }
    return null;
  }

  public Byte getCode() {
    return code;
  }

  public String getDesc() {
    return desc;
  }
}
