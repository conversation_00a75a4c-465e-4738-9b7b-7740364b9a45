package com.xhgj.srm.common.utils;
import cn.hutool.core.collection.ListUtil;
import com.xhgj.srm.common.handler.StreamAutoCloseHandler;
import com.xhgj.srm.common.handler.StreamAutoCloseHandlerOne;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * 流自动关闭<br>
 * ByteArrayInputStream、ByteArrayInputStream  不需要自动关闭，内存流由JVM自动回收
 */
@Slf4j
public class StreamAutoCloseUtil {

  /**
   * 定义一个方法来处理多个资源
   *
   * @param handler 对多个资源的操作
   * @param resources 可变参数的资源列表
   * @param <T> 资源类型
   */
  @SafeVarargs
  public static <T extends AutoCloseable> void withResource(StreamAutoCloseHandler<T> handler, T... resources) {
    // 使用 try-with-resources 管理多个资源
    try {
      handler.handleList(ListUtil.toList(resources));
    } catch (Exception e) {
      log.error("操作失败: " + e.getMessage(), e);
    } finally {
      // 确保所有资源都被关闭
      for (T resource : resources) {
        try {
          if (resource != null) {
            resource.close();
          }
        } catch (Exception e) {
          log.error("关闭资源失败: " + e.getMessage(), e);
        }
      }
    }
  }

  /**
   * 定义一个方法来处理单个资源
   *
   * @param handler 对多个资源的操作
   * @param resource 可变参数的资源列表
   * @param <T> 资源类型
   */
  public static <T extends AutoCloseable> void withResource(StreamAutoCloseHandlerOne<T> handler, T resource) {
    // 使用 try-with-resources 管理多个资源
    try (T autoCloseableResource = resource) {
      // 执行操作
      handler.handleOne(autoCloseableResource);
    } catch (Exception e) {
      // 处理异常
      log.error("操作失败: " + e.getMessage(), e);
    }
  }


}
