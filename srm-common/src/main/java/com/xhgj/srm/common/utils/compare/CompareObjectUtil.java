package com.xhgj.srm.common.utils.compare;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import java.lang.reflect.Field;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/8/17 9:22
 */
@Slf4j
public class CompareObjectUtil {
  public static String getObjectFieldValueWithCompareMap(Object o, Field field) {
    String compareMap =
        Optional.ofNullable(AnnotationUtil.getAnnotationValue(field, CompareMapField.class))
            .map(Objects::toString)
            .orElse(null);
    String value = Convert.toStr(ReflectUtil.getFieldValue(o, field));
    String finalValue;
    if (StrUtil.isNotBlank(compareMap) && StrUtil.isNotBlank(value)) {
      // 截取常量类
      String constantsClass = StrUtil.subBefore(compareMap, "#", false);
      // 截取 map 字段
      String mapFieldName = StrUtil.subAfter(compareMap, "#", false);
      Field mapField = ReflectUtil.getField(ClassUtil.loadClass(constantsClass), mapFieldName);
      Map<String, String> map = (Map<String, String>) ReflectUtil.getStaticFieldValue(mapField);
      if (map == null) {
        log.error("获取【" + mapFieldName + "】类中的静态常量 map【" + mapField + "】异常！");
        throw new CheckException("解析映射字段异常，请联系管理员！");
      }
      if (!map.containsKey(value)) {
        log.error(
            "解析字段【"
                + field.toString()
                + "】异常：【"
                + mapFieldName
                + "】类中的 map【"
                + mapField
                + "】不存在 key 值【"
                + value
                + "】");
        throw new CheckException("解析映射字段值异常，请联系管理员！");
      } else {
        finalValue = map.get(value);
      }
    } else {
      finalValue = value;
    }
    return finalValue;
  }
}
