package com.xhgj.srm.common.enums.order;/**
 * @since 2025/3/18 19:05
 */

/**
 *<AUTHOR>
 *@date 2025/3/18 19:05:35
 *@description 付款单来源
 */
public enum OrderPaymentSource {
  /**
   * 来源后台提交
   */
  BACKEND((byte)1, "后台提交"),
  /**
   * 来源前台提交
   */
  FRONTEND((byte)2, "前台提交"),
  /**
   * 来源移动端提交
   */
  MOBILE((byte)3, "移动端提交"),
  ;

  private Byte code;

  private String desc;

  OrderPaymentSource(Byte code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public static String getDescByCode(Byte code) {
    for (OrderPaymentSource source : OrderPaymentSource.values()) {
      if (source.getCode().equals(code)) {
        return source.getDesc();
      }
    }
    return null;
  }

  public static Byte getCodeByDesc(String desc) {
    for (OrderPaymentSource source : OrderPaymentSource.values()) {
      if (source.getDesc().equals(desc)) {
        return source.getCode();
      }
    }
    return null;
  }

  public Byte getCode() {
    return code;
  }

  public String getDesc() {
    return desc;
  }
}
