package com.xhgj.srm.common;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/5 11:17
 */
@Data
public class Constants_DingCarTemplate {

  /** 钉钉普通卡片消息 */
  public static final String DING_TALK_ROBOT_STANDARD_CARD_MSG_TYPE = "StandardCard";

  /** 钉钉高级卡片消息 */
  public static final String DING_TALK_ROBOT_STANDARD_ADVANCED_CARD_MSG_TYPE = "advancedCard";

  /** markdown 格式 */
  public static final String DING_TALK_ROBOT_MARKDOWN_MSG_TYPE = "markdown";
  /** 文本格式 */
  public static final String DING_TALK_ROBOT_TEXT_MSG_TYPE = "text";
  /** 按钮格式 */
  public static final String DING_TALK_ROBOT_BUTTON_MSG_TYPE = "button";
  /** 打卡链接 */
  public static final String DING_TALK_ROBOT_BUTTON_TYPE_OPEN_LINK = "openLink";
  /** 请求链接 */
  public static final String DING_TALK_ROBOT_BUTTON_TYPE_REQUEST = "request";
  /** 普通按钮 */
  public static final String DING_TALK_ROBOT_BUTTON_TYPE_STATUS_NORMAL = "normal";
  /** 请求按钮 */
  public static final String DING_TALK_ROBOT_BUTTON_TYPE_STATUS_PRIMARY = "primary";
  /** 事件类型 */
  public static final String DING_TALK_ROBOT_ACTION_TYPE = "action";
  /** 入库通知 */
  public static final String TITLE_HEAD_WAREHOUSING_NOTICE = "入库通知";
  /** erp 驳回 */
  public static final String TITLE_HEAD_ERP_REJECT_NOTICE = "驳回通知";
  /** 入库通知标题内容 */
  public static final String TITLE_HEAD_CONTENT_WAREHOUSING =
      "**<font size=15 color=#000000 >您有一个直发订单需要入库！</font>**";
  /** erp 驳回 */
  public static final String TITLE_HEAD_CONTENT_ERP_REJECT =
      "**<font size=15 color=#000000 >您的采购退料单被驳回，请前往SRM修改！</font>**";
  /** 消息通知模板标题 采购订单号 */
  public static final String NEED_NOTICE_TEMPLATE_ORDER_CODE = "**采购订单号：**";
  /** 消息通知模板标题 供应商 */
  public static final String NEED_NOTICE_TEMPLATE_SUPPLIER = "**供应商：**";
  /** 消息通知模板标题 发货时间 */
  public static final String NEED_NOTICE_TEMPLATE_DELIVERY_TIME = "**发货时间：**";
  /** 消息通知模板标题 物流公司 */
  public static final String NEED_NOTICE_TEMPLATE_LOGISTICS_COMPANY = "**物流公司：**";
  /** 消息通知模板标题 采购退料单号 */
  public static final String NEED_NOTICE_TEMPLATE_RETURN_CODE = "**采购退料单号：**";

  /** 申请付款通知模板标题 申请付款通知 */
  public static final String APPLY_PAYMENT_TEMPLATE_APPLY_PAYMENT_NOTICE = "申请付款通知";
  /** 申请付款通知模板 */
  public static final String TITLE_HEAD_CONTENT_APPLY_PAYMENT =
      "**<font size=15 color=#000000 >【{}】提交的付款申请</font>**";
  /** 付款申请单号 */
  public static final String APPLY_PAYMENT_CODE =
      "**付款申请单号：**";

  /** 申请付款金额 */
  public static final String APPLY_PAYMENT_PRICE =
      "**申请付款金额：**";
  public static final String APPLY_ORDER_CODE_LIST =
      "**申请订单号：**";

  public static final String INVOICE_CODE_LIST =
      "**关联发票号：**";


  /** 供应商未确认收货通知 */
  public static final String TITLE_HEAD_CONTENT_CONFIRM_RECEIPT = "退货通知";

  public static final String TITLE_HEAD_CONFIRM_RECEIPT =
      "**<font size=15 color=#000000 >供应商的退货单仍未确认收货！</font>**";

  public static final String ORDER_OPEN_INVOICE_CALL_BACK_KEY = "orderOpenInvoiceCallBackKey";
  /** 发送进项票确认通知（老） */
  public static final String DING_CARD_ID_ORDER_INVOICE = "990a0864-a698-4f93-81d4-64f4800f3d0a.schema";
  /** 发送进项票确认通知（新） */
  public static final String DING_CARD_ID_ORDER_INVOICE_NEW = "709cf776-c2b6-4f09-9b21-d67c869b88f0.schema";
  public static final String DING_CARD_ID_FILING_ORDER = "711ac865-5dcc-43ff-87c4-ac8d3fba2b81"
      + ".schema";
  /**拒单通知 */
  public static final String DING_CARD_ID_SUPPLIER_ORDER_REFUSE = "d6fe707d-f168-4faf-bf11-73a4e9efcc18.schema";

  /** 已签收erp未入库通知 */
  public static final String DING_CARD_ID_SIGN_UNINSTOCK = "5f4271f4-77b5-41bc-932e-aa8ec1ae7866.schema";

  /**
   * 进项票驳回通知
   */
  public static final String DING_CARD_ID_INVOICE_REJECT_NOTICE = "ab324814-78cf-41f1-a1e5-ae66134c2114.schema";
  /**
   * 进项票开票通知
   */
  public static final String DING_CARD_ID_INVOICE_OPEN_NOTICE = "709cf776-c2b6-4f09-9b21-d67c869b88f0.schema";

  /**
   * 落地商入驻报备单通知
   */
  public static final String DING_CARD_ID_LANDING_BUSINESS_REGISTRATION_FORM = "4bf52059-a3a4-471b-b6e6-90feacb6cf11.schema";
  /**
   * 落地商合同到期通知
   */
  public static final String DING_CARD_ID_LANDING_BUSINESS_CONTRACT_EXPIRATION_NOTICE  = "66098b9d-5bab-4f21-911a-60bd8ba22153.schema";
  /**
   * 合同附件审批
   */
  public static final String DING_CARD_ID_LANDING_BUSINESS_APPROVAL_OF_CONTRACT_ATTACHMENTS  = "da7924da-5c4e-4613-bb3d-9caef4a2ad42.schema";

  /**
   * 库存安全检查模板
   */
  public static final String DING_CARD_ID_INVENTORY_SAFETY_CHECK = "64b191ca-9df8-4113-b067-ff0745208577.schema";
}
