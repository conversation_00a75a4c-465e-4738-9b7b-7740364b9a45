package com.xhgj.srm.common.utils;

import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.io.resource.Resource;
import cn.hutool.system.OsInfo;
import cn.hutool.system.SystemUtil;
import com.aspose.words.Document;
import com.aspose.words.FontSettings;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2024/8/12 16:44
 */
public final class AsposeWordsUtil {
  private AsposeWordsUtil() {}

  static {
    // aspose依赖需要加载配置
    final String licenseFilePath = "static/license.xml";
    Resource resource = new ClassPathResource(licenseFilePath, AsposeWordsUtil.class);
    try (InputStream is = resource.getStream()) {
      License aposeLic = new License();
      aposeLic.setLicense(is);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * @param inputStream doc文件流
   * @return pdf文件byte数组
   * @throws Exception 转换失败时抛出
   */
  public static byte[] docConvertPdf(final InputStream inputStream) throws Exception {
    try {
      // 加载Word文档
      Document doc = new Document(inputStream);
      // 将文档保存为PDF
      ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
      OsInfo osInfo = SystemUtil.getOsInfo();
      if (osInfo.isLinux()) {
        FontSettings.setDefaultFontName("SIMSUN");
        FontSettings.setFontsFolder("/usr/share/fonts/chinese", true);
      }
      doc.save(outputStream, SaveFormat.PDF);
      return outputStream.toByteArray();
    }finally {
      if (inputStream != null) {
        inputStream.close();
      }
    }
  }
}
