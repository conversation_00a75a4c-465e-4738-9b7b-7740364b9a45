package com.xhgj.srm.common.vo.order;/**
 * @since 2025/3/17 18:41
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.order.OrderReceiptPaymentMethodEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 *<AUTHOR>
 *@date 2025/3/17 18:41:19
 *@description 可付款履约单列表
 */
@Data
public class OrderNeedPaymentListVO {
  /**
   * 主键
   */
  private String id;

  // -----------------------------1.关联Order-----------------------------

  /**
   * 关联订单id
   * @see com.xhgj.srm.jpa.entity.Order
   */
  private String orderId;

  /**
   * 客户订单号
   */
  private String orderNo;

  /**
   * 下单时间
   */
  private Long createTime;

  /**
   * 实际订货金额
   */
  private BigDecimal orderActualAmount;

  /**
   * 取消金额
   */
  private BigDecimal orderCancelAmount;

  /**
   * 退货金额
   */
  private BigDecimal orderReturnAmount;

  /**
   * 发货状态
   */
  @ApiModelProperty("发货状态 1：未发货（两个时间都没有），2：部分发货：（有首次发货时间），3：已发货：（有全部发货时间）")
  private String shipState;

  /**
   * 首次发货时间
   */
  private Long firstShipTime;

  /**
   * 全部发货时间
   */
  private Long allShipTime;

  /**
   * 签收凭证
   */
  private String signVoucherState;

  /**
   * 签收凭证通过时间
   */
  private Long confirmVoucherTime;

  /**
   * 客户开票状态
   */
  private String invoicingState;

  /**
   * 客户开票日期
   */
  private Long customerInvoiceTime;

  /**
   * 供应商开票状态
   */
  private String supplierOpenInvoiceStatus;

  /**
   * 供应商开票日期
   */
  private Long confirmAccountOpenInvoiceTime;

  /**
   * 客户签收时间
   */
  private Long customerAcceptTime;

  /**
   * 付款发起条件
   * @see com.xhgj.srm.common.enums.contract.LandingMerchantContractPaymentConditionEnum
   */
  private String paymentCondition;

  /**
   * 付款满足日期
   */
  private Long paymentConditionTime;

  /**
   * 账期
   */
  private String accountingPeriod;

  /**
   * 预计付款日期
   */
  private Long paymentDate;
  /**
   * 客户回款状态：已完成，未完成
   */
  private String customerPayback;

  // -----------------------------2.关联OrderReceipt-----------------------------

  /**
   * 关联回款id
   * @see com.xhgj.srm.jpa.entity.OrderReceiptRecord
   */
  private String orderReceiptId;

  /**
   * 客户回款方式，oms传过来的（存的是中文）
   **/
  private String paymentType;

  /**
   * 客户回款方式，这里需要基于客户回款映射出付款方式
   * @see com.xhgj.srm.common.enums.order.OrderReceiptPaymentMethodEnum
   */
  private String paymentMethod;

  /**
   * 客户回款方式中文，这里需要基于客户回款映射出付款方式
   * @see com.xhgj.srm.common.enums.order.OrderReceiptPaymentMethodEnum
   */
  private String paymentMethodValue;
  /**
   * 付款方式
   * @see com.xhgj.srm.common.enums.order.OrderReceiptPaymentMethodEnum
   */
  private String sapPaymentMethod;
  /**
   * 付款方式中文
   * @see com.xhgj.srm.common.enums.order.OrderReceiptPaymentMethodEnum
   */
  private String sapPaymentMethodValue;
  /**
   * 银行流水号
   */
  private String bankSerialNo;
  /**
   * 汇票号
   */
  private String billNo;


  // -----------------------------3.关联Supplier-----------------------------

  /**
   * 供应商id，冗余字段用于联表
   */
  private String supplierId;
  /**
   * 供应商名称
   */
  private String supplierName;

  // -----------------------------4.关联Platform-----------------------------

  /**
   * 平台code，冗余字段用于联表
   */
  private String platformCode;

  /**
   * 平台名称
   */
  private String platformName;

  // -----------------------------5.需付款表原始字段-----------------------------

  /**
   * 已提款金额
   */
  private BigDecimal paidAmount;

  /**
   * 扣除明细
   */
  private List<DeductDetail> deductDetails;

  /**
   * 提款中的金额(记得需要相应时机释放)
   */
  private BigDecimal pendingAmount;

  /**
   * 退款金额(付款单申请退款)
   */
  private BigDecimal returnAmount;

  /**
   * 可提款金额
   */
  private BigDecimal availableAmount;

  /**
   * 剩余可提款金额
   */
  private BigDecimal remainingAmount;

  /**
   * 实际可提款金额
   */
  private BigDecimal realAmount;

  /**
   * 是否被冲销
   */
  private Boolean offset;

  /**
   * 是否扣除
   */
  private Boolean deduct;

  /**
   * 是否背靠背
   */
  private Boolean backToBack;

  /**
   * 回款比例
   */
  private BigDecimal rate;

  /**
   * 回款展示比例
   */
  private BigDecimal showRate;

  // -----------------------------6.关联付款申请-----------------------------

  /**
   * 关联付款申请id
   * todo 需要修改返回数据模型，应该返回对应id + paymentNo
   */
  private List<LinkedPayment> linkedPayments;

  @ApiModelProperty("erp类型")
  private String erpType;

  @ApiModelProperty("禁止付款状态")
  private Boolean prohibitionPaymentState;

  @ApiModelProperty("签约抬头（组织编码）")
  private String titleOfTheContract;
  /**
   * 淡红色：付款数据可以直接付款，剩余可提款金额大于0且到达预计可付款时间
   * 绿色：有关联付款单且剩余可提款金额=0
   * 灰色：预计付款日期为空且剩余可提款＞0
   **/
  @ApiModelProperty("行颜色")
  private String color;

  /**
   * 行颜色
   * @return
   */
  public String getColor() {
    if (NumberUtil.isGreater(NumberUtil.toBigDecimal(remainingAmount), BigDecimal.ZERO)
        && this.getPaymentDate() != null && System.currentTimeMillis() >= this.getPaymentDate()) {
      return "淡红色";
    } else if (CollUtil.isNotEmpty(linkedPayments) && NumberUtil.equals(
        NumberUtil.toBigDecimal(remainingAmount), BigDecimal.ZERO)) {
      return "绿色";
    } else if (this.getPaymentDate() == null && NumberUtil.isGreater(
        NumberUtil.toBigDecimal(remainingAmount), BigDecimal.ZERO)) {
      return "灰色";
    }
    return StrUtil.EMPTY;
  }

  public String getPaymentMethodValue() {
    return OrderReceiptPaymentMethodEnum.getDescFromCode(paymentMethod);
  }

  public String getSapPaymentMethod() {
    PayTypeSAPEnums payTypeSAPEnum =
        OrderReceiptPaymentMethodEnum.getSapEnumFromCode(paymentMethod);
    if (payTypeSAPEnum != null) {
      return payTypeSAPEnum.getCode();
    } else {
      return null;
    }
  }

  public String getSapPaymentMethodValue() {
    PayTypeSAPEnums payTypeSAPEnum =
        OrderReceiptPaymentMethodEnum.getSapEnumFromCode(paymentMethod);
    if (payTypeSAPEnum != null) {
      return payTypeSAPEnum.getName();
    } else {
      return null;
    }
  }

  public BigDecimal getAvailableAmount() {
    // 可提款金额计算规则
    // 账期 可提金额 = 100% * 订单实际订货金额
    // 背靠背 = 回款比例 * 订单实际订货金额
    BigDecimal tempActualOrderAmount =
        Optional.ofNullable(this.orderActualAmount).orElse(BigDecimal.ZERO);
    BigDecimal tempRate = Optional.ofNullable(this.rate).orElse(BigDecimal.ZERO);
    return tempActualOrderAmount.multiply(tempRate);
  }

  public BigDecimal getRemainingAmount() {
    // 剩余可提款金额 = 可提款金额 - 已提款金额 + 退款金额
    BigDecimal tempAvailableAmount =
        Optional.ofNullable(this.getAvailableAmount()).orElse(BigDecimal.ZERO);
    BigDecimal tempPaidAmount = Optional.ofNullable(this.paidAmount).orElse(BigDecimal.ZERO);
    BigDecimal tempReturnAmount = Optional.ofNullable(this.returnAmount).orElse(BigDecimal.ZERO);
    return tempAvailableAmount.subtract(tempPaidAmount).add(tempReturnAmount);
  }

  public BigDecimal getRealAmount() {
    // 实际可提款金额 = 剩余可提款金额 - 提款中的金额
    BigDecimal tempRemainingAmount =
        Optional.ofNullable(this.getRemainingAmount()).orElse(BigDecimal.ZERO);
    BigDecimal tempPendingAmount = Optional.ofNullable(this.pendingAmount).orElse(BigDecimal.ZERO);
    return tempRemainingAmount.subtract(tempPendingAmount);
  }

  public List<DeductDetail> getDeductDetails() {
    List<DeductDetail> res = new ArrayList<>();
    // 如过订单取消金额大于0
    if (orderCancelAmount != null && orderCancelAmount.compareTo(BigDecimal.ZERO) > 0) {
      DeductDetail deductDetail = new DeductDetail();
      deductDetail.setType("取消金额");
      deductDetail.setDeduct(orderCancelAmount);
      res.add(deductDetail);
    }
    // 如过订单退货金额大于0
    if (orderReturnAmount != null && orderReturnAmount.compareTo(BigDecimal.ZERO) > 0) {
      DeductDetail deductDetail = new DeductDetail();
      deductDetail.setType("退货");
      deductDetail.setDeduct(orderReturnAmount);
      res.add(deductDetail);
    }
    // 如果冲销
    if (Boolean.TRUE.equals(offset)) {
      DeductDetail deductDetail = new DeductDetail();
      deductDetail.setType("回款扣除");
      BigDecimal tempActualOrderAmount =
          Optional.ofNullable(this.orderActualAmount).orElse(BigDecimal.ZERO);
      BigDecimal tempShowRate = Optional.ofNullable(this.showRate).orElse(BigDecimal.ZERO);
      deductDetail.setDeduct(tempActualOrderAmount.multiply(tempShowRate));
      res.add(deductDetail);
    }
    // 如果扣减
    if (Boolean.TRUE.equals(deduct)) {
      DeductDetail deductDetail = new DeductDetail();
      deductDetail.setType("回款扣除");
      BigDecimal tempActualOrderAmount =
          Optional.ofNullable(this.orderActualAmount).orElse(BigDecimal.ZERO);
      BigDecimal tempShowRate = Optional.ofNullable(this.showRate).orElse(BigDecimal.ZERO);
      deductDetail.setDeduct(tempActualOrderAmount.multiply(tempShowRate));
      res.add(deductDetail);
    }
    return res;
  }

  /**
   * 扣减明细
   */
  @Data
  public static class DeductDetail {

    /**
     * 扣减类型
     */
    private String type;

    /**
     * 扣减金额
     */
    private BigDecimal deduct;
  }

  /**
   * 关联付款申请
   */
  @Data
  @AllArgsConstructor
  public static class LinkedPayment {
    /**
     * 付款申请id
     */
    private String id;

    /**
     * 付款申请单号
     */
    private String paymentNo;
  }
}
