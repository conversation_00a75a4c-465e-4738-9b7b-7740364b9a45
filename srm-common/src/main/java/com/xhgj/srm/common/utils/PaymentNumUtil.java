package com.xhgj.srm.common.utils;

import com.xhgj.srm.common.utils.runner.JedisUtil;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 订单编号生成规则
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
public class PaymentNumUtil {
  private int seq = 0;
  private static final JedisUtil jedisUtil = JedisUtil.getInstance();
  private static final String PAYMENTRECORDNO10SRM_REDISKEY = "paymentrecordno10srm_counter"; // srm订单编号计数器

  private static class OrderNumContainer {
    private static PaymentNumUtil instance = new PaymentNumUtil();
  }

  public static PaymentNumUtil getInstance() {
    return OrderNumContainer.instance;
  }
  // 获取今日订单顺序号
  public synchronized String getSeq() {
    LocalDate currentDate = LocalDate.now();
//    String datetime = currentDate.format(DateTimeFormatter.ofPattern("yyMM"));
    if (jedisUtil.exists(PAYMENTRECORDNO10SRM_REDISKEY)) {
      String tempStr = jedisUtil.get(PAYMENTRECORDNO10SRM_REDISKEY);
      String[] numStr = tempStr.split(",");
      this.seq = Integer.parseInt(numStr[1]) + 1;
    } else {
      // 拿不到key 分两种情况：1、redis服务异常；2、redis初始安装
      if (jedisUtil.checkConnection()) {
        this.seq = 1;
      } else {
        // redis服务异常
        return "0";
      }
    }
    jedisUtil.set(PAYMENTRECORDNO10SRM_REDISKEY, "FKSQ," + this.seq);

    String result = String.valueOf(seq);
    if (result.length() < 10) {
      int mun = 10 - result.length();
      for (int i = 0; i < mun; i++) {
        result = "0" + result;
      }
    }
    return "FKSQ"+result;
  }

}
