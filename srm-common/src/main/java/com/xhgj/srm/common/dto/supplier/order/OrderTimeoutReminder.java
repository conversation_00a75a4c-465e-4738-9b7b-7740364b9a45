package com.xhgj.srm.common.dto.supplier.order;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON> on 2023/11/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderTimeoutReminder {
  public static final String HEADER_FIELDS_NAME_1 = "采购订单号";
  public static final String HEADER_FIELDS_NAME_2 = "供应商名称";
  public static final String HEADER_FIELDS_ALIAS_1 = "orderNo";
  public static final String HEADER_FIELDS_ALIAS_2 = "name";


  private List<Data> data;
  private List<Meta> meta;

  @lombok.Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Data {

    private String orderNo;
    private String name;
  }

  @lombok.Data
  @AllArgsConstructor
  public static class Meta {
    private String aliasName;
    private String dataType = "STRING";
    private String alias;
    private Integer weight = 30;

    public Meta(String aliasName, String alias) {
      this.aliasName = aliasName;
      this.alias = alias;
    }
    public Meta(String aliasName, String alias, Integer weight) {
      this.aliasName = aliasName;
      this.alias = alias;
      this.weight = weight;
    }
  }
}
