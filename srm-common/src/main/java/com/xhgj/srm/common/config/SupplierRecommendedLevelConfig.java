package com.xhgj.srm.common.config;/**
 * @since 2025/1/21 11:37
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/1/21 11:37:08
 *@description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierRecommendedLevelConfig {

  public static final String KEY = "recommendedLevelJson";

  /**
   * 等级code
   */
  private String levelCode;

  /**
   * 等级名称
   */
  private String levelName;

  /**
   * 大于
   */
  private BigDecimal greater;

  /**
   * 小于等于
   */
  private BigDecimal lessOrEqual;

  public static List<SupplierRecommendedLevelConfig> createDefault() {
    return ListUtil.of(
        new SupplierRecommendedLevelConfig(SupplierLevelEnum.POTENTIAL.getCode(), SupplierLevelEnum.POTENTIAL.getAbbr(), null, null),
        new SupplierRecommendedLevelConfig(SupplierLevelEnum.SPORADIC.getCode(), SupplierLevelEnum.SPORADIC.getAbbr(), null, null),
        new SupplierRecommendedLevelConfig(SupplierLevelEnum.GENERAL.getCode(), SupplierLevelEnum.GENERAL.getAbbr(), null, null),
        new SupplierRecommendedLevelConfig(SupplierLevelEnum.HIGH_QUALITY.getCode(), SupplierLevelEnum.HIGH_QUALITY.getAbbr(), null, null),
        new SupplierRecommendedLevelConfig(SupplierLevelEnum.STRATEGIC.getCode(), SupplierLevelEnum.STRATEGIC.getAbbr(), null, null)
    );
  }

  /**
   * 通过json获取推荐等级配置
   * @param json
   * @return
   */
  public static List<SupplierRecommendedLevelConfig> formatJson(String json) {
    if (StrUtil.isBlank(json)) {
      return createDefault();
    }
    return JSON.parseArray(json, SupplierRecommendedLevelConfig.class);
  }

  /**
   * 根据推荐等级配置和金额判断推荐等级
   */
  public static String makeRecommendedLevel(List<SupplierRecommendedLevelConfig> recommendedLevelConfigs, BigDecimal amount) {
    if (amount == null) {
      return null;
    }
    // 过滤出recommendedLevelConfigs中有值的配置,并且根据greater进行正序排序，null值的greater排在最前面
    List<SupplierRecommendedLevelConfig> filterConfigs = recommendedLevelConfigs.stream()
        .filter(config -> config.getGreater() != null || config.getLessOrEqual() != null)
        .collect(Collectors.toList());
    if (CollUtil.isEmpty(filterConfigs)) {
      return null;
    }
    filterConfigs.sort((o1, o2) -> {
      if (o1.getGreater() == null && o2.getGreater() == null) {
        return 0;
      }
      if (o1.getGreater() == null) {
        return -1;
      }
      if (o2.getGreater() == null) {
        return 1;
      }
      return o1.getGreater().compareTo(o2.getGreater());
    });
    // 根据金额判断推荐等级
    for (SupplierRecommendedLevelConfig config : filterConfigs) {
      if (config.getGreater() == null) {
        if (amount.compareTo(config.getLessOrEqual()) <= 0) {
          return config.getLevelCode();
        }
      } else if (config.getLessOrEqual() == null) {
        if (amount.compareTo(config.getGreater()) > 0) {
          return config.getLevelCode();
        }
      } else {
        if (amount.compareTo(config.getGreater()) > 0 && amount.compareTo(config.getLessOrEqual()) <= 0) {
          return config.getLevelCode();
        }
      }
    }
    return null;
  }
}
