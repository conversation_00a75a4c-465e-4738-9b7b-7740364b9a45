package com.xhgj.srm.common.enums.transferOrder;/**
 * @since 2025/2/25 11:14
 */

/**
 *<AUTHOR>
 *@date 2025/2/25 11:14:35
 *@description
 */
public enum TransferOrderStatus {
  /**
   * 暂存
   */
  TEMPORARY((byte) 1, "暂存"),
  /**
   * 审核中
   */
  AUDITING((byte) 2, "审核中"),
  /**
   * 驳回
   */
  REJECT((byte) -1, "驳回"),
  /**
   * 待仓库执行
   */
  WAITING_WAREHOUSE((byte) 3, "待仓库执行"),
  /**
   * 已完成
   */
  FINISHED((byte) 4, "已完成"),
  ;

  private Byte code;

  private String name;

  TransferOrderStatus(Byte code, String name) {
    this.code = code;
    this.name = name;
  }

  public Byte getCode() {
    return code;
  }

  public String getName() {
    return name;
  }

  public static String getNameByCode(Byte code) {
    for (TransferOrderStatus value : TransferOrderStatus.values()) {
      if (value.getCode().equals(code)) {
        return value.getName();
      }
    }
    return null;
  }
}
