package com.xhgj.srm.common.enums.paymentApply;

import cn.hutool.core.collection.ListUtil;
import java.util.List;

/**
 * <AUTHOR>
 */
public enum PaymentApplyRecordExportEnums {
  /**
   * 常规导出
   */
  NORMAL_EXPORT(
      ListUtil.toList("付款申请单号", "申请类型", "申请金额", "供应商", "申请备注", "付款方式",
          "审核状态", "申请采购订单号", "进项发票号", "财务凭证号", "采购部门", "申请人",
          "申请时间", "上次审核时间"),
      "srm/model/%E4%BB%98%E6%AC%BE%E7%94%B3%E8%AF%B7%E5%88%97%E8%A1%A8.xlsx",
      "srm/upload/batch",
      "付款申请%s.xlsx"
  );

  PaymentApplyRecordExportEnums(List<String> tempFields, String tempFileUrl, String uploadPath,
      String fileName) {
    this.uploadPath = uploadPath;
    this.tempFields = tempFields;
    this.tempFileUrl = tempFileUrl;
    this.fileName = fileName;
  }

  public List<String> getTempFields() {
    return tempFields;
  }

  public String getTempFileUrl() {
    return tempFileUrl;
  }

  public String getFileName() {
    return fileName;
  }

  public String getUploadPath() {
    return uploadPath;
  }

  private List<String> tempFields;

  private String tempFileUrl;

  private String uploadPath;

  /**
   * 文件名
   */
  private String fileName;
}
