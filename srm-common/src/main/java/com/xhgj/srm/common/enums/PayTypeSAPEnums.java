package com.xhgj.srm.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
  *@ClassName PayTypeSAPEnums
  *<AUTHOR>
  *@Date 2024/1/5 11:01
*/
@Getter
@AllArgsConstructor
@SuppressWarnings("ALL")
public enum PayTypeSAPEnums {
  ALIPAY("A", "支付宝"),
  COMMERCIAL("F", "商业汇票"),
  GUARANTEE("G", "银行保函"),
  YKB("S", "报销"),
  DRAFT("B", "银行汇票"),
  CASH("E", "现金支付"),
  TRANSFER("T", "银行转账(含网银、转账支票、现金支票)"),
  IMPREST("U", "备用金"),
  OHTER("Z", "其它"),
  GO_PROCESS("K", "走流程"),
  CONSTRUCTION_RONGXIN("H", "电建融信"),
  CCB("I", "建信融通"),
  CHAINED_FINANCIALL("J", "供应链金融（工银e信）"),
  CHAINED_FINANCIALL_CLOUD("5", "供应链金融（中企云链）"),
  ;

  /**
   * 支付类型
   */
  private final String code;

  /**
   * 名称
   */
  private final String name;

  /**
   * 通过code获取枚举
   *
   * @param code 省份code
   * @return ProvinceEnum
   */
  public static String getNameByCode(String code) {
    for (PayTypeSAPEnums value : PayTypeSAPEnums.values()) {
      if(StrUtil.equals(value.getCode(),code)){
        return value.name;
      }
    }
    return null;
  }

  public static String getCodeByName(String name) {
    PayTypeSAPEnums[] values = PayTypeSAPEnums.values();
    for (PayTypeSAPEnums value : PayTypeSAPEnums.values()) {
      if(StrUtil.equals(value.getName(),name)){
        return value.code;
      }
    }
    return null;
  }

  public static PayTypeSAPEnums fromKey(String code) {
    for (PayTypeSAPEnums value : PayTypeSAPEnums.values()) {
      if(StrUtil.equals(value.getCode(), code)){
        return value;
      }
    }
    return null;
  }

  public static PayTypeSAPEnums fromName(String name) {
    for (PayTypeSAPEnums value : PayTypeSAPEnums.values()) {
      if(StrUtil.equals(value.getName(), name)){
        return value;
      }
    }
    return null;
  }

  /**
   * 合同导入付款方式常量-银行转账
   */
  public static final String CONTRACT_IMPORT_PAYMENT_METHOD_BANK_TRANSFER = "银行转账";

  /**
   * 合同导入付款方式常量-供应链金额
   */
  public static final String CONTRACT_IMPORT_PAYMENT_METHOD_CHAINED_FINANCIAL = "供应链金额";

  /**
   * 合同导入付款方式常量-供应链金融
   */
  public static final String CONTRACT_IMPORT_PAYMENT_METHOD_CHAINED_FINANCIAL_FINANCE = "供应链金融";

  /**
   * 合同导入付款方式常量-工银e信
   */
  public static final String CONTRACT_IMPORT_PAYMENT_METHOD_CHAINED_FINANCIAL_E_BANK = "工银e信";

  /**
   * 合同导入方式常量-其他
   */
  public static final String CONTRACT_IMPORT_PAYMENT_METHOD_OTHER = "其他";

  /**
   * 合同导入-正则常量-匹配x个月字符串
   */
  public static final String CONTRACT_IMPORT_MONTH_REGEX = ".*\\d+个月.*";


  /**
   * 通过name模糊查询获取枚举值
   */
  public static PayTypeSAPEnums getEnumForExcelImport(String name) {
    if (StrUtil.isBlank(name)) {
      return null;
    }
    // 1 .匹配是否有x几个月
    if (name.matches(CONTRACT_IMPORT_MONTH_REGEX)) {
      if (name.contains(COMMERCIAL.name)) {
        // 商业汇票
        return COMMERCIAL;
      } else if (name.contains(DRAFT.name)) {
        // 银行汇票
        return DRAFT;
      } else if (name.contains(CONTRACT_IMPORT_PAYMENT_METHOD_CHAINED_FINANCIAL) || name.contains(
          CONTRACT_IMPORT_PAYMENT_METHOD_CHAINED_FINANCIAL_FINANCE) || name.contains(
          CONTRACT_IMPORT_PAYMENT_METHOD_CHAINED_FINANCIAL_E_BANK)) {
        // 供应链金融、供应链金额、工银e信
        return CHAINED_FINANCIALL;
      }
    } else if (name.contains(CONTRACT_IMPORT_PAYMENT_METHOD_BANK_TRANSFER)) {
      // 2. 银行转账
      return TRANSFER;
    } else if (name.contains(OHTER.getName()) || name.contains(
        CONTRACT_IMPORT_PAYMENT_METHOD_OTHER)) {
      // 3. 其他
      return OHTER;
    }
    return null;
  }

  /**
   * 获取合同下载中付款方式
   * @param key 键
   * @param payTypeInput 付款方式输入
   * @return
   */
  public static String getContractDownloadPayType(String key, String payTypeInput) {
    // 如果是其他，则只返回payTypeInput
    if (StrUtil.equals(key, OHTER.getCode())) {
      return payTypeInput;
    }
    StringBuilder builder = new StringBuilder();
    builder.append(getNameByCode(key));
    if (StrUtil.isNotBlank(payTypeInput)) {
      builder.append(StrUtil.COMMA).append(payTypeInput).append("个月");
    }
    return builder.toString();
  }
}
