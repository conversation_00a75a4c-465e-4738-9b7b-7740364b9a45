package com.xhgj.srm.common.enums.landingContract;/**
 * @since 2025/1/3 8:48
 */

import com.xhgj.srm.common.enums.FileReviewStateEnum;

/**
 *<AUTHOR>
 *@date 2025/1/3 08:48:47
 *@description 合同状态
 */
public enum ContractStatus {
  /**
   * 生效
   */
  EFFECTIVE("1", "生效"),
  /**
   * 失效
   */
  INVALID("2", "失效"),
  /**
   * 无效
   */
  VOID("3", "无效");

  private String code;

  private String desc;

  ContractStatus(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public static ContractStatus getEnum(String code) {
    for (ContractStatus value : ContractStatus.values()) {
      if (value.getCode().equals(code)) {
        return value;
      }
    }
    return null;
  }

  public static ContractStatus getEnumByDesc(String desc) {
    for (ContractStatus value : ContractStatus.values()) {
      if (value.getDesc().equals(desc)) {
        return value;
      }
    }
    return null;
  }

  public static String getDescByCode(String code) {
    for (ContractStatus value : ContractStatus.values()) {
      if (value.getCode().equals(code)) {
        return value.getDesc();
      }
    }
    return null;
  }

  public String getCode() {
    return code;
  }

  public String getDesc() {
    return desc;
  }

  /**
   * 判断合同状态
   *
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  public static ContractStatus judgeStatus(Long startTime, Long endTime) {
   return judgeStatus(startTime, endTime, null);
  }

  /**
   * 判断合同状态
   * 附件审核通过且在合同有效期内为生效
   * 小于开始时间为无效
   * 大于结束时间为失效
   * 附件未审核通过为无效
   *
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  public static ContractStatus judgeStatus(Long startTime, Long endTime, String fileReviewState) {
    if (startTime == null || endTime == null) {
      return ContractStatus.VOID;
    }
    long now = System.currentTimeMillis();
    if (now < startTime) {
      return ContractStatus.VOID;
    }
    if (now > endTime) {
      return ContractStatus.INVALID;
    }
    if (FileReviewStateEnum.THROUGH_THE.getKey().equals(fileReviewState)) {
      return EFFECTIVE;
    }
    return ContractStatus.VOID;
  }
}
