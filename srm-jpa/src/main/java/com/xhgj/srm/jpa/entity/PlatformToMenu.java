package com.xhgj.srm.jpa.entity;

import com.xhgj.srm.common.Constants;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * Created by Geng Shy on 2023/10/23
 */
@Entity
@Table(name = "t_platform_to_menu")
@Data
public class PlatformToMenu {

  @Id
  @Column(name = "id")
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 平台id
   */
  @Column(name = "platform_id")
  private String platformId;

  /**
   * 菜单id
   */
  @Column(name = "menu_id")
  private String menuId;

  /**
   * 数据状态
   */
  @Column(name = "c_state")
  private String state = Constants.STATE_OK;
}
