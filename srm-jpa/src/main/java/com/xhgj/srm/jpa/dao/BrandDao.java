package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.Brand;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import org.springframework.data.domain.Page;

public interface BrandDao extends BootBaseDao<Brand> {

  /**
   * @Title: getBrandListBySid @Description:获取该供应商下所有管理品牌
   *
   * <AUTHOR>
   * @date 2019年8月6日上午11:18:27
   */
  List<Brand> getBrandListBySid(String sid);

  /**
   * 根据关联关系获取品牌列表
   *
   * @param relationId 关联 id
   * @param relationType 关联类型
   */
  List<Brand> getBrandListByRelationId(String relationId, String relationType);

  /**
   * @Title: getBrandListByFbid @Description:获取该供应商副本下所有管理品牌
   *
   * <AUTHOR>
   * @date 2019年8月6日上午11:18:27
   */
  List<Brand> getBrandListByFbid(String sid);

  /**
   * @Title: getCurBrandList @Description:获取该供应商下所有管理品牌名称(中/英)
   *
   * <AUTHOR>
   * @date 2019年8月6日上午11:18:27
   */
  List<String> getCurBrandList(String sid);

  /**
   * @Title: getCurBrandList @Description:获取该供应商下所有mdmid不为空的品牌名称(中/英)
   *
   * <AUTHOR>
   * @date 2019年8月6日上午11:18:27
   */
  List<String> getCurMdmBrandList(String sid);

  List<String> getCurBrandIdList(String sid, String brandName);

  List<Brand> getNoReasonBrandListByEnAndCn(String en, String cn);

  Brand getCurBrandByMdmId(String mdmid, String supplierId);

  List<Brand> getBrandListBySidAndMdmId(String sid, String mdmId);

  List<Brand> getBrandByEnAndCn(String en, String cn);

  /**
   * @Autho: liuyq @Date: 2022/8/21 17:24 根据品牌名称获取品牌mdmId为空的品牌列表
   *
   * @param en
   * @param cn
   * @return java.util.List<com.xhgj.srm.jpa.entity.Brand>
   */
  List<Brand> getBrandByEnAndCnAndBrandMdmIdIsNUll(String en, String cn);

  Brand getCurBrandByEnCnAndSid(String sid, String en, String cn);

  /**
   * 根据关联关系获取品牌
   *
   * @param relationId 关联 id
   * @param relationType 关联类型
   * @param en 英文名
   * @param cn 中文名
   */
  Brand getBrandByRelationId(String relationId, String relationType, String en, String cn);

  /**
   * 根据关键词搜索获取品牌列表
   *
   * @param supplierId
   * @param brandName
   * @param manageType
   * @param pageNo
   * @param pageSize
   * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.Brand> @Author:
   *     liuyq @Date: 2021/6/3 9:38
   */
  Page<Brand> getBrandPage(
      String supplierId,
      String brandName,
      String manageType,
      String isPermission,
      String pageNo,
      String pageSize);

  Brand getCurBrandByEnCn(String sid, String en, String cn);

  /**
   * 通过组织下供应商 id 查询品牌
   * @param supplierInGroupIdList 组织下供应商 id 集合 必传
   * @param brandName  品牌名称
   * @param manageType 经营类型
   * @param isPermission 是否上传授权书
   * @param pageNo 页码
   * @param pageSize 每页展示数量
   */
  Page<Object[]> getBrandPageBySupplierInGroupId(
      List<String> supplierInGroupIdList,
      String brandName,
      String manageType,
      String isPermission,
      int pageNo,
      int pageSize);

  /**
   * 通过品牌中英文名和供应商 id 获得品牌
   * @param brandEnName 品牌英文名 必传
   * @param brandCnName 品牌中文名 必传
   * @param supplierId 供应商 id 必传
   */
  Brand getFirstByPermissionIdAndBrandEnAndCnName(String brandEnName, String brandCnName, String supplierId);

  /**
   * 更新品牌至组织下供应商
   * @param supplierInGroupId
   * @param supplierId
   */
  void updateBrandSupplierInGroup(String supplierInGroupId,String supplierId);

  /**
   * 通过组织下供应商 id 和名称判断是否存在该品牌
   * @param supplierInGroupList 组织下供应商 id 集合 必传
   * @param type 品牌类型 必传
   * @param brandNameEn 品牌英文名称 必传
   * @param brandNameCn 品牌中文名称 必传
   * @return
   */
  boolean existBySupplierInGroupAndBrandName(List<String> supplierInGroupList, String type, String brandNameEn, String brandNameCn);

  /**
   * 通过 mdmId 和组织下供应商 id 获取品牌
   * @param mdmId mdmId 必传
   * @param supplierInGroupIdList 组织下供应商 id 必传
   */
  Brand getMdmIdAndSupplierIdList(String mdmId, List<String> supplierInGroupIdList);

  /**
   * 获取所有初始化品牌码分页
   * @param pageNo
   * @param pageSize
   * @return
   */
  Page<Brand> getAllInitializesBrandCodePage (
      int pageNo,
      int pageSize);
}
