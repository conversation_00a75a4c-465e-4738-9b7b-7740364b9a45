package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.Meeting;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;

import java.util.List;

public interface MeetingDao extends BootBaseDao<Meeting> {

     Page getMeetingPage(String groupId, String supplierName, String starttime, String endtime, String name, String personnel, int pageNo, int pageSize);

     long getRepeatMeetingCount(String name, String id);

     List<Meeting> getMeetingListByName(String name, String erpCode);

     /**
      * @Description 获取会议列表(下拉选择)
      * @Auther: liuyq
      * @Date: 2021/3/30 14:12
      * @param name
      * @param erpCode
      * @return java.util.List<com.xhiot.project.entity.Meeting>
      **/
     List<Meeting> getMeetingListByNameAndGroup(String name, String erpCode);

     /**
      *
      * @Title:
      * @Description:
      * @param
      * <AUTHOR>
      * @date 2021/6/28 17:16
      */
     List<Meeting> getMeetingListByLastWeek();
}
