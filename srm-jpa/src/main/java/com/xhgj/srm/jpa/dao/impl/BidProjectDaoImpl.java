package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.BidProjectDao;
import com.xhgj.srm.jpa.entity.BidProject;
import com.xhiot.boot.core.common.util.ObjectUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * BidProjectDaoImpl
 */
@Repository
public class BidProjectDaoImpl extends AbstractExtDao<BidProject> implements BidProjectDao {

  @Override
  public Page<BidProject> getBidProjectPage(String platformId, Integer pageNo, Integer pageSize) {
    StringBuilder sql = new StringBuilder("select distinct bp.* from t_bid_project bp ");
    sql.append("left join t_bid_project_to_platform ptpf on bp.id = ptpf.c_bid_project_id ");
    sql.append("where bp.c_state = ? ");
    Object[] params = new Object[]{Constants.STATE_OK};
    if (StrUtil.isNotBlank(platformId)) {
      sql.append(" and ptpf.c_platform_id = ? ");
      params = ObjectUtils.objectAdd(params, platformId);
    }
    sql.append(" order by bp.c_batch_year desc, bp.c_service_start_time desc, bp.c_project_name asc ");
    return findPageSql(sql.toString(), params, pageNo, pageSize);
  }

  @Override
  public List<BidProject> searchProjectNameByProjectCategory(String projectCategory) {
    StringBuilder sql = new StringBuilder("select distinct bp.* from t_bid_project bp ");
    sql.append("left join t_bid_project_to_platform ptpf on bp.id = ptpf.c_bid_project_id ");
    sql.append("left join t_platform p on ptpf.c_platform_id = p.id ");
    sql.append("where bp.c_state = ? and p.c_state = ? ");
    Object[] params = new Object[]{Constants.STATE_OK,Constants.STATE_OK};
    if (StrUtil.isNotBlank(projectCategory)) {
      sql.append("and p.c_project_category = ? ");
      params = ObjectUtils.objectAdd(params, projectCategory);
    }
    return getSqlList(sql.toString(), params);
  }
}
