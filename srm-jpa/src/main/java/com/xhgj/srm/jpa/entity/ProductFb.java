package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "t_product_fb")
@Data
@Deprecated
/**
 * 废弃的表
 */
public class ProductFb implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @JoinColumn(name = "productId")
    @ManyToOne
    private Product product;

    @Column(name = "c_productMdmId")
    private String productMdmId;

    @Column(name = "c_brandMdmId")
    private String brandMdmId;

    @Column(name = "c_brandname_en")
    private String brandnameEn;

    @Column(name = "c_brandname_cn")
    private String brandnameCn;

    @Column(name = "c_name")
    private String name;

    @Column(name = "c_code")
    private String code;

    @Column(name = "c_marketPrice")
    private Double marketPrice;

    @Column(name = "c_model")
    private String model;

    @Column(name = "c_basicUnit")
    private String basicUnit;

    @Column(name = "c_grossWeight")
    private String grossWeight;

    @Column(name = "c_netWeight")
    private String netWeight;

    @Column(name = "c_length")
    private String length;

    @Column(name = "c_width")
    private String width;

    @Column(name = "c_height")
    private String height;

    @Column(name = "c_info")
    private String info;

    @Column(name = "c_resource")
    private String resource;

    @Column(name = "c_remark")
    private String remark;

    @Column(name = "c_agreeResource")
    private String agreeResource;

    @Column(name = "c_createTime")
    private Long createTime;

    @Column(name = "c_state")
    private String state;

    @Column(name = "c_volume")
    private String volume;

    @Column(name = "c_firstCateMdmId")
    private String firstCateMdmId;

    @Column(name = "c_secondCateMdmId")
    private String secondCateMdmId;

    @Column(name = "c_thirdCateMdmId")
    private String thirdCateMdmId;

    @Column(name = "c_fourthCateMdmId")
    private String fourthCateMdmId;

    @Column(name = "c_fourthCateName")
    private String fourthCateName;

    @Column(name = "c_barCode")
    private String barCode;

    @Column(name = "c_purchasePrice")
    private Double purchasePrice;

}
