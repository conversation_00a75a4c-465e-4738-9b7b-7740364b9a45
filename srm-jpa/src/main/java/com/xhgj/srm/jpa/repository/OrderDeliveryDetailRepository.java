package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.OrderDeliveryDetail;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;

public interface OrderDeliveryDetailRepository extends BootBaseRepository<OrderDeliveryDetail, String> {

  /**
   * @param deliveryId 发货单id
   * @return List<OrderDeliveryDetail>
   */
  List<OrderDeliveryDetail> findAllByDeliveryIdAndState(String deliveryId, String state);

  List<OrderDeliveryDetail> findAllByDeliveryIdInAndState(List<String> deliveryIds, String state);
}
