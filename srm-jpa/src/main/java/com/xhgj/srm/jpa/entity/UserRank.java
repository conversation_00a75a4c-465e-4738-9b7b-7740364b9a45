package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

@Table(name = "t_user_rank")
@Entity
@Data
public class UserRank implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "rank")
    private Integer rank;

    @Column(name = "group_id")
    private String groupId;

    @Column(name = "c_create_time")
    private Long createTime;

    @Column(name = "c_state")
    private String state;


}
