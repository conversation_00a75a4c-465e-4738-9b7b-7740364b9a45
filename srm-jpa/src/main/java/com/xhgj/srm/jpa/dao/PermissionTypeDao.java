package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.PermissionType;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;

/**
 * <AUTHOR>
 * @ClassName UserPermissionDao
 **/
public interface PermissionTypeDao extends BootBaseDao<PermissionType> {

	/**
	 * 删除用户当前类型的数据权限
	 * @Author: liuyq
	 * @Date: 2022/7/8 10:11
	 * @param userId 用户id 必传
	 * @param type 数据权限类型 必传
	 * @return void
	 **/
	void deletePermissionByUserAndType(String userId, String type);


	/**
	 * 根据用户id和类型获取数据权限集合
	 * @Author: liuyq
	 * @Date: 2022/7/8 15:23
	 * @param userId 用户id 必传
	 * @param type 类型 必传 {@link com.xhgj.srm.common.Constants}
	 * @return PermissionType
	 **/
	PermissionType getUserToPermissionList(String userId, String type);
}
