package com.xhgj.srm.jpa.util;/**
 * @since 2025/2/13 17:28
 */

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/2/13 17:28:14
 *@description 自定义BigDecimal序列化
 */
public class BigDecimalToPlainSerializer implements ObjectSerializer {

  @Override
  public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
    BigDecimal value = Convert.toBigDecimal(object);
    if (value != null) {
      int scale = value.scale();
      serializer.write( scale >= -100 && scale < 100
          ? value.toPlainString()
          : value.toString());
    } else {
      serializer.writeNull();
    }
  }
}
