package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.InquiryDao;
import com.xhgj.srm.jpa.dto.inquiry.InquiryStatistics;
import com.xhgj.srm.jpa.entity.Inquiry;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import com.xhiot.boot.framework.jpa.util.HqlUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@Service
public class InquiryDaoImpl extends AbstractExtDao<Inquiry> implements InquiryDao {
  /**
   * Title：所有询价记录
   *
   * <p>Description:
   *
   * <p>
   *
   * @return
   * <AUTHOR>
   * @date 2019年8月15日 上午10:11:47
   */
  @Override
  public List<Inquiry> getAllInquiryList() {
    String hql = "from Inquiry e where e.state != ? order by e.createTime desc";
    Object[] params = new Object[] {Constants.STATE_DELETE};
    return getHqlList(hql, params);
  }

  @Override
  public long getInquiryCountByUser(String userId, String createCode) {
    String hql = "select count(e.id) from Inquiry e where e.state != ? ";
    Object[] params = new Object[] {Constants.COMMONSTATE_DELETE};
    if (!StringUtils.isNullOrEmpty(userId)) {
      hql += "and e.createMan = ? ";
      params = ObjectUtils.objectAdd(params, userId);
    }
    if (!StringUtils.isNullOrEmpty(createCode)) {
      hql += "and e.createCode = ? ";
      params = ObjectUtils.objectAdd(params, createCode);
    }
    return count(hql, params);
  }

  @Override
  public Inquiry getInquiryCountByExl(
      long inquirerTime,
      String enterpriseName,
      String brandname,
      String model,
      String num,
      String unit,
      String inquirer,
      String offererName,
      String offererPhone,
      String name) {
    String hql = "from Inquiry e where e.state != ? ";
    Object[] params = new Object[] {Constants.COMMONSTATE_DELETE};
    if (inquirerTime > 0) {
      hql += "and e.inquirerTime = ? ";
      params = ObjectUtils.objectAdd(params, inquirerTime);
    }
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      hql += "and e.enterpriseName = ? ";
      params = ObjectUtils.objectAdd(params, enterpriseName);
    }
    if (!StringUtils.isNullOrEmpty(name)) {
      hql += "and e.name = ? ";
      params = ObjectUtils.objectAdd(params, name);
    }
    if (!StringUtils.isNullOrEmpty(brandname)) {
      hql += "and e.brandnameCn = ? ";
      params = ObjectUtils.objectAdd(params, brandname);
    }
    if (!StringUtils.isNullOrEmpty(model)) {
      hql += "and e.model = ? ";
      params = ObjectUtils.objectAdd(params, model);
    }
    if (!StringUtils.isNullOrEmpty(num)) {
      hql += "and e.num = ? ";
      params = ObjectUtils.objectAdd(params, num);
    }
    if (!StringUtils.isNullOrEmpty(unit)) {
      hql += "and e.unit = ? ";
      params = ObjectUtils.objectAdd(params, unit);
    }
    if (!StringUtils.isNullOrEmpty(inquirer)) {
      hql += "and e.inquirer = ? ";
      params = ObjectUtils.objectAdd(params, inquirer);
    }
    if (!StringUtils.isNullOrEmpty(offererName)) {
      hql += "and e.offererName = ? ";
      params = ObjectUtils.objectAdd(params, offererName);
    }
    if (!StringUtils.isNullOrEmpty(offererPhone)) {
      hql += "and e.offererPhone = ? ";
      params = ObjectUtils.objectAdd(params, offererPhone);
    }
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public Inquiry getInquiryById(String id) {
    String hql = "from Inquiry i where i.state != ? and i.id = ? order by i.createTime desc";
    Object[] params = new Object[] {Constants.STATE_DELETE, id};
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public Page<Inquiry> getInquiryPageRef(Map<String, Object> queryMap) {
    StringBuilder hql = new StringBuilder("from Inquiry e ");
    List<Object> params = new ArrayList<>();
    buildWhereQuery(hql, params, queryMap);
    hql.append("order by e.createTime desc");
    return findPage(hql.toString(), params.toArray(), (Integer) queryMap.get("pageNo"), (Integer)queryMap.get("pageSize"));
  }

  @Override
  public List<Inquiry> getInquiryStatistics(Map<String, Object> queryMap) {
    StringBuilder hql = new StringBuilder("select e.id, e.marketPrice, e.salesPrice, e.transferPrice from Inquiry e ");
    List<Object> params = new ArrayList<>();
    buildWhereQuery(hql, params, queryMap);
    List<Object[]> hqlList = getHqlObjList(hql.toString(), params.toArray());
    return hqlList.stream().map(item -> {
      Inquiry inquiry = new Inquiry();
      inquiry.setId(Convert.toStr(item[0]));
      inquiry.setMarketPrice(Convert.toStr(item[1]));
      inquiry.setSalesPrice(Convert.toStr(item[2]));
      inquiry.setTransferPrice(Convert.toStr(item[3]));
      return inquiry;
    }).collect(Collectors.toList());
  }

  @Override
  public InquiryStatistics getInquiryStatistics2(Map<String, Object> queryMap) {
    StringBuilder hql = new StringBuilder();
    hql.append("select COALESCE(SUM(e.marketPrice), 0), COALESCE(SUM(e.salesPrice), 0), COALESCE(SUM(e.transferPrice), 0)  ")
        .append("from Inquiry e ");
    List<Object> params = new ArrayList<>();
    buildWhereQuery(hql, params, queryMap);
    Object[] hqlList = (Object[]) getUniqueHqlObj(hql.toString(), params.toArray());
    InquiryStatistics inquiryStatistics = new InquiryStatistics();
    inquiryStatistics.setMarketPrice(Convert.toBigDecimal(hqlList[0]));
    inquiryStatistics.setSalesPrice(Convert.toBigDecimal(hqlList[1]));
    inquiryStatistics.setTransferPrice(Convert.toBigDecimal(hqlList[2]));
    return inquiryStatistics;
  }

  private void buildWhereQuery(StringBuilder hql, List<Object> params, Map<String, Object> queryMap) {
    hql.append("where e.state != ? ");
    params.add(Constants.STATE_DELETE);
    if (!StrUtil.isBlankIfStr(queryMap.get("userGroup"))) {
      hql.append("and e.createCode = ? ");
      params.add(queryMap.get("userGroup"));
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("enterpriseName"))) {
      hql.append("and e.enterpriseName like ? ");
      params.add("%" + queryMap.get("enterpriseName") + "%");
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("productName"))) {
      hql.append("and e.name like ? ");
      params.add("%" + queryMap.get("productName") + "%");
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("brands"))) {
      hql.append("and e.brandnameCn like ? ");
      params.add("%" + queryMap.get("brands") + "%");
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("marketPrice"))) {
      hql.append("and e.marketPrice like ? ");
      params.add("%" + queryMap.get("marketPrice") + "%");
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("salesPrice"))) {
      hql.append("and e.salesPrice like ? ");
      params.add("%" + queryMap.get("salesPrice") + "%");
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("model"))) {
      hql.append("and e.model like ? ");
      params.add("%" + queryMap.get("model") + "%");
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("offererName"))) {
      hql.append("and e.offererName like ? ");
      params.add("%" + queryMap.get("offererName") + "%");
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("offererPhone"))) {
      hql.append("and e.offererPhone like ? ");
      params.add("%" + queryMap.get("offererPhone") + "%");
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("startDate"))) {
      hql.append("and e.inquirerTime >= ? ");
      params.add(DateUtils.parseNormalDateToTimeStamp(queryMap.get("startDate").toString()));
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("endDate"))) {
      hql.append("and e.inquirerTime < ? ");
      params.add(
          DateUtils.parseNormalDateToTimeStamp(queryMap.get("endDate").toString()) + 24 * 60 * 60 * 1000);
    }
    if (queryMap.get("userNameList") != null) {
      // 转换为List
      List<String> userNameList = Convert.toList(String.class, queryMap.get("userNameList"));
      if (CollUtil.isNotEmpty(userNameList)) {
        hql.append("and e.inquirer in (");
        for (int i = 0; i < userNameList.size(); i++) {
          if (i == 0) {
            hql.append("?");
          } else {
            hql.append(",?");
          }
          params.add(userNameList.get(i));
        }
        hql.append(") ");
      }
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("inquirer"))) {
      hql.append("and e.inquirer like ? ");
      params.add("%" + queryMap.get("inquirer") + "%");
    }
  }
}
