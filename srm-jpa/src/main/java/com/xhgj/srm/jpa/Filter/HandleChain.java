package com.xhgj.srm.jpa.Filter;

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.jpa.entity.Order;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by Geng Shy on 2023/10/31
 */
public class HandleChain {

  private List<OfflinePaymentHandle> handles = new ArrayList<>();

  private static HandleChain instance = new HandleChain().add(new OfflinePaymentPlatformHandle())
      .add(new OfflinePaymentSupplierAndPlatformHandle()).add(new OfflinePaymentSupplierHandle());

  private HandleChain() {
  }

  private HandleChain add(OfflinePaymentHandle handle) {
    handles.add(handle);
    return this;
  }

  public boolean doHandle(Order order) {
    Assert.notEmpty(handles);
    Set<Boolean> results = new HashSet<>();
    handles.forEach(handle -> {
      results.add(handle.doHandle(order));
    });
    boolean contains = results.contains(true);
    //包含未通过的处理器
    if (contains) {
      return true;
    }
    return false;
  }

  public static HandleChain getOfflinePaymentInstance() {
    return instance;
  }
}
