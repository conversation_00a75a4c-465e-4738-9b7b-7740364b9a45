package com.xhgj.srm.jpa.annotations;

import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * 默认搜索方案注解<br>
 * 使用此注解时，请确保表单继承自 {@link BaseDefaultSearchSchemeForm} 类。
 * <p>
 * 例如：
 * <pre>
 * {@code
 * public class MySearchForm implements BaseDefaultSearchSchemeForm {
 *     // 表单字段和方法
 * }
 * }
 * </pre>
 * </p>
 * <p>
 * 该注解用于定义搜索方案的类型。
 * </p>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface DefaultSearchScheme {
  /**
   * 搜索方案类型
   */
  String searchType();
}