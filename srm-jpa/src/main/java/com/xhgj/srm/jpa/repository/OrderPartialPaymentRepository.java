package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.OrderPartialPayment;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderPartialPaymentRepository extends BootBaseRepository<OrderPartialPayment, String> {

  List<OrderPartialPayment> findAllByOrderIdInAndStateOrderByCreateTimeDesc(List<String> orderIds, String state);

  boolean existsByOrderIdAndState(String orderId, String state);

  boolean existsByOrderIdAndPaymentIdAndState(String orderId, String paymentId, String state);

  List<OrderPartialPayment> findAllByOrderIdAndState(String orderId, String state);

  /**
   * 查询支付ID为空且状态为指定值且支付类型在指定范围内的部分付款记录
   * @param state 状态
   * @param orderPaymentTypes 支付类型列表
   * @return 符合条件的部分付款记录列表
   */
  List<OrderPartialPayment> findAllByPaymentIdIsNullAndStateAndOrderPaymentTypeIn(String state, List<String> orderPaymentTypes);
}
