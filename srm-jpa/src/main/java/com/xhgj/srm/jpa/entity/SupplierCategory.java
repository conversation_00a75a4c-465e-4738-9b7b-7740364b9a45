package com.xhgj.srm.jpa.entity;/**
 * @since 2024/12/16 19:13
 */

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 *<AUTHOR>
 *@date 2024/12/16 19:13:44
 *@description 供应商关联类目
 */
@Data
@Entity
@Table(name = "t_supplier_category")
public class SupplierCategory {
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 供应商id
   */
  @Column(name = "c_supplier_id", length = 32)
  private String supplierId;

  /**
   * 供应商组织内id
   */
  @Column(name = "c_supplier_in_group_id", length = 32)
  private String supplierInGroupId;

  /**
   * 类目编码
   */
  @Column(name = "c_category_code", length = 100)
  private String categoryCode;

  /**
   * 类目名称
   */
  @Column(name = "c_category_name", length = 200)
  private String categoryName;

  /**
   * 类目路径
   */
  @Column(name = "c_category_path", length = 300)
  private String categoryPath;

  /**
   * 类目路径名称
   */
  @Column(name = "c_category_path_name", length = 800)
  private String categoryPathName;

  /**
   * 排序
   */
  @Column(name = "c_sort")
  private Integer sort;

  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 创建人
   */
  @Column(name = "c_create_man", length = 32)
  private String createMan;

  /**
   * 更新时间
   */
  @Column(name = "c_update_time")
  private Long updateTime;

  /**
   * 更新人
   */
  @Column(name = "c_update_man", length = 32)
  private String updateMan;

  /**
   * 状态 1 启用 0 禁用
   */
  @Column(name = "c_state", length = 1)
  private String state;
}
