package com.xhgj.srm.jpa.entity;


import com.xhgj.srm.common.Constants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import org.hibernate.annotations.Where;

@ApiModel("任务中心")
@Data
@NoArgsConstructor
@Entity
@Table(name = "t_mission")
@Where(clause = "c_state <> " + Constants.MISSION_STATE_DEL)
public class Mission {

    // Fields
    @ApiModelProperty(notes = "任务id")
    @Id
    @Column(name = "id", insertable = false, nullable = false)
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;


    @ApiModelProperty(notes = "操作类型")
    @Column(name = "c_type")
    private String type;

    @ApiModelProperty(notes = "任务编码")
    @Column(name = "c_code")
    private String code;

    @ApiModelProperty(notes = "总行数")
    @Column(name = "c_total_row")
    private int totalRow;

    @ApiModelProperty(notes = "开始时间")
    @Column(name = "c_create_time")
    private long createTime;

    @ApiModelProperty(notes = "开始时间")
    @Column(name = "c_start_time")
    private long startTime;

    @ApiModelProperty(notes = "完成时间")
    @Column(name = "c_complete_time")
    private long completeTime;

    @ApiModelProperty(notes = "创建人")
    @Column(name = "c_create_man")
    private String createMan;

    @ApiModelProperty(notes = "创建人id")
    @Column(name = "c_create_man_id")
    private String createManId;

    @ApiModelProperty(notes = "文件名")
    @Column(name = "c_file_name")
    private String fileName;

    @ApiModelProperty(notes = "文件链接")
    @Column(name = "c_link")
    private String link;

    @ApiModelProperty(notes = "失败原因")
    @Column(name = "c_reason")
    private String reason;

    @ApiModelProperty(notes = "失败文件链接")
    @Column(name = "c_fail_link")
    private String failLink;

    @ApiModelProperty(notes = "状态")
    @Column(name = "c_state")
    private String state;

    @ApiModelProperty(notes = "来源")
    @Column(name = "c_resource")
    private String resource;

  /**
   * 创建任务 初始化创建时间
   * @param code 任务编码
   * @param type 任务类型
   * @param createManId 创建人id
   * @param resource 任务来源
   * @return
   */
  public static Mission createStartingMission(String code, String type, String createManId, String resource) {
      Mission mission = new Mission();
      mission.setCreateTime(System.currentTimeMillis());
      mission.setCode(code);
      mission.setType(type);
      mission.setCreateManId(createManId);
      mission.setResource(resource);
      mission.setState(Constants.MISSION_STATE_ING);
      return mission;
  }

  /**
   * 创建任务 初始化创建时间
   * @param code 任务编码
   * @param type 任务类型
   * @param createManId 创建人id
   * @param resource 任务来源
   * @param fileName 文件名
   * @param fileLink 文件链接
   * @return
   */
  public static Mission createStartingMission(String code, String type, String createManId,
      String resource, String fileName, String fileLink) {
    Mission mission= createStartingMission(code,type,createManId,resource);
    mission.setFileName(fileName);
    mission.setLink(fileLink);
    return mission;
  }

}
