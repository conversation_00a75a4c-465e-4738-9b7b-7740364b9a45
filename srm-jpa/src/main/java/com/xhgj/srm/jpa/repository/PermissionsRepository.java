package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.GrpPermissions;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;

/**
 * Created by Geng Shy on 2023/10/23
 */
public interface PermissionsRepository extends BootBaseRepository<GrpPermissions, String> {

  /**
   * 根据数据状态查询
   * @param state 数据状态
   * @return List<GrpPermissions>
   */
  List<GrpPermissions> findAllByState(String state);

  List<GrpPermissions> findAllByNameInAndState(List<String> nameList,String state);
}
