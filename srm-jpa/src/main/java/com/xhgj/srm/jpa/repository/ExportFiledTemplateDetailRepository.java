package com.xhgj.srm.jpa.repository;



import com.xhgj.srm.jpa.entity.ExportFiledTemplateDetail;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.Optional;

/**
 * (ExportFiledTemplateDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-24 09:15:18
 */
public interface ExportFiledTemplateDetailRepository extends BootBaseRepository<ExportFiledTemplateDetail,String> {


  Optional<ExportFiledTemplateDetail> getFirstByFiledBaseIdAndTemplateId(String filedBaseId,
      String templateId);
}


