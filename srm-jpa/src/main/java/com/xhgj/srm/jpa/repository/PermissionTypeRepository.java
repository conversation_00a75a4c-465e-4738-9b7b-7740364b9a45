package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.PermissionType;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;

public interface PermissionTypeRepository extends BootBaseRepository<PermissionType, String> {

  /**
   *  @Author: liuyq @Date: 2022/8/17 17:36 根据用户和数据范围/操作范围类型获取权限编码
   *
   * @param userId 用户id
   * @param type 权限类型
   * @return com.xhgj.srm.jpa.entity.PermissionType
   */
  PermissionType getPermissionTypeByUserIdAndType(String userId, String type);

  PermissionType findFirstByUserIdAndTypeAndState(String userId, String type,
      String state);

  List<PermissionType> findAllByUserIdAndTypeAndState(String userId, String type,
      String state);
}