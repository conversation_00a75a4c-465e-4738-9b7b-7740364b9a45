package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import java.util.Optional;

public interface OrderDetailRepository extends BootBaseRepository<OrderDetail, String> {

  /**
   * @param erpRowId erp物料行id
   * @param state 数据状态
   * @return OrderDetail
   */
  Optional<OrderDetail> findFirstByErpRowIdAndState(String erpRowId, String state);

  /**
   * @param orderId 订单id
   * @param state 数据状态
   * @return 订单详情集合
   */
  List<OrderDetail> findAllByOrderIdAndState(String orderId, String state);
}
