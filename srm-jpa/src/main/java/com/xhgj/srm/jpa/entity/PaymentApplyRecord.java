package com.xhgj.srm.jpa.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.PaymentAuditStateEnum;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
  *@ClassName PaymentApplyRecord
  *<AUTHOR>
  *@Date 2023/12/28 16:14
*/
@Data
@NoArgsConstructor
@Entity
@Table(name = "t_payment_apply_record")
@Builder
@AllArgsConstructor
public class PaymentApplyRecord {
  /**
   * 主键
   */
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 申请单号
   */
  @Column(name = "c_payment_apply_no")
  private String paymentApplyNo;

  /**
   * {@link com.xhgj.srm.common.enums.PaymentApplyTypeEnums}
   */
  @Column(name = "c_apply_type")
  private String applyType;

  /**
   * 驳回理由
   */
  @Column(name = "c_reject_reason")
  private String rejectReason;

  /**
   * 申请订单号（多个，逗号分隔）
   */
  @Column(name = "c_supplier_order_no")
  private String supplierOrderNo;

  /**
   * 进项票发票号（多个，逗号分隔）
   */
  @Column(name = "c_invoice_number")
  private String invoiceNumber;

  /**
   * 申请人
   */
  @Column(name = "c_apply_man")
  private String applyMan;

  /**
   * 申请时间（以毫秒为单位）
   */
  @Column(name = "c_apply_time")
  private Long applyTime;

  /**
   * 审核时间（以毫秒为单位）
   */
  @Column(name = "c_audit_time")
  private Long auditTime;

  /**
   * 修改时间（以毫秒为单位）
   */
  @Column(name = "c_update_time")
  private Long updateTime;

  /**
   * 数据状态
   */
  @Column(name = "c_state")
  private String state;

  /**
   * 申请状态  1 审核中 2 通过 3 驳回 4 已放弃
   * {@link PaymentAuditStateEnum}
   */
  @Column(name = "c_apply_state")
  private String applyState;

  /**
   * 组织编码code
   */
  @Column(name = "c_group_code")
  private String groupCode;

  /**
   * 期初订单号
   */
  @Column(name = "c_initial_order")
  private Boolean initialOrder;

  /**
   * 同步sap成功时间
   */
  @Column(name = "c_sync_sap_time")
  private Long syncSapTime;

  /**
   * 审批实例id
   */
  @Column(name = "c_review_id")
  private String reviewId;

  // todo 可能有问题需要修改
  public void makeGroupCode(SupplierOrderRepository supplierOrderRepository, String groupCode) {
    if (Boolean.TRUE.equals(initialOrder)) {
      this.groupCode = groupCode;
    } else {
      List<String> supplierOrderNoList = this.getSupplierOrderNoList();
      // 通过订单号获取采购订单(取第一条数据)
      if (CollUtil.isNotEmpty(supplierOrderNoList)) {
        SupplierOrder supplierOrder =
            supplierOrderRepository.findFirstByCodeAndState(supplierOrderNoList.get(0),
                Constants.STATE_OK);
        if (supplierOrder != null) {
          this.groupCode = supplierOrder.getGroupCode();
        }
      }
    }
  }

  public List<String> getSupplierOrderNoList() {
    if (StrUtil.isBlank(supplierOrderNo)) {
      return new ArrayList<>();
    }
    return new ArrayList<>(Arrays.asList(supplierOrderNo.split(",")));
  }

}
