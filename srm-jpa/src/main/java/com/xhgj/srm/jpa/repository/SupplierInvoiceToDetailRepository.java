package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.SupplierInvoiceToDetail;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.List;

public interface SupplierInvoiceToDetailRepository extends BootBaseRepository<SupplierInvoiceToDetail, String> {
  /**
   * 根据进项票id删除关联数据
   * @param inputInvoiceOrderId
   */
  void deleteByInputInvoiceOrderId(String inputInvoiceOrderId);

  /**
   * 根据进项票id查询关联数据
   * @param inputInvoiceOrderId
   * @return
   */
  List<SupplierInvoiceToDetail> findAllByInputInvoiceOrderId(String inputInvoiceOrderId);


  List<SupplierInvoiceToDetail> findAllByDetailIdIn(List<String> detailIds);
}