package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.Mission;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;

public interface MissionDao extends BootBaseDao<Mission> {

    Page<Mission> getMissionPage(String type , String state,String startDate, String endDate, String code,
        String resource,String createManId, Integer curpage, Integer pagesize);

}
