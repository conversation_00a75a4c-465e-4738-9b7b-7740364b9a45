package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.SupplierTemplateField;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/6 14:28
 */
public interface SupplierTemplateFieldDao extends BootBaseDao<SupplierTemplateField> {

  /**
   * 根据模板的 id 查询供应商模板的字段配置
   * @param supplierTempId 模板 id 必传
   */
  List<SupplierTemplateField> getSupplierTemplateFieldBySupplierTempId(String supplierTempId);

  /**
   * 根据组织编码和供应商类型获得这些字段是否必填
   * @param templateId 模板 id
   * @param type 模板类型
   */
  List<SupplierTemplateField> getSupplierTemplateFieldByTemplateIdAndType(String templateId, String type);
}
