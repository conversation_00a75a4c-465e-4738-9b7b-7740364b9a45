package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 用户数据权限类型表
 */
@Entity
@Data
@Table(name = "t_permission_type")
public class PermissionType implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@Column(name = "id", nullable = false)
	@GeneratedValue(generator = "system-uuid")
	@GenericGenerator(name = "system-uuid", strategy = "uuid")
	private String id;

	/**
	 * 权限编码
	 */
	@Column(name = "c_permission_code")
	private String permissionCode;

	/**
	 * 权限类型
	 */
	@Column(name = "c_type")
	private String type;

	/**
	 * 用户id
	 */
	@Column(name = "user_id")
	private String userId;

	/**
	 * 数据状态
	 */
	@Column(name = "c_state")
	private String state;

	/**
	 * 创建时间
	 */
	@Column(name = "c_create_time")
	private Long createTime;

}
