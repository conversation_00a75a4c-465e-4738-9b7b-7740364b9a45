package com.xhgj.srm.jpa.dto;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
  *@ClassName DrawApplyAddParams
  *<AUTHOR>
  *@Date 2024/1/2 8:41
*/
@Data
public class  DrawApplyAddParams {
  /**
   * 申请id
   */
  @ApiModelProperty("申请id")
  private String id;

  /**
   * 财务凭证id
   */
  @ApiModelProperty("财务凭证id")
  private List<String> financialVouchersId;
  /**
   * 提款总金额
   */
  @ApiModelProperty("提款总金额")
  private String drawMoney;
  /**
   * 付款方式
   */
  @ApiModelProperty("付款方式")
  private String payType;

  /**
   * 付款方式描述
   * 付款方式为其他时填写
   */
  @ApiModelProperty("付款方式描述")
  private String payDesc;

  /**
   * 开户行
   */
  @ApiModelProperty("开户行")
  private String bank;

  /**
   * 银行账号
   */
  @ApiModelProperty("银行账号")
  private String bankAccount;

  /**
   * 联行号
   */
  @ApiModelProperty("联行号")
  private String bankCode;

  /**
   * 账户名称
   */
  @ApiModelProperty("账户名称")
  private String accountName;

  /**
   * 备注
   */
  @ApiModelProperty("备注")
  private String remark;
  /**
   * 申请人
   */
  @ApiModelProperty("申请人")
  private String applyMan;

  /**
   * 申请人编码
   */
  @ApiModelProperty("申请人编码")
  private String applyManCode;

  /**
   * 本次操作金额
   */
  @ApiModelProperty("本次操作金额")
  private String thisAmount;
  /**
   * 期望付款日期
   */
  @ApiModelProperty("期望付款日期")
  private Long desireDate;

  @ApiModelProperty("凭证类型 0 提款 1 退款")
  private String financialType;

  /**
   * 购买账号（一次性供应商使用到的字段）
   */
  @ApiModelProperty("购买账号（一次性供应商使用到的字段）")
  private String purchaseAccount;

  /**
   * 订单号（一次性供应商使用到的字段）
   */
  @ApiModelProperty("订单号（一次性供应商使用到的字段）")
  private String orderNo;

  /**
   * 物料名称（一次性供应商使用到的字段）
   */
  @ApiModelProperty("物料名称（一次性供应商使用到的字段）")
  private String productName;

  /**
   * 订单链接（一次性供应商使用到的字段）
   */
  @ApiModelProperty("订单链接（一次性供应商使用到的字段）")
  private String orderLink;

  /**
   * 申请人组织
   */
  @ApiModelProperty("申请人组织")
  private String userGroup;

  /**
   * 供应商id
   */
  @ApiModelProperty("供应商id")
  private String supplierId;

  /**
   * 获取付款描述(去掉前后空格)
   * @return
   */
  public String getPayDesc() {
    return StrUtil.trim(payDesc);
  }
}
