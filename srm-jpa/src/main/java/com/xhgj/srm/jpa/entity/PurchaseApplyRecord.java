package com.xhgj.srm.jpa.entity;/**
 * @since 2025/4/21 13:37
 */

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/4/21 13:37:43
 *@description
 */
@Entity
@Table(name = "t_purchase_apply_record")
@Data
public class PurchaseApplyRecord {
  /**
   * 唯一id
   */
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "snowflake-id")
  @GenericGenerator(name = "snowflake-id", strategy = "com.xhgj.srm.jpa.util.SnowflakeIdGenerator")
  private String id;

  /**
   * 采购申请单id
   */
  @Column(name = "c_purchase_apply_id")
  private String purchaseApplyId;

  /**
   * 审核唯一id
   */
  @Column(name = "c_review_id")
  private String reviewId;

  /**
   * 审核时间
   */
  @Column(name = "c_review_time")
  private Long reviewTime;

  /**
   * 审核原因
   */
  @Column(name = "c_review_reason")
  private String reviewReason;

  /**
   * 审核状态 1 审核中、2 通过 、-1驳回
   * @see com.xhgj.srm.common.enums.purchaseApplyForOrder.PurchaseApplyRecordStatus
   */
  @Column(name = "c_status")
  private Byte status;

  /**
   * 来源 SRM，SAP
   */
  @Column(name = "c_source")
  private String source;

  /**
   * 变动内容，格式为{"字段名": {"old": "旧值", "new": "新值", "desc":"字段描述"}}
   */
  @Column(name = "c_changes")
  private String changes;

  /**
   * 变动人
   */
  @Column(name = "c_change_user")
  private String changeUser;

  /**
   * 修改人工号
   */
  @Column(name = "c_change_user_id")
  private String changeUserId;

  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 数据状态
   */
  @Column(name = "c_state")
  private String state;

  /**
   * 获取变动列表
   * @return
   */
  public List<PurchaseApplyRecordJson> getChangeList() {
    if (StrUtil.isBlank(changes)) {
      return new ArrayList<>();
    }
    return JSON.parseArray(changes, PurchaseApplyRecordJson.class);
  }

  /**
   * 获取仅前端展示的变动列表
   */
  public List<PurchaseApplyRecordJson> getShowChangeList() {
    List<PurchaseApplyRecordJson> changeList = getChangeList();
    // 过滤出showFlag为true的项
    return changeList.stream().filter(item -> Boolean.TRUE.equals(item.getShowFlag()))
        .collect(Collectors.toList());
  }

  /**
   * 变动记录
   */
  @Data
  public static class PurchaseApplyRecordJson {
    /**
     * 字段key
     * @see com.xhgj.srm.jpa.enums.PurchaseApplyRecordFields
     */
    private String key;

    /**
     * 字段描述
     */
    private String desc;

    /**
     * 旧值
     */
    private String oldValue;

    /**
     * 新值
     */
    private String newValue;

    /**
     * 反射字段
     */
    private String fieldName;

    /**
     * 是否前端展示
     */
    private Boolean showFlag;
  }
}
