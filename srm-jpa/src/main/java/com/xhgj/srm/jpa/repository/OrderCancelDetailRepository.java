package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.OrderCancelDetail;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;

public interface OrderCancelDetailRepository extends BootBaseRepository<OrderCancelDetail, String> {

  /**
   * 根据取消单id查询取消单明细
   * @param cancelId
   * @return
   */
  List<OrderCancelDetail> findAllByCancelIdAndState(String cancelId, String state);

  /**
   * 根据取消单id查询取消单明细
   * @param cancelIds
   * @return
   */
  List<OrderCancelDetail> findAllByCancelIdInAndState(List<String> cancelIds, String state);
}