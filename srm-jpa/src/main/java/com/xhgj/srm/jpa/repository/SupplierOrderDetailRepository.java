package com.xhgj.srm.jpa.repository;


import com.xhgj.srm.jpa.dto.purchase.order.SupplierOrder2DetailTemp;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.Query;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/28 15:22
 */
public interface SupplierOrderDetailRepository extends BootBaseRepository<SupplierOrderDetail,String> {

  /**
   * 通过 erp id 获得该行的订单明细
   * @param erpId erpId 必传
   * @param orderToFormId 关联的表单 id 必传
   * @param state 数据状态 必传
   */
  SupplierOrderDetail getFirstByErpIdAndOrderToFormIdAndState(String erpId,String orderToFormId,String state);


  /**
   * 通过关联的表单 id 获得详情
   * @param orderToFormId 关联的表单 id 必传
   * @param state 数据状态 id 必传
   */
  List<SupplierOrderDetail> getAllByOrderToFormIdAndStateOrderBySortNumAsc(String orderToFormId,String state);

  /**
   * 根据订单详情表单id查询
   * @param orderToFormId 订单关联发货表数据id
   * @return 供应商订单发货明细集合
   */
  List<SupplierOrderDetail> findByOrderToFormIdAndState(String orderToFormId, String state);

  /**
   * 根据订单详情表单id查询
   * @param orderToFormIds 订单关联发货表数据id
   * @return 供应商订单发货明细集合
   */
  List<SupplierOrderDetail> findByOrderToFormIdInAndState(List<String> orderToFormIds, String state);

  /**
   * @param purchaseApplyForOrderId 采购申请单id
   * @param state 数据状态
   */
  List<SupplierOrderDetail> findAllByPurchaseApplyForOrderIdAndState(String purchaseApplyForOrderId,
      String state);

  /**
   * @param purchaseApplyForOrderId 采购申请单id
   */
  List<SupplierOrderDetail> findAllByPurchaseApplyForOrderId(String purchaseApplyForOrderId);

  /**
   * @param entrustDetailId 委外加工id
   * @param state 数据状态
   */
  List<SupplierOrderDetail> findAllByEntrustDetailIdAndState(String entrustDetailId, String state);


  /**
   * 根据订单详情表单id查询
   * @param orderToFormId 订单关联发货表数据id
   * @return 供应商订单发货明细集合
   */
  SupplierOrderDetail findByOrderToFormIdAndStateAndSortNum(String orderToFormId,
      String state,Integer SortNum);

  SupplierOrderDetail getFirstByInWareHouseIdAndDetailedId(String inWareHouseId, String detailedId);

  /**
   * 根据采购订单号查询
   * @param purchaseOrderId 采购订单号
   * @param state 数据状态
   */
  List<SupplierOrderDetail> findAllByPurchaseOrderIdAndState(String purchaseOrderId, String state);

  /**
   * 根据采购订单号查询
   * @param purchaseOrderId 采购订单号
   * @param state 数据状态
   */
  List<SupplierOrderDetail> findAllByPurchaseOrderIdInAndState(List<String> purchaseOrderId, String state);

  SupplierOrderDetail findFirstByDetailedIdAndState(String detailedId, String state);

  /**
   * 根据单据 id 获取详情数量
   * @param orderToFormId 单据 id 必传
   * @param state 数据状态
   * @return
   */
  long countByOrderToFormIdAndState(String orderToFormId, String state);

  SupplierOrderDetail getFirstByOrderToFormIdAndDetailedIdAndState(String orderToFormId,
      String detailedId, String state);

  /**
   * 通过sapRowId 获得该行的订单明细
   * @param sapRowId sapRowId 必传
   * @param orderToFormId 关联的表单 id 必传
   * @param state 数据状态 必传
   */
  SupplierOrderDetail getFirstBySapRowIdAndOrderToFormIdAndState(String sapRowId,
      String orderToFormId,String state);

  /**
   * 查找最新一条采购申请单的订单明细
   *
   * @param purchaseApplyForOrderId 采购申请单id
   * @param state 数据状态
   */
  SupplierOrderDetail getFirstByPurchaseApplyForOrderIdAndStateOrderByCreateTimeDesc(
      String purchaseApplyForOrderId, String state);


  /**
   * @description: 获取所有详情通过关联的表单id
   * @param detailedFormIds 关联的表单 ids
   * @param state 数据状态
   */
  List<SupplierOrderDetail> findAllByOrderToFormIdInAndState(List<String> detailedFormIds,
      String state);

  List<SupplierOrderDetail> findAllByDetailedIdAndState(String detailId, String state);

  List<SupplierOrderDetail> findAllByDetailedIdInAndState(List<String> detailIds, String state);

  @Query(value = "select so as supplierOrder, sod as supplierOrderDetail " +
      "from SupplierOrderDetail sod " +
      "left join SupplierOrderToForm sof on sod.orderToFormId = sof.id " +
      "left join SupplierOrder so on sof.supplierOrderId = so.id " +
      "where sod.state = '1' and so.state in ('1','2') and sof.state = '1' " +
      "and so.id in :orderIds and sof.type = :orderFormType")
  List<SupplierOrder2DetailProjection> getDetailsByOrderIds2(List<String> orderIds,
      String orderFormType);

  // 接口定义
  interface SupplierOrder2DetailProjection {
    SupplierOrder getSupplierOrder();
    SupplierOrderDetail getSupplierOrderDetail();
  }


  /**
   * 根据采购申请单id查询
   * @param purchaseApplyForOrderIds
   * @param stateOk
   * @return
   */
  List<SupplierOrderDetail> findAllByPurchaseApplyForOrderIdInAndState(List<String> purchaseApplyForOrderIds, String stateOk);

  /**
   * 旧数据处理 7.1.2也需要跑
   * @param pageRequest
   * @return
   */
  @Query(
      value = "select detail from SupplierOrderDetail detail "
          +"left join SupplierOrderProduct product on detail.orderProductId = product.id "
          + "left join SupplierOrderToForm form on detail.orderToFormId = form.id "
          + "left join SupplierOrder tor on form.supplierOrderId = tor.id "
          + "where detail.state = '1' and form.state = '1' and tor.state = '1' "
          + "and (product.soldToParty is null or detail.projectName is null) and detail.salesOrderNo is not null and "
          + "detail.projectNo is not null and tor.selfState = true "
  )
  Page<SupplierOrderDetail> getOldData(PageRequest pageRequest);


  /**
   * 旧数据处理 7.1.2也需要跑
   * @param pageRequest
   * @return
   */
  @Query(
      value = "select detail from SupplierOrderDetail detail "
          +"left join SupplierOrderProduct product on detail.orderProductId = product.id "
          + "left join SupplierOrderToForm form on detail.orderToFormId = form.id "
          + "left join SupplierOrder tor on form.supplierOrderId = tor.id "
          + "where detail.state = '1' and form.state = '1' and tor.state = '1' "
          + "and detail.purchaseApplyForOrderId is not null and (product.makeManName is null or "
          + "product.makeManName = '' )"
  )
  Page<SupplierOrderDetail> getOldData2(PageRequest pageRequest);
}
