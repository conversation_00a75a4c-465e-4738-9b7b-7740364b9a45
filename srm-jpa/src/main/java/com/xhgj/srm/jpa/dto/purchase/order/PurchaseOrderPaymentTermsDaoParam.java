package com.xhgj.srm.jpa.dto.purchase.order;

import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.jpa.dto.BasePageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Set;

@Data
public class PurchaseOrderPaymentTermsDaoParam extends BasePageParam {

  @ApiModelProperty("创建时间 【开始】")
  private Long startCreateTime;

  @ApiModelProperty("创建时间 【结束】")
  private Long endCreateTime;

  @ApiModelProperty("查询未签收的订单")
  private Boolean selectUnReceipt;

  @ApiModelProperty("是否挂起")
  private Boolean pending;

  @ApiModelProperty("是否暂存")
  private Boolean staging;

  @ApiModelProperty("是否审核中")
  private Boolean unaudited;

  @ApiModelProperty("是否驳回")
  private Boolean reject;

  @ApiModelProperty("采购部门")
  private String purchaseDept;

  @ApiModelProperty("采购员")
  private String purchaseMan;

  @ApiModelProperty("付款条件")
  private Set<String> paymentTerms;

  @ApiModelProperty("预计付款日期 - 开始")
  private Long expectedPaymentDateStart;

  @ApiModelProperty("预计付款日期 - 结束")
  private Long expectedPaymentDateEnd;

  @ApiModelProperty("采购订单号")
  private String orderCode;

  @ApiModelProperty("订单状态")
  private SupplierOrderState orderState;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("是否厂家直发")
  private Boolean directShipment;

  @ApiModelProperty("是否有待确认")
  private Boolean confirmState;

  @ApiModelProperty("是否有取消")
  private Boolean cancelState;

  @ApiModelProperty("是否有退货")
  private Boolean returnState;

  @ApiModelProperty("是否有拒单")
  private Boolean refuseState;

  @ApiModelProperty("是否有发货单待入库")
  private Boolean shipWaitStock;

  @ApiModelProperty("采购组织")
  private String purchaseGroupName;

  @ApiModelProperty("收件人")
  private String receiveMan;

  @ApiModelProperty("开票状态")
  private String supplierOpenInvoiceState;

  @ApiModelProperty("销售订单号")
  private String salesOrderNo;

  @ApiModelProperty("大票项目号")
  private String largeTicketProjectNumbers;

  @ApiModelProperty("大票项目名称")
  private String largeTicketProjectName;
  private SupplierOrderFormType supplierOrderFormType;
  private SupplierOrderFormStatus supplierOrderFormStatus;
  private String purchaseId;
  private String createMan;
  @ApiModelProperty("预付标签")
  private boolean paymentInAdvance;
  @ApiModelProperty("应付标签")
  private boolean accountsPayable ;
  /**
   * 用户组
   */
  private String userGroup;
}
