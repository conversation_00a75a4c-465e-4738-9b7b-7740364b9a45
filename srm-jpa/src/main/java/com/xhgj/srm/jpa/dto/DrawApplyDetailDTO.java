package com.xhgj.srm.jpa.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
  *@ClassName PaymentAdvanceDTO
  *<AUTHOR>
  *@Date 2023/12/28 17:17
*/
@Data
public class DrawApplyDetailDTO {
  /**
   * 财务凭证信息
   */
  @ApiModelProperty("财务凭证信息")
  List<FinancialVoucherDTO> financialVoucherList;
  /**
   * 申请单号
   */
  @ApiModelProperty("申请单号")
  private String paymentApplyNo;

  /**
   * 提款总金额
   */
  @ApiModelProperty("提款总金额")
  private String drawMoney;
  /**
   * 付款方式
   */
  @ApiModelProperty("付款方式")
  private String payType;

  @ApiModelProperty("付款方式描述")
  private String payDesc;

  /**
   * 开户行
   */
  @ApiModelProperty("开户行")
  private String bank;


  @ApiModelProperty("银行联号")
  private String bankCode;

  /**
   * 银行账号
   */
  @ApiModelProperty("银行账号")
  private String bankAccount;

  /**
   * 账户名称
   */
  @ApiModelProperty("账户名称")
  private String accountName;

  /**
   * 采购订单号
   */
  @ApiModelProperty("采购订单号")
  private String supplierOrderNo;
  /**
   * 备注
   */
  @ApiModelProperty("备注")
  private String remark;
  /**
   * 申请状态  1 审核中 2 通过 3 驳回 4 已放弃
   */
  @ApiModelProperty("申请状态  1 审核中 2 通过 3 驳回 4 已放弃")
  private String applyState;

  /**
   * 驳回理由
   */
  @ApiModelProperty("驳回理由")
  private String rejectReason;

  /**
   * 提交人
   */
  @ApiModelProperty("提交人")
  private String createMan;

  /**
   * 期望付款日期
   */
  @ApiModelProperty("期望付款日期")
  private Long desireDate;


  /**
   * 本次操作金额
   */
  @ApiModelProperty("本次操作金额")
  private String thisAmount;

  /**
   * 购买账号（一次性供应商使用到的字段）
   */
  @ApiModelProperty("购买账号（一次性供应商使用到的字段）")
  private String purchaseAccount;

  /**
   * 订单号（一次性供应商使用到的字段）
   */
  @ApiModelProperty("订单号（一次性供应商使用到的字段）")
  private String orderNo;

  /**
   * 物料名称（一次性供应商使用到的字段）
   */
  @ApiModelProperty("物料名称（一次性供应商使用到的字段）")
  private String productName;

  /**
   * 订单链接（一次性供应商使用到的字段）
   */
  @ApiModelProperty("订单链接（一次性供应商使用到的字段）")
  private String orderLink;

  @ApiModelProperty("供应商 id")
  private String supplierId;

  @ApiModelProperty("供应商类型")
  private String supType;
}
