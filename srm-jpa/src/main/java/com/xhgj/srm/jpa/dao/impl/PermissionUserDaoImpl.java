package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.jpa.dao.PermissionUserDao;
import com.xhgj.srm.jpa.entity.PermissionUser;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import java.util.List;
import org.springframework.stereotype.Service;

/** <AUTHOR> @ClassName UserPermissionDaoImpl */
@Service
public class PermissionUserDaoImpl extends AbstractExtDao<PermissionUser>
    implements PermissionUserDao {

  @Override
  public void deletePermissionUser(String permissionTypeId) {
    Assert.notEmpty(permissionTypeId);
    String hql = " delete from PermissionUser  where permissionTypeId = ?  ";
    Object[] params = new Object[] {permissionTypeId};
    executeUpdate(hql, params);
  }

  @Override
  public List<String> getPermissionUserList(String permissionTypeId) {
    Assert.notEmpty(permissionTypeId);
    String hql = "select userId from PermissionUser where permissionTypeId = ? ";
    Object[] params = new Object[] {permissionTypeId};
    return getHqlObjList(hql, params);
  }
}
