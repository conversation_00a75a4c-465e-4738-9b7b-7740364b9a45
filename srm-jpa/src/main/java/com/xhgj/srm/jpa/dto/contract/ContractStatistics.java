package com.xhgj.srm.jpa.dto.contract;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * 供应商合同统计数据
 */
@Data
public class ContractStatistics {
  /**
   * 含税总额
   */
  @ApiModelProperty("含税总额")
  private BigDecimal totalAmount;

  /**
   * 转换2位小数
   * @return
   */
  public BigDecimal getTotalAmount() {
    if (totalAmount == null) {
      return BigDecimal.ZERO;
    }
    return totalAmount.setScale(2, RoundingMode.HALF_UP);
  }


}
