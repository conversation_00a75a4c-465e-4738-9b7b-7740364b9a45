package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.common.dto.OrderDetailDTO;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.data.domain.Page;

public interface OrderDetailDao extends BootBaseDao<OrderDetail> {

  /**
   * 获取订单退货个数
   *
   * @param orderId 订单id
   * @return
   */
  BigDecimal getOrderReturnSkuCountByOrderId(String orderId);

  /**
   * 获取订单商品总数
   *
   * @param orderId
   * @return
   */
  BigDecimal getOrderSkuNumCountByOrderId(String orderId);

  /**
   * 获取订单发货个数
   *
   * @param orderId
   * @return
   */
  BigDecimal getShipOrderSkuNumCountByOrderId(String orderId);

  /**
   * 订单id
   *
   * @param orderId
   * @return
   */
  List<OrderDetail> getOrderDetailByOrderId(String orderId);

  /**
   * 根据sku及订单id获取订单
   *
   * @param orderId
   * @return
   */
  OrderDetail getOrderDetailByOrderIdAndCode(String orderId, String code);

  OrderDetail getOrderDetailByOrderIdAndRowNo(String orderId, String rowNo);

  /**
   * 根据订单 id 获得未发货数量
   *
   * @param orderId 订单 id 必传
   * @return
   */
  BigDecimal getSumUnShipCount(String orderId);

  /**
   * 获取所有订单详情
   *
   * @return
   */
  List<OrderDetail> getAllOrderDetail();

  /** 获取订单详情信息 */
  OrderDetail getOrderDetailByOrderNoAndTypeAndCode(
      String supplierOrderId, String orderNo, String type, String code);

  /** 获取订单明细 */
  List<OrderDetailDTO> getDetailListByOrderId(String orderId);

  /**
   * 根据下单时间查询此时间之前的数据
   *
   * @param orderTime 下单时间
   * @return 此下单时间之前的数据
   */
  List<OrderDetail> findBeforeByOrderTime(Long orderTime);

  /**
   * 获取税率为空的详情物料
   *
   * @return
   */
  List<OrderDetail> getListBySalesPriceRateAndCostPrice();

  /**
   * @param orderId 订单id
   * @return 顶单退货个数总计、发货个数总计、商品总计
   */
  Object[] getReturnCountAndShipCountAndSkuNumCount(String orderId);

  /**
   * 根据查询条件及订单id获取订单 <AUTHOR> @Date: 2024年6月17日 17:25:44
   *
   * @param orderId 订单id
   * @param keyWord 物料编码以及品牌商品名称型号
   * @param pageNo 页数
   * @param pageSize 条数
   * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.OrderDetail>
   */
  Page<OrderDetail> getOrderDetailPageByOrderId(
      String orderId, String keyWord, Integer pageNo, Integer pageSize, List<String> ids);

  /**
   * 根据订单id获取订单详情数量
   * @param orderIds
   * @return
   */
  Long countByOrderIds(List<String> orderIds);

  /**
   * 根据查询条件获取订单详情数量
   */
  Long countOrdersByQueryParams(String userIds,
      String orderNo,
      String customer,
      String consignee,
      String mobile,
      String accepted,
      String orderState,
      String startDate,
      String endDate,
      String platform,
      String supplierName,
      String price,
      String invoicingState,
      Boolean hasUploadSignVoucher,
      String customerReturnProgress,
      Long allShipTimeStart,
      String paymentStatus,
      List<String> customerReturnProgressList,
      String accountStatus,
      String accountOpenInvoiceStatus,
      Boolean salesReturnState,
      String saleOrderNo,
      String erpOrderNo,
      String startCreateTime,
      String endCreateTime,
      String paymentProportionOperators, BigDecimal paymentProportion, Long startArrivalTime,
      Long endArrivalTime,Long startWriteOffTime,Long endWriteOffTime,String paymentType,
      String paymentCondition, Boolean backToBack,Integer accountingPeriod,
      Long startPaymentConditionTime,Long endPaymentConditionTime,Long startPredictPaymentTime, Long endPredictPaymentTime,Boolean receivableState,Boolean payableDate
  );

  /**
   * 查询出所有已退货的订单ids
   */
  List<String> getAllReturnOrderIds(List<String> orderIds);

}
