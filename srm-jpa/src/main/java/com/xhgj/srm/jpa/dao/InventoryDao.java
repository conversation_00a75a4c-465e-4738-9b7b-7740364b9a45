package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.Inventory;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;
import java.util.List;
import java.util.Map;

/**
 * InventoryDao
 */
public interface InventoryDao extends BootBaseDao<Inventory> {

  Page<Inventory> getAllInventoryList(Map<String, Object> queryMap);

  /**
   * 根据组织code + 库房code + 物料codes
   * 查询相关的库存信息
   */
  List<Inventory> checkInventorySafe(String groupCode, String warehouse, List<String> productCodes);
}
