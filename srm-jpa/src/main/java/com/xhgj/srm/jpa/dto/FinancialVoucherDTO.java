package com.xhgj.srm.jpa.dto;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class FinancialVoucherDTO {

  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("财务凭证号")
  private String financialVoucherNo;

  /**
   * SAP会计年度。
   */
  @ApiModelProperty("SAP会计年度")
  private String accountingYear;

  /**
   * {@link com.xhgj.srm.common.enums.VoucherTypeEnum}
   * 凭证类型。
   */
  @ApiModelProperty("凭证类型")
  private String voucherType;

  /**
   * 进项发票单号。
   */
  @ApiModelProperty("进项发票单号")
  private String invoiceOrderNo;

  /**
   * 凭证金额。
   */
  @ApiModelProperty("凭证金额")
  private BigDecimal voucherPrice;

  /**
   * {@link com.xhgj.srm.common.enums.VoucherPaymentStateEnum}
   * 凭证付款状态。
   */
  @ApiModelProperty("凭证付款状态")
  private String voucherPaymentState;

  /**
   * 基准日期。
   */
  @ApiModelProperty("基准日期")
  private Long baseDate;

  /**
   * 账期枚举。
   */
  @ApiModelProperty("账期枚举")
  private String accountPeriod;

  /**
   * 预计付款日期。
   */
  @ApiModelProperty("预计付款日期")
  private Long expectedPaymentDate;

  /**
   * {@link com.xhgj.srm.common.enums.VoucherPaymentTypeEnum}
   * 付款方式。
   */
  @ApiModelProperty("付款方式")
  private String paymentType;

  /**
   * 付款冻结状态。
   * {@link com.xhgj.srm.common.enums.PaymentFreezeStatusEnum}
   */
  @ApiModelProperty("付款冻结状态")
  private String paymentFreezeStatus;

  /**
   * 采购订单号。
   */
  @ApiModelProperty("采购订单号")
  private String purchaseOrderNo;
  /**
   * 采购单id
   */
  @ApiModelProperty("采购订单号关联的id")
  private String purchaseOrderId;

  /**
   * 供应商名称。
   */
  @ApiModelProperty("供应商名称")
  private String supplierName;

  /**
   * {@link com.xhgj.srm.common.enums.PaymentTermsEnum}
   * 付款条件。
   */
  @ApiModelProperty("付款条件")
  private String paymentTerms;

  /**
   * 客户回款状态（1==已完成，0==未完成）。
   */
  @ApiModelProperty("客户回款状态（1==已完成，0==未完成）")
  private String customerCollectionState;

  /**
   * 关联金额。
   */
  @ApiModelProperty("关联金额")
  private BigDecimal relatedAmount;

  @ApiModelProperty("发票单id")
  private String invoiceRelationId;

  @ApiModelProperty("进项票类型")
  private String orderSource;

  @ApiModelProperty("来源标识")
  private String manageFlag;

  /**
   * 剩余可提款金额
   */
  @ApiModelProperty("剩余可提款金额")
  private BigDecimal remainingWithdrawableAmount;

  @ApiModelProperty("本次操作金额")
  private BigDecimal thisAmount;

  @ApiModelProperty("剩余可退款金额")
  private BigDecimal remainingRefundableAmount;

  /**
   * 期初订单标识
   */
  @ApiModelProperty("期初订单标识")
  private Boolean initialOrderFlag;

  public FinancialVoucherDTO(FinancialVoucher financialVoucher, String supplierName) {
    String defaultStr = "-";
    this.id = financialVoucher.getId();
    this.financialVoucherNo = StrUtil.emptyToDefault(financialVoucher.getFinancialVoucherNo(), defaultStr);
    this.accountingYear = StrUtil.emptyToDefault(financialVoucher.getAccountingYear(), defaultStr);
    this.voucherType = StrUtil.emptyToDefault(financialVoucher.getVoucherType(), defaultStr);
    this.invoiceOrderNo = StrUtil.emptyToDefault(financialVoucher.getInvoiceOrderNo(), defaultStr);
    this.voucherPrice = BigDecimalUtil.formatForStandard(financialVoucher.getVoucherPrice());
    this.voucherPaymentState = StrUtil.emptyToDefault(financialVoucher.getVoucherPaymentState(), defaultStr);
    this.baseDate = financialVoucher.getBaseDate() == null ? 0 : financialVoucher.getBaseDate();
    this.accountPeriod = StrUtil.emptyToDefault(financialVoucher.getAccountPeriod(), defaultStr);
    this.expectedPaymentDate = financialVoucher.getExpectedPaymentDate() == null ? 0 : financialVoucher.getExpectedPaymentDate();
    this.paymentFreezeStatus = StrUtil.emptyToDefault(financialVoucher.getPaymentFreezeStatus(), defaultStr);
    this.paymentType = StrUtil.emptyToDefault(financialVoucher.getPaymentType(), defaultStr);
    this.purchaseOrderNo = StrUtil.emptyToDefault(financialVoucher.getPurchaseOrderNo(), defaultStr);
    this.supplierName = supplierName;
    this.paymentTerms = StrUtil.emptyToDefault(financialVoucher.getPaymentTerms(), defaultStr);
    this.customerCollectionState =
        StrUtil.emptyToDefault(financialVoucher.getCustomerCollectionState(), defaultStr);
    this.relatedAmount = BigDecimalUtil.formatForStandard(financialVoucher.getRelatedAmount());
    BigDecimal settlementTotal =
        NumberUtil.add(financialVoucher.getRelatedAmount(), financialVoucher.getRefundAmount());
    BigDecimal prepaidAndWithdrawalTotal = NumberUtil.add(financialVoucher.getOffsetPrepaidAmount(),
        financialVoucher.getWithdrawnAmount());
    this.remainingWithdrawableAmount =
        NumberUtil.sub(settlementTotal, prepaidAndWithdrawalTotal).stripTrailingZeros();
    // 可退款金额 = 凭证关联金额-已退款金额
    this.remainingRefundableAmount = NumberUtil.sub(financialVoucher.getRelatedAmount(),
        financialVoucher.getRefundAmount()).stripTrailingZeros();
    this.initialOrderFlag = financialVoucher.getInitialOrder();
  }
}
