package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.OrderInvoice;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;

/**
 * 落地商订单发票信息(OrderInvocie)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-02 09:07:51
 */
public interface OrderInvoiceRepository extends BootBaseRepository<OrderInvoice, String> {

  /**
   * 根据落地商订单 id 查询发票信息
   *
   * @param orderId 落地商订单 id 必传
   */
  OrderInvoice getFirstByOrderId(String orderId);

  /**
   * 创建时间倒序获取全部开票信息
   */
  List<OrderInvoice> getAllByOrderByCreateTimeDesc();

  /**
   * 根据开票 id 获取开票信息
   */

  List<OrderInvoice> getAllByIdInOrderByCreateTimeDesc(List<String> idList);
}


