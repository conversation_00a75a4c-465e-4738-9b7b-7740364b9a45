package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
  *@ClassName SupplierInvoiceToDetail
  *<AUTHOR>
  *@Date 2023/12/26 10:14
*/
@Entity
@Table(name="t_supplier_invoice_to_detail")
@Data
public class SupplierInvoiceToDetail {
  /**
   * 主键
   */
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 发票号
   */
  @Column(name="c_invoice_number")
  private String invoiceNumber;

  /**
   * 本次开票数量,现在已经修改为存储多张(应对前端录入发票的情况)
   */
  @Column(name="c_invoice_num")
  private BigDecimal invoiceNum;
  /**
   * 订单明细id
   */
  @Column(name="c_detail_id")
  private String detailId;

  /**
   * 创建时间
   */
  @Column(name="c_create_time")
  private Long createTime;

  /**
   * 本次去税金额
   */
  @Column(name="c_tax_free_amount")
  private BigDecimal taxFreeAmount;

  /**
   * 本次开票金额
   */
  @Column(name="c_open_tax_amount")
  private BigDecimal openTaxAmount;

  /**
   * 本次开票税额
   */
  @Column(name="c_tax_amount")
  private BigDecimal taxAmount;

  @Column(name="c_seller")
  private String seller;

  /**
   * 关联的进项票id
   */
  @Column(name="c_input_invoice_order_id")
  private String inputInvoiceOrderId;

}
