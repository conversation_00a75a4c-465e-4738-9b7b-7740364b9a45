package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.utils.SQLUtils;
import com.xhgj.srm.jpa.dao.PlatformDao;
import com.xhgj.srm.jpa.entity.Platform;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import java.util.Collections;
import java.util.List;
import com.xhiot.boot.framework.jpa.util.HqlUtil;
import org.springframework.stereotype.Repository;

/**
 * Created by Geng Shy on 2023/10/19
 */
@Repository
public class PlatformDaoImpl extends AbstractExtDao<Platform> implements PlatformDao {

  @Override
  public List<Platform> findLikeByCodeAndName(String code, String name,String platformAbbreviation) {
    StringBuilder sql = new StringBuilder(" select * from t_platform where c_state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    if (StrUtil.isNotBlank(code)) {
      sql.append("and c_code like ? ");
      params = ObjectUtils.objectAdd(params, "%" + code + "%");
    }
    if (StrUtil.isNotBlank(name)) {
      sql.append("and c_name like ? ");
      params = ObjectUtils.objectAdd(params, "%" + name + "%");
    }
    if (StrUtil.isNotBlank(platformAbbreviation)) {
      sql.append("and c_platform_abbreviation like ? ");
      params = ObjectUtils.objectAdd(params, "%" + platformAbbreviation + "%");
    }

    sql.append(" order by c_platform_abbreviation asc, c_code asc");
    return getSqlList(sql.toString(), params);
  }

  @Override
  public Platform findByCode(String platform) {
    String hql = "  from Platform where c_code = ?  and c_state = ? ";
    Object[] params = new Object[]{platform,Constants.STATE_OK};
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public void updateAbbreviationByProjectCategory(String abbreviation, String projectCategory,
      String userId) {
    String sql = "update t_platform set c_platform_abbreviation = ?, c_update_user = ?, c_update_time = ? "
        + "where c_project_category = ? and c_state = ? ";
    Object[] params = new Object[] {abbreviation, userId, System.currentTimeMillis(), projectCategory,
        Constants.STATE_OK};
    executeSqlUpdate(sql, params);
  }

  @Override
  public List<String> findProjectNamesByCodes(List<String> codes) {
    StringBuilder sql = new StringBuilder("select distinct c_project_name from t_platform where c_state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    // 生成in条件
    params = HqlUtil.appendFieldIn(sql,params,"c_code",codes);
    return getSqlObjList(sql.toString(), params);
  }

  @Override
  public List<Platform> getAllByCodes(List<String> platformCodes) {
    StringBuilder hql = new StringBuilder(" from Platform where state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    // 生成in条件
    params = HqlUtil.appendFieldIn(hql,params,"code",platformCodes);
    return getHqlList(hql.toString(), params);
  }

  @Override
  public List<Platform> searchProjectNames(String projectName, String abbreviation) {
    StringBuilder sql = new StringBuilder();
    sql.append("select * from t_platform where c_state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    if (StrUtil.isNotBlank(projectName) || StrUtil.isNotBlank(abbreviation)) {
      sql.append("and( ");
      if (StrUtil.isNotBlank(projectName)) {
        sql.append("c_project_name like ? ");
        params = ObjectUtils.objectAdd(params, "%" + projectName + "%");
      }
      if (StrUtil.isNotBlank(abbreviation)) {
        if (StrUtil.isNotBlank(projectName)) {
          sql.append("or ");
        }
        sql.append("c_platform_abbreviation like ? ");
        params = ObjectUtils.objectAdd(params, "%" + abbreviation + "%");
      }
      sql.append(") ");
    }
    return getSqlList(sql.toString(), params);
  }

  @Override
  public List<Platform> getListByProjectName(String projectName) {
    StringBuilder hql = new StringBuilder(" from Platform where state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    if (StrUtil.isNotBlank(projectName)) {
      hql.append("and c_project_name = ? ");
      params = ObjectUtils.objectAdd(params, projectName);
    }
    return getHqlList(hql.toString(), params);
  }

  @Override
  public List<Platform> searchProjectCategory(String projectCategory) {
    StringBuilder hql = new StringBuilder(" select * from t_platform where c_state = ?  ");
    Object[] params = new Object[] {Constants.STATE_OK};
    if (StrUtil.isNotBlank(projectCategory)) {
      hql.append("and c_project_category like ? ");
      params = ObjectUtils.objectAdd(params, "%" + projectCategory + "%");
    }
    return getSqlList(hql.toString(), params);
  }
}
