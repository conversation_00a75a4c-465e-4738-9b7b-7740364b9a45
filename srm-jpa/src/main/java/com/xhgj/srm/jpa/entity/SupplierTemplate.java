package com.xhgj.srm.jpa.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.xhgj.srm.jpa.util.LazyLoadEntityListener;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

@Table(name = "t_supplier_template")
@Entity
@Data
@EntityListeners(LazyLoadEntityListener.class)
public class SupplierTemplate {

  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  @JoinColumn(name = "c_group_id",updatable = false,insertable = false)
  @ManyToOne(fetch = FetchType.LAZY)
  private Group group;

  @Column(name = "c_group_id")
  private String groupId;

  @Column(name = "c_state")
  private String state = "1";

  @Column(name = "c_create_time",updatable = false)
  private Long createTime;

  @Column(name = "c_update_time")
  private Long updateTime;

  @Column(name = "c_create_man",updatable = false)
  private String createMan;

  @Column(name = "c_update_man")
  private String updateMan;
}
