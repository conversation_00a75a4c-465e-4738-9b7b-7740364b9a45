package com.xhgj.srm.jpa.dao.impl;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.OrderFilingDao;
import com.xhgj.srm.jpa.dao.OrderFilingDetailDao;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.entity.OrderFiling;
import com.xhgj.srm.jpa.entity.OrderFilingDetail;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;

@Slf4j
@Service
public class OrderFilingDetailDaoImpl extends AbstractExtDao<OrderFilingDetail> implements OrderFilingDetailDao {

  @Override
  public List<OrderFilingDetail> getOrderFilingDetailListByFiling(String filingId) {
    String hql = "from OrderFilingDetail o where o.state != ? and o.filingId = ? order by o.createTime desc";
    Object[] params = new Object[] { Constants.STATE_DELETE,filingId};
    return getHqlList(hql, params);
  }

  @Transactional
  @Override
  public void deleteFilingDetailByFilingId(String filingId) {
    String hql = "delete from OrderFilingDetail od where od.filingId = ? ";
    Object[] params = new Object[]{filingId};
    executeUpdate(hql, params);
  }
}
