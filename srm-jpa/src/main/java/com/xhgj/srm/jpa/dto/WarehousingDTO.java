package com.xhgj.srm.jpa.dto;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-31 14:06
 */
@Data
public class WarehousingDTO {

  private String id;
  private String code;
  private Long createTime;
  private String source;
  private String logisticsCompany;
  private String trackNum;
  private String productVoucherNo;
  private String productCode;
  private String brand;
  private String productName;
  private String manuCode;
  private String sapRowId;
  private BigDecimal stockInputQty;
  private BigDecimal stockOutputQty;
  private BigDecimal invoicedNum;
  private BigDecimal productPrice;
  private String batchNo;
  private String formStatus;
  private String unit;
  private Integer productSort;
  private String detailId;
  private String supplierName;
  private String purchaseMan;
  private String purchaseDept;
  private BigDecimal taxRate;
  /**
   * 仓库名称
   */
  private String warehouseName;
  private String orderType;
  /**
   * 采购部门编码
   */
  private String purchaseDeptCode;
  /**
   * 发货单id
   */
  private String orderToFormId;
  /**
   * 含税金额：价税合计
   */
  private BigDecimal totalPriceAndTax;
  /**
   * 已开票金额
   */
  private BigDecimal invoicedAmount;
  /**
   * 未开票金额
   */
  private BigDecimal unInvoicedAmount;
  /**
   * 采购申请单号
   */
  private String purchaseApplyCode;
  /**
   * 项目编码
   */
  private String projectNo;
  /**
   * 业务员
   */
  private String salesman;
  /**
   * 销售订单号
   */
  private String saleOrderNo;

  /**
   * 订货数量
   */
  private BigDecimal num;

  /**
   * 可开票数量
   */
  private BigDecimal invoiceAbleNum;

  /**
   * 结算单价
   */
  private BigDecimal settlementPrice;
}
