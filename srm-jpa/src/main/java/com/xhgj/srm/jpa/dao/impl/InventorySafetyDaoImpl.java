package com.xhgj.srm.jpa.dao.impl;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.inventorySafety.InventorySafetyStatus;
import com.xhgj.srm.common.utils.SQLUtils;
import com.xhgj.srm.common.vo.inventorySafety.InventorySafetyListVO;
import com.xhgj.srm.jpa.dao.InventorySafetyDao;
import com.xhgj.srm.jpa.entity.InventorySafety;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/7/21 23:32
 */
@Repository
public class InventorySafetyDaoImpl extends AbstractExtDao<InventorySafety> implements InventorySafetyDao {

  @Override
  public PageResult<InventorySafetyListVO> getPage(Map<String, Object> queryMap) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    sql.append("SELECT s.id, ");
    sql.append("w.c_warehouse_name AS warehouseName, ");
    sql.append("s.c_warehouse AS warehouseCode, ");
    sql.append("s.c_warehouse_id AS warehouseId, ");
    sql.append("s.c_product_code AS productCode, ");
    sql.append("s.c_name AS name, ");
    sql.append("s.c_brand_name_cn AS brandNameCn, ");
    sql.append("s.c_brand_name_en AS brandNameEn, ");
    sql.append("s.c_brand_name AS brandName, ");
    sql.append("s.c_model AS model, ");
    sql.append("s.c_unit AS unit, ");
    sql.append("s.c_min_safety_stock AS minSafetyStock, ");
    sql.append("s.c_notified_person AS notifiedPersons, ");
    sql.append("s.c_notified_person_code AS notifiedPersonCode, ");
    sql.append("s.c_notified_person_id AS notifiedPersonId, ");
    sql.append("s.c_notified_person_phone AS notifiedPersonPhone, ");
    sql.append("s.c_last_reminder_time AS lastReminderTime, ");
    sql.append("s.c_next_reminder_time AS nextReminderTime, ");
    sql.append("s.c_status AS status, ");
    sql.append("s.c_create_time AS createTime, ");
    sql.append("s.c_update_time AS updateTime, ");
    sql.append("s.c_group_code AS groupCode ");

    sql.append("from t_inventory_safety s ");
    sql.append("left join t_inventory_location w on s.c_warehouse_id = w.id ");

    this.buildWhereQuery(sql, params, queryMap);
    sql.append(" order by s.c_create_time desc, s.id desc ");
    Page page =
        findPageSqlObject(sql.toString(), params.toArray(), (Integer) queryMap.get("pageNo"),
            (Integer) queryMap.get("pageSize"));
    List<Object[]> content = page.getContent();
    List<InventorySafetyListVO> res = content.parallelStream().map(item -> {
      InventorySafetyListVO vo = new InventorySafetyListVO();
      int index = 0;
      vo.setId(Convert.toStr(item[index++]));
      vo.setWarehouseName(Convert.toStr(item[index++]));
      vo.setWarehouseCode(Convert.toStr(item[index++]));
      vo.setWarehouseId(Convert.toStr(item[index++]));
      vo.setProductCode(Convert.toStr(item[index++]));
      vo.setName(Convert.toStr(item[index++]));
      vo.setBrandNameCn(Convert.toStr(item[index++]));
      vo.setBrandNameEn(Convert.toStr(item[index++]));
      vo.setBrandName(Convert.toStr(item[index++]));
      vo.setModel(Convert.toStr(item[index++]));
      vo.setUnit(Convert.toStr(item[index++]));
      vo.setMinSafetyStock(Convert.toBigDecimal(item[index++]));
      vo.setNotifiedPerson(Convert.toStr(item[index++]));
      vo.setNotifiedPersonCode(Convert.toStr(item[index++]));
      vo.setNotifiedPersonId(Convert.toStr(item[index++]));
      vo.setNotifiedPersonPhone(Convert.toStr(item[index++]));
      vo.setLastReminderTime(Convert.toLong(item[index++]));
      vo.setNextReminderTime(Convert.toLong(item[index++]));
      vo.setStatus(Convert.toByte(item[index++]));
      vo.setCreateTime(Convert.toLong(item[index++]));
      vo.setUpdateTime(Convert.toLong(item[index++]));
      vo.setGroupCode(Convert.toStr(item[index++]));
      return vo;
    }).collect(Collectors.toList());
    return new PageResult<>(res, page.getTotalElements(), page.getTotalPages(),
        (Integer) queryMap.get("pageNo"), (Integer) queryMap.get("pageSize"));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void updateStatus(long nowLong) {
    StringBuilder sql = new StringBuilder();
    sql.append("update t_inventory_safety set c_status = ?, c_update_time = ? where "
        + "c_next_reminder_time <= ? and "
        + "c_status = ? and c_state = ? ");
    List<Object> params = new ArrayList<>();
    params.add(InventorySafetyStatus.OPEN.getCode());
    params.add(System.currentTimeMillis());
    params.add(nowLong);
    params.add(InventorySafetyStatus.CLOSE.getCode());
    params.add(Constants.STATE_OK);
    executeSqlUpdate(sql.toString(), params.toArray());
  }

  private void buildWhereQuery(StringBuilder sql, List<Object> params, Map<String, Object> queryMap) {
    sql.append("where s.c_state = ? ");
    params.add(Constants.STATE_OK);
    // warehouse
    String warehouse = Convert.toStr(queryMap.get("warehouse"));
    if (StrUtil.isNotBlank(warehouse)) {
      sql.append("and w.c_warehouse = ? ");
      params.add(warehouse);
    }
    // productCode
    String productCode = Convert.toStr(queryMap.get("productCode"));
    if (StrUtil.isNotBlank(productCode)) {
      sql.append("and s.c_product_code like ? ");
      params.add("%" + productCode + "%");
    }
    // name
    String name = Convert.toStr(queryMap.get("name"));
    if (StrUtil.isNotBlank(name)) {
      sql.append("and s.c_name like ? ");
      params.add("%" + name + "%");
    }
    // brand
    String brand = Convert.toStr(queryMap.get("brand"));
    if (StrUtil.isNotBlank(brand)) {
      sql.append("and c_brand_name like ? ");
      params.add("%" + brand + "%");
    }
    // model
    String model = Convert.toStr(queryMap.get("model"));
    if (StrUtil.isNotBlank(model)) {
      sql.append("and s.c_model like ? ");
      params.add("%" + model + "%");
    }
    // unit
    String unit = Convert.toStr(queryMap.get("unit"));
    if (StrUtil.isNotBlank(unit)) {
      sql.append("and s.c_unit like ? ");
      params.add("%" + unit + "%");
    }
    // minSafetyStock + minSafetyStockOperator
    BigDecimal minSafetyStock = Convert.toBigDecimal(queryMap.get("minSafetyStock"));
    String minSafetyStockOperator = Convert.toStr(queryMap.get("minSafetyStockOperator"));
    if (minSafetyStock != null && StrUtil.isNotBlank(minSafetyStockOperator)) {
      SQLUtils.addStrLogicalOperators(minSafetyStockOperator, minSafetyStock, sql, params, "s.c_min_safety_stock");
    }
    // notifiedPersons
    String notifiedPersons = Convert.toStr(queryMap.get("notifiedPersons"));
    if (StrUtil.isNotBlank(notifiedPersons)) {
      // 根据,分割
      String[] notifiedPersonArr = notifiedPersons.split(",");
      int index = 0;
      if (notifiedPersonArr.length > 0) {
        sql.append("and (");
        for (String s : notifiedPersonArr) {
          if (index > 0) {
            sql.append("or ");
          }
          sql.append("s.c_notified_person like ? ");
          params.add("%" + s + "%");
          index++;
        }
        sql.append(") ");
      }
    }
    // lastReminderTimeStart
    Long lastReminderTimeStart = Convert.toLong(queryMap.get("lastReminderTimeStart"));
    if (lastReminderTimeStart != null) {
      sql.append("and s.c_last_reminder_time >= ? ");
      params.add(lastReminderTimeStart);
    }
    // lastReminderTimeEnd
    Long lastReminderTimeEnd = Convert.toLong(queryMap.get("lastReminderTimeEnd"));
    if (lastReminderTimeEnd != null) {
      sql.append("and s.c_last_reminder_time <= ? ");
      params.add(lastReminderTimeEnd);
    }
    // nextReminderTimeStart
    Long nextReminderTimeStart = Convert.toLong(queryMap.get("nextReminderTimeStart"));
    if (nextReminderTimeStart != null) {
      sql.append("and s.c_next_reminder_time >= ? ");
      params.add(nextReminderTimeStart);
    }
    // nextReminderTimeEnd
    Long nextReminderTimeEnd = Convert.toLong(queryMap.get("nextReminderTimeEnd"));
    if (nextReminderTimeEnd != null) {
      sql.append("and s.c_next_reminder_time <= ? ");
      params.add(nextReminderTimeEnd);
    }
    // status
    Byte status = Convert.toByte(queryMap.get("status"));
    if (status != null) {
      sql.append("and s.c_status = ? ");
      params.add(status);
    }
    // userGroup
    String userGroup = Convert.toStr(queryMap.get("userGroup"));
    if (StrUtil.isNotBlank(userGroup)) {
      sql.append("and s.c_group_code = ? ");
      params.add(userGroup);
    }
    // ids
    List<String> ids = Convert.toList(String.class, queryMap.get("ids"));
    if (CollUtil.isNotEmpty(ids)) {
      sql.append("and s.id in (");
      for (int i = 0; i < ids.size(); i++) {
        if (i > 0) {
          sql.append(",");
        }
        sql.append("?");
        params.add(ids.get(i));
      }
      sql.append(") ");
    }
  }
}
