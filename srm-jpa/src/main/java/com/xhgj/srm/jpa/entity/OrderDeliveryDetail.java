package com.xhgj.srm.jpa.entity;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/** 订单发货详情表 */
@Entity
@Table(name = "t_order_delivery_detail")
@Data
public class OrderDeliveryDetail {

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    /** 所属发货单 */
    @JoinColumn(name = "delivery_id", insertable = false, updatable = false)
    @ManyToOne
    private OrderDelivery delivery;

    /** 所属发货单 */
    @Column(name = "delivery_id")
    private String deliveryId;

    /** 商品编码 */
    @Column(name = "c_code")
    private String code;

    /** 品牌 */
    @Column(name = "c_brand")
    private String brand;

    /** 商品名称 */
    @Column(name = "c_name")
    private String name;

    /** 型号 */
    @Column(name = "c_model")
    private String model;

    /** 数量 */
    @Column(name = "c_num")
    private BigDecimal num;

    /** 单位 */
    @Column(name = "c_unit")
    private String unit;

    /** 发货数量 */
    @Column(name = "c_ship_num")
    private BigDecimal shipNum;

    /** 单价 */
    @Column(name = "c_price")
    private BigDecimal price;

    /** 状态 */
    @Column(name = "c_state")
    private String state;

    /** 创建时间 */
    @Column(name = "c_create_time")
    private Long createTime;

    /** erp 物料明细行 id */
    @Column(name = "c_erp_row_id")
    private String erpRowId;

  /**
   * sap 批号
   */
    @Column(name = "c_batch_no")
    private String batchNo;

    /**
     * 退货数量
     */
    @Column(name = "c_return_num")
    private BigDecimal returnNum;
}
