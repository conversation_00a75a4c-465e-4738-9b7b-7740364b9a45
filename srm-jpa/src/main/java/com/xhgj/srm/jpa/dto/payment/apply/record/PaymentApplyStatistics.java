package com.xhgj.srm.jpa.dto.payment.apply.record;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 */
@Data
public class PaymentApplyStatistics {
  /**
   * 申请金额
   */
  @ApiModelProperty("申请金额")
  private BigDecimal applyAmount;

  public BigDecimal getApplyAmount() {
    if (applyAmount == null) {
      return BigDecimal.ZERO;
    }
    // 保留两位小数
    return applyAmount.setScale(2, RoundingMode.HALF_UP);
  }
}
