package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.CustomerReceivableDao;
import com.xhgj.srm.jpa.entity.CustomerReceivable;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import com.xhiot.boot.framework.jpa.util.HqlUtil;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.stereotype.Repository;

/** <AUTHOR> @ClassName CustomerReceivableDaoImpl */
@Repository
public class CustomerReceivableDaoImpl extends AbstractExtDao<CustomerReceivable>
    implements CustomerReceivableDao {

  @Override
  public CustomerReceivable getCustomerReceivable(
      String orderId, String projectNo, String invoiceNo, Long invoiceTime, BigDecimal price) {
    String hql =
        "from CustomerReceivable where state != ? and orderId = ? and projectNo = ? and invoiceNo = ?  ";
    Object[] params = new Object[] {Constants.STATE_DELETE, orderId, projectNo, invoiceNo};
    if (invoiceTime != null && invoiceTime > 0) {
      hql += " and invoiceTime = ? ";
      params = ObjectUtils.objectAdd(params, invoiceTime);
    }
    if (price != null) {
      hql += "  and price = ? ";
      params = ObjectUtils.objectAdd(params, price);
    }
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public CustomerReceivable getCustomerReceivableByOrderIdAndNumber(
      String orderId, String projectNo) {
    Assert.notEmpty(projectNo);
    String hql = "from CustomerReceivable where state != ? and projectNo = ? ";
    Object[] params = new Object[] {Constants.STATE_DELETE, projectNo};
    if (StrUtil.isNotEmpty(orderId)) {
      hql += "and orderId = ? ";
      params = ObjectUtils.objectAdd(params, orderId);
    }
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public int delCustomerReceivableExcludeOrderIdList(List<String> excludeOrderIdList) {
    StringBuilder hql =
        new StringBuilder("update t_customer_receivable set c_state = ? where c_state =  ? ");
    Object[] params = new Object[] {Constants.STATE_DELETE, Constants.STATE_OK};
    params = HqlUtil.appendFieldNotIn(hql, params, "order_id", excludeOrderIdList);
    return executeSqlUpdate(hql.toString(), params);
  }

  @Override
  public int delCustomerReceivableByOrderIdList(List<String> orderIdList) {
    StringBuilder hql =
        new StringBuilder("update t_customer_receivable set c_state = ? where c_state =  ? ");
    Object[] params = new Object[] {Constants.STATE_DELETE, Constants.STATE_OK};
    params = HqlUtil.appendFieldIn(hql, params, "order_id", orderIdList);
    return executeSqlUpdate(hql.toString(), params);
  }
}
