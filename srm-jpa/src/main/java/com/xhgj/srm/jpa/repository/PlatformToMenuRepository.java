package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.PlatformToMenu;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;

/**
 * Created by Geng Shy on 2023/10/23
 */
public interface PlatformToMenuRepository extends BootBaseRepository<PlatformToMenu, String> {

  /**
   * 根据平台id删除
   *
   * @param platformId 平台id
   */
  void deleteAllByPlatformId(String platformId);

  /**
   * 根据平台id和数据状态查询
   *
   * @param platformId 平台id
   * @param state 数据状态
   * @return List<PlatformToMenu>
   */
  List<PlatformToMenu> findAllByPlatformIdAndState(String platformId, String state);
}

