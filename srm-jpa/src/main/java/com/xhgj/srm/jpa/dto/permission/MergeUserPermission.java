package com.xhgj.srm.jpa.dto.permission;

import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MergeUserPermission {
  /**
   * 查询为当前用户 或 导出为当前用户
   */
  private String currentUserId;

  /**
   * 查询为当前用户 或 导出为当前用户
   */
  private String currentUserName;

  /**
   * 查询为当前用户 或 导出为当前用户
   */
  private String currentUserNameWithCode;

  /**
   * 查询所在组织
   */
  private String currentGroupId;

  /**
   * 查询所在组织
   */
  private String currentGroupErpCode;

  /**
   * 查询所在部门 或 导出所在部门
   */
  private List<String> currentDepartmentIds;

  /**
   * 查询所在部门 或 导出所在部门
   */
  private List<String> currentDepartmentCodes;

  /**
   * 查询所在部门及下级部门 或 导出所在部门及下级部门
   */
  private List<String> currentDepartmentAndChildCodes;

  /**
   * 查询所在部门及下级部门 或 导出所在部门及下级部门
   */
  private List<String> currentDepartmentAndChildIds;

  /**
   * 用户Name集合
   */
  private List<String> selectUserNames;

  /**
   * 用户id集合
   */
  private List<String> selectUserIds;

  /**
   * 额外用户id集合
   */
  private List<String> extraUserIds;

  /**
   * 额外用户name集合
   */
  private List<String> extraUserNames;

  /**
   * 是否筛选出落地商进项票
   */
  private Boolean showLandingMerchant;

  /**
   * 权限类型
   */
  private String permissionCode;

  /**
   * 是否为超级管理员
   */
  private boolean isSuperAdmin;

  /**
   * 是否为管理员
   */
  private boolean isAdmin;

  /**
   * 导出采购
   */
  private Boolean purchaseMan;

  /**
   * 查询所在组织
   */
  private Boolean includeAllUserGroups;

  /**
   * 所在组织ids
   */
  private List<String> currentGroupIds;

  /**
   * 所在组织erpCodes
   */
  private List<String> currentGroupErpCodes;

  /**
   * 查询为当前用户 或 导出为当前用户 工号
   */
  private String currentUserJobNumber;
}
