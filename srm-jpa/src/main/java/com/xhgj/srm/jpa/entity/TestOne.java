package com.xhgj.srm.jpa.entity;/**
 * @since 2025/4/15 11:35
 */

import com.xhgj.srm.jpa.annotations.ShardingTable;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 *<AUTHOR>
 *@date 2025/4/15 11:35:54
 *@description
 */
@Data
@Entity
@Table(name = "t_test")
@ShardingTable(
    logicTable = "t_test",
    actualDataNodes = "ds0.t_test,ds0.t_test_v2"
)
public class TestOne {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private String id;

  /**
   * 组装拆卸单号
   */
  @Column(name = "c_version")
  private String version;
}
