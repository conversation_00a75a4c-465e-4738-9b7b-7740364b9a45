package com.xhgj.srm.jpa.entity;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * t_order_invoice表实体
 *
 * <AUTHOR>
 * @since 2023-02-02 09:21:03
 */
@Data
@Entity
@Table(name = "t_order_invoice")
public class OrderInvoice implements Serializable {

 private static final long serialVersionUID = -34149303592292500L;
 /**
  * 主键
  */
 @Id
 @Column(name = "id", nullable = false)
 @GeneratedValue(generator = "system-uuid")
 @GenericGenerator(name = "system-uuid", strategy = "uuid")
 private String id;
 /**
  * 落地商订单 id
  */
 @Column(name = "order_id")
 @Deprecated
 private String orderId;
 /**
  * 发票类型
  */
 @Column(name = "c_type")
 private String type;
 /**
  * 发票抬头
  */
 @Column(name = "c_title")
 private String title;
 /**
  * 税号
  */
 @Column(name = "c_tax_number")
 private String taxNumber;
 /**
  * 开户银行
  */
 @Column(name = "c_bank_name")
 private String bankName;
 /**
  * 银行账号
  */
 @Column(name = "c_bank_account")
 private String bankAccount;
 /**
  * 电话
  */
 @Column(name = "c_mobile")
 private String mobile;
 /**
  * 地址
  */
 @Column(name = "c_address")
 private String address;
 /**
  * 票面信息
  */
 @Column(name = "c_content")
 private String content;
 /**
  * 收件人
  */
 @Column(name = "c_receive_man")
 private String receiveMan;
 /**
  * 收件人地址
  */
 @Column(name = "c_receive_address")
 private String receiveAddress;
 /**
  * 其它备注
  */
 @Column(name = "c_remark")
 private String remark;
 /**
  * 收件人联系电话
  */
 @Column(name = "c_receive_mobile")
 private String receiveMobile;
 /**
  * 创建时间
  */
 @Column(name = "c_create_time")
 private Long createTime;
 /**开票申请单号*/
 @Column(name = "c_invoice_application_number")
 private String invoiceApplicationNumber;
 /**
  * 邮箱
  */
 @Column(name = "c_mail")
 private String mail;
}

