package com.xhgj.srm.jpa.dao.impl;/**
 * @since 2025/3/17 15:38
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.enums.BooleanEnum;
import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.common.enums.TitleOfTheContractEnum;
import com.xhgj.srm.common.utils.DateUtil;
import com.xhgj.srm.common.utils.SQLUtils;
import com.xhgj.srm.common.vo.order.OrderNeedPaymentListVO;
import com.xhgj.srm.common.vo.order.OrderNeedPaymentStatistics;
import com.xhgj.srm.jpa.dao.OrderNeedPaymentDao;
import com.xhgj.srm.jpa.entity.OrderNeedPayment;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class OrderNeedPaymentDaoImpl extends AbstractExtDao<OrderNeedPayment> implements OrderNeedPaymentDao {

  @Override
  public PageResult<OrderNeedPaymentListVO> getPage(Map<String, Object> queryMap) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    sql.append("select ");
    sql.append("onp.id, ");
    sql.append("o.id as orderId, ");
    sql.append("o.c_order_no as orderNo, ");
    sql.append("onp.c_platform_code as platformCode, ");
    sql.append("o.c_order_time as createTime, ");
    sql.append("o.c_first_ship_time as firstShipTime, ");
    sql.append("o.c_all_ship_time as allShipTime, ");

    sql.append("o.c_confirm_voucher_audit_status as signVoucherState, ");
    sql.append("o.c_confirm_voucher_time as confirmVoucherTime, ");
    sql.append("o.c_invoicing_state as invoicingState, ");
    sql.append("o.c_customer_invoice_time as customerInvoiceTime, ");
    sql.append("o.c_account_open_invoice_status as supplierOpenInvoiceStatus, ");
    sql.append("o.c_confirm_account_open_invoice_time as confirmAccountOpenInvoiceTime, ");
    sql.append("o.c_customer_accept_time as customerAcceptTime, ");
    sql.append("o.c_customer_return_progress as customerPayback, ");
    //客户回款方式
    sql.append("orr.c_payment_method as paymentMethod, ");
    //付款方式
    sql.append("orr.c_sap_payment_method as sapPaymentMethod, ");
    sql.append("onp.c_show_rate as showRate, ");
    sql.append("orr.c_bank_serial_no as bankSerialNo, ");
    sql.append("orr.c_bill_no as billNo, ");
    sql.append("o.c_payment_condition as paymentCondition, ");
    sql.append("o.c_payment_condition_time as paymentConditionTime, ");
    sql.append("o.c_back_to_back as backToBack, ");
    sql.append("o.c_accounting_period as accountingPeriod, ");
    sql.append("o.c_predict_payment_time as paymentDate, ");
    sql.append("s.c_enterpriseName as supplierName, ");
    //实际订货金额=订单金额-退货金额-取消金额
    sql.append("COALESCE(o.c_price, 0) - COALESCE(o.c_refund_price, 0) - COALESCE(o.c_cancel_price, 0) as orderActualAmount, ");
//    sql.append("orr.c_payment_method as paymentMethod, ");
    sql.append("onp.c_rate as rate, ");
    sql.append("onp.c_paid_amount as paidAmount, ");
    sql.append("onp.c_return_amount as returnAmount, ");
    sql.append("o.c_erp_type as erpType, ");
    sql.append("s.id as supplierId, ");
    sql.append("o.c_prohibition_payment_state as prohibitionPaymentState, ");
    sql.append("o.c_title_of_the_contract as titleOfTheContract, ");
    //取消金额
    sql.append("o.c_cancel_price as orderCancelAmount, ");
    //退货金额
    sql.append("o.c_refund_price as orderReturnAmount, ");
    sql.append("onp.c_offset as offset, ");
    sql.append("onp.c_deduct as deduct ");

    sql.append("from t_order_need_payment onp ");
    sql.append("left join t_order o on CONVERT(onp.c_order_id USING utf8) = o.id ");
    sql.append("left join t_order_receipt_record orr on orr.id = onp.c_order_receipt_id ");
    sql.append("left join t_supplier s on s.id = CONVERT(onp.c_supplier_id USING utf8) ");
    this.buildWhere(sql, params, queryMap);
    sql.append(" order by onp.c_create_time desc ");
    Page page =
        findPageSqlObject(sql.toString(), params.toArray(), (Integer) queryMap.get("pageNo"),
            (Integer) queryMap.get("pageSize"));
    List<Object[]> content = page.getContent();

    List<OrderNeedPaymentListVO> res = content.parallelStream().map(item -> {
      int index = 0;
      OrderNeedPaymentListVO vo = new OrderNeedPaymentListVO();
      vo.setId(Convert.toStr(item[index++]));
      vo.setOrderId(Convert.toStr(item[index++]));
      vo.setOrderNo(Convert.toStr(item[index++]));
      vo.setPlatformCode(Convert.toStr(item[index++]));
      vo.setCreateTime(Convert.toLong(item[index++]));
      //首次发货时间
      vo.setFirstShipTime(Convert.toLong(item[index++]));
      //全部发货时间
      vo.setAllShipTime(Convert.toLong(item[index++]));
      //签收凭证状态
      vo.setSignVoucherState(Convert.toStr(item[index++]));
      //签收凭证通过时间
      vo.setConfirmVoucherTime(Convert.toLong(item[index++]));
      //客户开票状态
      vo.setInvoicingState(Convert.toStr(item[index++]));
      //客户开票日期
      vo.setCustomerInvoiceTime(Convert.toLong(item[index++]));
      //供应商开票状态
      vo.setSupplierOpenInvoiceStatus(Convert.toStr(item[index++]));
      //供应商开票日期
      vo.setConfirmAccountOpenInvoiceTime(Convert.toLong(item[index++]));
      //客户签收时间
      vo.setCustomerAcceptTime(Convert.toLong(item[index++]));
      //客户回款状态
      String customerReturnProgress = Convert.toStr(item[index++]);
      vo.setCustomerPayback(Constants_order.getCustomerPaybackStateNameByReturnProgress(
          customerReturnProgress, Constants_order.CUSTOMER_PAYBACK_UN));
      //客户回款方式
      vo.setPaymentMethod(Convert.toStr(item[index++]));
      vo.setPaymentMethodValue(vo.getPaymentMethodValue());
      //付款方式
      vo.setSapPaymentMethod(Convert.toStr(item[index++]));
      vo.setSapPaymentMethodValue(vo.getSapPaymentMethodValue());
      //回款比例
      vo.setShowRate(Convert.toBigDecimal(item[index++]));
      //银行流水号
      vo.setBankSerialNo(Convert.toStr(item[index++]));
      //汇票号
      vo.setBillNo(Convert.toStr(item[index++]));
      //付款发起条件
      vo.setPaymentCondition(Convert.toStr(item[index++]));
      //付款满足日期
      vo.setPaymentConditionTime(Convert.toLong(item[index++]));
      //是否背靠背
      vo.setBackToBack(Convert.toBool(item[index++]));
      //账期
      vo.setAccountingPeriod(Convert.toStr(item[index++]));
      //预计付款日期
      vo.setPaymentDate(Convert.toLong(item[index++]));
      //供应商名称
      vo.setSupplierName(Convert.toStr(item[index++]));
      //实际订货金额
      vo.setOrderActualAmount(Convert.toBigDecimal(item[index++]));
//      //付款方式，这里需要基于客户回款映射出付款方式
//      vo.setPaymentMethod(Convert.toStr(item[index++]));
//      //付款方式中文
//      vo.setPaymentMethodValue(vo.getPaymentMethodValue());
      //比例,用于计算
      vo.setRate(Convert.toBigDecimal(item[index++]));
      //已提款金额
      vo.setPaidAmount(Convert.toBigDecimal(item[index++]));
      //退款金额
      vo.setReturnAmount(Convert.toBigDecimal(item[index++]));
      //erp类型
      vo.setErpType(Convert.toStr(item[index++]));
      //供应商id
      vo.setSupplierId(Convert.toStr(item[index++]));
      //禁止付款标签
      vo.setProhibitionPaymentState(Convert.toBool(item[index++]));
      //签约抬头（组织编码）
      TitleOfTheContractEnum titleOfTheContractEnum =
          TitleOfTheContractEnum.getEnumByCode(Convert.toStr(item[index++]));
      vo.setTitleOfTheContract(titleOfTheContractEnum == null ? StrUtil.EMPTY : titleOfTheContractEnum.getName());
      //取消金额
      vo.setOrderCancelAmount(Convert.toBigDecimal(item[index++]));
      //退货金额
      vo.setOrderReturnAmount(Convert.toBigDecimal(item[index++]));
      //是否被冲销
      vo.setOffset(Convert.toBool(item[index++]));
      //是否扣除
      vo.setDeduct(Convert.toBool(item[index++]));

      //可提款金额
      vo.setAvailableAmount(vo.getAvailableAmount());
      //剩余可提款金额
      vo.setRemainingAmount(vo.getRemainingAmount());
      return vo;
    }).collect(Collectors.toList());

    return new PageResult<>(res, page.getTotalElements(), page.getTotalPages(),
        (Integer) queryMap.get("pageNo"), (Integer) queryMap.get("pageSize"));
  }

  private void buildWhere(StringBuilder sql, List<Object> params, Map<String, Object> queryMap) {
    sql.append("where onp.c_state = ? ");
    params.add(Constants.STATE_OK);
    sql.append("and onp.c_part_pay = ? ");
    params.add(BooleanEnum.NO.getKey());
    sql.append("and o.c_state = ?  and s.c_state= ? ");
    params.add(Constants.STATE_OK);
    params.add(Constants.STATE_OK);
    String orderNo = Convert.toStr(queryMap.get("orderNo"));
    if (StrUtil.isNotBlank(orderNo)) {
      sql.append("and o.c_order_no = ? ");
      params.add(orderNo);
    }
    if (StrUtil.isNotBlank((CharSequence) queryMap.get("platformCode"))) {
      // 下单平台可以多选，用,分割
      String platform = (String) queryMap.get("platformCode");
      String[] split = platform.split(",");
      ArrayList<String> platformList = new ArrayList<>(Arrays.asList(split));
      if (CollUtil.isNotEmpty(platformList)) {
        sql.append("and onp.c_platform_code in (");
        for (int i = 0; i < platformList.size(); i++) {
          if (i == platformList.size() - 1) {
            sql.append("?");
          } else {
            sql.append("?,");
          }
          params.add(platformList.get(i));
        }
        sql.append(") ");
      }
    }
    if (queryMap.get("startCreateTime") != null && queryMap.get("endCreateTime") != null) {
      sql.append(" and o.c_order_time >= ? and o.c_order_time <= ? ");
      params.add(queryMap.get("startCreateTime"));
      params.add(queryMap.get("endCreateTime"));
    }
    // 发货状态 1：未发货（两个时间都没有），2：部分发货：（有首次发货时间且没有全部发货时间），已发货：（有全部发货时间）
    String shipState = Convert.toStr(queryMap.get("shipState"));
    if (StrUtil.isNotBlank(shipState)) {
      if (StrUtil.equals(shipState, Constants.NEED_PAYMENT_NOT_SHIPPED)) {
        sql.append(" and o.c_first_ship_time is null and o.c_all_ship_time is null ");
      } else if (StrUtil.equals(shipState, Constants.NEED_PAYMENT_PARTIAL_SHIPPED)) {
        sql.append(" and o.c_first_ship_time is not null and o.c_all_ship_time is null ");
      } else if (StrUtil.equals(shipState, Constants.NEED_PAYMENT_SHIPPED)) {
        sql.append(" and o.c_all_ship_time is not null ");
      }
    }
    // 首次发货时间
    if (queryMap.get("startFirstShipTime") != null && queryMap.get("endFirstShipTime") != null) {
      sql.append(" and o.c_first_ship_time >= ? and o.c_first_ship_time <= ? ");
      params.add(queryMap.get("startFirstShipTime"));
      params.add(queryMap.get("endFirstShipTime"));
    }
    // 全部发货时间
    if (queryMap.get("startAllShipTime") != null && queryMap.get("endAllShipTime") != null) {
      sql.append(" and o.c_all_ship_time >= ? and o.c_all_ship_time <= ? ");
      params.add(queryMap.get("startAllShipTime"));
      params.add(queryMap.get("endAllShipTime"));
    }
    if (StrUtil.isNotBlank((CharSequence) queryMap.get("signVoucherState"))) {
      // 签收凭证可以多选，用,分割
      String signVoucherState = (String) queryMap.get("signVoucherState");
      String[] split = signVoucherState.split(",");
      ArrayList<String> signVoucherStateList = new ArrayList<>(Arrays.asList(split));
      if (CollUtil.isNotEmpty(signVoucherStateList)) {
        sql.append("and o.c_confirm_voucher_audit_status in (");
        for (int i = 0; i < signVoucherStateList.size(); i++) {
          if (i == signVoucherStateList.size() - 1) {
            sql.append("?");
          } else {
            sql.append("?,");
          }
          params.add(signVoucherStateList.get(i));
        }
        sql.append(") ");
      }
    }
    if (queryMap.get("startConfirmVoucherTime") != null && queryMap.get("endConfirmVoucherTime") != null) {
      sql.append(" and o.c_confirm_voucher_time >= ? and o.c_confirm_voucher_time <= ? ");
      params.add(queryMap.get("startConfirmVoucherTime"));
      params.add(queryMap.get("endConfirmVoucherTime"));
    }
    if (StrUtil.isNotBlank((CharSequence) queryMap.get("invoicingState"))) {
      // 客户开票状态可以多选，用,分割
      String result = (String) queryMap.get("invoicingState");
      String[] split = result.split(",");
      ArrayList<String> resultList = new ArrayList<>(Arrays.asList(split));
      if (CollUtil.isNotEmpty(resultList)) {
        sql.append("and o.c_invoicing_state in (");
        for (int i = 0; i < resultList.size(); i++) {
          if (i == resultList.size() - 1) {
            sql.append("?");
          } else {
            sql.append("?,");
          }
          params.add(resultList.get(i));
        }
        sql.append(") ");
      }
    }
    if (queryMap.get("startCustomerInvoiceTime") != null && queryMap.get("endCustomerInvoiceTime") != null) {
      sql.append(" and o.c_customer_invoice_time >= ? and o.c_customer_invoice_time <= ? ");
      params.add(queryMap.get("startCustomerInvoiceTime"));
      params.add(queryMap.get("endCustomerInvoiceTime"));
    }
    if (StrUtil.isNotBlank((CharSequence) queryMap.get("supplierOpenInvoiceStatus"))) {
      // 供应商开票状态可以多选，用,分割
      String result = (String) queryMap.get("supplierOpenInvoiceStatus");
      String[] split = result.split(",");
      ArrayList<String> resultList = new ArrayList<>(Arrays.asList(split));
      if (CollUtil.isNotEmpty(resultList)) {
        sql.append("and o.c_account_open_invoice_status in (");
        for (int i = 0; i < resultList.size(); i++) {
          if (i == resultList.size() - 1) {
            sql.append("?");
          } else {
            sql.append("?,");
          }
          params.add(resultList.get(i));
        }
        sql.append(") ");
      }
    }
    if (queryMap.get("startConfirmAccountOpenInvoiceTime") != null && queryMap.get("endConfirmAccountOpenInvoiceTime") != null) {
      sql.append(" and o.c_confirm_account_open_invoice_time >= ? and o.c_confirm_account_open_invoice_time <= ? ");
      params.add(queryMap.get("startConfirmAccountOpenInvoiceTime"));
      params.add(queryMap.get("endConfirmAccountOpenInvoiceTime"));
    }
    if (queryMap.get("startCustomerAcceptTime") != null && queryMap.get("endCustomerAcceptTime") != null) {
      sql.append(" and o.c_customer_accept_time >= ? and o.c_customer_accept_time <= ? ");
      params.add(queryMap.get("startCustomerAcceptTime"));
      params.add(queryMap.get("endCustomerAcceptTime"));
    }
    if (StrUtil.isNotBlank((CharSequence) queryMap.get("customerPayback"))) {
      // 客户回款状态可以多选，用,分割
      String result = (String) queryMap.get("customerPayback");
      String[] split = result.split(",");
      ArrayList<String> resultList = new ArrayList<>(Arrays.asList(split));
      if (CollUtil.isNotEmpty(resultList)) {
        sql.append("and o.c_customer_return_progress in (");
        for (int i = 0; i < resultList.size(); i++) {
          if (i == resultList.size() - 1) {
            sql.append("?");
          } else {
            sql.append("?,");
          }
          params.add(resultList.get(i));
        }
        sql.append(") ");
      }
    }
    if (StrUtil.isNotBlank((CharSequence) queryMap.get("paymentMethod"))) {
      // 客户回款方式可以多选，用,分割
      String result = (String) queryMap.get("paymentMethod");
      String[] split = result.split(",");
      ArrayList<String> resultList = new ArrayList<>(Arrays.asList(split));
      if (CollUtil.isNotEmpty(resultList)) {
        sql.append("and orr.c_payment_method in (");
        for (int i = 0; i < resultList.size(); i++) {
          if (i == resultList.size() - 1) {
            sql.append("?");
          } else {
            sql.append("?,");
          }
          params.add(resultList.get(i));
        }
        sql.append(") ");
      }
    }
    //回款比例
    if (queryMap.get("paymentProportionOperators") != null && queryMap.get("paymentProportion") != null) {
      LogicalOperatorsEnums amountOperators =
          (LogicalOperatorsEnums) queryMap.get("paymentProportionOperators");
      SQLUtils.addLogicalOperators(amountOperators, queryMap.get("paymentProportion"), sql, params,
          "onp.c_show_rate");
    }

    if (StrUtil.isNotBlank((CharSequence) queryMap.get("bankSerialNo"))) {
      sql.append(" and orr.c_bank_serial_no = ? ");
      params.add(queryMap.get("bankSerialNo"));
    }
    if (StrUtil.isNotBlank((CharSequence) queryMap.get("billNo"))) {
      sql.append(" and orr.c_bill_no = ? ");
      params.add(queryMap.get("billNo"));
    }
    //付款发起条件
    if (StrUtil.isNotBlank((CharSequence) queryMap.get("paymentCondition"))) {
      sql.append(" and ( ");
      String[] split = StrUtil.split((String) queryMap.get("paymentCondition"), StrUtil.COMMA);
      for (int i = 0; i < split.length; i++) {
        String paymentConditionStr = split[i];
        if (i == split.length - 1) {
          sql.append(" o.c_payment_condition like ? ");
        } else {
          sql.append(" o.c_payment_condition like ? and ");
        }
        params.add("%" + paymentConditionStr + "%");
      }
      sql.append(" ) ");
    }
    if (queryMap.get("startPaymentConditionTime") != null && queryMap.get("endPaymentConditionTime") != null) {
      sql.append(" and o.c_payment_condition_time >= ? and o.c_payment_condition_time <= ? ");
      params.add(queryMap.get("startPaymentConditionTime"));
      params.add(queryMap.get("endPaymentConditionTime"));
    }
    if (queryMap.get("accountingPeriod") != null) {
      sql.append( " and o.c_accounting_period = ? ");
      params.add(queryMap.get("accountingPeriod"));
    }
    //预计付款日期
    if (queryMap.get("startPaymentDate") != null && queryMap.get("endPaymentDate") != null) {
      sql.append(" and o.c_predict_payment_time >= ? and o.c_predict_payment_time <= ? ");
      params.add(queryMap.get("startPaymentDate"));
      params.add(queryMap.get("endPaymentDate"));
    }
    //付款方式
    if (StrUtil.isNotBlank((CharSequence) queryMap.get("sapPaymentMethod"))) {
      // 付款方式可以多选，用,分割
      String result = (String) queryMap.get("sapPaymentMethod");
      String[] split = result.split(",");
      ArrayList<String> resultList = new ArrayList<>(Arrays.asList(split));
      if (CollUtil.isNotEmpty(resultList)) {
        sql.append("and orr.c_sap_payment_method in (");
        for (int i = 0; i < resultList.size(); i++) {
          if (i == resultList.size() - 1) {
            sql.append("?");
          } else {
            sql.append("?,");
          }
          params.add(resultList.get(i));
        }
        sql.append(") ");
      }
    }
    //供应商名称
    if (StrUtil.isNotBlank((CharSequence) queryMap.get("supplierName"))) {
      sql.append("and s.c_enterpriseName like ? ");
      params.add("%" + queryMap.get("supplierName") + "%");
    }
    //已提款金额
    if (queryMap.get("paidAmountOperators") != null) {
      LogicalOperatorsEnums amountOperators = (LogicalOperatorsEnums) queryMap.get(
          "paidAmountOperators");
      SQLUtils.addLogicalOperators(amountOperators, queryMap.get("paidAmount"), sql, params,
          "onp.c_paid_amount");
    }
    //退款金额
    if (queryMap.get("returnAmountOperators") != null) {
      LogicalOperatorsEnums amountOperators = (LogicalOperatorsEnums) queryMap.get(
          "returnAmountOperators");
      SQLUtils.addLogicalOperators(amountOperators, queryMap.get("returnAmount"), sql, params,
          "onp.c_return_amount");
    }
    //可提款金额:
    // 账期 可提金额 = 100% * 订单实际订货金额
    //背靠背 = 回款比例 * 订单实际订货金额
    if (queryMap.get("availableAmountOperators") != null
        && queryMap.get("availableAmount") != null) {
      LogicalOperatorsEnums amountOperators =
          (LogicalOperatorsEnums) queryMap.get("availableAmountOperators");
      SQLUtils.addLogicalOperators(amountOperators, queryMap.get("availableAmount"), sql, params,
          "(COALESCE(o.c_price, 0) - COALESCE(o.c_refund_price, 0) - COALESCE(o.c_cancel_price, 0)) * COALESCE(onp.c_rate, 0)");
    }

    //剩余可提款金额 = 可提款金额 - 已提款金额 + 退款金额
    if (queryMap.get("remainingAmountOperators") != null && queryMap.get("remainingAmount") != null) {
      LogicalOperatorsEnums amountOperators =
          (LogicalOperatorsEnums) queryMap.get("remainingAmountOperators");
      String filedStr =
          "(COALESCE(o.c_price, 0) - COALESCE(o.c_refund_price, 0) - COALESCE(o.c_cancel_price, 0)) * COALESCE(onp.c_rate, 0) -"
              + " COALESCE(onp.c_paid_amount, 0) + COALESCE(onp.c_return_amount, 0)";
      SQLUtils.addLogicalOperators(amountOperators, queryMap.get("remainingAmount"), sql, params,
          filedStr);
    }
    //达到预计付款时间且剩余可提款金额大于0的
    if (BooleanUtil.isTrue((Boolean) queryMap.get("isArrivePayable"))) {
      sql.append( " and o.c_predict_payment_time <= ? ");
      params.add(DateUtil.getDailyEndTime(System.currentTimeMillis()));
      //剩余可提款金额
      sql.append( " and ((COALESCE(o.c_price, 0) - COALESCE(o.c_refund_price, 0) - COALESCE(o.c_cancel_price, 0)) * COALESCE(onp"
          + ".c_rate, 0)) - COALESCE(onp.c_paid_amount, 0) + COALESCE(onp.c_return_amount, 0) > 0 ");
    }

    if (BooleanUtil.isTrue((Boolean) queryMap.get("isReturn"))) {
      sql.append( " and o.c_order_return_state = ? ");
      params.add(queryMap.get("isReturn"));
    }
    //存在回款类型为B或回款类型为C，回款金额为负
    if (BooleanUtil.isTrue((Boolean) queryMap.get("isDeductionPayable"))) {
      sql.append( " and (orr.c_payment_type = ? or orr.c_payment_type = ? ) and COALESCE(orr.c_payment_amount, 0) < 0 ");
      params.add("B");
      params.add("C");
    }
    if (queryMap.get("backToBack") != null) {
      sql.append(" and o.c_back_to_back = ? ");
      params.add(queryMap.get("backToBack"));
    }

    // ids
    List<String> ids = Convert.toList(String.class, queryMap.get("ids"));
    if (CollUtil.isNotEmpty(ids)) {
      sql.append("and onp.id in (");
      for (int i = 0; i < ids.size(); i++) {
        if (i > 0) {
          sql.append(",");
        }
        sql.append("?");
        params.add(ids.get(i));
      }
      sql.append(") ");
    }

  }

  @Override
  public OrderNeedPaymentStatistics getOrderAmountStatistics(Map<String, Object> queryMap) {
    StringBuilder sql = new StringBuilder();
    sql.append("select ");
    //实际订货金额总计
    sql.append("COALESCE(SUM(COALESCE(o.c_price, 0) - COALESCE(o.c_refund_price, 0) - COALESCE(o.c_cancel_price, 0)), 0) as "
        + "totalOrderActualAmount, ");
    //已提款金额总计
    sql.append("COALESCE(SUM(onp.c_paid_amount), 0) as totalPaidAmount, ");
    //已退款金额总计
    sql.append("COALESCE(SUM(onp.c_return_amount), 0) as totalReturnAmount, ");
    //可提款金额总计
    sql.append("COALESCE(SUM((COALESCE(o.c_price, 0) - COALESCE(o.c_refund_price, 0) - COALESCE(o.c_cancel_price, 0)) * COALESCE"
        + "(onp.c_rate, 0)),0) as totalAvailableAmount, ");
    //剩余可提款金额总计，剩余可提款金额 = 可提款金额 - 已提款金额 + 退款金额
    sql.append("coalesce(SUM((coalesce(o.c_price, 0) - coalesce(o.c_refund_price, 0) - COALESCE(o.c_cancel_price, 0)) * coalesce"
        + "(onp.c_rate, 0) - coalesce(onp.c_paid_amount, 0) + coalesce(onp.c_return_amount, 0)),0) as totalRemainingAmount ");

    sql.append("from t_order_need_payment onp ");
    sql.append("left join t_order o on CONVERT(onp.c_order_id USING utf8) = o.id ");
    sql.append("left join t_order_receipt_record orr on orr.id = onp.c_order_receipt_id ");
    sql.append("left join t_supplier s on s.id = CONVERT(onp.c_supplier_id USING utf8) ");

    List<Object> params = new ArrayList<>();
    this.buildWhere(sql, params, queryMap);

    Object[] sqlObj = (Object[]) getUniqueSqlObj(sql.toString(), params.toArray());
    OrderNeedPaymentStatistics orderNeedPaymentStatistics = new OrderNeedPaymentStatistics();
    orderNeedPaymentStatistics.setTotalOrderActualAmount(Convert.toBigDecimal(sqlObj[0]));
    orderNeedPaymentStatistics.setTotalPaidAmount(Convert.toBigDecimal(sqlObj[1]));
    orderNeedPaymentStatistics.setTotalReturnAmount(Convert.toBigDecimal(sqlObj[2]));
    orderNeedPaymentStatistics.setTotalAvailableAmount(Convert.toBigDecimal(sqlObj[3]));
    orderNeedPaymentStatistics.setTotalRemainingAmount(Convert.toBigDecimal(sqlObj[4]));
    return orderNeedPaymentStatistics;
  }
}

