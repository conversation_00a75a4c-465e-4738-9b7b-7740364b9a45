package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Entity
@Table(name = "t_contract")
@Data
public class Contract implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  @JoinColumn(name = "supplierId", insertable = false, updatable = false)
  @ManyToOne
  private Supplier supplier;

  @Column(name = "supplierId")
  private String supplierId;

  /** 组织内供应商 id */
  @Column(name = "supplier_in_group_id")
  private String supplierInGroupId;

  @Column(name = "c_supplierName")
  private String supplierName;

  @Column(name = "c_code")
  private String code;

  @Column(name = "c_contractNum")
  private String contractNum;

  @Column(name = "c_contractMoney")
  private String contractMoney;

  @Column(name = "c_contractTime")
  private Long contractTime;

  @Column(name = "c_isFile")
  private String isFile;

  @Column(name = "c_uploadTime")
  private Long uploadTime;

  @Column(name = "c_createUser")
  private String createUser;

  /** 负责采购 */
  @Column(name = "c_purchaserName")
  private String purchaserName;

  @Column(name = "c_createTime")
  private Long createTime;

  @Column(name = "c_state")
  private String state;

  @Column(name = "c_createManId")
  private String createManId;

  /** 负责采购code */
  @Column(name = "c_purchaserManId")
  private String purchaserManId;


}
