package com.xhgj.srm.jpa.enums;/**
 * @since 2025/4/21 14:14
 */

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.PurchaseApplyRecord.PurchaseApplyRecordJson;
import com.xhgj.srm.jpa.entity.v2.PurchaseApplyForOrderV2;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/4/21 14:14:18
 *@description
 */
public enum PurchaseApplyRecordFields {
  /**
   * 采购员
   * 格式：XHGJ00231张三 → XHGJ00299李四
   */
  PURCHASER("PURCHASER", "采购员"),
  /**
   * 采购部门
   * 格式：数字化部门 →办公用品采购部
   */
  PURCHASING_DEPARTMENT("PURCHASING_DEPARTMENT", "采购部门"),
  /**
   * 申请数量
   * 格式：100 → 77
   */
  APPLY_QUANTITY("APPLY_QUANTITY", "申请数量"),
  /**
   * 申请单备注
   * 格式：我是备注 → 我是备注
   */
  APPLY_ORDER_REMARK("APPLY_ORDER_REMARK", "申请单备注"),
  /**
   * 科目分配类别
   * 格式：123数字化部门 → 345办公用品采购部
   */
  SUBJECT_ALLOCATION_TYPE("SUBJECT_ALLOCATION_TYPE", "科目分配类别"),
  /**
   * 订单
   * 格式：555订单 → 2333订单
   */
  ORDER("ORDER", "订单"),
  /**
   * 成本中心
   * 格式：123数字化部门 → 345办公用品采购部
   */
  COST_CENTER("COST_CENTER", "成本中心"),
  /**
   * 总账单科目
   * 格式：123数字化部门 → 345办公用品采购部
   */
  GENERAL_LEDGER_ACCOUNT("GENERAL_LEDGER_ACCOUNT", "总账单科目"),
  /**
   * 交货日期
   * 格式：2025-04-21 → 2025-04-22
   */
  DELIVERY_DATE("DELIVERY_DATE", "交货日期"),

  ;

  /**
   * 字段key
   */
  private String key;

  /**
   * 字段值
   */
  private String value;

  PurchaseApplyRecordFields(String key, String value) {
    this.key = key;
    this.value = value;
  }

  /**
   * PurchaseApplyForOrderV2 通过反射字段 + 字段值获取
   */
  public static void updateFiles(PurchaseApplyForOrderV2 orderV2,
      List<PurchaseApplyRecordJson> changeList) {
    // changeList过滤出有fieldName的
    List<PurchaseApplyRecordJson> fieldNameList =
        changeList.stream().filter(item -> StrUtil.isNotBlank(item.getFieldName()))
            .collect(Collectors.toList());
    for (PurchaseApplyRecordJson recordJson : fieldNameList) {
      try {
        ReflectUtil.setFieldValue(orderV2, recordJson.getFieldName(), recordJson.getNewValue());
      } catch (Exception e) {
        // 反射失败，可能是字段不存在
        // 记录日志
        System.err.println("反射失败，字段名：" + recordJson.getFieldName() + "，异常信息：" + e.getMessage());
      }
    }
  }

  public String getValue() {
    return value;
  }

  public String getKey() {
    return key;
  }

  /**
   * 判断采购员
   * @param oldCode
   * @param newCode
   * @param oldName
   * @param newName
   * @return
   */
  public static List<PurchaseApplyRecordJson> judgePurchaser(
      String oldCode,
      String newCode,
      String oldName,
      String newName) {
    oldName = StrUtil.emptyIfNull(oldName);
    newName = StrUtil.emptyIfNull(newName);
    oldCode = StrUtil.emptyIfNull(oldCode);
    newCode = StrUtil.emptyIfNull(newCode);
    List<PurchaseApplyRecordJson> jsonArray = new ArrayList<>();
    if (!StrUtil.equals(oldCode, newCode)) {
      PurchaseApplyRecordJson json = new PurchaseApplyRecordJson();
      json.setKey(PurchaseApplyRecordFields.PURCHASER.getKey());
      json.setDesc(PurchaseApplyRecordFields.PURCHASER.getValue());
      String oldValue =
          StrUtil.format("{}{}", oldCode, oldName);
      json.setOldValue(oldValue);
      String newValue =
          StrUtil.format("{}{}", newCode, newName);
      json.setNewValue(newValue);
      json.setFieldName(null);
      json.setShowFlag(true);
      PurchaseApplyRecordJson json2 = new PurchaseApplyRecordJson();
      json2.setKey(PurchaseApplyRecordFields.PURCHASER.getKey());
      json2.setDesc(PurchaseApplyRecordFields.PURCHASER.getValue());
      json2.setOldValue(oldName);
      json2.setNewValue(newName);
      json2.setFieldName("purchaseMan");
      json2.setShowFlag(false);
      PurchaseApplyRecordJson json3 = new PurchaseApplyRecordJson();
      json3.setKey(PurchaseApplyRecordFields.PURCHASER.getKey());
      json3.setDesc(PurchaseApplyRecordFields.PURCHASER.getValue());
      json3.setOldValue(oldCode);
      json3.setNewValue(newCode);
      json3.setFieldName("purchaseManNumber");
      json3.setShowFlag(false);
      jsonArray.add(json);
      jsonArray.add(json2);
      jsonArray.add(json3);
    }
    return jsonArray;
  }

  /**
   * 判断采购部门
   */
  public static List<PurchaseApplyRecordJson> judgePurchasingDepartment(
      String oldCode,
      String newCode,
      String oldName,
      String newName) {
    oldName = StrUtil.emptyIfNull(oldName);
    newName = StrUtil.emptyIfNull(newName);
    oldCode = StrUtil.emptyIfNull(oldCode);
    newCode = StrUtil.emptyIfNull(newCode);
    List<PurchaseApplyRecordJson> jsonArray = new ArrayList<>();
    if (!StrUtil.equals(oldCode, newCode)) {
      PurchaseApplyRecordJson json = new PurchaseApplyRecordJson();
      json.setKey(PurchaseApplyRecordFields.PURCHASING_DEPARTMENT.getKey());
      json.setDesc(PurchaseApplyRecordFields.PURCHASING_DEPARTMENT.getValue());
      json.setOldValue(oldName);
      json.setNewValue(newName);
      json.setFieldName(null);
      json.setShowFlag(true);
      PurchaseApplyRecordJson json2 = new PurchaseApplyRecordJson();
      json2.setKey(PurchaseApplyRecordFields.PURCHASING_DEPARTMENT.getKey());
      json2.setDesc(PurchaseApplyRecordFields.PURCHASING_DEPARTMENT.getValue());
      json2.setOldValue(oldCode);
      json2.setNewValue(newCode);
      json2.setFieldName("purchaseDepartment");
      json2.setShowFlag(false);
      jsonArray.add(json);
      jsonArray.add(json2);
    }
    return jsonArray;
  }

  /**
   * 判断申请数量
   */
  public static List<PurchaseApplyRecordJson> judgeApplyQuantity(
      BigDecimal oldValue,
      BigDecimal newValue) {
    oldValue = oldValue == null ? BigDecimal.ZERO : oldValue;
    newValue = newValue == null ? BigDecimal.ZERO : newValue;
    List<PurchaseApplyRecordJson> jsonArray = new ArrayList<>();
    if (!NumberUtil.equals(oldValue, newValue)) {
      PurchaseApplyRecordJson json = new PurchaseApplyRecordJson();
      json.setKey(PurchaseApplyRecordFields.APPLY_QUANTITY.getKey());
      json.setDesc(PurchaseApplyRecordFields.APPLY_QUANTITY.getValue());
      json.setOldValue(oldValue.stripTrailingZeros().toPlainString());
      json.setNewValue(newValue.stripTrailingZeros().toPlainString());
      json.setFieldName("applyForNumber");
      json.setShowFlag(true);
      jsonArray.add(json);
    }
    return jsonArray;
  }

  /**
   * 判断申请单备注
   */
  public static List<PurchaseApplyRecordJson> judgeApplyOrderRemark(
      String oldValue,
      String newValue) {
    oldValue = StrUtil.emptyIfNull(oldValue);
    newValue = StrUtil.emptyIfNull(newValue);
    List<PurchaseApplyRecordJson> jsonArray = new ArrayList<>();
    if (!StrUtil.equals(oldValue, newValue)) {
      PurchaseApplyRecordJson json = new PurchaseApplyRecordJson();
      json.setKey(PurchaseApplyRecordFields.APPLY_ORDER_REMARK.getKey());
      json.setDesc(PurchaseApplyRecordFields.APPLY_ORDER_REMARK.getValue());
      json.setOldValue(oldValue);
      json.setNewValue(newValue);
      json.setFieldName("applicationFormRemarks");
      json.setShowFlag(true);
      jsonArray.add(json);
    }
    return jsonArray;
  }

  /**
   * 判断科目分配类别
   */
  public static List<PurchaseApplyRecordJson> judgeSubjectAllocationType(
      String oldCode,
      String newCode,
      String oldName,
      String newName) {
    oldName = StrUtil.emptyIfNull(oldName);
    newName = StrUtil.emptyIfNull(newName);
    oldCode = StrUtil.emptyIfNull(oldCode);
    newCode = StrUtil.emptyIfNull(newCode);
    List<PurchaseApplyRecordJson> jsonArray = new ArrayList<>();
    if (!StrUtil.equals(oldCode, newCode)) {
      PurchaseApplyRecordJson json = new PurchaseApplyRecordJson();
      json.setKey(PurchaseApplyRecordFields.SUBJECT_ALLOCATION_TYPE.getKey());
      json.setDesc(PurchaseApplyRecordFields.SUBJECT_ALLOCATION_TYPE.getValue());
      String oldValue =
          StrUtil.format("{}{}", oldCode, oldName);
      json.setOldValue(oldValue);
      String newValue =
          StrUtil.format("{}{}", newCode, newName);
      json.setNewValue(newValue);
      json.setFieldName("assignmentCategory");
      json.setShowFlag(true);
      PurchaseApplyRecordJson json2 = new PurchaseApplyRecordJson();
      json2.setKey(PurchaseApplyRecordFields.SUBJECT_ALLOCATION_TYPE.getKey());
      json2.setDesc(PurchaseApplyRecordFields.SUBJECT_ALLOCATION_TYPE.getValue());
      json2.setOldValue(oldName);
      json2.setNewValue(newName);
      json2.setFieldName("assignmentCategoryName");
      json2.setShowFlag(false);
      PurchaseApplyRecordJson json3 = new PurchaseApplyRecordJson();
      json3.setKey(PurchaseApplyRecordFields.SUBJECT_ALLOCATION_TYPE.getKey());
      json3.setDesc(PurchaseApplyRecordFields.SUBJECT_ALLOCATION_TYPE.getValue());
      json3.setOldValue(oldCode);
      json3.setNewValue(newCode);
      json3.setFieldName("assignmentCategoryCode");
      json3.setShowFlag(false);
      jsonArray.add(json);
      jsonArray.add(json2);
      jsonArray.add(json3);
    }
    return jsonArray;
  }

  /**
   * 判断订单
   */
  public static List<PurchaseApplyRecordJson> judgeOrder(
      String oldCode,
      String newCode,
      String oldName,
      String newName) {
    oldName = StrUtil.emptyIfNull(oldName);
    newName = StrUtil.emptyIfNull(newName);
    oldCode = StrUtil.emptyIfNull(oldCode);
    newCode = StrUtil.emptyIfNull(newCode);
    List<PurchaseApplyRecordJson> jsonArray = new ArrayList<>();
    if (!StrUtil.equals(oldCode, newCode)) {
      PurchaseApplyRecordJson json = new PurchaseApplyRecordJson();
      json.setKey(PurchaseApplyRecordFields.ORDER.getKey());
      json.setDesc(PurchaseApplyRecordFields.ORDER.getValue());
      String oldValue = StrUtil.format("{}{}", oldCode, oldName);
      json.setOldValue(oldValue);
      String newValue = StrUtil.format("{}{}", newCode, newName);
      json.setNewValue(newValue);
      json.setFieldName("order");
      json.setShowFlag(true);
      PurchaseApplyRecordJson json2 = new PurchaseApplyRecordJson();
      json2.setKey(PurchaseApplyRecordFields.ORDER.getKey());
      json2.setDesc(PurchaseApplyRecordFields.ORDER.getValue());
      json2.setOldValue(oldName);
      json2.setNewValue(newName);
      json2.setFieldName("orderName");
      json2.setShowFlag(false);
      PurchaseApplyRecordJson json3 = new PurchaseApplyRecordJson();
      json3.setKey(PurchaseApplyRecordFields.ORDER.getKey());
      json3.setDesc(PurchaseApplyRecordFields.ORDER.getValue());
      json3.setOldValue(oldCode);
      json3.setNewValue(newCode);
      json3.setFieldName("orderCode");
      json3.setShowFlag(false);
      jsonArray.add(json);
      jsonArray.add(json2);
      jsonArray.add(json3);
    }
    return jsonArray;
  }

  /**
   * 判断成本中心
   */
  public static List<PurchaseApplyRecordJson> judgeCostCenter(
      String oldCode,
      String newCode,
      String oldName,
      String newName) {
    oldName = StrUtil.emptyIfNull(oldName);
    newName = StrUtil.emptyIfNull(newName);
    oldCode = StrUtil.emptyIfNull(oldCode);
    newCode = StrUtil.emptyIfNull(newCode);
    List<PurchaseApplyRecordJson> jsonArray = new ArrayList<>();
    if (!StrUtil.equals(oldCode, newCode)) {
      PurchaseApplyRecordJson json = new PurchaseApplyRecordJson();
      json.setKey(PurchaseApplyRecordFields.COST_CENTER.getKey());
      json.setDesc(PurchaseApplyRecordFields.COST_CENTER.getValue());
      String oldValue = StrUtil.format("{}{}", oldCode, oldName);
      json.setOldValue(oldValue);
      String newValue = StrUtil.format("{}{}", newCode, newName);
      json.setNewValue(newValue);
      json.setFieldName("costCenter");
      json.setShowFlag(true);
      PurchaseApplyRecordJson json2 = new PurchaseApplyRecordJson();
      json2.setKey(PurchaseApplyRecordFields.COST_CENTER.getKey());
      json2.setDesc(PurchaseApplyRecordFields.COST_CENTER.getValue());
      json2.setOldValue(oldName);
      json2.setNewValue(newName);
      json2.setFieldName("costCenterName");
      json2.setShowFlag(false);
      PurchaseApplyRecordJson json3 = new PurchaseApplyRecordJson();
      json3.setKey(PurchaseApplyRecordFields.COST_CENTER.getKey());
      json3.setDesc(PurchaseApplyRecordFields.COST_CENTER.getValue());
      json3.setOldValue(oldCode);
      json3.setNewValue(newCode);
      json3.setFieldName("costCenterCode");
      json3.setShowFlag(false);
      jsonArray.add(json);
      jsonArray.add(json2);
      jsonArray.add(json3);
    }
    return jsonArray;
  }

  /**
   * 判断总账单科目
   */
  public static List<PurchaseApplyRecordJson> judgeGeneralLedgerAccount(
      String oldCode,
      String newCode,
      String oldName,
      String newName) {
    oldName = StrUtil.emptyIfNull(oldName);
    newName = StrUtil.emptyIfNull(newName);
    oldCode = StrUtil.emptyIfNull(oldCode);
    newCode = StrUtil.emptyIfNull(newCode);
    List<PurchaseApplyRecordJson> jsonArray = new ArrayList<>();
    if (!StrUtil.equals(oldCode, newCode)) {
      PurchaseApplyRecordJson json = new PurchaseApplyRecordJson();
      json.setKey(PurchaseApplyRecordFields.GENERAL_LEDGER_ACCOUNT.getKey());
      json.setDesc(PurchaseApplyRecordFields.GENERAL_LEDGER_ACCOUNT.getValue());
      String oldValue = StrUtil.format("{}{}", oldCode, oldName);
      json.setOldValue(oldValue);
      String newValue = StrUtil.format("{}{}", newCode, newName);
      json.setNewValue(newValue);
      json.setFieldName("ledgerSubject");
      json.setShowFlag(true);
      PurchaseApplyRecordJson json2 = new PurchaseApplyRecordJson();
      json2.setKey(PurchaseApplyRecordFields.GENERAL_LEDGER_ACCOUNT.getKey());
      json2.setDesc(PurchaseApplyRecordFields.GENERAL_LEDGER_ACCOUNT.getValue());
      json2.setOldValue(oldName);
      json2.setNewValue(newName);
      json2.setFieldName("ledgerSubjectName");
      json2.setShowFlag(false);
      PurchaseApplyRecordJson json3 = new PurchaseApplyRecordJson();
      json3.setKey(PurchaseApplyRecordFields.GENERAL_LEDGER_ACCOUNT.getKey());
      json3.setDesc(PurchaseApplyRecordFields.GENERAL_LEDGER_ACCOUNT.getValue());
      json3.setOldValue(oldCode);
      json3.setNewValue(newCode);
      json3.setFieldName("ledgerSubjectCode");
      json3.setShowFlag(false);
      jsonArray.add(json);
      jsonArray.add(json2);
      jsonArray.add(json3);
    }
    return jsonArray;
  }

}
