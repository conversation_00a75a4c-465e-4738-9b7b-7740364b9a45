package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 招投标项目关联客户平台中间表(多对多)
 */
@Data
@Entity
@Table(name = "t_bid_project_to_platform")
public class BidProjectToPlatform {
  /**
   * 主键
   */
  @Id
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  @Column(name = "id", nullable = false)
  private String id;
  /**
   * 招投标项目id
   */
  @Column(name = "c_bid_project_id")
  private String bidProjectId;
  /**
   * 客户平台id
   */
  @Column(name = "c_platform_id")
  private String platformId;
}
