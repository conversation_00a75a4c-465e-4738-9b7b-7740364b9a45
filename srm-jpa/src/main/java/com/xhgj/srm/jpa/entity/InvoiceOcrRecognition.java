package com.xhgj.srm.jpa.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import java.math.BigDecimal;

@Table(name = "t_invoice_ocr_recognition")
@Entity
@Data
public class InvoiceOcrRecognition {

  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 发票号
   */
  @Column(name = "c_invoice_number")
  private String invoiceNumber;

  /**
   * 识别信息
   */
  @Column(name = "c_ocr_info")
  private String ocrInfo;
  /**
   * 合计金额
   */
  @Column(name = "c_total_amount")
  private BigDecimal totalAmount;

  /**
   * 合计税额
   */
  @Column(name = "c_total_tax_amount")
  private BigDecimal totalTaxAmount;

  /**
   * 价税合计
   */
  @Column(name = "c_total_amount_including_tax")
  private BigDecimal totalAmountIncludingTax;

  /**
   * 销方名称
   */
  @Column(name = "c_seller_name")
  private String sellerName;

  /**
   * 购方名称
   */
  @Column(name = "c_payer_name")
  private String payerName;
}
