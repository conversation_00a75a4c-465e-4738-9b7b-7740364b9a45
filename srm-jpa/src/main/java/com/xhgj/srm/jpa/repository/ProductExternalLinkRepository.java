package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.ProductExternalLink;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;

public interface ProductExternalLinkRepository extends BootBaseRepository<ProductExternalLink, String> {

  /**
   * 通过产品id获取外部链接
   * @param productId 产品id
   * @return
   */
  List<ProductExternalLink> findAllByProductId(String productId);
}