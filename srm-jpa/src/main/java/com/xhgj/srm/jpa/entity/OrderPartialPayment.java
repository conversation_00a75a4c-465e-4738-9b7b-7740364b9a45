package com.xhgj.srm.jpa.entity;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "t_order_partial_payment")
public class OrderPartialPayment {

  @Id
  @Column(name = "id")
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 关联订单id
   */
  @Size(max = 32)
  @Column(name = "c_order_id", length = 32)
  private String orderId;

  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 部分付款金额
   */
  @Column(name = "c_amount", precision = 18, scale = 2)
  private BigDecimal amount;

  /**
   * 创建人
   */
  @Size(max = 32)
  @Column(name = "c_create_man", length = 32)
  private String createMan;

  /**
   * 数据状态
   */
  @Size(max = 1)
  @Column(name = "c_state", length = 1)
  private String state;

  /**
   * 付款时间
   */
  @Column(name = "c_payment_time")
  private Long paymentTime;

  /**
   * 付款方式：{@link com.xhgj.srm.common.enums.OrderPaymentTypeEnum}
   */
  @Column(name = "c_order_payment_type")
  private String orderPaymentType;

  /**
   * 付款单ID
   */
  @Size(max = 32)
  @Column(name = "c_payment_id", length = 32)
  private String paymentId;
}
