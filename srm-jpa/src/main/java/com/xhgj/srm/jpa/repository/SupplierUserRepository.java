package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import java.util.Optional;

public interface SupplierUserRepository extends BootBaseRepository<SupplierUser, String> {

  /**
   * 根据真实姓名查找
   * @param realName
   * @param state
   * @return
   */
  Optional<SupplierUser> findFirstByRealNameAndState(String realName, String state);

  /**
   * @param name 根据账号查找
   */
  Optional<SupplierUser> findFirstByNameAndState(String name, String state);

  /**
   * 根据供应商id查询
   */
  List<SupplierUser> findAllBySupplierIdAndState(String supplierId, String state);

  /**
   * 根据供应商id + 账号名称查询
   */
  SupplierUser findFirstBySupplierIdAndNameAndState(String supplierId, String name, String state);

  /**
   * 根据供应商id + 手机号
   */
  SupplierUser findFirstBySupplierIdAndMobileAndState(String supplierId, String mobile, String state);

  /**
   * 根据供应商id + role
   */
  SupplierUser findFirstBySupplierIdAndRoleAndState(String supplierId, String role, String state);
}