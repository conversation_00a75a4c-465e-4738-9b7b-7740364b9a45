package com.xhgj.srm.jpa.dao.impl;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.EntryRegistrationLandingMerchantDao;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import com.xhiot.boot.framework.jpa.util.HqlUtil;
import org.springframework.stereotype.Service;
import java.util.List;

/**入驻报备单落地商信息*/
@Service
public class EntryRegistrationLandingMerchantDaoImpl extends AbstractExtDao<EntryRegistrationLandingMerchant>
    implements EntryRegistrationLandingMerchantDao {

  @Override
  public EntryRegistrationLandingMerchant getEntryRegistrationOrderId(String id) {
    StringBuilder hql = new StringBuilder("  FROM EntryRegistrationLandingMerchant where "
        + "c_state = ?  and entry_registration_order_id = ? ");
    Object[] params = new Object[]{Constants.STATE_OK,id};
    return getFirstHqlEntity(hql.toString(), params);
  }

  @Override
  public void logicDeleteByEntryRegistrationOrderIds(List<String> EROIds, User user) {
    StringBuilder sql = new StringBuilder("update t_entry_registration_landing_merchants a "
        + "set a.c_state = ?, a.c_update_time = ? , a.c_update_man = ? ");
    Object[] params = new Object[] {Constants.STATE_DELETE,System.currentTimeMillis(),user.getId()};
    params = HqlUtil.appendFieldIn(sql,params,"a.entry_registration_order_id", EROIds);
    executeSqlUpdate(sql.toString(), params);
  }
}
