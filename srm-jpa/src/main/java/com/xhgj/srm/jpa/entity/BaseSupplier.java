package com.xhgj.srm.jpa.entity;

import com.xhgj.srm.common.Constants;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * <AUTHOR>
 * @since 2022/7/31 16:10
 */
@Data
@MappedSuperclass
public abstract class BaseSupplier {

  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /** 企业名称/个人供应商姓名 */
  @Column(name = "c_enterpriseName")
  private String enterpriseName;


  /** 国别 */
  @Column(name = "c_country")
  private String country;

  /** 区域 */
  @Column(name = "c_region")
  private String region;

  /** 省份 */
  @Column(name = "c_province")
  private String province;

  /** 城市 */
  @Column(name = "c_city")
  private String city;
  /** 统一社会信用代码：Uniform social credit code */
  @Column(name = "c_uscc", length = 32)
  private String uscc;

  /** 行业 */
  @Column(name = "c_industry")
  private String industry;
  /** 供应商类型：{@link Constants#SUPPLIERTYPE} */
  @Column(name = "c_supType")
  private String supType;

  /** 联系方式/主联系人联系方式 */
  @Column(name = "c_mobile")
  private String mobile;
}
