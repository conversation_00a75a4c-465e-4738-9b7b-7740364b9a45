package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 询价表
 */
@Table(name = "t_inquiry")
@Entity
@Data
public class Inquiry implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = "supplierId")
    private String supplierId;

    @Column(name = "c_enterpriseName")
    private String enterpriseName;

    @Column(name = "c_brandname_cn")
    private String brandnameCn;

    @Column(name = "c_brandname_en")
    private String brandnameEn;

    @Column(name = "c_name")
    private String name;

    @Column(name = "c_model")
    private String model;

    @Column(name = "c_marketPrice")
    private String marketPrice;

    @Column(name = "c_salesPrice")
    private String salesPrice;

    @Column(name = "c_offererName")
    private String offererName;

    @Column(name = "c_offererPhone")
    private String offererPhone;

    @Column(name = "c_inquirerTime")
    private Long inquirerTime;

    @Column(name = "c_inquirer")
    private String inquirer;

    @Column(name = "c_remark")
    private String remark;

    @Column(name = "c_des")
    private String des;

    @Column(name = "c_createTime")
    private Long createTime;

    @Column(name = "c_state")
    private String state;

    @Column(name = "c_transferPrice")
    private String transferPrice;

    @Column(name = "c_salesMan")
    private String salesMan;

    @Column(name = "c_createMan")
    private String createMan;

    @Column(name = "c_createCode")
    private String createCode;

    @Column(name = "c_num")
    private String num;

    @Column(name = "c_unit")
    private String unit;

}
