package com.xhgj.srm.jpa.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * 客户收款表
 */
@Entity
@Table(name = "t_customer_receivable")
@Data
public class CustomerReceivable implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 主键
   */
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 落地商订单 id
   */
  @Column(name = "order_id")
  private String orderId;

  /**
   * 落地商订单编码
   */
  @Column(name = "c_order_no")
  private String orderNo;

  /**
   * 大票项目编号
   */
  @Column(name = "c_project_no")
  private String projectNo;

  /**
   * 发票号码
   */
  @Column(name = "c_invoice_no")
  private String invoiceNo;

  /**
   * 发票日期
   */
  @Column(name = "c_invoice_time")
  private Long invoiceTime;

  /**
   * 价税合计
   */
  @Column(name = "c_price")
  private BigDecimal price;

  /**
   * 回款状态
   */
  @Column(name = "c_return_state")
  private String returnState;

  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;


  /**
   * 数据状态
   */
  @Column(name = "c_state")
  private String state;

}
