package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.File;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import java.util.Optional;

public interface FileRepository extends BootBaseRepository<File, String> {

  /**
   * 根据条件查询第一个
   * @param relationId 关联id
   * @param relationType  关联类型
   * @param state 数据状态
   * @return 文件对象
   */
  Optional<File> findFirstByRelationIdAndRelationTypeAndState(String relationId,
      String relationType,
      String state);

  /**
   * 根据条件查询全部
   * @param relationId 关联id
   * @param relationType  关联类型
   * @param state 数据状态
   * @return  文件集合
   */
  Optional<List<File>> findAllByRelationIdAndRelationTypeAndState(String relationId,
      String relationType, String state);

  /**
   * 根据关联id和关联类型进行统计
   * @param relationId 关联id
   * @param relationType 关联类型
   * @return 统计数量
   */
  int countAllByRelationIdAndRelationType(String relationId, String relationType);

  /**
   * 根据关联ids和关联类型进行查询
   * @param contractIds
   * @param fileType
   * @param state
   * @return
   */
  List<File> findByRelationIdInAndRelationTypeAndState(List<String> relationIds,
      String relationType, String state);

  void deleteAllByRelationIdAndRelationType(String relationId,String relationType);
}