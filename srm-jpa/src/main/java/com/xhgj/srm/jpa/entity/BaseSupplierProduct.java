package com.xhgj.srm.jpa.entity;/**
 * @since 2025/4/28 9:13
 */

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 *<AUTHOR>
 *@date 2025/4/28 09:13:54
 *@description
 */
@Data
@MappedSuperclass
public class BaseSupplierProduct implements Serializable {

  private static final long serialVersionUID = 127373607783803849L;

  /**
   * 主键
   */
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private String id;

  /**
   * 物料编码
   */
  @Column(name = "c_code")
  private String code;
  /**
   * 品牌
   */
  @Column(name = "c_brand")
  private String brand;
  /**
   * 品牌编码
   */
  @Column(name = "c_brand_code")
  private String brandCode;
  /**
   * 商品名称
   */
  @Column(name = "c_name")
  private String name;
  /**
   * 规格型号
   */
  @Column(name = "c_manu_code")
  private String manuCode;
  /**
   * 物料单位
   */
  @Column(name = "c_unit")
  private String unit;
  /**
   * 物料单位编码
   */
  @Column(name = "c_unit_code")
  private String unitCode;

  /**
   * 单位的位数
   */
  @Column(name = "c_unit_digit")
  private Integer unitDigit;

  /**
   * 业务员
   */
  @Column(name = "c_salesman")
  private String salesman;

  /**
   * 跟单员
   */
  @Column(name = "c_follow_up_person_name")
  private String followUpPersonName;

  /**
   * 业务员所在公司名称
   */
  @Column(name = "c_business_company_name")
  private String businessCompanyName;

  /**
   * 制单员名称
   */
  @Column(name = "c_make_man_name")
  private String makeManName;

  /**
   * 售达方
   */
  @Column(name = "c_sold_to_party")
  private String soldToParty;

  /**
   * 规格
   */
  @Column(name = "c_specification")
  private String specification;

  /**
   * 型号->就是原来的规格型号字段
   */
  @Column(name = "c_model")
  private String model;

  /**
   * 去除空格
   */
  public void setCode(String code) {
    code = StrUtil.removeAll(code, " ");
    this.code = code;
  }

  public String getManuCode() {
    return this.model;
  }

  public void setManuCode(String manuCode) {
    this.manuCode = manuCode;
    this.model = manuCode;
  }
}
