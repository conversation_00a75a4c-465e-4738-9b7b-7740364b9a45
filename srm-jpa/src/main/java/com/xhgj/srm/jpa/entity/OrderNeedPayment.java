package com.xhgj.srm.jpa.entity;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import cn.hutool.core.util.NumberUtil;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * 需付款表
 */
@Data
@Entity
@Table(name = "t_order_need_payment", schema = "srm_prod")
public class OrderNeedPayment {

  @Id
  @Column(name ="id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 关联订单id
   */
  @Size(max = 32)
  @Column(name = "c_order_id", length = 32)
  private String orderId;

  /**
   * 关联回款id
   */
  @Size(max = 32)
  @Column(name = "c_order_receipt_id", length = 32)
  private String orderReceiptId;

  /**
   * 已提款金额
   */
  @Column(name = "c_paid_amount", precision = 18, scale = 2)
  private BigDecimal paidAmount;

  /**
   * 提款中的金额(记得需要相应时机释放)
   */
  @Column(name = "c_pending_amount", precision = 18, scale = 2)
  private BigDecimal pendingAmount;

  /**
   * 退款金额(付款单申请退款)
   */
  @Column(name = "c_return_amount", precision = 18, scale = 2)
  private BigDecimal returnAmount;

  /**
   * 是否被冲销
   */
  @Column(name = "c_offset")
  private Boolean offset;

  /**
   * 是否扣除
   */
  @Column(name = "c_deduct")
  private Boolean deduct;

  /**
   * 是否为部分付款
   */
  @Column(name = "c_part_pay")
  private Boolean partPay;

  /**
   * 供应商id，冗余字段用于联表
   */
  @Size(max = 32)
  @Column(name = "c_supplier_id", length = 32)
  private String supplierId;

  /**
   * 平台code，冗余字段用于联表
   */
  @Size(max = 32)
  @Column(name = "c_platform_code", length = 32)
  private String platformCode;

  /**
   * 履约订单是否为背靠背，冗余字段，用于计算
   */
  @Column(name = "c_back_to_back")
  private Boolean backToBack;


  /**
   * 回款实际比例，用于计算
   */
  @Column(name = "c_rate")
  private BigDecimal rate;

  /**
   * 回款展示比例
   */
  @Column(name = "c_show_rate")
  private BigDecimal showRate;

  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 更新时间
   */
  @Column(name = "c_update_time")
  private Long updateTime;

  /**
   * 数据状态
   */
  @Size(max = 1)
  @Column(name = "c_state", length = 1)
  private String state;
  /**
   * 实际提款金额(含提款中)
   * 已提款 - 退款 + 提款中
   */
  public BigDecimal getRealPaidAmount() {
    return NumberUtil.null2Zero(paidAmount)
        .subtract(NumberUtil.null2Zero(returnAmount))
        .add(NumberUtil.null2Zero(pendingAmount));
  }
}