package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import java.util.Optional;

/**
 * InventoryLocationRepository
 */
public interface InventoryLocationRepository extends BootBaseRepository<InventoryLocation, String> {

  Optional<InventoryLocation> findFirstByGroupCodeAndWarehouseAndState(String groupCode,
      String warehouse, String state);

  /**
   * 根据仓库和状态查询
   * @param groupCodes
   * @param state
   * @return
   */
  List<InventoryLocation> findAllByGroupCodeInAndState(List<String> groupCodes, String state);
}
