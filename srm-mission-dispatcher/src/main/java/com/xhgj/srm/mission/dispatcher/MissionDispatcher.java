package com.xhgj.srm.mission.dispatcher;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.mission.common.DispatchModeEnum;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.sender.mq.sender.BatchTaskMqSender;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 任务调度类
 *
 * <AUTHOR>
 * @since 2024/8/28 9:46
 */
@Component
@Slf4j
public class MissionDispatcher {
  private final MissionDispatcherConfig config;
  private final BatchTaskMqSender batchTaskMqSender;

  public MissionDispatcher(MissionDispatcherConfig config, BatchTaskMqSender batchTaskMqSender) {
    this.config = config;
    this.batchTaskMqSender = batchTaskMqSender;
  }

  /**
   * 调度任务
   *
   * @param missionId 任务 id
   * @param params 任务参数
   * @param missionType 任务类型
   */
  public void doDispatch(String missionId, String params, MissionTypeEnum missionType) {
    doDispatch(new MissionDispatchParam(missionId, params, missionType));
  }

  /**
   * 调度任务
   *
   * @param dispatchParam 调度参数
   */
  public void doDispatch(MissionDispatchParam dispatchParam) {
    log.info("开始调度任务，调度参数【{}】",dispatchParam.toString());
    String fcInvokeUrl = config.getFcInvokeUrl();
    // 满足两个条件才使用 FC 函数计算调度任务
    if (
    // 1. 任务枚举的优选调度模式是 DispatchModeEnum.FC
    Objects.equals(dispatchParam.getType().getPreferDispatchMode(), DispatchModeEnum.FC)
        // 2. 当前运行环境配置了 FC 地址
        && StrUtil.isNotBlank(fcInvokeUrl)) {
      log.info("使用 FC 调度，fcInvokeUrl[{}]",fcInvokeUrl);
      cn.hutool.http.HttpUtil.createPost(fcInvokeUrl)
          // 开启 FC 任务模式（即异步模式，接口会马上响应并创建 FC 任务）
          .header("X-Fc-Invocation-Type", "Async")
          // FC 任务id，为方便排查问题设置为和 mission 表的 id 一样
          .header("x-fc-async-task-id", dispatchParam.getMissionId())
          .body(JSON.toJSONString(dispatchParam))
          .execute()
          .body();
    } else {
      log.info("使用 MQ 调度");
      // 否则仍使用传统 MQ 调度
      batchTaskMqSender.sendMissionCreatedMessage(
          dispatchParam.getMissionId(),
          dispatchParam.getParams(),
          dispatchParam.getType().getCode());
    }
  }
}
