package com.xhgj.srm.mission.common;

import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/8/7 17:16
 */
@Data
@NoArgsConstructor
public class MissionDispatchParam implements Serializable {
  private static final long serialVersionUID = 2832587884249337769L;

  /** 任务 id */
  private String missionId;

  /** 任务参数 */
  private String params;

  /**
   * 任务类型（因目前任务表内 type 字段存的是中文，所以类型编码还是作为参数传递比较靠谱） <br>
   * TODO 未来将 Mission 表的 type 字段刷成编码存储之后该参数可以删除
   */
  private MissionTypeEnum type;

  /** 创建人 Id */
  private String userId;

  public MissionDispatchParam(String missionId, String params, MissionTypeEnum type) {
    this.missionId = missionId;
    this.params = params;
    this.type = type;
  }

  public MissionDispatchParam(
      String missionId, String params, MissionTypeEnum type, String userId) {
    this.missionId = missionId;
    this.params = params;
    this.type = type;
    this.userId = userId;
  }
}
