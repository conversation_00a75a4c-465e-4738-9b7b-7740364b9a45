package com.xhgj.srm.api.dto.Mission;

import com.xhiot.boot.mvc.base.PageResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class MissionPageDetailParam {

    @ApiModelProperty("操作类型")
    private String type;

    @ApiModelProperty("执行结果")
    private String reason;

    @ApiModelProperty("状态")
    private String state;

    @ApiModelProperty("失败链接")
    private String failLink;

    @ApiModelProperty("链接")
    private String link;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("任务详情列表")
    PageResult<MissionDetailPageData> pageDataList;

}