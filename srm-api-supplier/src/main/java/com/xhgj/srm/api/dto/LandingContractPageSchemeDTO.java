package com.xhgj.srm.api.dto;

import lombok.Data;

@Data
public class LandingContractPageSchemeDTO {

  /**
   * 合同号
   */
  private String contractNum;
  /**
   * 合同类型
   */
  private String contractType;
  /**
   * 开始时间
   */
  private String createTimeStart;
  /**
   * 结束时间
   */
  private String createTimeFinish;
  /**
   * 供应商名称
   */
  private String enterpriseName;
  /**
   * 供应商编码
   */
  private String enterpriseCode;
  /**
   * 签订方式
   */
  private String signingType;
  /**
   * 签章状态
   */
  private String signatureStatus;
  /**
   * 合同状态
   */
  private String landingContractStatus;
  /**
   * 平台
   */
  private String platform;
  /**
   * 创建人
   */
  private String createUser;
}
