package com.xhgj.srm.api.dto.order;

import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.jpa.entity.OrderOpenInvoice;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023-02-03 11:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class OpenInvoiceDTO extends BaseOpenInvoiceDTO{


  @ApiModelProperty("文件")
  private List<FileDTO> fileDTO;

  public OpenInvoiceDTO(OrderOpenInvoice orderOpenInvoice) {
    super(orderOpenInvoice);
  }
}
