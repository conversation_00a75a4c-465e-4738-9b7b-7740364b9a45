package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.supplierBrand.BrandPageDataDTO;
import com.xhgj.srm.api.dto.supplierBrand.BrandPageQueryParam;
import com.xhgj.srm.api.dto.supplierBrand.SupplierBrandAddParam;
import com.xhgj.srm.api.dto.supplierBrand.SupplierBrandDTO;
import com.xhiot.boot.mvc.base.PageResult;

/**
 * @description  接口
 * <AUTHOR>
 * @date 2023-07-19 15:17:32
 */
public interface SupplierBrandService {

  /**
   * * 供应商品牌新增
   *
   * @param param 下推入参 必填
   * <AUTHOR>
   * @date 2023/07/19 14:27
   */
  void supplierBrandAdd(SupplierBrandAddParam param);

  /**
   * * 供应商品牌详情
   *
   * @param brandId
   * <AUTHOR>
   * @date 2023/07/19 14:27
   */
  SupplierBrandDTO getSupplierBrandDetail(String brandId);


  /**
   * * 分页获取我的申请品牌列表
   *
   * @param brandPageQueryParam
   * <AUTHOR>
   * @date 2023/07/19 14:27
   */
  PageResult<BrandPageDataDTO> getSupplierBrandPage(BrandPageQueryParam brandPageQueryParam);

  /**
   * 放弃申请
   * @param brandId
   */
  void giveUpApply(String brandId);
}

