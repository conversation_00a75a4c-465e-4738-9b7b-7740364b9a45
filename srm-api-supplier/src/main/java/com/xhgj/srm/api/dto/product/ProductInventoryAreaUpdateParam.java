package com.xhgj.srm.api.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @description: ProductInventoryAreaUpdateParam
 **/
@Data
public class ProductInventoryAreaUpdateParam {


  @ApiModelProperty("区域库存信息")
  @NotEmpty(message = "区域库存信息不能为空")
  private List<InventoryInformationData> inventoryInformation;

  @ApiModelProperty("供应商名称")
  private String userName;

  @ApiModelProperty("物料编码")
  @NotBlank(message = "物料编码不能为空")
  private String productCode;

  @ApiModelProperty("平台编码")
  @NotBlank(message = "平台编码不能为空")
  private String platformCode;

  @Data
  public static class InventoryInformationData {
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("区域列表")
    private List<ProductArea> regionItemList;

    @ApiModelProperty("区域库存数量")
    private Integer inventoryAmount;

    @ApiModelProperty("库存状态 0:禁用 1启用")
    private String inventoryStatus;


    @Data
    public static class ProductArea {
      @ApiModelProperty("区域名称")
      private String label;

      @ApiModelProperty("区域code")
      private String value;
    }
  }
}

