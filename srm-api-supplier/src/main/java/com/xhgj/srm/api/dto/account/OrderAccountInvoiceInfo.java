package com.xhgj.srm.api.dto.account;

import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/1/9 15:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class OrderAccountInvoiceInfo extends BaseOrderSupplierInvoice{
  @ApiModelProperty("发票附件")
  private List<FileDTO> fileList;

  public OrderAccountInvoiceInfo(OrderSupplierInvoice orderAccountInvoice) {
    super(orderAccountInvoice);
  }
}
