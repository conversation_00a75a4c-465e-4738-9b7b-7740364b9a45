package com.xhgj.srm.api.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.Brand;
import com.xhgj.srm.jpa.entity.File;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * @ClassName BrandPageDto Create by Liuyq on 2021/6/3 8:52
 */
@Data
public class BrandPageData {

  @ApiModelProperty("品牌logo")
  private String logo;

  @ApiModelProperty("品牌英文名")
  private String brandEnName;

  @ApiModelProperty("品牌中文名")
  private String brandCnName;

  @ApiModelProperty("经营形式")
  private String manageType;

  @ApiModelProperty("授权书")
  private String isPermission;

  @ApiModelProperty("授权书路径")
  private Map<String,List<String>> brandUrl;

  @ApiModelProperty("品牌 mdmId")
  private String brandMdmId;

  @ApiModelProperty("基础路径")
  private String baseUrl;


  public BrandPageData(Brand brand, File file, String uploadUrl) {
    this.logo = brand.getLogoUrl();
    this.brandEnName = brand.getBrandnameEn();
    this.brandCnName = brand.getBrandnameCn();
    this.manageType =
        !StringUtils.isNullOrEmpty(brand.getManageType())
            ? Constants.BRAND_MANAGE_TYPE_TO_NAME.get(brand.getManageType())
            : "";
    this.isPermission =
        !StringUtils.isNullOrEmpty(brand.getIsPermission())
            ? Constants.UPLOAD_STATUS_TO_NAME.get(brand.getIsPermission())
            : Constants.UPLOAD_STATUS_TO_NAME.get(Constants.UPLOAD_STATUS_NOT_UPLOADED);
  }

  public BrandPageData(
      String logo,
      String brandEnName,
      String brandCnName,
      String manageType,
      String isPermission,
      List<File> files,
      String uploadUrl) {
    this.logo = StrUtil.emptyIfNull(logo);
    this.brandEnName = StrUtil.emptyIfNull(brandEnName);
    this.brandCnName = StrUtil.emptyIfNull(brandCnName);
    this.manageType =
        StrUtil.isNotBlank(StrUtil.emptyIfNull(manageType))
            ? Constants.BRAND_MANAGE_TYPE_TO_NAME.get(manageType)
            : StrUtil.EMPTY;
    this.brandUrl = MapUtil.emptyIfNull(CollUtil.emptyIfNull(files).stream()
        .collect(Collectors.toMap(
            File::getType, file -> StrUtil.split(file.getDescription(), ',', true, true)
        )));
    this.baseUrl = uploadUrl;
  }
}
