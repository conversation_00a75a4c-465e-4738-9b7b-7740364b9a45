package com.xhgj.srm.api.front.factory;/**
 * @since 2024/12/5 17:13
 */
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.api.dto.GwPlatformProductPageQuery;
import com.xhgj.srm.api.dto.filing.FilingParamProductDTO;
import com.xhgj.srm.api.dto.product.ProductAddParam;
import com.xhgj.srm.api.dto.product.ProductInventoryAreaUpdateMDMParam;
import com.xhgj.srm.api.dto.product.ProductInventoryAreaUpdateParam;
import com.xhgj.srm.api.dto.product.ProductPageParam;
import com.xhgj.srm.api.dto.product.ProductUpdateParam;
import com.xhgj.srm.api.dto.supplierBrand.SupplierBrandAddParam;
import com.xhgj.srm.common.dto.MdmBrandPageData;
import com.xhgj.srm.dto.product.externalLink.ExternalLinkSaveForm;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.map.domain.BaseMapStruct;
import com.xhgj.srm.open.dto.product.OpenExternalLinkAndLabelDTO;
import com.xhgj.srm.open.form.brand.OpenSupplierBrandSaveForm;
import com.xhgj.srm.open.form.product.OpenProductSaveForm;
import com.xhgj.srm.open.vo.brand.OpenSupplierBrandVO;
import com.xhgj.srm.open.vo.product.OpenExternalLinkVO;
import com.xhgj.srm.request.dto.mpm.GwPlatformProductSearchForm;
import com.xhgj.srm.request.dto.oms.OMSFilingSheetAddParam.OMSProduct;
import com.xhgj.srm.vo.product.ExternalLinkVO;
import org.mapstruct.AfterMapping;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;
import java.util.Map;

/**
 *<AUTHOR>
 *@date 2024/12/5 17:13:45
 *@description
 */
@Mapper
public interface MapStructFactory extends BaseMapStruct {
  MapStructFactory INSTANCE = Mappers.getMapper(MapStructFactory.class);

  /**
   * OpenSupplierBrandSaveForm to SupplierBrandAddParam
   * @param form
   * @return
   */
  SupplierBrandAddParam toSupplierBrandAddParam(OpenSupplierBrandSaveForm form);

  /**
   * MdmBrandPageData to OpenSupplierBrandVO
   * @param data
   * @return
   */
  OpenSupplierBrandVO toOpenSupplierBrandVO(MdmBrandPageData data);

  /**
   * OpenProductSaveForm to ProductAddParam
   * 忽略类型不匹配的字段 productLiteratures、reports、certificateOfQualitys、mainPictures、detailedPictures
   * @param form
   * @return
   */
  @Mapping(source = "productLiteratures", target = "productLiteratures", ignore = true)
  @Mapping(source = "reports", target = "reports", ignore = true)
  @Mapping(source = "certificateOfQualitys", target = "certificateOfQualitys", ignore = true)
  @Mapping(source = "mainPictures", target = "mainPictures", ignore = true)
  @Mapping(source = "detailedPictures", target = "detailedPictures", ignore = true)
  ProductAddParam toProductAddParam(OpenProductSaveForm form);

  /**
   * OpenProductSaveForm to ProductUpdateParam
   * 忽略类型不匹配的字段 productLiteratures、reports、certificateOfQualitys、mainPictures、detailedPictures
   * @param form
   * @return
   */
  @Mapping(source = "productLiteratures", target = "productLiteratures", ignore = true)
  @Mapping(source = "reports", target = "reports", ignore = true)
  @Mapping(source = "certificateOfQualitys", target = "certificateOfQualitys", ignore = true)
  @Mapping(source = "mainPictures", target = "mainPictures", ignore = true)
  @Mapping(source = "detailedPictures", target = "detailedPictures", ignore = true)
  ProductUpdateParam toProductUpdateParam(OpenProductSaveForm form);

  /**
   * Order to Order
   * @param form
   * @return
   */
  Order toOrder(Order form);

  /**
   * FilingParamProductDTO to OMSProduct
   * @param dto
   * @return
   */
  OMSProduct toOMSProduct(FilingParamProductDTO dto);

  /**
   * update ProductPageParam
   * @param source
   * @param target
   */
  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  void updateProductPageParam(ProductPageParam source, @MappingTarget ProductPageParam target);

  /**
   * update ProductPageParam
   * @param source
   * @param target
   */
  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  void updateProductPageParam(Map source, @MappingTarget ProductPageParam target);

  @AfterMapping
  default void afterMapping(Map source, @MappingTarget ProductPageParam target) {
    ProductPageParam productPageParam = JSON.parseObject(JSON.toJSONString(source), new TypeReference<ProductPageParam>() {});
    this.updateProductPageParam(productPageParam, target);
  }

  /**
   * ProductInventoryAreaUpdateParam to ProductInventoryAreaUpdateMDMParam
   * @param source
   */
  ProductInventoryAreaUpdateMDMParam toProductInventoryAreaUpdateMDMParam(ProductInventoryAreaUpdateParam source);

  /**
   * OrderDetail to OrderDetail
   * @param source
   * @return
   */
  OrderDetail toOrderDetail(OrderDetail source);

  /**
   * GwPlatformProductPageQuery to GwPlatformProductSearchForm
   * @param gwPlatformProductPageQuery
   * @return
   */
  @Mapping(source = "productCode", target = "code")
  @Mapping(source = "proName", target = "name")
  GwPlatformProductSearchForm toGwPlatformProductSearchForm(GwPlatformProductPageQuery gwPlatformProductPageQuery);

  /**
   * OpenExternalLinkAndLabelDTO to OpenProductSaveForm
   * @param source
   * @return
   */
  ExternalLinkSaveForm toExternalLinkSaveForm(OpenExternalLinkAndLabelDTO source);

  /**
   * ExternalLinkVO to OpenExternalLinkVO
   * @param source
   * @return
   */
  OpenExternalLinkVO toOpenExternalLinkVO(ExternalLinkVO source);
}
