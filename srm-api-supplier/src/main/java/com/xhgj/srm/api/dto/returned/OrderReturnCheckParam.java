package com.xhgj.srm.api.dto.returned;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


@Data
public class OrderReturnCheckParam {

    @ApiModelProperty("退货单id")
    @NotBlank(message = "退货单id不能为空")
    private String orderReturnId;
    @ApiModelProperty("是否通过")
    @NotBlank(message = "审核结果不能为空")
    private String isPass;
    @ApiModelProperty("原因")
    private String reason;

}
