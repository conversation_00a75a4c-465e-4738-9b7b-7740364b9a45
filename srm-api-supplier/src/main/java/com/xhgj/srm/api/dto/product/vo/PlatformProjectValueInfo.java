package com.xhgj.srm.api.dto.product.vo;

import com.xhgj.srm.api.dto.product.DTO.ProjectFieldDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-05-09 14:34
 */
@Data
public class PlatformProjectValueInfo {

  @ApiModelProperty("编码")
  private String platformCode;

  @ApiModelProperty("名称")
  private String platformName;

  @ApiModelProperty("简称")
  private String abbreviation;

  @ApiModelProperty("项目信息")
  private List<ProjectFieldDTO> fieldKeyAndValue;

}
