package com.xhgj.srm.api.dto.supplierBrand;

import com.xhgj.srm.jpa.entity.SupplierBrand;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SupplierBrandDTO {

  private String id;

  @ApiModelProperty(value = "品牌mpmId")
  private String brandMpmId;

  @ApiModelProperty(value = "品牌名称(中文)")
  private String brandNameCn;

  @ApiModelProperty(value = "品牌名称(英文)")
  private String brandNameEn;

  @ApiModelProperty(value = "品牌logo地址")
  private String brandLogoUrl;

  @ApiModelProperty(value = "经营形式(1_品牌方 2_集货商)")
  private String manageType;

  @ApiModelProperty(value = "描述信息")
  private String desc;

  @ApiModelProperty(value = "审核状态(1_审核中 2_驳回)")
  private String mpmState;

  @ApiModelProperty(value = "审核意见")
  private String mpmComment;

  public SupplierBrandDTO(SupplierBrand supplierBrand) {
    this.id = supplierBrand.getId();
    this.brandMpmId = supplierBrand.getBrandMpmId();
    this.brandNameCn = supplierBrand.getBrandNameCn();
    this.brandNameEn = supplierBrand.getBrandNameEn();
    this.brandLogoUrl = supplierBrand.getBrandLogoUrl();
    this.manageType = supplierBrand.getManageType();
    this.desc = supplierBrand.getDesc();
    this.mpmState = supplierBrand.getMpmState();
    this.mpmComment = supplierBrand.getMpmComment();
  }
}
