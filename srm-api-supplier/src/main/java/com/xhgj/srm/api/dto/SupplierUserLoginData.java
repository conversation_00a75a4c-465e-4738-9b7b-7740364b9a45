package com.xhgj.srm.api.dto;

import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import com.xhgj.srm.jpa.dto.supplierUser.SupplierUserPermissionAware;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 *
 **/
@Data
public class SupplierUserLoginData implements SupplierUserPermissionAware {

  @ApiModelProperty("供应商账号id")
  private String supplierUserId;
  @ApiModelProperty("账号姓名")
  private String name;
  @ApiModelProperty("账号用户名")
  private String realName;
  @ApiModelProperty("供应商id")
  private String supplierId;
  @ApiModelProperty("token")
  private String token;
  @ApiModelProperty("供应商code")
  private String code;
  @ApiModelProperty("供应商等级")
  private String enterpriseLevel;
  @ApiModelProperty("供应商等级名称")
  private String enterpriseLevelName;
  @ApiModelProperty("供应商erpCode")
  private String erpCode;
  @ApiModelProperty("统一路径")
  private String baseUrl;
  @ApiModelProperty("角色")
  private String role;
  @ApiModelProperty("合作类型")
  private String cooperateType;
  @ApiModelProperty("是否有接单权限")
  private Boolean hasSupplierOrderAuthority;
  @ApiModelProperty("MDM主数据编码")
  private String mdmCode;
  @ApiModelProperty("是否版本上线后第一次登录")
  private Boolean isFirst;
  @ApiModelProperty("菜单路径集合")
  private List<String> menus;

  /**
   * 供应商权限
   */
  @ApiModelProperty("供应商权限")
  private String permission;

  /**
   * 构造函数
   */
  public SupplierUserLoginData(SupplierUser supplierUser, Supplier supplier, String token,
      String baseUrl, boolean isFirst, List<String> menus) {
    this.supplierUserId = supplierUser.getId();
    this.name = supplierUser.getName();
    this.realName = supplierUser.getRealName();
    this.supplierId = supplier.getId();
    this.code = supplier.getCode();
    this.enterpriseLevel = supplier.getEnterpriseLevel();
    this.enterpriseLevelName =
        !StringUtils.isNullOrEmpty(supplier.getEnterpriseLevel()) ? SupplierLevelEnum.getAbbrByCode(
            supplier.getEnterpriseLevel()) : "";
    this.erpCode = supplier.getErpCode();
    this.token = token;
    this.baseUrl = baseUrl;
    this.role = supplierUser.getRole();
    this.cooperateType = StringUtils.emptyIfNull(supplier.getCooperateType());
    this.hasSupplierOrderAuthority = supplier.getOpenSupplierOrder();
    this.mdmCode = supplier.getMdmCode();
    this.isFirst = isFirst;
    this.menus = menus;
    this.permission = supplierUser.getPermission();
  }
}
