package com.xhgj.srm.api.service;

import cn.hutool.core.date.DateField;
import com.alibaba.fastjson.JSONArray;
import com.xhgj.srm.api.dto.supplierOrder.LogisticsInformationParam;
import com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn.SupplierOrderCancelDetailDTO;
import com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn.SupplierOrderCountDTO;
import com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn.SupplierOrderReturnDetailDTO;
import com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn.SupplierOrderReturnQueryDTO;
import com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn.SupplierOrderReturnTableDTO;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.List;
import org.springframework.data.domain.Pageable;

/** <AUTHOR> @ClassName SupplierOrderToFormService */
public interface SupplierOrderToFormService extends BootBaseService<SupplierOrderToForm, String> {
  /**
   * 获得订单明细的表单信息
   *
   * @param supplierOrderId 供应商订单 id 必传
   */
  SupplierOrderToForm getDetailedBySupplierOrderId(String supplierOrderId);

  /**
   * 根据单子类型和供应商订单 id 获得对应的单据
   *
   * @param type 单据类型 必传
   * @param supplierOrderId 供应商订单 id 必传
   */
  List<SupplierOrderToForm> getByTypeAndSupplierOrderId(
      SupplierOrderFormType type, String supplierOrderId);

   /**
   * 根据单子类型和供应商订单 id 获得对应的 取消或退货 单据
   *
   * @param type 单据类型 必传
   * @param supplierOrderId 供应商订单 id 必传
   */
  List<SupplierOrderToForm> getReturnOrCancelFormByTypeAndAndStatusSupplierOrderId(
      SupplierOrderFormType type, String supplierOrderId,List<String> statusList);

  /**
   * 创建发货单 @Author: liuyq @Date: 2022/12/2 16:15
   *
   * @param supplierOrderId 履约订单id
   * @return void
   */
  SupplierOrderToForm createSupplierOrderForm(String supplierOrderId, SupplierOrderFormType type);

  /**
   * 根据单子类型和供应商 id 获得对应的单据
   *
   * @param queryDTO 查询对象
   * @param toPageable 分页对象
   */
  PageResult<SupplierOrderReturnTableDTO> getSupplierOrderReturnPage(SupplierOrderReturnQueryDTO queryDTO, Pageable toPageable);

  /**
   * 获取履约退货单详情和物料明细 @Author: liuyq @Date: 2022/11/30 9:11
   *
   * @param id 订单id
   * @return com.xhgj.srm.api.dto.supplierOrder.SupplierOrderDTO
   */
  SupplierOrderReturnDetailDTO getSupplierOrderReturnDetail(String id);

 /**
   * 获取履约取消单详情和物料明细 @Author: liuyq @Date: 2022/11/30 9:11
   *
   * @param id 订单id
   * @return com.xhgj.srm.api.dto.supplierOrder.SupplierOrderDTO
   */
 SupplierOrderCancelDetailDTO getSupplierOrderCancelDetail(String id);

  /**
   * 根据供应商订单 id 统计数量
   *
   * @param supplierOrderId 供应商 id
   * @return com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn.SupplierOrderCountDTO
   */
  SupplierOrderCountDTO getSupplierOrderFormCountById(String supplierOrderId);

  /**
   * 根据单子类型和供应商订单 id 获得对应的单据
   *
   * @param type   单据类型 必传
   * @param status 单据状态 必传
   */
  List<SupplierOrderToForm> getAllOrderToFormByType(String type, String status);

  /**
   * 更新订单签收状态
   */
  void updateOrderSignState(DateField dateField, Integer offSetInt);

  /**
   * 提醒采购收货
   */
  void remindPurchaseReceipt(DateField dateField,Integer offSetInt);

  /**
   * 获取物流信息
   */
  JSONArray getLogisticsInformation(LogisticsInformationParam param);

  /**
   * 针对当前订单已签收，但订单状态是未完成的订单，给负责采购发送消息通知
   */
  void sendDingMsgToPurchase();

  /**
   * @param supplierOrderId 订单id
   */
  List<SupplierOrderToForm> findBySupplierOrderId(String supplierOrderId);
}
