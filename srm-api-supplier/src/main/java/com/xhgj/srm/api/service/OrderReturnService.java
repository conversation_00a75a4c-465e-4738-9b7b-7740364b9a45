package com.xhgj.srm.api.service;

import com.xhgj.srm.dto.returned.OrderReturnAddParam;
import com.xhgj.srm.api.dto.returned.OrderReturnCountDTO;
import com.xhgj.srm.api.dto.returned.OrderReturnDetailDTO;
import com.xhgj.srm.api.dto.returned.OrderReturnPageDTO;
import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.List;

public interface OrderReturnService extends BootBaseService<OrderReturn, String> {

    /**
     * 获取订单退货单状态及其个数
     * @param supplierId
     * @return
     */
    OrderReturnCountDTO getOrderReturnStateCount(String supplierId);

    /**
     * 分页获取退货/取消单
     * @param supplierId
     * @param orderNo
     * @param orderReturnNo
     * @param platform
     * @param returnPrice
     * @param returnCount
     * @param customer
     * @param consignee
     * @param mobile
     * @param address
     * @param returnType
     * @param returnState
     * @param returnApplyStartDate
     * @param returnApplyEndDate
     * @param schemeId
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageResult<OrderReturnPageDTO> getReturnOrderPage(String supplierId,String supplierUserId, String orderNo, String orderReturnNo, String platform, String returnPrice, String returnCount, String customer, String consignee, String mobile, String address, String returnType, String returnState, String returnApplyStartDate, String returnApplyEndDate, String schemeId, Integer pageNo, Integer pageSize);

    /**
     * 获取订单详情
     * @param orderReturnId
     * @return
     */
    OrderReturnDetailDTO getOrderReturnDetail(String orderReturnId);



    /**
     * 订单退货
     * @param orderReturnId
     */
    void checkOrderReturn(String orderReturnId, String isPass, String reason);

    /**
     * 订单取消
     * @param orderReturnId
     */
    void checkCancelReturn(String orderReturnId, String isPass, String reason);

    /**
     * 订单完结
     * @param orderReturnId
     */
    @Deprecated
    void OrderReturnComplete(String orderReturnId);
}
