package com.xhgj.srm.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class LandingContractPageDTO {

  /** id */
  @ApiModelProperty("id")
  private String id;

  /** 合同号 */
  @ApiModelProperty("合同号")
  private String contractNo;

  /** 合同类型 */
  @ApiModelProperty("合同类型")
  private String type;

  /** 签订方式 */
  @ApiModelProperty("签订方式")
  private String signingType;

  /** 对方签约主体 */
  @ApiModelProperty("对方签约主体")
  private String secondSigningSupplierId;

  /** 创建时间 */
  @ApiModelProperty("创建时间")
  private String createTime;

  /** 创建人 */
  @ApiModelProperty("创建人")
  private String createMan;

  /** 签章状态 */
  @ApiModelProperty("签章状态")
  private String signatureStatus;

  /** 合同状态 */
  @ApiModelProperty("合同状态")
  private String contractStatus;

  /** 累计金额 */
  @ApiModelProperty("积累金额")
  private BigDecimal accruingAmounts;

  @ApiModelProperty("合同附件")
  private FileDTO fileDTO;

  @ApiModelProperty("下单平台编码")
  private String platformCode;

  /** 供应商新增字段 */
  /** 保证金 */
  @ApiModelProperty("保证金")
  private BigDecimal deposit;

  /** 附件审核状态 */
  @ApiModelProperty("附件审核状态")
  private String fileReviewState;

  /** 保证金状态 */
  @ApiModelProperty("保证金状态")
  private Boolean depositState;

  /** 报备单审核状态 */
  @ApiModelProperty("报备单审核状态")
  private String reportReviewState;

  /** 合作类型 */
  @ApiModelProperty("合作类型")
  private String typeOfCooperation;

  /** 合作区域 */
  @ApiModelProperty("合作区域")
  private String cooperationRRegion;

  /** 合作有效期 */
  @ApiModelProperty("合作有效期")
  private String cooperationValidity;
  /**
   * 报备单号。
   */
  @ApiModelProperty("报备单号")
  private String registrationNumber;


}
