package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.xhgj.srm.api.dto.supplierOrder.ProductDetailParam;
import com.xhgj.srm.api.service.SupplierOrderDetailService;
import com.xhgj.srm.api.service.SupplierOrderToFormService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.service.ShareSupplierOrderDetailService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> @ClassName SupplierOrderDetailServiceImpl
 */
@Service
public class SupplierOrderDetailServiceImpl implements SupplierOrderDetailService {
  @Autowired private SupplierOrderDetailRepository repository;
  @Resource private SupplierOrderToFormService supplierOrderToFormService;
  @Autowired private ShareSupplierOrderDetailService shareSupplierOrderDetailService;
  @Override
  public BootBaseRepository<SupplierOrderDetail, String> getRepository() {
    return repository;
  }

  @Override
  public List<SupplierOrderDetail> getByOrderToFormId(String orderToFormId) {
    return shareSupplierOrderDetailService.getByOrderToFormId(orderToFormId);
  }

  @Override
  public void saveOrderDetail(
      String orderFormId,
      List<ProductDetailParam> productDetailList,
      Map<String, BigDecimal> shipProductMap) {
    Assert.notEmpty(orderFormId);
    Assert.notNull(productDetailList);
    productDetailList.forEach(
        productDetailParam ->
            saveSupplierOrderDetail(orderFormId, productDetailParam, shipProductMap));
  }

  private void saveSupplierOrderDetail(
      String orderFormId,
      ProductDetailParam detailParam,
      Map<String, BigDecimal> shipProductMap) {
    String detailId = detailParam.getDetailId();
    SupplierOrderDetail one =
        repository.findById(detailId).orElseThrow(() -> new CheckException("未找到对应的物料明细"));
    SupplierOrderDetail supplierOrderDetail = new SupplierOrderDetail();
    supplierOrderDetail.setSortNum(one.getSortNum());
    supplierOrderDetail.setOrderToFormId(orderFormId);
    supplierOrderDetail.setShipQty(detailParam.getDeliveryQty());
    supplierOrderDetail.setState(Constants.STATE_OK);
    supplierOrderDetail.setCreateTime(System.currentTimeMillis());
    SupplierOrderProduct supplierOrderProduct =one.getSupplierOrderProduct();
    supplierOrderDetail.setOrderProductId(supplierOrderProduct.getId());
    supplierOrderDetail.setSupplierOrderProduct(supplierOrderProduct);
    supplierOrderDetail.setSapRowId(one.getSapRowId());
    supplierOrderDetail.setErpRowNum(one.getErpRowNum());
    supplierOrderDetail.setWaitQty(one.getWaitQty());
    supplierOrderDetail.setBatchNo(one.getBatchNo());
    supplierOrderDetail.setSapReversalRowNo(one.getSapReversalRowNo());
    supplierOrderDetail.setDetailedId(detailParam.getDetailId());
    supplierOrderDetail.setPrice(detailParam.getProductPrice());
    supplierOrderDetail.setStockInputQty(one.getShipQty());
    supplierOrderDetail.setInvoicableNum(one.getShipQty());
    supplierOrderDetail.setTaxRate(one.getTaxRate());
    supplierOrderDetail.setProductRate(one.getProductRate());
    save(supplierOrderDetail);
    shipProductMap.put(detailParam.getDetailId(), detailParam.getDeliveryQty());
  }

  @Override
  public BigDecimal countSettleQtyBySupplierOrderId(String orderId) {
    List<SupplierOrderToForm> supplierOrderToForms =
        supplierOrderToFormService.findBySupplierOrderId(orderId);
    if (CollUtil.isEmpty(supplierOrderToForms)) {
      return BigDecimal.ZERO;
    }
    BigDecimal result = BigDecimal.ZERO;
    for (SupplierOrderToForm supplierOrderToForm : supplierOrderToForms) {
      List<SupplierOrderDetail> supplierOrderDetails =
          repository.findByOrderToFormIdAndState(supplierOrderToForm.getId(), Constants.STATE_OK);
      BigDecimal reduce =
          supplierOrderDetails.stream()
              .map(SupplierOrderDetail::getSettleQty)
              .filter(Objects::nonNull)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
      result = result.add(reduce);
    }
    return result;
  }
}
