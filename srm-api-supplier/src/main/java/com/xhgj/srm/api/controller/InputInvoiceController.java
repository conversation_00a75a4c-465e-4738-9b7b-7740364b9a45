package com.xhgj.srm.api.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.xhgj.srm.api.dto.InvoiceResultParam;
import com.xhgj.srm.api.dto.OrderInvoiceRelationDTO;
import com.xhgj.srm.api.dto.OrderInvoiceRelationPageQuery;
import com.xhgj.srm.api.dto.SupplierInvoiceSaveParam;
import com.xhgj.srm.api.dto.account.AccountDetailDTO;
import com.xhgj.srm.api.dto.account.AccountOpenInvoiceParams;
import com.xhgj.srm.api.dto.account.AccountPaymentDTO;
import com.xhgj.srm.api.dto.account.InvoiceOrderPageDTO;
import com.xhgj.srm.api.dto.account.InvoiceOrderPageQuery;
import com.xhgj.srm.api.dto.supplier.invoice.SupplierInvoiceDetailsDTO;
import com.xhgj.srm.api.dto.supplier.invoice.SupplierOpenInvoiceDetailsDTO;
import com.xhgj.srm.api.dto.supplierinvoice.SaveLogisticsParam;
import com.xhgj.srm.api.dto.supplierinvoice.SupplierInvoiceOrderPageParam;
import com.xhgj.srm.api.dto.supplierinvoice.SupplierInvoiceOrderPageVO;
import com.xhgj.srm.api.service.OrderSupplierInvoiceService;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.common.dto.invoice.InvoiceIdentifyResult;
import com.xhgj.srm.common.dto.invoice.InvoiceIdentifyResultDTO;
import com.xhgj.srm.common.dto.invoice.InvoiceOcrRecognitionResultDTO;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.dto.order.invoice.InvoiceParam;
import com.xhgj.srm.dto.order.invoice.InvoiceVerificationResult;
import com.xhgj.srm.dto.order.invoice.OtherInvoiceParam;
import com.xhgj.srm.service.ShareInputInvoiceService;
import com.xhgj.srm.service.SupplierInvoiceService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.repeat.annotation.RepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.net.URLEncoder;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("input/invoice")
@Api(tags = {"进项票相关接口"})
@Slf4j
@Validated
public class InputInvoiceController {

  @Autowired
  private OrderSupplierInvoiceService orderSupplierInvoiceService;
  @Resource
  private DingUtils dingUtils;
  @Resource
  private SupplierInvoiceService supplierInvoiceService;
  @Resource
  private RedissonClient redissonClient;
  @Resource
  ShareInputInvoiceService shareInputInvoiceService;

  @ApiOperation(value = "分页获取可开票列表")
  @GetMapping("/order/page")
  public ResultBean<PageResult<InvoiceOrderPageDTO>> getCanInvoiceOrderPage(
      InvoiceOrderPageQuery query, @Valid PageParam param) {
    return new ResultBean<>(
        orderSupplierInvoiceService.getCanInvoiceOrderPage(query, param.toPageable()));
  }
  @ApiOperation(value = "分页获取供应商可开票列表")
  @GetMapping("/supplierOrder/page")
  public ResultBean<PageResult<SupplierInvoiceOrderPageVO>> getSupplierCanInvoiceOrderPage(
      SupplierInvoiceOrderPageParam param
  ) {
    return new ResultBean<>(
        orderSupplierInvoiceService.getSupplierCanInvoiceOrderPage(param));
  }

  @ApiOperation("批量提取发票信息")
  @GetMapping("/identify")
  public ResultBean<List<InvoiceIdentifyResultDTO>> identify(@RequestParam("fileUrls") List<String> fileUrls) {
    return new ResultBean<>(supplierInvoiceService.batchInvoiceIdentify(fileUrls));
  }

  @SneakyThrows
  @ApiOperation(value = "导出寄票随单")
  @PostMapping(value = "downloadAcceptTemp", consumes = {MediaType.APPLICATION_JSON_VALUE})
  public ResponseEntity<byte[]> downloadSendTickets(@RequestBody List<String> orderIds) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData("attachment",
        URLEncoder.encode("sendTickets" + System.currentTimeMillis() + ".PDF", "UTF-8"));
    byte[] bytes = orderSupplierInvoiceService.downloadAcceptTemp(orderIds);
    return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
  }


  @SneakyThrows
  @ApiOperation(value = "导出供应商订单寄票随单")
  @PostMapping(value = "downloadSupplierOrderAcceptTemp", consumes =
      {MediaType.APPLICATION_JSON_VALUE})
  public ResponseEntity<byte[]> downloadSupplierOrderAcceptTemp(@RequestBody List<String> orderIds) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData("attachment",
        URLEncoder.encode("sendTickets" + System.currentTimeMillis() + ".PDF", "UTF-8"));
    byte[] bytes = orderSupplierInvoiceService.downloadSupplierOrderAcceptTemp(orderIds);
    return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
  }

  @ApiOperation(value = "发票验真")
  @PostMapping(value = "/verification")
  public ResultBean<InvoiceVerificationResult> invoiceVerification(
      @Valid @RequestBody InvoiceParam invoiceParams) {
    return new ResultBean<>(supplierInvoiceService.invoiceVerification(invoiceParams));
  }


  @ApiOperation(value = "其他发票验真")
  @PostMapping(value = "/verification/other")
  public ResultBean<InvoiceVerificationResult> invoiceOtherVerification(
      @Valid @RequestBody OtherInvoiceParam param) {
    return new ResultBean<>(supplierInvoiceService.invoiceOtherVerification(param));
  }

  @ApiOperation(value = "落地商发票保存")
  @PostMapping()
  @RepeatSubmit()
  public ResultBean<String> save(@RequestBody SupplierInvoiceSaveParam params) {
    RLock lock = null;
    String save;
    try {
      lock =
          redissonClient.getLock(Constants_LockName.SUPPLIER_INVOICE_SAVE + params.getSupplierId());
      lock.lock();
      save = orderSupplierInvoiceService.save(params);
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("未知错误，请联系管理员！");
    } finally {
      if (lock != null) {
        lock.unlock();
      }
    }
    return new ResultBean<>(save);
  }

  @ApiOperation(value = "供应商发票保存")
  @PostMapping(value = "/supplierInvoice")
  @RepeatSubmit()
  public ResultBean<String> supplierInvoiceSave(@RequestBody SupplierInvoiceSaveParam params) {
      RLock lock = null;
      String result;
      try {
          lock = redissonClient.getLock(
                  Constants_LockName.SUPPLIER_OPEN_INVOICE_SAVE + params.getSupplierId());
          lock.lock();
          result = orderSupplierInvoiceService.supplierInvoiceSave(params);
      } catch (CheckException checkException) {
          throw checkException;
      } catch (Exception e) {
          log.error(ExceptionUtil.stacktraceToString(e, -1));
          throw new CheckException("未知错误，请联系管理员！");
      } finally {
          if (lock != null) {
              lock.unlock();
          }
      }
      return new ResultBean<>(result);
  }

  @ApiOperation(value = "分页获取订单和供应商发票关联数据列表")
  @GetMapping("/relation/page")
  public ResultBean<PageResult<OrderInvoiceRelationDTO>> getRelationPage(
      OrderInvoiceRelationPageQuery dto) {
    return new ResultBean<>(orderSupplierInvoiceService.getRelationPage(dto));
  }

  @ApiOperation(value = "落地上开票订单详情")
  @GetMapping("/relation/details")
  public ResultBean<SupplierInvoiceDetailsDTO> getRelationDataDetails(
      @NotBlank String relationDataId) {
    return new ResultBean<>(orderSupplierInvoiceService.getRelationDataDetails(relationDataId));
  }

  @ApiOperation(value = "供应商开票订单详情")
  @GetMapping("/relation/supplier/invoice/details")
  public ResultBean<SupplierOpenInvoiceDetailsDTO> getSupplierInvoiceRelationDataDetails(
      @NotBlank String relationDataId) {
    return new ResultBean<>(orderSupplierInvoiceService.getSupplierInvoiceRelationDataDetails(relationDataId));
  }

  //todo 前后台都变更了发票信息接口
  @ApiOperation(value = "获取发票验真结果（已经验真过的数据）")
  @PostMapping(value = "/verification-result")
  public ResultBean<InvoiceIdentifyResult> getVerificationResult(
      @Valid @RequestBody InvoiceResultParam invoiceParams) {
    return new ResultBean<>(orderSupplierInvoiceService.getVerificationResult(invoiceParams));
  }

  @ApiOperation(value = "获取发票识别结果")
  @GetMapping(value = "/ocr-recognition-result")
  public ResultBean<InvoiceOcrRecognitionResultDTO> getOcrRecognitionResult(String invoiceNum) {
    return new ResultBean<>(shareInputInvoiceService.getOcrRecognitionResult(invoiceNum));
  }



  @ApiOperation("首页数据看板 - 开票付款")
  @GetMapping(value = "data-count")
  public ResultBean<AccountPaymentDTO> getDataCount(
      @NotBlank(message = "缺少供应商id参数") String supplierId) {
    return new ResultBean<>(orderSupplierInvoiceService.getDataCount(supplierId));
  }

  /**
   * 注册回调地址
   */
  @GetMapping(value = "callBackRegister")
  public ResultBean<Boolean> callBackRegister(@RequestParam @NotBlank String url,
      Boolean whetherToOverwrite, @RequestParam @NotBlank String callbackRouteKey) {
    dingUtils.callBackRegister(url, whetherToOverwrite, callbackRouteKey);
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "获取对账单详情", notes = "获取对账单详情")
  @ApiImplicitParams({@ApiImplicitParam(name = "accountId", value = "对账单id", dataType = "String",
      required = true),})
  @RequestMapping(value = "/getAccountDetail", method = RequestMethod.GET)
  @ResponseBody
  public ResultBean<AccountDetailDTO> getAccountDetail(
      @NotBlank(message = "对账单 id 必传 ") String accountId) {
    return new ResultBean<>(orderSupplierInvoiceService.getAccountDetail(accountId));
  }

  @ApiOperation(value = "删除暂存/驳回的发票单", notes = "删除暂存/驳回的发票单")
  @ApiImplicitParams(
      {@ApiImplicitParam(name = "invoiceOrderId", value = "发票单id", dataType = "String",
          required = true),})
  @DeleteMapping()
  public ResultBean<Boolean> deleteInvoiceOrder(
      @NotBlank(message = "缺少必要参数") String invoiceOrderId) {
    return new ResultBean<>(orderSupplierInvoiceService.deleteInvoiceOrder(invoiceOrderId));
  }

  @ApiOperation(value = "对账单开票")
  @PostMapping(value = "accountOpenInvoice", consumes = {MediaType.APPLICATION_JSON_VALUE})
  @RepeatSubmit
  public ResultBean<Boolean> accountOpenInvoice(
      @RequestBody @Valid AccountOpenInvoiceParams params) {
    orderSupplierInvoiceService.accountOpenInvoice(params);
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "保存发票单的物流信息", notes = "保存发票单的物流信息")
  @ApiImplicitParams(
      {@ApiImplicitParam(name = "invoiceOrderId", value = "发票单id", dataType = "String",
          required = true),})
  @PostMapping("/logistics")
  public ResultBean<Boolean> saveLogistics(@Valid @RequestBody SaveLogisticsParam saveLogisticsParam) {
    return new ResultBean<>(orderSupplierInvoiceService.saveLogistics(saveLogisticsParam));
  }

  @ApiOperation(value = "导出可开票订单详细")
  @GetMapping(value = "exportDetail")
  @SneakyThrows
  public ResponseEntity<byte[]> exportDetail(@RequestParam List<String> orderIds) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData("attachment", URLEncoder.encode("invoicableOrderDetail.xlsx", "UTF-8"));
    byte[] bytes = orderSupplierInvoiceService.exportDetail(orderIds);
    return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
  }

  @ApiOperation(value = "导出供应商可开票订单详细")
  @GetMapping(value = "exportSupplierOrderDetail")
  @SneakyThrows
  public ResponseEntity<byte[]> exportSupplierOrderDetail(@RequestParam List<String> orderIds) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData("attachment", URLEncoder.encode("SupplierOrderDetail"
            + ".xlsx", "UTF-8"));
    byte[] bytes = orderSupplierInvoiceService.exportSupplierOrderDetail(orderIds);
    return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
  }
}

