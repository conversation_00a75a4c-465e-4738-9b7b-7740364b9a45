package com.xhgj.srm.api.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class DeliveryParamDTO {
    @ApiModelProperty(value = "supplierId", required = true)
    private String supplierId;
    @ApiModelProperty(value = "orderId", required = true)
    @NotBlank(message = "订单id不能为空")
    private String orderId;

    @ApiModelProperty(value = "deliveryId(变更发货单传入)")
    private String deliveryId;

    @ApiModelProperty(value = "物流公司", required = true)
    @NotBlank(message = "物流公司不能为空！")
    private String expressCompany;

    @ApiModelProperty(value = "物流公司编码", required = true)
    @NotBlank(message = "物流公司编码不能为空！")
    private String expressCode;

    @ApiModelProperty(value = "物流单号")
    private String expressNo;

    @ApiModelProperty(value = "是否为erp订单")
    private Boolean isToErp;

    @ApiModelProperty(value = "发货详情(变更发货单无需传入)")
    private List<DeliveryProductParamDTO> productDetailList;

}
