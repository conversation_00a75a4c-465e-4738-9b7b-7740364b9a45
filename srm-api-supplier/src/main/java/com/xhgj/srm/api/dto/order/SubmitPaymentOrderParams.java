package com.xhgj.srm.api.dto.order;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @since 2023-03-16 14:42
 */
@Data
public class SubmitPaymentOrderParams {

  @ApiModelProperty("用户 id ")
  @NotBlank(message = "用户 id 必传")
  private String userId;

  @ApiModelProperty("订单 id")
  @NotEmpty(message = "订单 id 集合必传")
  private List<String> orderIdList;

  @ApiModelProperty("供应商备注")
  @Length(max = 140,message = "供应商备注超长")
  private String remark;

  @ApiModelProperty("付款单金额")
  @NotNull(message = "付款单金额必传")
  private BigDecimal paymentPrice;

}
