package com.xhgj.srm.api.provider.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.api.dto.product.ProductAddParam;
import com.xhgj.srm.api.dto.product.ProductDetailDTO;
import com.xhgj.srm.api.dto.product.ProductUpdateParam;
import com.xhgj.srm.api.dto.supplierBrand.SupplierBrandAddParam;
import com.xhgj.srm.api.front.factory.MapStructFactory;
import com.xhgj.srm.api.service.BrandService;
import com.xhgj.srm.api.service.FileService;
import com.xhgj.srm.api.service.ProductService;
import com.xhgj.srm.api.service.SupplierBrandService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.dto.MdmBrandPageData;
import com.xhgj.srm.dto.product.externalLink.ExternalLinkSaveForm;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.repository.SupplierBrandRepository;
import com.xhgj.srm.open.dto.product.OpenExternalLinkAndLabelDTO;
import com.xhgj.srm.open.form.product.OpenProductSaveForm;
import com.xhgj.srm.open.vo.brand.OpenSupplierBrandAuditVO;
import com.xhgj.srm.open.vo.product.OpenExternalLinkVO;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.open.form.brand.OpenSupplierBrandQueryForm;
import com.xhgj.srm.open.form.brand.OpenSupplierBrandSaveForm;
import com.xhgj.srm.open.form.product.OpenProductQueryForm;
import com.xhgj.srm.open.provider.OpenProductProvider;
import com.xhgj.srm.open.utils.OpenSecurityUtil;
import com.xhgj.srm.open.vo.brand.OpenSupplierBrandVO;
import com.xhgj.srm.open.vo.product.OpenProductListVO;
import com.xhgj.srm.open.vo.product.OpenProductVO;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OpenProductProviderImpl implements OpenProductProvider {

  @Resource
  SupplierBrandService supplierBrandService;
  @Resource
  OpenSecurityUtil openSecurityUtil;
  @Resource
  DownloadThenUpUtil downloadThenUpUtil;
  @Resource
  BrandService brandService;
  @Resource
  MPMService mpmService;
  @Resource
  ProductService productService;

  private static final String UPLOAD_PATH = "srm/brand";

  private static final String UPLOAD_PATH_2 = "srm/product";

  private static final String UPLOAD_PATH_MAIN_PIC = "srm/upload/mainPicture/%s";

  private static final String UPLOAD_PATH_DETAILED_PIC = "srm/upload/detailsPicture/%s";

  @Override
  public void saveSupplierBrand(OpenSupplierBrandSaveForm form) {
    SupplierUser supplierUser = openSecurityUtil.getOpenUserDetails().supplierUser();
    SupplierBrandAddParam param = MapStructFactory.INSTANCE.toSupplierBrandAddParam(form);
    param.setSupplierId(supplierUser.getSupplierId());
    param.setSupplierUserId(supplierUser.getId());
    String url = downloadThenUpUtil.downloadAndUpload(form.getBrandLogoUrl(), UPLOAD_PATH);
    if (StrUtil.isBlank(url)) {
      throw new CheckException("品牌logo文件上传失败");
    }
    param.setBrandLogoUrl(url);
    param.setManageType(form.getManageType());
    param.setDesc(form.getDesc());
    supplierBrandService.supplierBrandAdd(param);
  }

  @Override
  public PageResult<OpenSupplierBrandVO> getSupplierBrandPage(OpenSupplierBrandQueryForm form) {
    SupplierUser supplierUser = openSecurityUtil.getOpenUserDetails().supplierUser();
    PageResult<MdmBrandPageData> mdmBrandPage =
        brandService.getMdmBrandPage(supplierUser.getSupplierId(), form.getName(), form.getPageNo(),
            form.getPageSize());
    List<OpenSupplierBrandVO> openSupplierBrandVOS = mdmBrandPage.getContent().stream().map(
        MapStructFactory.INSTANCE::toOpenSupplierBrandVO).collect(Collectors.toList());
    return new PageResult<>(openSupplierBrandVOS, mdmBrandPage.getTotalCount(),
        mdmBrandPage.getTotalPages(), mdmBrandPage.getPageNo(),
        mdmBrandPage.getPageSize());
  }

  @Override
  public PageResult<OpenProductListVO> getMpmMaterialPage(OpenProductQueryForm form) {
    SupplierUser supplierUser = openSecurityUtil.getOpenUserDetails().supplierUser();
    form.setSupplierId(supplierUser.getSupplierId());
    TypeReference<PageResult<OpenProductListVO>> typeRef = new TypeReference<PageResult<OpenProductListVO>>() {};
    return mpmService.getProductTablePageBySupplier(form.toQueryMap(), typeRef);
  }

  @Override
  public OpenProductVO getMpmMaterialDetail(String code) {
    TypeReference<OpenProductVO> typeRef = new TypeReference<OpenProductVO>() {};
    // 已检验正确性
    return mpmService.getProductDetail(code, typeRef);
  }

  @Override
  public OpenProductVO getAuditProductDetail(String id) {
    ProductDetailDTO productDetail = productService.getProductDetail(id);
    OpenProductVO openProductVO =
        JSON.parseObject(JSON.toJSONString(productDetail), new TypeReference<OpenProductVO>() {});
    openProductVO.setCateMdmId(productDetail.getFourthCateMdmId());
    openProductVO.setShipperCode(productDetail.getOwner());
    openProductVO.setShipperName(productDetail.getOwnerName());
    openProductVO.setIsFreeShipping(productDetail.getIsDelisting());
    List<OpenExternalLinkVO> externalLinks = productDetail.getExternalLinks().stream().map(
        MapStructFactory.INSTANCE::toOpenExternalLinkVO).collect(Collectors.toList());
    openProductVO.setExternalLinks(externalLinks);
    // marketPrice
    // supplyPrice
    return openProductVO;
  }

  @Override
  public void saveProduct(OpenProductSaveForm form) {
    if (form.getId() == null) {
      addProduct(form);
    } else {
      updateProduct(form);
    }
  }

  private void addProduct(OpenProductSaveForm form) {
    ProductAddParam productAddParam = MapStructFactory.INSTANCE.toProductAddParam(form);
    common(null, productAddParam, form);
    productService.addProduct(productAddParam);
  }

  private void updateProduct(OpenProductSaveForm form) {
    ProductUpdateParam productUpdateParam = MapStructFactory.INSTANCE.toProductUpdateParam(form);
    common(productUpdateParam, null, form);
    productService.updateProduct(productUpdateParam);
  }

  private void common(ProductUpdateParam update, ProductAddParam add ,OpenProductSaveForm form) {
    boolean isUpdate = form.getId() != null;
    // 获取供应商
    SupplierUser supplierUser = openSecurityUtil.getOpenUserDetails().supplierUser();
    Supplier supplier = supplierUser.getSupplier();
    // form设置brand
    if (isUpdate) {
      update.setBrand(StrUtil.join("/", form.getBrandNameCn(), form.getBrandNameEn()));
    } else {
      add.setBrand(StrUtil.join("/", form.getBrandNameCn(), form.getBrandNameEn()));
    }
    // 校验外部链接是否超过4个
    if (form.getExternalLinks() == null) {
      form.setExternalLinks(new ArrayList<>());
    }
    if (form.getExternalLinks().size() > 4) {
      throw new CheckException("外部链接最多只能添加4个");
    }
    // 如果不足4个则补全到4个
//    if (form.getExternalLinks().size() < 4) {
//      for (int i = form.getExternalLinks().size(); i < 4; i++) {
//        form.getExternalLinks().add(new OpenExternalLinkAndLabelDTO("", "1"));
//      }
//    }
//    // 最后一个外部链接修改为类似
//    if (form.getExternalLinks() != null && !form.getExternalLinks().isEmpty()) {
//      form.getExternalLinks().get(form.getExternalLinks().size() - 1).setExternalLinkLabel("0");
//    }
    // 设置品牌
    if (isUpdate) {
      update.setBrandMdmId(form.getBrandId());
    } else {
      add.setBrandMdmId(form.getBrandId());
    }
    // 生成临时编码
    Map<String, Object> tempProductCode =
        productService.getTempProductCode(supplier.getMdmCode(), supplier.getId(), null);
    String tempCode = Convert.toStr(tempProductCode.get("tempCode"));
    if (isUpdate) {
      update.setTempCode(tempCode);
    } else {
      add.setTempCode(tempCode);
    }
    // 产品资料
    List<String> urls =
        downloadThenUpUtil.downloadAndUploadConcurrent(form.getProductLiteratures(), UPLOAD_PATH_2);
    List<File> fileList1 = downloadThenUpUtil.saveFile(urls, Constants.FILE_TYPE_XCZL, supplierUser.getId());
    if (isUpdate) {
      update.setProductLiteratures(buildFiles(fileList1));
    } else {
      add.setProductLiteratures(buildFiles(fileList1));
    }
    // 检测报告
    List<String> urls2 =
        downloadThenUpUtil.downloadAndUploadConcurrent(form.getReports(), UPLOAD_PATH_2);
    List<File> fileList2 = downloadThenUpUtil.saveFile(urls2, Constants.FILE_TYPE_JCBG, supplierUser.getId());
    if (isUpdate) {
      update.setReports(buildFiles(fileList2));
    } else {
      add.setReports(buildFiles(fileList2));
    }
    // 质量证明
    List<String> urls3 =
        downloadThenUpUtil.downloadAndUploadConcurrent(form.getCertificateOfQualitys(), UPLOAD_PATH_2);
    List<File> fileList3 = downloadThenUpUtil.saveFile(urls3, Constants.FILE_TYPE_CERTIFICATE_OF_QUALITY, supplierUser.getId());
    if (isUpdate) {
      update.setCertificateOfQualitys(buildFiles(fileList3));
    } else {
      add.setCertificateOfQualitys(buildFiles(fileList3));
    }
    // 外部链接
    List<OpenExternalLinkAndLabelDTO> externalLinks = form.getExternalLinks();
    List<ExternalLinkSaveForm> externalLinkSaveForms = externalLinks.stream().map(
        MapStructFactory.INSTANCE::toExternalLinkSaveForm).collect(Collectors.toList());
    if (isUpdate) {
      update.setExternalLinks(externalLinkSaveForms);
    } else {
      add.setExternalLinks(externalLinkSaveForms);
    }
    // 主图
    List<String> urls4 =
        downloadThenUpUtil.downloadAndUploadConcurrent(form.getMainPictures(), String.format(UPLOAD_PATH_MAIN_PIC, tempCode));
    List<File> fileList4 = downloadThenUpUtil.saveFile(urls4, Constants.FILE_TYPE_ZT,
        supplierUser.getId());
    if (isUpdate) {
      update.setMainPictures(buildFiles(fileList4));
    } else {
      add.setMainPictures(buildFiles(fileList4));
    }
    // 详情图
    List<String> urls5 =
        downloadThenUpUtil.downloadAndUploadConcurrent(form.getDetailedPictures(), String.format(UPLOAD_PATH_DETAILED_PIC, tempCode));
    List<File> fileList5 = downloadThenUpUtil.saveFile(urls5, Constants.FILE_TYPE_ZT,
        supplierUser.getId());
    if (isUpdate) {
      update.setDetailedPictures(buildFiles(fileList5));
    } else {
      add.setDetailedPictures(buildFiles(fileList5));
    }
    // 保存类型
    if (isUpdate) {
      update.setSaveType(Constants.PRODUCTCHECKTYPE_MAP_UPDATE);
      // 供应商id
      update.setSupplierId(supplier.getId());
      // 用户id
      update.setUserId(supplierUser.getId());
    } else {
      add.setSaveType(Constants.PRODUCTCHECKTYPE_MAP_ADD);
      // 供应商id
      add.setSupplierId(supplier.getId());
      // 用户id
      add.setUserId(supplierUser.getId());
    }
  }

  private List<FileDTO> buildFiles(List<File> files) {
    return files.stream().map(FileDTO::new).collect(Collectors.toList());
  }
}
