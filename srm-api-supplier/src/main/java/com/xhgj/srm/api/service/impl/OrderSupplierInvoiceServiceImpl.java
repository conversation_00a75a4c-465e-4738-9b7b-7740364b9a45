package com.xhgj.srm.api.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.UnicodeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.api.dto.InvoiceParam;
import com.xhgj.srm.api.dto.InvoiceResultParam;
import com.xhgj.srm.api.dto.OrderInvoiceRelationDTO;
import com.xhgj.srm.api.dto.OrderInvoiceRelationPageQuery;
import com.xhgj.srm.api.dto.SupplierInvoiceSaveParam;
import com.xhgj.srm.api.dto.TableMetaDTO;
import com.xhgj.srm.api.dto.account.AccountDetailDTO;
import com.xhgj.srm.api.dto.account.AccountOpenInvoiceParams;
import com.xhgj.srm.api.dto.account.AccountOpenInvoiceParams.InvoiceParams;
import com.xhgj.srm.api.dto.account.AccountPaymentDTO;
import com.xhgj.srm.api.dto.account.InvoiceOrderPageDTO;
import com.xhgj.srm.api.dto.account.InvoiceOrderPageQuery;
import com.xhgj.srm.api.dto.account.OrderAccountDingTalkMessage;
import com.xhgj.srm.api.dto.account.OrderAccountDingTalkMessageParams;
import com.xhgj.srm.api.dto.account.OrderAccountInvoiceInfo;
import com.xhgj.srm.api.dto.account.OrderInfoDTO;
import com.xhgj.srm.api.dto.account.ProductInfoDTO;
import com.xhgj.srm.api.dto.supplier.invoice.OrderAmount;
import com.xhgj.srm.api.dto.supplier.invoice.SupplierInvoiceDetailsDTO;
import com.xhgj.srm.api.dto.supplier.invoice.SupplierInvoiceDetailsDTO.SupplierInvoiceDetailsInvoiceDTO;
import com.xhgj.srm.api.dto.supplier.invoice.SupplierInvoiceDetailsDTO.SupplierInvoiceDetailsOrderDTO;
import com.xhgj.srm.api.dto.supplier.invoice.SupplierOpenInvoiceDetailsDTO;
import com.xhgj.srm.api.dto.supplier.invoice.SupplierOpenInvoiceDetailsDTO.SupplierOpenInvoiceDetailsInvoiceDTO;
import com.xhgj.srm.api.dto.supplier.invoice.SupplierOpenInvoiceDetailsDTO.SupplierOpenInvoiceDetailsOrderDTO;
import com.xhgj.srm.api.dto.supplierinvoice.DingFileDTO;
import com.xhgj.srm.api.dto.supplierinvoice.ExportSupplierOrderDetailDTO;
import com.xhgj.srm.api.dto.supplierinvoice.SaveLogisticsParam;
import com.xhgj.srm.api.dto.supplierinvoice.SupplierInvoiceOrderPageParam;
import com.xhgj.srm.api.dto.supplierinvoice.SupplierInvoiceOrderPageVO;
import com.xhgj.srm.api.service.FileService;
import com.xhgj.srm.api.service.OrderAccountInvoiceService;
import com.xhgj.srm.api.service.OrderAccountToOrderService;
import com.xhgj.srm.api.service.OrderDetailService;
import com.xhgj.srm.api.service.OrderService;
import com.xhgj.srm.api.service.OrderSupplierInvoiceService;
import com.xhgj.srm.api.service.SearchSchemeService;
import com.xhgj.srm.api.service.SupplierOrderDetailService;
import com.xhgj.srm.api.service.SupplierOrderService;
import com.xhgj.srm.api.service.SupplierOrderToFormService;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.api.service.SupplierUserService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.dto.DingTaskDTO;
import com.xhgj.srm.common.dto.DingTaskDTO.DetailUrlDTO;
import com.xhgj.srm.common.dto.DingTaskDTO.FiledKeyAndValueDTO;
import com.xhgj.srm.common.dto.DingTaskDTO.NotifyConfigsDTO;
import com.xhgj.srm.common.dto.DingTaskResultDTO;
import com.xhgj.srm.common.dto.OrderDetailDTO;
import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.common.dto.invoice.InvoiceIdentifyResult;
import com.xhgj.srm.common.dto.invoice.InvoiceOcrRecognitionResultDTO;
import com.xhgj.srm.common.dto.invoice.InvoiceOcrRecognitionResultDTO.Result;
import com.xhgj.srm.common.dto.invoice.InvoiceVerificationInfo;
import com.xhgj.srm.common.dto.invoice.OtherInvoiceIdentifyResult;
import com.xhgj.srm.common.enums.InvoiceTypeEnum;
import com.xhgj.srm.common.enums.TitleOfTheContractEnum;
import com.xhgj.srm.common.enums.VoucherAccountPeriodEnum;
import com.xhgj.srm.common.enums.VoucherAccountPeriodEnum.VoucherAccountPeriod;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.utils.CommonlyUseUtil;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.utils.FileUtil;
import com.xhgj.srm.common.utils.WordPoiUtils;
import com.xhgj.srm.common.utils.WordUtil;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.component.OrderAccountInvoiceDingTalkMessage;
import com.xhgj.srm.dto.OrderAccountInvoiceDingTalkMessageParams;
import com.xhgj.srm.dto.OrderAccountInvoiceDingTalkMessageParams.InvoiceTableDTO;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.dao.InvoiceVerificationDao;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.dao.OrderDetailDao;
import com.xhgj.srm.jpa.dao.OrderInvoiceRelationDao;
import com.xhgj.srm.jpa.dao.OrderSupplierInvoiceDao;
import com.xhgj.srm.jpa.dao.SupplierDao;
import com.xhgj.srm.jpa.dao.SupplierInGroupDao;
import com.xhgj.srm.jpa.dao.SupplierOrderDao;
import com.xhgj.srm.jpa.dao.SupplierPerformanceDao;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.InvoiceOcrRecognition;
import com.xhgj.srm.jpa.entity.InvoiceVerification;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderAccount;
import com.xhgj.srm.jpa.entity.OrderAccountToOrder;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInvoiceToDetail;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.InvoiceOcrRecognitionRepository;
import com.xhgj.srm.jpa.repository.OrderAccountRepository;
import com.xhgj.srm.jpa.repository.OrderAccountToOrderRepository;
import com.xhgj.srm.jpa.repository.OrderInvoiceRelationRepository;
import com.xhgj.srm.jpa.repository.OrderInvoiceRelationSplitRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.jpa.repository.OrderSupplierInvoiceRepository;
import com.xhgj.srm.jpa.repository.SearchSchemeRepository;
import com.xhgj.srm.jpa.repository.SupplierInGroupRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderToFormRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhgj.srm.service.OrderAcceptService;
import com.xhgj.srm.service.ShareInputInvoiceService;
import com.xhgj.srm.service.SharePlatformService;
import com.xhgj.srm.service.SupplierInvoiceService;
import com.xhgj.srm.service.SupplierInvoiceToDetailService;
import com.xhgj.srm.service.TempOrderAccountService;
import com.xhgj.srm.util.PlatformUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.ExcelUtil;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.upload.util.OssUtil;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
public class OrderSupplierInvoiceServiceImpl implements OrderSupplierInvoiceService {

  private final SupplierInGroupRepository supplierInGroupRepository;

  private final String url;

  public OrderSupplierInvoiceServiceImpl(SrmConfig config,
      SupplierInGroupRepository supplierInGroupRepository) {
    this.url = config.getUploadUrl();
    this.supplierInGroupRepository = supplierInGroupRepository;
  }

  @Deprecated
  @Autowired
  private OrderAccountRepository repository;
  @Autowired
  private OrderRepository orderRepository;
  @Autowired
  private OrderDao orderDao;
  @Autowired
  private OrderDetailDao orderDetailDao;
  @Autowired
  private FileRepository fileRepository;
  @Resource
  private OrderAccountInvoiceService orderAccountInvoiceService;
  @Autowired
  private SearchSchemeService searchSchemeService;
  @Autowired
  private SupplierUserService userService;
  @Autowired
  private OrderAccountToOrderRepository orderAccountToOrderRepository;
  @Autowired
  private OrderAccountToOrderService orderAccountToOrderService;
  @Autowired
  private OrderService orderService;
  @Autowired
  private TempOrderAccountService tempOrderAccountService;

  @Autowired
  private GroupRepository groupRepository;
  @Resource
  private SupplierPerformanceDao supplierPerformanceDao;
  @Resource
  private UserDao userDao;
  @Resource
  private SupplierDao supplierDao;
  @Autowired
  private SrmConfig srmConfig;
  @Resource
  private FileDao fileDao;
  @Autowired private OssUtil ossUtil;
  @Resource
  private OrderAccountDingTalkMessage orderAccountDingTalkMessage;
  @Resource
  private OrderAccountInvoiceDingTalkMessage orderAccountInvoiceDingTalkMessage;
  @Autowired
  private DingUtils dingUtils;
  @Resource
  private OrderAcceptService orderAcceptService;
  @Resource
  private OrderSupplierInvoiceRepository orderSupplierInvoiceRepository;
  @Autowired
  private OrderSupplierInvoiceDao orderSupplierInvoiceDao;
  @Resource
  private OrderInvoiceRelationRepository orderInvoiceRelationRepository;
  @Resource
  private OrderInvoiceRelationDao orderInvoiceRelationDao;
  @Resource
  private SearchSchemeRepository searchSchemeRepository;
  @Resource
  private OrderDetailService orderDetailService;
  @Resource
  private InvoiceVerificationDao invoiceVerificationDao;
  @Resource
  private FileService fileService;

  @Autowired
  private ExportUtil exportUtil;
  @Resource
  private SupplierInvoiceService supplierInvoiceService;
  @Resource
  private SharePlatformService platformService;
  @Resource
  private SupplierOrderService supplierOrderService;
  @Resource
  private SupplierOrderDao supplierOrderDao;
  @Resource
  private SupplierOrderDetailService supplierOrderDetailService;
  @Resource
  private SupplierOrderToFormService supplierOrderToFormService;
  @Resource
  private SupplierService supplierService;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;
  @Resource
  private ShareInputInvoiceService shareInputInvoiceService;
  @Resource
  private SupplierOrderToFormRepository supplierOrderToFormRepository;
  @Autowired
  private SupplierInGroupDao supplierInGroupDao;
  @Resource
  private InvoiceOcrRecognitionRepository invoiceOcrRecognitionRepository;
  @Resource
  private DownloadThenUpUtil downloadThenUpUtil;
  @Autowired
  private SupplierInvoiceToDetailService supplierInvoiceToDetailService;
  @Resource
  OrderInvoiceRelationSplitRepository orderInvoiceRelationSplitRepository;
  @Resource
  private SupplierRepository supplierRepository;
  private static final String DELIVERY_NOTE_URL_TYPE_JPSD = "srm/model/SRM寄票随单模板.docx";
  private static final String DELIVERY_NOTE_URL_TYPE_JPSD_SUPPLIER =
      "srm/model/SRM寄票随单模板 (供应商).docx";
  private static final String INVOICABLE_ORDERS = "srm/upload/可开票订单明细导出模板.xlsx";
  private static final String SUPPLIER_INVOICE_ORDERS = "srm/upload/供应商订单开票明细导出.xlsx";

  @Override
  public BootBaseRepository<OrderAccount, String> getRepository() {
    return repository;
  }



  @Override
  public AccountDetailDTO getAccountDetail(String accountId) {
    OrderAccount orderAccount = repository.findById(accountId)
        .orElseThrow(() -> CheckException.noFindException(OrderAccount.class, accountId));
    //根据订单的状态去展示对账金额，如果是完成确认过后的对账单，展示存好的值，否则展示动态去取的值
    List<String> orderIdList =
        orderAccountToOrderService.getOrderIdLIstByOrderAccountId(orderAccount.getId());
    BigDecimal accountFinalPrice;
    if (Objects.equals(orderAccount.getAccountState(),
        Constants_order.ORDER_ACCOUNT_STATUS_COMPLETE)) {
      accountFinalPrice = orderAccount.getPrice();
    } else {
      accountFinalPrice = tempOrderAccountService.getFinalAccountPrice(orderIdList);
    }
    AccountDetailDTO accountDetailDTO = new AccountDetailDTO(orderAccount, accountFinalPrice);
    if (CollUtil.isNotEmpty(orderIdList)) {
      accountDetailDTO.setOrderInfoList(
          CollUtil.emptyIfNull(orderDao.getByOrderIdList(orderIdList)).stream().map(order -> {
            String acceptState = orderAcceptService.getAcceptState(order.getId());
            String typeName = platformService.findNameByCode(order.getType());
            OrderInfoDTO orderInfoDTO = new OrderInfoDTO(order, typeName);
            orderInfoDTO.setSignVoucherState(acceptState);
            return orderInfoDTO;
          }).collect(Collectors.toList()));
    } else {
      accountDetailDTO.setOrderInfoList(ListUtil.toList());
    }
    List<ProductInfoDTO> productInfoList = new ArrayList<>();
    for (String orderId : orderIdList) {
      // 商品明细
      productInfoList.addAll(
          CollUtil.emptyIfNull(orderDetailDao.getOrderDetailByOrderId(orderId)).stream()
              .map(ProductInfoDTO::new).collect(Collectors.toList()));
    }
    accountDetailDTO.setProductInfoList(productInfoList);
    // 获取发票信息
    List<OrderAccountInvoiceInfo> orderAccountInvoiceInfoList =
        orderAccountInvoiceService.getByAccountId(orderAccount.getId(), url);
    accountDetailDTO.setOrderAccountInvoiceInfoList(orderAccountInvoiceInfoList);
    return accountDetailDTO;
  }

  /**
   * 是否是需要排除勾选的订单
   *
   * @param orderId 订单id
   * @param orderIds 需要排除勾选订单id的集合
   */
  private boolean isExclude(final String orderId, final List<String> orderIds) {
    if (CollUtil.isEmpty(orderIds)) {
      return false;
    }
    return orderIds.contains(orderId);
  }

  @Override
  public PageResult<InvoiceOrderPageDTO> getCanInvoiceOrderPage(InvoiceOrderPageQuery query,
      Pageable pageable) {
    String[] split = StrUtil.split(query.getExcludeOrderIds(), ",");
    List<String> orderIds = Arrays.asList(split);
    String schemeId = query.getSchemeId();
    String supplierUserId = query.getSupplierUserId();
    SupplierUser supplierUser = userService.get(supplierUserId,
        () -> CheckException.noFindException(SupplierUser.class, supplierUserId));
    Supplier supplier = supplierUser.getSupplier();
    String supplierId = supplier.getId();
    String orderNo = query.getOrderNo();
    String customer = query.getCustomer();
    String platform = query.getPlatform();
    String consignee = query.getReceiveMan();
    String customerPayback = query.getCustomerPayback();
    String signVoucher = query.getSignVoucher();
    Long orderTimeStart = query.getOrderTimeStart();
    Long orderTimeEnd = query.getOrderTimeEnd();
    Long dispatchTimeStart = query.getDispatchTimeStart();
    Long dispatchTimeEnd = query.getDispatchTimeEnd();
    // 查询方案
    if (StrUtil.isBlank(schemeId)) {
      SearchScheme search = searchSchemeService.getDefaultSearchScheme(supplierUser.getId(),
          Constants.SEARCH_TYPE_ACCOUNT_PAGE);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (StrUtil.isNotEmpty(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        InvoiceOrderPageQuery supplierChinaSchemeDTO =
            JSON.parseObject(search.getContent(), new TypeReference<InvoiceOrderPageQuery>() {});
        if (supplierChinaSchemeDTO != null) {
          orderNo = StrUtil.blankToDefault(orderNo, supplierChinaSchemeDTO.getOrderNo());
          customer = StrUtil.blankToDefault(customer, supplierChinaSchemeDTO.getCustomer());
          platform = StrUtil.blankToDefault(platform, supplierChinaSchemeDTO.getPlatform());
          consignee = StrUtil.blankToDefault(consignee, supplierChinaSchemeDTO.getReceiveMan());
          customerPayback =
              StrUtil.blankToDefault(customerPayback, supplierChinaSchemeDTO.getCustomerPayback());
          signVoucher =
              StrUtil.blankToDefault(signVoucher, supplierChinaSchemeDTO.getSignVoucher());
          orderTimeStart = ObjectUtil.defaultIfNull(orderTimeStart,
              supplierChinaSchemeDTO.getOrderTimeStart());
          orderTimeEnd = ObjectUtil.defaultIfNull(orderTimeEnd,
              supplierChinaSchemeDTO.getOrderTimeEnd());
          dispatchTimeStart = ObjectUtil.defaultIfNull(dispatchTimeStart,
              supplierChinaSchemeDTO.getDispatchTimeStart());
          dispatchTimeEnd = ObjectUtil.defaultIfNull(dispatchTimeEnd,
              supplierChinaSchemeDTO.getDispatchTimeEnd());
        }
      }
    }
    ArrayList<String> customerReturnList = new ArrayList<>();
    if (StrUtil.isNotBlank(customerPayback)) {
      if (customerPayback.equals(Constants_order.RETURN_PROGRESS_PART)) {
        customerReturnList.add(Constants_order.RETURN_PROGRESS_NO);
        customerReturnList.add(Constants_order.RETURN_PROGRESS_PART);
      } else {
        customerReturnList.add(Constants_order.RETURN_PROGRESS_ALL);
      }
    }

    return PageResultBuilder.buildPageResult(
        orderDao.getCanInvoiceOrderPage(orderNo, customer, platform, supplierId, consignee,
            Constants.ORDER_INVOICE_STATE_NOT_DONE, customerReturnList, signVoucher,
            orderTimeStart,orderTimeEnd,dispatchTimeStart,dispatchTimeEnd, pageable),
        order -> {
          String platformName = platformService.findNameByCode(order.getType());
          InvoiceOrderPageDTO dto =
              new InvoiceOrderPageDTO(order, orderDetailService.getOrderAmount(order.getId()), platformName);
          boolean exclude = isExclude(order.getId(), orderIds);
          dto.setIsDisabled(exclude);
          return dto;
        });
  }

  @Override
  public PageResult<SupplierInvoiceOrderPageVO> getSupplierCanInvoiceOrderPage(
      SupplierInvoiceOrderPageParam param) {
    String[] split = StrUtil.split(param.getExcludeOrderIds(), ",");
    List<String> excludeOrderIds = Arrays.asList(split);
    String schemeId = param.getSchemeId();
    String supplierUserId = param.getSupplierUserId();
    SupplierUser supplierUser = userService.get(supplierUserId,
        () -> CheckException.noFindException(SupplierUser.class, supplierUserId));
    String supplierId = param.getSupplierId();
    String code = param.getSupplierOrderCode();
    String receiveMan = param.getReceiveMan();
    // 查询方案
    if (StrUtil.isBlank(schemeId)) {
      SearchScheme search = searchSchemeService.getDefaultSearchScheme(supplierUser.getId(),
          Constants.SEARCH_TYPE_SUPPLIER_ACCOUNT_PAGE);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (StrUtil.isNotEmpty(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        SupplierInvoiceOrderPageParam supplierChinaSchemeDTO =
            JSON.parseObject(search.getContent(), new TypeReference<SupplierInvoiceOrderPageParam>() {});
        if (supplierChinaSchemeDTO != null) {
          receiveMan = StrUtil.blankToDefault(receiveMan, supplierChinaSchemeDTO.getReceiveMan());
          code = StrUtil.blankToDefault(code, supplierChinaSchemeDTO.getSupplierOrderCode());
        }
      }
    }
    return PageResultBuilder.buildPageResult(
        supplierOrderDao.getCanInvoiceOrderPage(code, SupplierOrderState.COMPLETE.getOrderState(),
            Constants.ORDER_INVOICE_STATE_NOT_DONE, receiveMan, supplierId,true, false, param.getPageNo(),
            param.getPageSize()),
        order -> {
          boolean exclude = isExclude(order.getId(), excludeOrderIds);
          return new SupplierInvoiceOrderPageVO(order, exclude);
        });
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void accountOpenInvoice(AccountOpenInvoiceParams params) {
    List<InvoiceParams> invoiceParams = params.getInvoiceParams();
    if (CollUtil.isEmpty(invoiceParams)) {
      return;
    }
    String orderAccountId = params.getOrderAccountId();
    OrderAccount orderAccount = get(orderAccountId,
        () -> CheckException.noFindException(OrderAccount.class, orderAccountId));
    if (!Constants_order.ORDER_ACCOUNT_STATUS_COMPLETE.equals(orderAccount.getAccountState())) {
      throw new CheckException("对账未完成，无法进行开票");
    }
    Supplier supplier = supplierDao.get(params.getSupplierId());
    if (supplier == null) {
      throw new CheckException("未找到此供应商");
    }
    //删除发票相关数据
    deleteOrderAccountInvoiceRelevanceData(params.getDeletedFileIds(), params.getDeletedIds());
    //采购人手机号
    Set<String> mobiles = new HashSet<>(srmConfig.getOrderOpenInvoiceDingOldNoticeFixed());
    //发票号
    List<String> invoiceNumbers = new ArrayList<>();
    //开票日期
    ArrayList<String> invoiceDate = new ArrayList<>();
    //金额
    ArrayList<String> money = new ArrayList<>();
    //物流公司
    Set<String> logisticsCompanyList = new HashSet<>();
    Set<String> logisticsInfList = new HashSet<>();
    //物流单号
    Set<String> logisticsNumList = new HashSet<>();
    //下单平台
    Set<String> platformList = new HashSet<>();
    //订单号
    HashMap<String, String> orderInfos = new HashMap<>();
    //发票附件
    ArrayList<FileDTO> fileDTOS = new ArrayList<>();
    AtomicInteger fileName = new AtomicInteger(1);
    List<InvoiceTableDTO> invoiceTableDTOS = new ArrayList<>();
    invoiceParams.forEach(param -> {
      String invoiceNum = param.getInvoiceNum();
      String invoiceCode = param.getInvoiceCode();
      Long invoiceTime = param.getInvoiceTime();
      BigDecimal price = param.getPrice();
      String logisticsCompany = param.getLogisticsCompany();
      String logisticsNum = param.getLogisticsNum();
      List<String> fileIdList = param.getFileIdList();
      List<com.xhgj.srm.api.dto.FileDTO> fileList = param.getFileList();
      OrderSupplierInvoice orderAccountInvoice = null;
      if (ObjectUtils.isEmpty(param.getInvoiceId())) {
        orderAccountInvoice =
            orderAccountInvoiceService.createOrderAccountInvoice(invoiceNum, invoiceCode,
                invoiceTime, price, logisticsCompany, logisticsNum, orderAccountId);
      } else {
        OrderSupplierInvoice invoice = orderAccountInvoiceService.get(param.getInvoiceId());
        if (invoice == null) {
          throw new CheckException("发票号：" + invoiceNum + "未找到");
        }
        invoice.setInvoiceNum(invoiceNum);
        invoice.setInvoiceCode(invoiceCode);
        invoice.setInvoiceTime(invoiceTime);
        invoice.setTotalAmountIncludingTax(price);
        invoice.setLogisticsCompany(logisticsCompany);
        invoice.setLogisticsNum(logisticsNum);
        invoice.setOrderAccountId(orderAccountId);
        orderAccountInvoice = orderAccountInvoiceService.update(invoice);
      }
      orderAccount.setAccountOpenInvoiceStatus(Constants.ORDER_INVOICE_STATE_ING);
      save(orderAccount);
      String accountInvoiceId = orderAccountInvoice.getId();
      if (CollUtil.isNotEmpty(fileIdList)) {
        fileIdList.forEach(fileId -> {
          fileService.updateRelevanceId(fileId, accountInvoiceId);
        });
      }
      if (CollUtil.isNotEmpty(fileList)) {
        OrderSupplierInvoice finalOrderAccountInvoice = orderAccountInvoice;
        fileList.forEach(fileDTO -> {
          if (StrUtil.isNotBlank(fileDTO.getId())) {
            return;
          }
          fileService.saveFile(fileDTO, null, finalOrderAccountInvoice.getId(),
              Constants.FILE_TYPE_INVOICE);
        });
      }
      //要发送钉钉通知的内容
      for (com.xhgj.srm.api.dto.FileDTO fileDTO : CollUtil.emptyIfNull(param.getFileList())) {
        FileDTO dto = new FileDTO();
        dto.setName("附件" + fileName.getAndIncrement());
        dto.setUrl(ossUtil.buildOssFileUrl(fileDTO.getUrl()));
        fileDTOS.add(dto);
      }
      InvoiceTableDTO invoiceTableDTO = new InvoiceTableDTO();
      invoiceNumbers.add(param.getInvoiceNum());
      String invoiceDateStr = "-";
      if (param.getInvoiceTime() != null) {
        DateTime date = DateUtil.date(param.getInvoiceTime());
        invoiceDateStr = DateUtil.format(date, "YYYY/MM/dd");
      }
      invoiceDate.add(invoiceDateStr);
      String invoicePrice = param.getPrice() == null ? "-" : param.getPrice().toPlainString();
      money.add(invoicePrice);
      invoiceTableDTO.setInvoiceDate(invoiceDateStr);
      invoiceTableDTO.setPrice(invoicePrice);
      invoiceTableDTO.setInvoiceNo(param.getInvoiceNum());
      invoiceTableDTOS.add(invoiceTableDTO);
      logisticsCompanyList.add(param.getLogisticsCompany());
      logisticsNumList.add(param.getLogisticsNum());
      logisticsInfList.add(param.getLogisticsCompany()+"-"+param.getLogisticsNum());
    });
    List<String> mobileNotice =
        CollUtil.emptyIfNull(srmConfig.getOrderOpenInvoiceDingTaskAndNewNotice());
    // 是否发送老的进项票通知（判断为该对账开票下的订单如果存在 采购订单号 为空的就发送老的）
    AtomicBoolean sendOldDingMsg = new AtomicBoolean(false);
    CollUtil.emptyIfNull(
            orderAccountToOrderService.getOrderIdLIstByOrderAccountId(orderAccount.getId()))
        .stream().map(orderId -> orderService.get(orderId))
        .filter(ObjectUtil::isNotNull).forEach(order -> {
          // 5.4.0 对账单开票新增待确认状态
          orderDao.optimisticLockUpdateOrder(order, o ->            {
              o.setSupplierOpenInvoiceStatus(Constants.ORDER_INVOICE_STATE_ING);
            if (StrUtil.isBlank(order.getSaleOrderNo())) {
              sendOldDingMsg.set(true);
            }
          });
          //获取采购人
          User purchaseUser = getPurchaseByInvoice(order);
          if (purchaseUser != null && !ObjectUtils.isEmpty(purchaseUser.getMobile())) {
            //采购人手机号
            mobiles.add(purchaseUser.getMobile());
          }
          //订单下单平台
          platformList.add(PlatformUtil.getPlatformNameByCode(order.getType()));
          orderInfos.put(order.getOrderNo(), order.getSaleOrderNo());
        });
    String id = orderAccountId + "," + Math.round((Math.random() + 1) * 1000);
    HashMap<String, Object> filesMap = new HashMap<>();
    filesMap.put("fileList", fileDTOS);
    Map<String, Object> tableMap = new HashMap<>();
    tableMap.put("data",invoiceTableDTOS);
    List<TableMetaDTO> tableMetaDTOS = buildTableMeta();
    tableMap.put("meta",tableMetaDTOS);
    filesMap.put("table_basic_usage", tableMap);
    boolean sendOldDingMsgBool = sendOldDingMsg.get();
    List<String> mobileList = new ArrayList<>(mobiles);
    String orderOpenInvoiceH5Url = srmConfig.getOrderOpenInvoiceH5Url();
    String detailLink = null;
    detailLink = sendOldDingMsgBool ? srmConfig.getSrmAdminLogin()
        : ("dingtalk://dingtalkclient/page/link?pc_slide=true&url="
            + URLUtil.encodeAll(orderOpenInvoiceH5Url + "?id=" + orderAccountId));
    OrderAccountInvoiceDingTalkMessageParams messageParams =
        OrderAccountInvoiceDingTalkMessageParams.builder()
            .title("【" + supplier.getEnterpriseName() + "】" + "开的进项票")
            //            .expressNames(logisticsCompanyList)
            //            .orderAccountNo(orderAccount.getAccountNo())
            //            .expressNos(logisticsNumList)
            .platforms(StrUtil.join(",", platformList))
            .orderInfos(orderInfos)
            .remark(
                sendOldDingMsgBool?"注意！本次开票无法自动生成应收，请确认此进项的 ERP 应收已做好，在确认通过":
                    "注意！此发票收到后无需在 ERP 做应付，核对后确认收票，可直接生成 ERP 应付单 ")
            .id(id)
            .detailLink(detailLink)
            .buttonShow("true")
            .sys_full_json_obj(filesMap)
            .mobileList(sendOldDingMsgBool?mobileList:mobileNotice).build();
    //发送钉钉通知
    Map<String, Object> messageInfo = sendDingTalkMessageForOrderAccountInvoice(messageParams,
        orderAccountId,sendOldDingMsgBool, false);
    //老的钉钉消息需要回调更改卡片 需要存储钉钉消息
    if (sendOldDingMsgBool) {
      saveMessageInfo(messageInfo,id,Constants.SAVE_MESSAGE_TYPE_DING_CARD);
    }else{
      Map<String, String> noticeMap =
          srmConfig.getOrderOpenInvoiceDingTaskAndNewNoticeMap();
      if(CollUtil.isNotEmpty(noticeMap)){
        String unionId =
            dingUtils.getUnionId(srmConfig.getOrderOpenInvoiceDingTaskAndNewNotice().get(0));
        for (Entry<String, String> entry : noticeMap.entrySet()) {
          try {
            String userId = dingUtils.getUnionId(entry.getKey());
            // 发送钉钉待办
            DingTaskResultDTO dingTask = dingUtils.createDingTask(
                buildAccountOpenInvoiceDingTask(supplier.getEnterpriseName(), unionId,
                    Arrays.asList(userId),
                    orderOpenInvoiceH5Url+"?id="+orderAccountId+"&userName="+entry.getValue(),
                    CollUtil.join(invoiceNumbers, "、"),
                    CollUtil.join(logisticsInfList, "、")), unionId);
            saveMessageInfo(BeanUtil.beanToMap(dingTask), orderAccountId + "," + dingTask.getId(),
                Constants.SAVE_MESSAGE_TYPE_DING_TODO);
          } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e,-1));
          }
        }
      }
    }
  }



  @Override
  public AccountPaymentDTO getDataCount(String supplierId) {
    long supplierOrderCount =
        supplierOrderService.getTotalByOrderState(supplierId,
            SupplierOrderState.COMPLETE.getOrderState());
    //可录入发票总计
    long canInvoiceOrderTotal =
        orderDao.getCanInvoiceOrderTotal(supplierId, Constants.ORDER_INVOICE_STATE_NOT_DONE);
    //被驳回的发票
    Supplier supplier = supplierService.get(supplierId,
        () -> CheckException.noFindException(Supplier.class, supplierId));
    String cooperateType = supplier.getCooperateType();
    long rejectInvoiceCount = 0;
    if (Objects.equals(cooperateType, Constants.COOPERATE_TYPE_ALL)) {
      rejectInvoiceCount =
          orderInvoiceRelationRepository.countByInvoiceStateAndStateAndSupplierId(
              Constants.ORDER_INVOICE_STATE_REJECT, Constants.STATE_OK, supplierId);
    }else {
      rejectInvoiceCount =
          orderInvoiceRelationRepository.countByInvoiceStateAndStateAndSupplierIdAndOrderSource(
              Constants.ORDER_INVOICE_STATE_REJECT, Constants.STATE_OK, supplierId, cooperateType);
    }
    //统计可付款订单的订单数量
    long payableOrderTotal =
        orderService.getOrderTotal(supplierId, null, Constants_order.WAIT_APPLY_PAYMENT_TYPE);
    return AccountPaymentDTO.builder().openInvoiceCount(canInvoiceOrderTotal)
        .payableOrdersTotal(payableOrderTotal).rejectInvoiceCount(rejectInvoiceCount)
        .supplierOrderOpenInvoiceCount(supplierOrderCount).build();
  }

  @Override
  public Collection<String> getPlatforms(String orderAccountId, boolean distinct) {
    if (StrUtil.isBlank(orderAccountId)) {
      return null;
    }
    List<String> orderIds =
        orderAccountToOrderService.getOrderIdLIstByOrderAccountId(orderAccountId);
    if (CollUtil.isEmpty(orderIds)) {
      return null;
    }
    List<String> platformCodes = orderIds.stream().map(orderId -> {
      Order order = orderDao.get(orderId);
      if (order != null) {
        return order.getType();
      }
      return null;
    }).filter(StrUtil::isNotBlank).collect(Collectors.toList());
    if (!distinct) {
      return platformCodes;
    }
    return new HashSet<>(platformCodes);
  }

  @Override
  @SneakyThrows
  public byte[] downloadAcceptTemp(List<String> orderIds) {
    String date = DateUtil.today();
    List<Order> orders = orderDao.getByOrderIdList(orderIds);
    if (CollUtil.isEmpty(orderIds)) {
      throw new CheckException("参数异常");
    }
    Set<String> supplierNames = orders.stream().map(order -> {
      return order.getSupplier().getEnterpriseName();
    }).collect(Collectors.toSet());
    if (CollUtil.isEmpty(supplierNames) || supplierNames.size() > 1) {
      throw new CheckException("订单数据异常，请联系管理员");
    }
    BigDecimal finalPrice = orders.stream().map(order -> {
      return NumberUtil.sub(order.getPrice(), order.getRefundPrice());
    }).reduce(BigDecimal.ZERO, BigDecimal::add);
    Map<String, String> fillInTemplateContent = new HashMap<>();
    fillInTemplateContent.put("${date}", date);
    fillInTemplateContent.put("${supplierName}", new ArrayList<>(supplierNames).get(0));
    fillInTemplateContent.put("${accountPrice}",
        CommonlyUseUtil.BigDecimalValue(finalPrice.toString()));
    try (InputStream inputStreamFromOSS = downloadThenUpUtil.getInputStreamFromOSS(DELIVERY_NOTE_URL_TYPE_JPSD)) {
      if (inputStreamFromOSS == null) {
        throw new CheckException("未找到寄票随单模板，请联系管理员放置模板");
      }
      try(XWPFDocument doc = new XWPFDocument(inputStreamFromOSS);) {
        //替换段落里面的变量
        WordPoiUtils.replaceInParaRef(doc, fillInTemplateContent);
        //替换表格里面的变量
        WordPoiUtils.replaceInTableRef(doc, fillInTemplateContent);
        // 填写订单信息
        List<OrderInfoDTO> orderInfoDTOList = orders.stream().map(order -> {
          String acceptState = orderAcceptService.getAcceptState(order.getId());
          String typeName = platformService.findNameByCode(order.getType());
          OrderInfoDTO orderInfoDTO = new OrderInfoDTO(order, typeName);
          orderInfoDTO.setSignVoucherState(acceptState);
          return orderInfoDTO;
        }).collect(Collectors.toList());
        int size = orderInfoDTOList.size();
        XWPFTable tableArray = doc.getTableArray(0);
        if (size > 1) {
          WordUtil.addRows(tableArray, 1, size - 1, 1);
        }
        int i = 1;
        //订单id对应的采购单号
        HashMap<String, String> orderIdToErpOrderNoMapping = new HashMap<>(orderInfoDTOList.size());
        //填充订单信息
        for (OrderInfoDTO accountDetail : orderInfoDTOList) {
          WordUtil.setRowsText(tableArray.getRow(i), StrUtil.emptyIfNull(accountDetail.getOrderNo()),
              //采购订单号
              StrUtil.isBlank(accountDetail.getErpOrderNo()) ? "-" : accountDetail.getErpOrderNo(),
              StrUtil.emptyIfNull(accountDetail.getPlatform()), StrUtil.emptyIfNull(
                  DateUtil.format(DateUtil.date(accountDetail.getOrderTime()),
                      "yyyy/MM/dd HH:mm:ss")), StrUtil.emptyIfNull(
                  CommonlyUseUtil.BigDecimalValue(accountDetail.getFinalPrice().toString())));
          i++;
          orderIdToErpOrderNoMapping.put(accountDetail.getOrderId(), accountDetail.getErpOrderNo());
        }
        XWPFTable tableArray2 = doc.getTableArray(1);
        //填充物料信息
        List<ProductInfoDTO> productList = new ArrayList<>();
        for (String orderId : orderIds) {
          productList.addAll(
              CollUtil.emptyIfNull(orderDetailDao.getOrderDetailByOrderId(orderId)).stream()
                  .map(ProductInfoDTO::new).collect(Collectors.toList()));
        }
        if (productList.size() > 1) {
          WordUtil.addRows(tableArray2, 1, productList.size() - 1, 1);
        }
        int n = 1;
        for (ProductInfoDTO productInfoDTO : productList) {
          WordUtil.setRowsText(tableArray2.getRow(n),
              StrUtil.emptyIfNull(productInfoDTO.getOrderNo()),
              StrUtil.isBlank(orderIdToErpOrderNoMapping.get(productInfoDTO.getOrderId())) ? "-"
                  : orderIdToErpOrderNoMapping.get(productInfoDTO.getOrderId()),
              StrUtil.emptyIfNull(productInfoDTO.getProductCode()),
              StrUtil.emptyIfNull(productInfoDTO.getBrandName()),
              StrUtil.emptyIfNull(productInfoDTO.getProductName()),
              StrUtil.emptyIfNull(productInfoDTO.getModel()), StrUtil.emptyIfNull(
                  productInfoDTO.getProductCount() != null ? CommonlyUseUtil.BigDecimalValue(
                      productInfoDTO.getProductCount().toString()) : "0"),
              productInfoDTO.getReturnNum() != null ? CommonlyUseUtil.BigDecimalValue(
                  productInfoDTO.getReturnNum().toString()) : "0",
              StrUtil.emptyIfNull(productInfoDTO.getUnit()), StrUtil.emptyIfNull(
                  productInfoDTO.getProductPrice() != null ? CommonlyUseUtil.BigDecimalValue(
                      productInfoDTO.getProductPrice().toPlainString()) : ""));
          n++;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        doc.write(out);
        try (InputStream inputStream = streamTran(out)) {
          return FileUtil.docConvertPdf(inputStream);
        }
      }
    }
  }


  @Override
  @SneakyThrows
  public byte[] downloadSupplierOrderAcceptTemp(List<String> orderIds) {
    String date = DateUtil.today();
    List<SupplierOrder> supplierOrders = orderIds.stream().map(id -> supplierOrderService.get(id,
            () -> CheckException.noFindException(SupplierOrder.class, id)))
        .collect(Collectors.toList());
    if (CollUtil.isEmpty(supplierOrders)) {
      throw new CheckException("参数异常");
    }
    Set<String> supplierIds = supplierOrders.stream().map(SupplierOrder::getSupplierId
    ).collect(Collectors.toSet());
    if (supplierIds.size() > 1) {
      throw new CheckException("订单数据异常，请联系管理员");
    }
    BigDecimal finalPriceCount = supplierOrders.stream().map(SupplierOrder::getFinalPrice)
        .filter(Objects::nonNull
        ).reduce(BigDecimal.ZERO, BigDecimal::add);
    Map<String, String> fillInTemplateContent = new HashMap<>();
    fillInTemplateContent.put("${date}", date);
    supplierIds.stream().findFirst().ifPresent(id ->{
      Supplier supplier =
          supplierService.get(id, () -> CheckException.noFindException(Supplier.class, id));
      fillInTemplateContent.put("${supplierName}", supplier.getEnterpriseName());
    });
    fillInTemplateContent.put("${accountPrice}",
        BigDecimalUtil.formatForStandard(finalPriceCount).toString());
    try (InputStream inputStreamFromOSS = downloadThenUpUtil.getInputStreamFromOSS(DELIVERY_NOTE_URL_TYPE_JPSD_SUPPLIER)) {
      if (inputStreamFromOSS == null) {
        throw new CheckException("未找到寄票随单模板，请联系管理员放置模板");
      }
      try(XWPFDocument doc = new XWPFDocument(inputStreamFromOSS);) {
        //替换段落里面的变量
        WordPoiUtils.replaceInParaRef(doc, fillInTemplateContent);
        //替换表格里面的变量
        WordPoiUtils.replaceInTableRef(doc, fillInTemplateContent);
        // 填写订单信息
        List<Map<String, Object>> orderInfoDTOList = supplierOrders.stream().map(order -> {
          String code = order.getCode();
          Long createTime = order.getCreateTime();
          String receiveMan = order.getReceiveMan();
          BigDecimal settleQty =
              supplierOrderDetailService.countSettleQtyBySupplierOrderId(order.getId());
          BigDecimal finalPrice = order.getFinalPrice();
          HashMap<String, Object> map = new HashMap<>();
          map.put("code", code);
          map.put("createTime", createTime);
          map.put("receiveMan", receiveMan);
          map.put("settleQty", BigDecimalUtil.formatForStandard(settleQty));
          map.put("finalPrice", BigDecimalUtil.formatForStandard(finalPrice));
          return map;
        }).collect(Collectors.toList());
        int size = orderInfoDTOList.size();
        XWPFTable tableArray = doc.getTableArray(0);
        if (size > 1) {
          WordUtil.addRows(tableArray, 1, size - 1, 1);
        }
        int i = 1;
        //填充订单信息
        for (Map<String, Object> map : orderInfoDTOList) {
          Long createTime = (Long) map.get("createTime");
          String createTimeFormat = DateUtil.format(DateUtil.date(createTime), "yyyy/MM/dd HH:mm:ss");
          WordUtil.setRowsText(tableArray.getRow(i),
              StrUtil.emptyIfNull(StrUtil.toString(map.get("code"))), createTimeFormat,
              (String) map.get("receiveMan"), StrUtil.toString(map.get("settleQty")),
              StrUtil.toString(map.get("finalPrice")));
          i++;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        doc.write(out);
        try (InputStream inputStream = streamTran(out)) {
          return FileUtil.docConvertPdf(inputStream);
        }
      }
    }
  }
  private InputStream streamTran(ByteArrayOutputStream in){
    return new ByteArrayInputStream(in.toByteArray());
  }

  @Override
  @Transactional
  public String save(SupplierInvoiceSaveParam param) {
      List<Order> orders = getOrders(param.getOrderIds());
      Set<String> titleOfTheContract =
          orders.stream().map(Order::getTitleOfTheContract).collect(Collectors.toSet());
    if (titleOfTheContract.size() > 1) {
      throw new CheckException("不同签约抬头的订单不能合并开票");
    }
    checkOrder(orders, param.getDateState());
      //删除发票
      batchDeleteInvoice(param.getRelationId());
      //解除订单
      batchUnboundOrder(param.getRelationId());
      //删除附件
      batchDeleteInvoiceFile(param.getDeleteFileIds());
      List<InvoiceParam> invoiceParam = param.getInvoiceParam();
      SupplierUser supplierUser = userService.get(param.getUserId(),
          () -> CheckException.noFindException(User.class, param.getUserId()));
      BigDecimal ordersAmount = getOrdersAmount(orders);
      //校验发票单id
      String auditor = null;
      Long examineTime = null;
      if (StrUtil.isNotBlank(param.getRelationId())) {
        Optional<InputInvoiceOrder> invoiceRelation =
            orderInvoiceRelationRepository.findById(param.getRelationId());
        if (invoiceRelation.isPresent()) {
          InputInvoiceOrder orderInvoiceRelation = invoiceRelation.get();
          auditor = orderInvoiceRelation.getAuditor();
          examineTime = orderInvoiceRelation.getExamineTime();
        } else {
          throw new CheckException("参数异常");
        }
      }
      //生成对应的关联记录
      String handleMan = getHandleMan(orders);
    InputInvoiceOrder orderInvoiceRelation =
          param.buildOrderInvoiceRelation(param.getRelationId(), orders.get(0).getType(), orders,
              supplierUser.getRealName(), ordersAmount, handleMan, auditor, examineTime,
              Constants.COOPERATE_TYPE_LANDER, orders.get(0).getTitleOfTheContract());
    if (StrUtil.isNotBlank(param.getSupplierId())) {
      String supplierId = param.getSupplierId();
      Supplier supplier = supplierService.getOptional(supplierId)
          .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
      // 设置 payType
      orderInvoiceRelation.setPayType(supplier.getPayType());
      // 设置账期
      Group group =
          groupRepository.findFirstByErpCodeAndState(orderInvoiceRelation.getGroupCode(),
              Constants.STATE_OK);
      if (group == null) {
        throw new CheckException("组织不存在");
      }
      supplierInGroupRepository.findFirstBySupplierIdAndGroupIdAndState(supplierId,
          group.getId(), Constants.STATE_OK).ifPresent(supplierInGroup -> {
        VoucherAccountPeriod accountPeriod =
            VoucherAccountPeriodEnum.getByType(supplierInGroup.getAccountPeriod());
        if (accountPeriod != null) {
          orderInvoiceRelation.setAccountPeriod(accountPeriod.getDesc());
        }
      });
    }

    Long firstOpenInvoiceTime =
        shareInputInvoiceService.getFirstOpenInvoiceDate(orderInvoiceRelation).orElse(null);
    if (firstOpenInvoiceTime==null&& !Constants.ORDER_INVOICE_STATE_TEMP.equals(param.getDateState())) {
      throw new CheckException("获取最早开票日期失败");
    }
    orderInvoiceRelation.setFirstOpenInvoiceDate(firstOpenInvoiceTime);
    List<InvoiceTypeEnum> invoiceTypeEnums =
        shareInputInvoiceService.getInvoiceTypeEnums(orderInvoiceRelation);
    Set<String> invoiceTypeCodes =
        invoiceTypeEnums.stream().map(InvoiceTypeEnum::getKey).collect(Collectors.toSet());
    if (CollUtil.isEmpty(invoiceTypeCodes)) {
      throw new CheckException("发票类型未能识别成功");
    }
    String invoiceTypeCodesStr = StrUtil.join(StrUtil.COMMA, invoiceTypeCodes);
    orderInvoiceRelation.setInvoiceType(invoiceTypeCodesStr);
    BigDecimal totalAmountOfTaxDeduction =
        shareInputInvoiceService.getTotalAmountOfTaxDeduction(orderInvoiceRelation);
    orderInvoiceRelation.setTotalAmountOfTaxDeduction(totalAmountOfTaxDeduction);
      orderInvoiceRelationRepository.save(orderInvoiceRelation);
      //订单存储关联数据id
      for (Order order : orders) {
        order.setOrderInvoiceRelationId(orderInvoiceRelation.getId());
        order.setSupplierOpenInvoiceStatus(param.getDateState());
        orderRepository.save(order);
      }
      //存储发票
      for (InvoiceParam invoice : invoiceParam) {
        this.orderNewCheckForV6_8_2(orders, invoice.getInvoiceNum());
        OrderSupplierInvoice firstByInvoiceNum =
            orderSupplierInvoiceDao.getValidInvoiceByNum(invoice.getInvoiceNum()).orElse(null);
        //能从系统中查出数据并且入参没有发票id的情况
        if (firstByInvoiceNum != null && StrUtil.isBlank(invoice.getId())) {
          throw new CheckException("系统中已存在的发票不能再次提交。");
        }
        OrderSupplierInvoice orderSupplierInvoice =
            invoice.buildOrderSupplierInvoice(orderInvoiceRelation.getId());
        //当发票类型为电子发票时，发票附件必填
        String invoiceType = invoice.getInvoiceType();
        boolean electronicInvoice =
            Objects.equals(InvoiceTypeEnum.ELECTRONIC_VAT_SPECIAL.getKey(), invoiceType)
                || Objects.equals(InvoiceTypeEnum.VAT_ELECTRONIC_SPECIAL.getKey(), invoiceType);
        FileDTO invoiceFile = invoice.getFile();
        if (electronicInvoice && invoiceFile == null) {
          throw new CheckException("电子发票必须同时上传发票附件");
        }
        orderSupplierInvoiceRepository.save(orderSupplierInvoice);
        if (invoiceFile != null) {
          File file =
              invoiceFile.buildFile(orderSupplierInvoice.getId(), Constants.FILE_TYPE_INVOICE);
          fileRepository.save(file);
        }
      }
      orderInvoiceRelationRepository.flush();
      orderRepository.flush();
      orderSupplierInvoiceRepository.flush();
      fileRepository.flush();
      if (Objects.equals(param.getDateState(), Constants.ORDER_INVOICE_STATE_ING)) {
        sendDingTalkMessageForOpenInvoice(orders, invoiceParam, param.getSupplierId(),
            orderInvoiceRelation.getId());
      }
      return orderInvoiceRelation.getId();
  }

  @Override
  @Transactional
  public String supplierInvoiceSave(SupplierInvoiceSaveParam param) {
    String relationId = param.getRelationId();
    //校验发票单id
    String auditor = null;
    Long examineTime = null;
    List<String> excludeOrderCodes;
    if (StrUtil.isNotBlank(relationId)) {
      InputInvoiceOrder invoiceRelation =
          orderInvoiceRelationRepository.findById(relationId)
              .orElseThrow(() -> new CheckException("参数异常, 未找到发票单"));
      auditor = invoiceRelation.getAuditor();
      examineTime = invoiceRelation.getExamineTime();
      excludeOrderCodes = StrUtil.split(invoiceRelation.getOrderCodes(),',');
    }else{
      excludeOrderCodes = null;
    }
    String supplierId = param.getSupplierId();
    Supplier supplier = supplierService.getOptional(supplierId)
        .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    List<SupplierOrder> supplierOrders = supplierOrderService.batchFindById(param.getOrderIds());
    checkSupplierOrder(supplierOrders, excludeOrderCodes);
    //删除发票
    batchDeleteInvoice(param.getRelationId());
    //解除订单
    unboundSupplierOrder(param.getRelationId());
    //删除附件
    batchDeleteInvoiceFile(param.getDeleteFileIds());
    List<InvoiceParam> invoiceParam = param.getInvoiceParam();
    List<String> orderCodes =
        supplierOrders.stream().map(SupplierOrder::getCode).filter(StrUtil::isNotBlank).collect(Collectors.toList());
    SupplierUser supplierUser = userService.get(param.getUserId(),
        () -> CheckException.noFindException(User.class, param.getUserId()));
    //最终结算金额
    BigDecimal ordersAmount = getSupplierOrdersAmount(supplierOrders);

    String invoiceOperator = getSupplierInvoiceOperator(supplierOrders);
    //生成发票单
    InputInvoiceOrder inputInvoiceOrder =
        param.buildOrderInvoiceRelationBySupplierInvoice(relationId, null, orderCodes,
            supplierUser.getRealName(), ordersAmount, invoiceOperator, auditor, examineTime,
            Constants.COOPERATE_TYPE_SUPPLIER, StrUtil.join(StrUtil.SLASH,
                invoiceParam.stream().map(InvoiceParam::getInvoiceNum)
                    .collect(Collectors.toList())));
    if (CollUtil.isNotEmpty(supplierOrders)) {
      // 设置groupCode
      inputInvoiceOrder.setGroupCode(supplierOrders.get(0).getGroupCode());
    }
    // 设置 payType
    inputInvoiceOrder.setPayType(supplier.getPayType());
    // 设置账期
    Group group =
        groupRepository.findFirstByErpCodeAndState(inputInvoiceOrder.getGroupCode(),
            Constants.STATE_OK);
    if (group == null) {
      throw new CheckException("组织不存在");
    }
    supplierInGroupRepository.findFirstBySupplierIdAndGroupIdAndState(supplierId,
        group.getId(), Constants.STATE_OK).ifPresent(supplierInGroup -> {
      VoucherAccountPeriod accountPeriod =
          VoucherAccountPeriodEnum.getByType(supplierInGroup.getAccountPeriod());
      if (accountPeriod != null) {
        inputInvoiceOrder.setAccountPeriod(accountPeriod.getDesc());
      }
    });

    Long firstOpenInvoiceTime =
        shareInputInvoiceService.getFirstOpenInvoiceDate(inputInvoiceOrder).orElse(null);
    if (firstOpenInvoiceTime==null&& !Constants.ORDER_INVOICE_STATE_TEMP.equals(param.getDateState())) {
      throw new CheckException("获取最早开票日期失败");
    }
    inputInvoiceOrder.setFirstOpenInvoiceDate(firstOpenInvoiceTime);
    List<InvoiceTypeEnum> invoiceTypeEnums =
        shareInputInvoiceService.getInvoiceTypeEnums(inputInvoiceOrder);
    Set<String> invoiceTypeCodes =
        invoiceTypeEnums.stream().map(InvoiceTypeEnum::getKey).collect(Collectors.toSet());
    if (CollUtil.isEmpty(invoiceTypeCodes)) throw new CheckException("发票类型未能识别成功");
    String invoiceTypeCodesStr = StrUtil.join(StrUtil.COMMA, invoiceTypeCodes);
    inputInvoiceOrder.setInvoiceType(invoiceTypeCodesStr);
    BigDecimal totalAmountOfTaxDeduction =
        shareInputInvoiceService.getTotalAmountOfTaxDeduction(inputInvoiceOrder);
    inputInvoiceOrder.setTotalAmountOfTaxDeduction(totalAmountOfTaxDeduction);
    // 更新
    shareInputInvoiceService.saveAndSplit(inputInvoiceOrder);
    //订单存储关联数据id
    for (SupplierOrder order : supplierOrders) {
      order.setOrderInvoiceRelationId(inputInvoiceOrder.getId());
      //开票状态调整
      // 不管是暂存还是提交都将订单的开票状态改为开票中
      order.setSupplierOpenInvoiceState(Constants.ORDER_INVOICE_STATE_HAND);
      supplierOrderService.save(order);
    }
    List<SupplierOrderDetail> supplierOrderDetails = getSupplierOrderDetails(supplierOrders);
    String invoiceNum =
        invoiceParam.stream().map(InvoiceParam::getInvoiceNum).distinct().collect(Collectors.joining(StrUtil.SLASH));
    String seller =
        invoiceParam.stream().map(InvoiceParam::getSeller).distinct().collect(Collectors.joining(StrUtil.SLASH));
    // 批量获取orderToForm
    List<String> orderFormIds =
        supplierOrderDetails.stream().map(SupplierOrderDetail::getOrderToFormId)
            .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
    Map<String, SupplierOrderToForm> id2SupplierOrderToForm =
        supplierOrderToFormRepository.findAllById(orderFormIds).stream()
            .collect(Collectors.toMap(SupplierOrderToForm::getId, Function.identity()));
    for (SupplierOrderDetail supplierOrderDetail : supplierOrderDetails) {
      SupplierOrderToForm supplierOrderToForm = id2SupplierOrderToForm.get(supplierOrderDetail.getOrderToFormId());
      // 获取orderToForm
      if (supplierOrderToForm == null) {
        throw new CheckException("未查询到相应入库单");
      }
      // 判断orderToForm的状态,如果是取消或者冲销状态则不允许开票,跳过
      if (SupplierOrderFormStatus.CANCEL.getStatus().equals(supplierOrderToForm.getStatus())) {
        continue;
      }
      if (SupplierOrderFormStatus.REVERSAL.getStatus().equals(supplierOrderToForm.getStatus())) {
        continue;
      }
      if (SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus().equals(supplierOrderToForm.getStatus())) {
        continue;
      }
      checkInvoiceNum(supplierOrderDetail);
      // 前台录入发票数量为入库数量，所以这里可开票数量为零
      supplierOrderDetail.setInvoicableNum(BigDecimal.ZERO);
      supplierOrderDetailService.save(supplierOrderDetail);
      //存储发票明细关联数据
      saveSupplierInvoiceToDetail(seller, invoiceNum, supplierOrderDetail, inputInvoiceOrder);
    }
//    List<String> updateIds =
//        invoiceParam.stream().map(InvoiceParam::getId).filter(Objects::nonNull)
//            .collect(Collectors.toList());
//    if (CollUtil.isNotEmpty(updateIds)) {
//      // 删除原发票明细关联数据
//      updateIds.forEach(this::returnInvoiceNumAndDeleteInvoiceDetail);
//    }
    //存储发票
    for (InvoiceParam invoice : invoiceParam) {
      this.supplierOrderNewCheckForV6_8_2(supplierOrders, invoice.getInvoiceNum());
      checkInvoice(invoice);
      String sellerOne = invoice.getSeller();
      // 前台此处默认万聚
      String groupCode = Constants.GROUP_WANJU_CODE;
      if (Objects.equals(Constants.SUPPLIER_TYPE_PROVISIONAL, supplier.getSupType())) {
        // 一次性供应商校验销方在该组织下是否存在
        if (supplierInGroupDao.countSupplierInGroupByEnterpriseNameAndGroupCode(
            ListUtil.toList(StrUtil.replace(StrUtil.replace(sellerOne, "(", "（"), ")", "）"),
                StrUtil.replace(StrUtil.replace(sellerOne, "（", "("), "）", ")")),
            groupCode, Constants.SUPPLIERTYPE_CHINA)==0) {
          throw new CheckException("当前组织下没有与发票销方名称一致的供应商，请先新增此供应商");
        }
      }
      OrderSupplierInvoice orderSupplierInvoice = invoice.buildOrderSupplierInvoice(inputInvoiceOrder.getId());
      orderSupplierInvoiceRepository.save(orderSupplierInvoice);
      //存储附件
      if (invoice.getFile() != null) {
        File file = invoice.getFile().buildFile(orderSupplierInvoice.getId(), Constants.FILE_TYPE_INVOICE);
        fileRepository.save(file);
      }
      orderInvoiceRelationRepository.flush();
      orderRepository.flush();
      orderSupplierInvoiceRepository.flush();
      fileRepository.flush();
      //之前会给应处理人发送钉钉消息 现在不发了
//      if (Objects.equals(param.getDateState(), Constants.ORDER_INVOICE_STATE_ING)) {
//        sendDingTalkMessageForSupplierOpenInvoice(supplierOrders, invoiceParam, param.getSupplierId(),
//            handleMan, inputInvoiceOrder.getId());
//      }
    }
    return inputInvoiceOrder.getId();

  }

  private void supplierOrderNewCheckForV6_8_2(List<SupplierOrder> supplierOrders, String invoiceNum) {
    if (CollUtil.isEmpty(supplierOrders)) {
      return;
    }
    InvoiceOcrRecognitionResultDTO ocrRecognitionResult = shareInputInvoiceService.getOcrRecognitionResult(invoiceNum);
    if (ocrRecognitionResult == null || ocrRecognitionResult.getResult() == null) {
      throw new CheckException("发票识别信息不存在");
    }
    Result result = ocrRecognitionResult.getResult();
    String sellerName = result.getSellerNameReplace();
    String purchaserName = result.getPurchaserNameReplace();
    Supplier supplier =
        supplierRepository.findFirstByEnterpriseNameAndState(sellerName, Constants.STATE_OK)
            .orElse(new Supplier());
    for (SupplierOrder supplierOrder : supplierOrders) {
      // 销方校验---只要销方名称 + 订单供应商名称一致 或 id一致那么也可以提交
      boolean sellerBool1 = StrUtil.equals(supplierOrder.makeSupplierNameReplace(), sellerName);
      boolean sellerBool2 = StrUtil.equals(supplierOrder.getSupplierId(), supplier.getId());
      if (!sellerBool2 && !sellerBool1) {
        throw new CheckException("发票销方与订单信息不一致，请核实");
      }
      // 购方信息核实
      Group group = groupRepository.findFirstByCodeAndState(supplierOrder.getGroupCode(), Constants.STATE_OK);
      if (group == null) {
        throw new CheckException("购方信息不存在");
      }
      if (!StrUtil.equals(group.makeNameReplace(), purchaserName)) {
        throw new CheckException("发票购方与订单信息不一致，请核实");
      }
    }
  }

  private void orderNewCheckForV6_8_2(List<Order> orders, String invoiceNum) {
    if (CollUtil.isEmpty(orders)) {
      return;
    }
    InvoiceOcrRecognitionResultDTO ocrRecognitionResult = shareInputInvoiceService.getOcrRecognitionResult(invoiceNum);
    if (ocrRecognitionResult == null || ocrRecognitionResult.getResult() == null) {
      throw new CheckException("发票识别信息不存在");
    }
    Result result = ocrRecognitionResult.getResult();
    String sellerName = result.getSellerNameReplace();
    String purchaserName = result.getPurchaserNameReplace();
    for (Order order : orders) {
      Supplier supplier = order.getSupplier();
      // 销方信息核实
      if (supplier == null) {
        throw new CheckException("订单数据异常，请联系管理员");
      }
      if (!StrUtil.equals(supplier.makeEnterpriseNameReplace(), sellerName)) {
        throw new CheckException("发票销方与订单信息不一致，请核实");
      }
      // 购方信息核实
      String titleOfTheContract = order.getTitleOfTheContract();
      Group group = groupRepository.findFirstByCodeAndState(titleOfTheContract, Constants.STATE_OK);
      if (group == null) {
        throw new CheckException("购方信息不存在");
      }
      if (!StrUtil.equals(group.makeNameReplace(), purchaserName)) {
        throw new CheckException("发票购方与订单信息不一致，请核实");
      }
    }
  }



  private void saveSupplierInvoiceToDetail(String seller, String invoiceNumber,
      SupplierOrderDetail supplierOrderDetail, InputInvoiceOrder inputInvoiceOrder) {
    SupplierInvoiceToDetail invoiceToDetail = new SupplierInvoiceToDetail();
    invoiceToDetail.setSeller(seller);
    invoiceToDetail.setInvoiceNumber(invoiceNumber);
    invoiceToDetail.setDetailId(supplierOrderDetail.getId());
    invoiceToDetail.setCreateTime(System.currentTimeMillis());
    BigDecimal openInvoiceAmount = supplierOrderDetail.getDetailed().getPrice()
        .multiply(NumberUtil.sub(supplierOrderDetail.getStockInputQty(),
            supplierOrderDetail.getStockOutputQty()));
    invoiceToDetail.setOpenTaxAmount(openInvoiceAmount);
    BigDecimal taxAmount = openInvoiceAmount
        .divide(supplierOrderDetail.getTaxRate() == null ?
                BigDecimal.ONE :supplierOrderDetail.getTaxRate().add(BigDecimal.ONE),2,
            BigDecimal.ROUND_HALF_UP);
    invoiceToDetail.setTaxAmount(openInvoiceAmount.subtract(taxAmount));
    invoiceToDetail.setTaxFreeAmount(taxAmount);
    invoiceToDetail.setInvoiceNum(NumberUtil.sub(supplierOrderDetail.getStockInputQty(),
        supplierOrderDetail.getStockOutputQty()));
    invoiceToDetail.setInputInvoiceOrderId(inputInvoiceOrder.getId());
    supplierInvoiceToDetailService.save(invoiceToDetail);
  }

  private void checkInvoiceNum(SupplierOrderDetail supplierOrderDetail) {
    String code = supplierOrderDetail.getSupplierOrderProduct().getCode();
    if (!NumberUtil.equals(NumberUtil.sub(supplierOrderDetail.getStockInputQty(),
        supplierOrderDetail.getStockOutputQty()),
        supplierOrderDetail.getInvoicableNum())) {
        throw new CheckException(code+
            "开票数量数据异常，请联系管理员");
    }
    // 校验可开票数量 修正
    // 不应该使用
  }

  /**
   * 获取供应商订单关联的明细(已过滤订货数量小于等于0的)
   * @param supplierOrders
   * @return
   */
  private List<SupplierOrderDetail> getSupplierOrderDetails(List<SupplierOrder> supplierOrders) {
    return supplierOrders.stream().map(supplierOrder -> {
      List<SupplierOrderToForm> supplierOrderToForms =
          supplierOrderToFormRepository.findBySupplierOrderIdAndTypeAndState(supplierOrder.getId(),
              SupplierOrderFormType.WAREHOUSING.getType(), Constants.STATE_OK);
      return supplierOrderToForms.stream().map(supplierOrderToForm -> supplierOrderDetailService.getByOrderToFormId(supplierOrderToForm.getId()))
          .flatMap(Collection::stream).collect(Collectors.toList());
    }).flatMap(Collection::stream)
        .filter(supplierOrderDetail -> {
          BigDecimal num = NumberUtil.sub(supplierOrderDetail.getStockInputQty(),
              supplierOrderDetail.getStockOutputQty());
          return num.compareTo(BigDecimal.ZERO) > 0;
        }).collect(Collectors.toList());
  }

  private void checkInvoice(InvoiceParam invoice) {
    OrderSupplierInvoice firstByInvoiceNum =
        orderSupplierInvoiceDao.getValidInvoiceByNum(invoice.getInvoiceNum()).orElse(null);
    //能从系统中查出数据并且入参没有发票id的情况
    if (firstByInvoiceNum != null && StrUtil.isBlank(invoice.getId())) {
      throw new CheckException("系统中已存在的发票不能再次提交。");
    }
    //当发票类型为电子发票时，发票附件必填
    String invoiceType = invoice.getInvoiceType();
    boolean electronicInvoice = Objects.equals(InvoiceTypeEnum.ELECTRONIC_VAT_SPECIAL.getKey()
        , invoiceType) ||
        Objects.equals(InvoiceTypeEnum.VAT_ELECTRONIC_SPECIAL.getKey(), invoiceType);
    FileDTO invoiceFile = invoice.getFile();
    if (electronicInvoice && invoiceFile == null) {
      throw new CheckException("电子发票必须同时上传发票附件");
    }
  }

  /**
   * 删除文件
   * @param fileIds 文件id
   */
  private void batchDeleteInvoiceFile(List<String> fileIds) {
    if (CollUtil.isEmpty(fileIds)) {
      return;
    }
    fileIds.stream().filter(StrUtil::isNotBlank).forEach(id ->{
      fileService.delete(id);
    });
  }

  /**
   * 校验订单是否都有销售订单号
   * 有一个没有的话返回false
   */
  private Boolean isNewOrder(List<Order> orders) {
    if (CollUtil.isEmpty(orders)) {
      return null;
    }
    for (Order order : orders) {
      String saleOrderNo = order.getSaleOrderNo();
      if (StrUtil.isBlank(saleOrderNo)) {
        return false;
      }
    }
    return true;
  }
  private void sendDingTalkMessageForOpenInvoice(List<Order> orders,
      List<InvoiceParam> invoiceParams,
      String supplierId, String orderInvoiceRelationId) {
    Supplier supplier = supplierDao.get(supplierId);
    if (supplier == null) {
      throw new CheckException("参数异常");
    }
    String id = orderInvoiceRelationId + "," + Math.round((Math.random() + 1) * 1000);
    boolean isNewOrder = Boolean.TRUE.equals(isNewOrder(orders));
    String handleMan = getHandleMan(orders);
    List<String> mobileNotice = new ArrayList<>();
    // 通知发给指定人员
    String design = "况容";
    if (design.equals(handleMan)) {
      srmConfig.getOrderOpenInvoiceDingTaskAndNewNoticeMap().forEach((k, v) -> {
        if (design.equals(v)) {
          mobileNotice.add(k);
        }
      });
    } else {
      srmConfig.getOrderOpenInvoiceDingTaskAndNewNoticeMap().forEach((k, v) -> {
        if (!design.equals(v)) {
          mobileNotice.add(k);
        }
      });
    }
    String detailLink =
        isNewOrder ? srmConfig.getOrderSupplierInvoiceH5Url() + "?id=" + orderInvoiceRelationId
            : srmConfig.getSrmAdminLogin();
    //采购人手机号
    Set<String> mobiles = new HashSet<>(srmConfig.getOrderOpenInvoiceDingOldNoticeFixed());
    HashMap<String, String> orderInfos = new HashMap<>();
    for (Order order : orders) {
      orderInfos.put(order.getOrderNo(), order.getSaleOrderNo());
      //获取采购人
      User purchaseUser = getPurchaseByInvoice(order);
      if (purchaseUser != null && StrUtil.isNotBlank(purchaseUser.getMobile())) {
        //采购人手机号
        mobiles.add(purchaseUser.getMobile());
      }
    }
    //发票附件
    List<com.xhgj.srm.api.dto.FileDTO> fileDTOS =
        invoiceParams.stream().map(InvoiceParam::getFile).filter(Objects::nonNull)
            .collect(Collectors.toList());
    List<DingFileDTO> dingFileDTOS = DingFileDTO.buildByFileList(fileDTOS);
    List<InvoiceTableDTO> invoiceTableDTOS = new ArrayList<>();
    for (InvoiceParam invoiceParam : invoiceParams) {
      InvoiceTableDTO invoiceTableDTO = new InvoiceTableDTO();
      String invoiceTime =
          DateUtil.format(new DateTime(invoiceParam.getInvoiceTime()), "yyyy-MM-dd");
      invoiceTableDTO.setInvoiceDate(invoiceTime);
      invoiceTableDTO.setInvoiceNo(invoiceParam.getInvoiceNum());
      invoiceTableDTO.setPrice(invoiceParam.getTotalAmountIncludingTax().toString());
      invoiceTableDTOS.add(invoiceTableDTO);
    }
    Map<String, Object> tableMap = new HashMap<>();
    tableMap.put("data", invoiceTableDTOS);
    //模版
    List<TableMetaDTO> tableMetaDTOS = buildTableMeta();
    tableMap.put("meta", tableMetaDTOS);
    HashMap<String, Object> filesMap = new HashMap<>();
    filesMap.put("fileList", dingFileDTOS);
    filesMap.put("table_basic_usage", tableMap);
    OrderAccountInvoiceDingTalkMessageParams messageParams =
        OrderAccountInvoiceDingTalkMessageParams.builder().orderInfos(orderInfos)
            .title("【" + supplier.getEnterpriseName() + "】" + "开的进项票")
            .platforms(PlatformUtil.getPlatformNameByCode(orders.get(0).getType())).remark(
                isNewOrder ? "注意！此发票收到后无需在 ERP 做应付，核对后确认收票，可直接生成 ERP 应付单 "
                    : "注意！本次开票无法自动生成应收，请确认此进项的 ERP 应收已做好，在确认通过").id(id)
            .detailLink(detailLink).buttonShow("true").sys_full_json_obj(filesMap)
            .mobileList(isNewOrder ? mobileNotice : new ArrayList<>(mobiles)).build();
    if (isNewOrder) {
      sendNewDingTalkMessageForSupplierInvoice(messageParams, supplier, invoiceParams, orderInvoiceRelationId);
    } else {
      sendOldDingTalkMessageForSupplierInvoice(id, messageParams, false);
    }
  }

  private void sendDingTalkMessageForSupplierOpenInvoice(List<SupplierOrder> orders,
      List<InvoiceParam> invoiceParams,
      String supplierId, User handleMan, String orderInvoiceRelationId) {
    Supplier supplier = supplierDao.get(supplierId);
    if (supplier == null) {
      throw new CheckException("参数异常");
    }
    String id = orderInvoiceRelationId + "," + Math.round((Math.random() + 1) * 1000);
    String detailLink =
        UnicodeUtil.toUnicode(srmConfig.getOrderSupplierInvoiceH5Url() + "?id=" + orderInvoiceRelationId + "&userName=" +
            handleMan.getRealName() + "&type=" + Constants.COOPERATE_TYPE_SUPPLIER);
    //采购人手机号
    HashMap<String, String> orderInfos = new HashMap<>();
    for (SupplierOrder order : orders) {
      orderInfos.put(order.getCode(), null);
    }
    //发票附件
    List<com.xhgj.srm.api.dto.FileDTO> fileDTOS =
        invoiceParams.stream().map(InvoiceParam::getFile).filter(Objects::nonNull)
            .collect(Collectors.toList());
    List<DingFileDTO> dingFileDTOS = DingFileDTO.buildByFileList(fileDTOS);
    List<InvoiceTableDTO> invoiceTableDTOS = new ArrayList<>();
    for (InvoiceParam invoiceParam : invoiceParams) {
      InvoiceTableDTO invoiceTableDTO = new InvoiceTableDTO();
      String invoiceTime =
          DateUtil.format(new DateTime(invoiceParam.getInvoiceTime()), "yyyy-MM-dd");
      invoiceTableDTO.setInvoiceDate(invoiceTime);
      invoiceTableDTO.setInvoiceNo(invoiceParam.getInvoiceNum());
      invoiceTableDTO.setPrice(invoiceParam.getTotalAmountIncludingTax().toString());
      invoiceTableDTOS.add(invoiceTableDTO);
    }
    Map<String, Object> tableMap = new HashMap<>();
    tableMap.put("data", invoiceTableDTOS);
    //模版
    List<TableMetaDTO> tableMetaDTOS = buildTableMeta();
    tableMap.put("meta", tableMetaDTOS);
    HashMap<String, Object> filesMap = new HashMap<>();
    filesMap.put("fileList", dingFileDTOS);
    filesMap.put("table_basic_usage", tableMap);
    String HIDE = "HIDE";
    OrderAccountInvoiceDingTalkMessageParams messageParams =
        OrderAccountInvoiceDingTalkMessageParams.builder().orderInfos(orderInfos)
            .title("【" + supplier.getEnterpriseName() + "】" + "开的进项票")
            .platforms(HIDE).remark("注意！此发票收到后无需在 ERP 做应付，核对后确认收票，可直接生成 ERP 应付单")
            .id(id).detailLink(detailLink).buttonShow("true").sys_full_json_obj(filesMap)
            .mobileList(ListUtil.toList(handleMan.getMobile())).build();
    sendOldDingTalkMessageForSupplierInvoice(id, messageParams, false);
  }

  private void sendOldDingTalkMessageForSupplierInvoice(String id,
      OrderAccountInvoiceDingTalkMessageParams messageParams, boolean isSupplierOpenInvoice) {
    //发送钉钉通知
    Map<String, Object> messageInfo =
        sendDingTalkMessageForOrderAccountInvoice(messageParams, id, false, isSupplierOpenInvoice);
    saveMessageInfo(messageInfo, id, Constants.SAVE_MESSAGE_TYPE_DING_CARD);
  }

  private void sendNewDingTalkMessageForSupplierInvoice(
      OrderAccountInvoiceDingTalkMessageParams messageParams, Supplier supplier,
      List<InvoiceParam> invoiceParam, String id) {
    String msg = "";
    String dingTalkToDoSubject = "";
    for (InvoiceParam param : invoiceParam) {
      String invoiceNum = param.getInvoiceNum();
      if(invoiceNum !=null){
        InvoiceVerification invoiceVerification = invoiceVerificationDao.findByExample(
            InvoiceVerification.builder().invoiceNumber(invoiceNum).state(Constants.STATE_OK).effective(Constants.YES)
                .build());
        if(invoiceVerification!=null){
          String dataJson = invoiceVerification.getDataJson();
          if(dataJson.contains("润滑油") || dataJson.contains("润滑脂")
              || dataJson.contains("柴油") || dataJson.contains("机油")){
            msg = "【成品油】";
          }
        }
      }
      dingTalkToDoSubject = "【电票】"+msg+"落地商【"+supplier.getEnterpriseName()+"】开的进项票，请线下收票后确认";
      String invoiceType = param.getInvoiceType();
      if (Objects.equals(invoiceType, InvoiceTypeEnum.VAT_SPECIAL.getKey()) ||
          Objects.equals(invoiceType, InvoiceTypeEnum.VAT_ORDINARY_INVOICE.getKey())  ) {
        dingTalkToDoSubject = "【纸制票】"+msg+"落地商【" + supplier.getEnterpriseName() + "】开的进项票，请线下收票后确认";
        break;
      }
    }
    //发送钉钉通知
    sendDingTalkMessageForOrderAccountInvoice(messageParams, id, false, false);
    String orderOpenInvoiceH5Url = srmConfig.getOrderOpenInvoiceH5Url();
    List<String> invoiceNumbers =
        invoiceParam.stream().map(InvoiceParam::getInvoiceNum).filter(Objects::nonNull)
            .collect(Collectors.toList());
    Map<String, String> noticeMap = srmConfig.getOrderOpenInvoiceDingTaskAndNewNoticeMap();
    if (CollUtil.isNotEmpty(noticeMap)) {
      String unionId =
          dingUtils.getUnionId(srmConfig.getOrderOpenInvoiceDingTaskAndNewNotice().get(0));
      String orderSource = "";
      InputInvoiceOrder orderInvoiceRelation =
          orderInvoiceRelationRepository.findById(id).orElse(null);
      if (orderInvoiceRelation!=null) {
        orderSource = orderInvoiceRelation.getOrderSource();
      }
      for (Entry<String, String> entry : noticeMap.entrySet()) {
        if (!messageParams.getMobileList().contains(entry.getValue())) {
          continue;
        }
        try {
          String userId = dingUtils.getUnionId(entry.getKey());
          // 发送钉钉待办
          DingTaskResultDTO dingTask = dingUtils.createDingTask(
              buildAccountOpenInvoiceDingTask(dingTalkToDoSubject, unionId,
                  Collections.singletonList(userId), UnicodeUtil.toUnicode(
                      orderOpenInvoiceH5Url + "?id=" + id + "&userName=" + entry.getValue() +
                          "&orderSource=" + Constants.COOPERATE_TYPE_LANDER, true),
                  CollUtil.join(invoiceNumbers, "、")), unionId);
          saveMessageInfo(BeanUtil.beanToMap(dingTask), id + "," + dingTask.getId(),
              Constants.SAVE_MESSAGE_TYPE_DING_TODO);
        } catch (Exception e) {
          log.error(ExceptionUtil.stacktraceToString(e, -1));
        }
      }
    }
  }

  @Transactional
  public void batchUnboundOrder(String orderInvoiceRelationId) {
    if (StrUtil.isBlank(orderInvoiceRelationId)) {
      return;
    }
    shareInputInvoiceService.deleteInputInvoiceLinkOrder(orderInvoiceRelationId);
  }


  private void unboundSupplierOrder(String orderInvoiceRelationId) {
    if (StrUtil.isBlank(orderInvoiceRelationId)) {
      return;
    }
    shareInputInvoiceService.deleteInputInvoiceLinkSupplierOrder(orderInvoiceRelationId);
  }


  @Transactional
  public void batchDeleteInvoice(String orderInvoiceRelationId) {
    if (StrUtil.isBlank(orderInvoiceRelationId)) {
      return;
    }
    shareInputInvoiceService.deleteInputInvoiceLinkInvoice(orderInvoiceRelationId);
  }

  @Override
  @Transactional
  public void deleteInvoice(String invoiceId) {
    if (StrUtil.isBlank(invoiceId)) {
      return;
    }
    // fixme 有问题的地方 ------
    Optional<OrderSupplierInvoice> invoice = orderSupplierInvoiceRepository.findById(invoiceId);
    String orderInvoiceRelationId = null;
    if (invoice.isPresent()) {
      OrderSupplierInvoice orderSupplierInvoice = invoice.get();
      orderInvoiceRelationId = orderSupplierInvoice.getOrderInvoiceRelationId();
      orderSupplierInvoice.setOrderInvoiceRelationId(null);
      orderSupplierInvoice.setState(Constants.STATE_DELETE);
      orderSupplierInvoiceRepository.save(orderSupplierInvoice);
      if (StrUtil.isBlank(orderInvoiceRelationId)) {
        return;
      }
      Optional<InputInvoiceOrder> orderInvoiceRelationOptional =
          orderInvoiceRelationRepository.findById(orderInvoiceRelationId);
      if (orderInvoiceRelationOptional.isPresent()) {
        InputInvoiceOrder orderInvoiceRelation = orderInvoiceRelationOptional.get();
        String invoiceNums = orderInvoiceRelation.getInvoiceNums();
        String invoiceNumJoin = deleteAElement(invoiceNums, orderSupplierInvoice.getInvoiceNum());
        orderInvoiceRelation.setInvoiceNums(invoiceNumJoin);
        orderInvoiceRelationRepository.save(orderInvoiceRelation);
      }
    }
    fileService.deleteFileByRelationIdAndType(invoiceId, Constants.FILE_TYPE_INVOICE);
  }

  @Override
  public PageResult<OrderInvoiceRelationDTO> getRelationPage(OrderInvoiceRelationPageQuery params) {
    // todo 优化分页查询 V6.3.0 p1上线后
    OrderInvoiceRelationPageQuery finalParams;
    String supplierId = params.getSupplierId();
    Supplier supplier = supplierService.get(supplierId,
        () -> CheckException.noFindException(Supplier.class, supplierId));
    String cooperateType = supplier.getCooperateType();
    if (StrUtil.isBlank(params.getOrderSource()) && !Objects.equals(Constants.COOPERATE_TYPE_ALL,
      cooperateType)) {
      params.setOrderSource(cooperateType);
    }
    if (!Objects.equals(params.getSchemeId(), Constants.SEARCH_TYPE_DEFAULT)) {
      String schemeId = params.getSchemeId();
      Optional<SearchScheme> repositoryById = searchSchemeRepository.findById(schemeId);
      if (!repositoryById.isPresent()) {
        throw new CheckException("无效的检索方案");
      }
      SearchScheme searchScheme = repositoryById.get();
      String content = searchScheme.getContent();
      finalParams =
          JSON.parseObject(content, new TypeReference<OrderInvoiceRelationPageQuery>() {});
      finalParams.setPageNo(params.getPageNo());
      finalParams.setPageSize(params.getPageSize());
      finalParams.setSupplierId(params.getSupplierId());
      finalParams.setUserId(params.getUserId());
      finalParams.setOrderSource(params.getOrderSource());
      finalParams.setSupplierOrderCode(params.getSupplierOrderCode());
    } else {
      finalParams = params;
    }
    Page<InputInvoiceOrder> page =
        orderInvoiceRelationDao.findPageByParams(finalParams.getOrderSource(),
            finalParams.getSupplierOrderCode(), finalParams.getOrderNo(),
            finalParams.getInvoiceNum(), finalParams.getPlatform(), finalParams.getInvoiceState(),
            finalParams.getSupplierId(), finalParams.getExcludeOffset(), finalParams.getPageNo(),
            finalParams.getPageSize());
    int totalPages = page.getTotalPages();
    List<OrderInvoiceRelationDTO> pageList = new ArrayList<>();
    if (finalParams.getPageNo() <= totalPages) {
      List<InputInvoiceOrder> relationList = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      List<String> orderIds = relationList.stream().map(InputInvoiceOrder::getOrderIdsList).flatMap(Collection::stream)
          .distinct()
          .collect(Collectors.toList());
      orderIds.add("-1");
      Map<String, Order> id2Order = orderRepository.findAllById(orderIds).stream()
          .collect(Collectors.toMap(Order::getId, Function.identity(), (k1, k2) -> k1));
      for (InputInvoiceOrder orderInvoiceRelation : relationList) {
        List<String> orderIdsList = orderInvoiceRelation.getOrderIdsList();
        List<Order> orders = orderIdsList.stream().map(id2Order::get).collect(Collectors.toList());
        OrderInvoiceRelationDTO dto = new OrderInvoiceRelationDTO(orderInvoiceRelation, orders);
        OrderPlatformDTO platformDTO =
            platformService.findByCode(orderInvoiceRelation.getPlatform());
        if (platformDTO != null) {
          dto.setPlatform(platformDTO.getPlatformName());
        }
        pageList.add(dto);
      }
    }
    return new PageResult<>(pageList, page.getTotalElements(), page.getTotalPages(),
        finalParams.getPageNo(), finalParams.getPageSize());
  }

  @Override
  public SupplierInvoiceDetailsDTO getRelationDataDetails(String relationDataId) {
    SupplierInvoiceDetailsDTO result = new SupplierInvoiceDetailsDTO();
    Optional<InputInvoiceOrder> orderInvoiceRelationOptional =
        orderInvoiceRelationRepository.findById(relationDataId);
    if (!orderInvoiceRelationOptional.isPresent()) {
      return result;
    }
    InputInvoiceOrder orderInvoiceRelation = orderInvoiceRelationOptional.get();
    List<String> orderIdsList = orderInvoiceRelation.getOrderIdsList();
    orderIdsList.add("-1");
    List<Order> orders = orderRepository.findAllById(orderIdsList);
    ArrayList<OrderAmount> orderAmounts = new ArrayList<>();
    for (Order order : CollUtil.emptyIfNull(orders)) {
      OrderAmount orderAmount = orderDetailService.getOrderAmount(order.getId());
      orderAmount.setOrder(order);
      String acceptState = orderAcceptService.getAcceptState(order.getId());
      orderAmount.setOrderAcceptState(Constants_order.SIGN_VOUCHER_MAP.get(acceptState));
      orderAmounts.add(orderAmount);
    }
    List<OrderSupplierInvoice> invoices = shareInputInvoiceService.getOrderSupplierInvoiceDistinct(orderInvoiceRelation.getId());
    result.setDataState(orderInvoiceRelation.getInvoiceState());
    result.setInvoiceTotalAmountIncludingTax(
        NumberUtil.null2Zero(orderInvoiceRelation.getInvoiceAmount()).toPlainString());
    result.setOrderTotalAmountIncludingTax(
        NumberUtil.null2Zero(orderInvoiceRelation.getOrderAmount()).toPlainString());
    result.setInvoices(SupplierInvoiceDetailsInvoiceDTO.buildList(invoices));
    //发票附件
    for (SupplierInvoiceDetailsInvoiceDTO supplierInvoiceDetailsInvoiceDTO : result.getInvoices()) {
      String relationId = supplierInvoiceDetailsInvoiceDTO.getId();
      Optional<File> file = fileRepository.findFirstByRelationIdAndRelationTypeAndState(relationId,
          Constants.FILE_TYPE_INVOICE, Constants.STATE_OK);
      file.ifPresent(value -> supplierInvoiceDetailsInvoiceDTO.setFile(new FileDTO(value)));
    }
    result.setOrders(SupplierInvoiceDetailsOrderDTO.buildList(orderAmounts));
    result.setRejection(orderInvoiceRelation.getRejection());
    result.setLogisticsCompany(orderInvoiceRelation.getLogisticsCompany());
    result.setLogisticsNum(orderInvoiceRelation.getLogisticsNum());
    return result;
  }

  @Override
  public SupplierOpenInvoiceDetailsDTO getSupplierInvoiceRelationDataDetails(String relationDataId) {
    SupplierOpenInvoiceDetailsDTO result = new SupplierOpenInvoiceDetailsDTO();
    Optional<InputInvoiceOrder> orderInvoiceRelationOptional =
        orderInvoiceRelationRepository.findById(relationDataId);
    if (!orderInvoiceRelationOptional.isPresent()) {
      return result;
    }
    InputInvoiceOrder orderInvoiceRelation = orderInvoiceRelationOptional.get();
    List<String> orderCodesList = orderInvoiceRelation.getOrderCodesList();
    orderCodesList.add("-1");
    List<SupplierOrder> orders =
        supplierOrderRepository.findAllByCodeInAndState(orderCodesList, Constants.STATE_OK);
    List<OrderSupplierInvoice> invoices = shareInputInvoiceService.getOrderSupplierInvoiceDistinct(orderInvoiceRelation.getId());
    result.setDataState(orderInvoiceRelation.getInvoiceState());
    result.setInvoiceTotalAmountIncludingTax(
        NumberUtil.null2Zero(orderInvoiceRelation.getInvoiceAmount()).toPlainString());
    result.setOrderTotalAmountIncludingTax(
        NumberUtil.null2Zero(orderInvoiceRelation.getOrderAmount()).toPlainString());
    result.setInvoices(SupplierOpenInvoiceDetailsInvoiceDTO.buildList(invoices));
    //发票附件
    for (SupplierOpenInvoiceDetailsInvoiceDTO supplierInvoiceDetailsInvoiceDTO :
        result.getInvoices()) {
      String relationId = supplierInvoiceDetailsInvoiceDTO.getId();
      Optional<File> file = fileRepository.findFirstByRelationIdAndRelationTypeAndState(relationId,
          Constants.FILE_TYPE_INVOICE, Constants.STATE_OK);
      file.ifPresent(value -> supplierInvoiceDetailsInvoiceDTO.setFile(new FileDTO(value)));
    }
    result.setOrders(SupplierOpenInvoiceDetailsOrderDTO.buildList(orders));
    for (SupplierOpenInvoiceDetailsOrderDTO order : result.getOrders()) {
      order.setNumber(BigDecimalUtil.formatForStandard(
          supplierOrderDetailService.countSettleQtyBySupplierOrderId(order.getId())));
    }
    result.setRejection(orderInvoiceRelation.getRejection());
    result.setLogisticsCompany(orderInvoiceRelation.getLogisticsCompany());
    result.setLogisticsNum(orderInvoiceRelation.getLogisticsNum());
    return result;
  }

  @Override
  public InvoiceIdentifyResult getVerificationResult(InvoiceResultParam invoiceParam) {
    InvoiceVerificationInfo result = new InvoiceVerificationInfo();
    //是否之前有过验真记录
    Optional<InvoiceVerification> invoiceVerificationOptional =
        supplierInvoiceService.getInvoiceVerification(invoiceParam.getInvoiceType(),
            invoiceParam.getInvoiceNum(), invoiceParam.getInvoiceCode(),
            invoiceParam.getInvoiceTime(), invoiceParam.getTotalAmount(),
            invoiceParam.getTotalTaxAmount(), invoiceParam.getTotalAmountIncludingTax(),
            invoiceParam.getCheckCode(), Constants.YES);
    if (invoiceVerificationOptional.isPresent()) {
      InvoiceVerification invoiceVerification = invoiceVerificationOptional.get();
      String invoiceType = invoiceVerification.getInvoiceType();
      String dataJson = invoiceVerification.getDataJson();
      if (InvoiceTypeEnum.BLOCKCHAIN_ELECTRONIC_INVOICE.getKey().equals(invoiceType)
          || InvoiceTypeEnum.OTHER.getKey().equals(invoiceType)) {
        return JSON.parseObject(dataJson, new TypeReference<OtherInvoiceIdentifyResult>() {});
      } else {
        return JSON.parseObject(dataJson, new TypeReference<InvoiceVerificationInfo>() {});
      }
    }
    return result;
  }

  @Override
  @Transactional
  public Boolean deleteInvoiceOrder(String invoiceOrderId) {
    Optional<InputInvoiceOrder> invoiceRelationOptional =
        orderInvoiceRelationRepository.findById(invoiceOrderId);
    if (!invoiceRelationOptional.isPresent()) {
      throw new CheckException("参数异常");
    }
    InputInvoiceOrder orderInvoiceRelation = invoiceRelationOptional.get();
    if (!Constants.ORDER_INVOICE_STATE_TEMP.equals(orderInvoiceRelation.getInvoiceState()) &&
        !Constants.ORDER_INVOICE_STATE_REJECT.equals(orderInvoiceRelation.getInvoiceState())) {
      throw new CheckException("只有暂存或驳回的发票单可以删除");
    }
    String id = orderInvoiceRelation.getId();
    shareInputInvoiceService.deleteInputInvoiceOrder(id);
    return true;
  }

  @Override
  public boolean saveLogistics(SaveLogisticsParam saveLogisticsParam) {
    String invoiceOrderId = saveLogisticsParam.getInvoiceOrderId();
    Optional<InputInvoiceOrder> invoiceRelationOptional =
        orderInvoiceRelationRepository.findById(invoiceOrderId);
    if (!invoiceRelationOptional.isPresent()) {
      throw new CheckException("参数不合法");
    }
    InputInvoiceOrder orderInvoiceRelation = invoiceRelationOptional.get();
    orderInvoiceRelation.setLogisticsCompany(saveLogisticsParam.getLogisticsCompany());
    orderInvoiceRelation.setLogisticsNum(saveLogisticsParam.getLogisticsNum());
    orderInvoiceRelationRepository.save(orderInvoiceRelation);
    return true;
  }

  @Override
  @SneakyThrows
  public byte[] exportDetail(List<String> orderIds) {
    List<OrderDetailDTO> list = new ArrayList<>();
    for (String orderId : orderIds) {
        List<OrderDetailDTO> detailListByOrderIdIn = orderDetailDao.getDetailListByOrderId(orderId);
        if(CollUtil.isEmpty(detailListByOrderIdIn)){
          throw new CheckException("订单信息有误！！");
        }
        list.addAll(detailListByOrderIdIn);
      }
    try ( InputStream inputStream = downloadThenUpUtil.getInputStreamFromOSS(INVOICABLE_ORDERS);
        Workbook book = ExcelUtil.buildByFile("可开票订单明细导出模板.xlsx", inputStream)){
      Sheet sheet = book.getSheetAt(0);
      Row title = sheet.createRow(1);
      CellStyle baseStyle = exportUtil.getBaseStyle(book);
      CellStyle titleStyle = exportUtil.getTitleStyle(book);
      exportUtil.createCell(title, 0, "客户订单号", titleStyle);
      exportUtil.createCell(title, 1, "订单结算金额", titleStyle);
      exportUtil.createCell(title, 2, "客户名称", titleStyle);
      exportUtil.createCell(title, 3, "收件人", titleStyle);
      exportUtil.createCell(title, 4, "下单平台", titleStyle);
      exportUtil.createCell(title, 5, "品牌", titleStyle);
      exportUtil.createCell(title, 6, "商品名称", titleStyle);
      exportUtil.createCell(title, 7, "规格型号", titleStyle);
      exportUtil.createCell(title, 8, "单位", titleStyle);
      exportUtil.createCell(title, 9, "数量", titleStyle);
      exportUtil.createCell(title, 10, "退货数量", titleStyle);
      exportUtil.createCell(title, 11, "结算数量", titleStyle);
      exportUtil.createCell(title, 12, "含税单价", titleStyle);
      exportUtil.createCell(title, 13, "成本价税率", titleStyle);
      exportUtil.createCell(title, 14, "去税单价", titleStyle);
      exportUtil.createCell(title, 15, "合计金额", titleStyle);
      exportUtil.createCell(title, 16, "合计税额", titleStyle);
      exportUtil.createCell(title, 17, "价税合计", titleStyle);
      int startRow = 2;
      for (OrderDetailDTO orderDetailDTO : list) {
        String name = platformService.findNameByCode(orderDetailDTO.getType());
        Row row = sheet.createRow(startRow);
        exportUtil.createCell(row, 0, StringUtils.emptyIfNull(orderDetailDTO.getOrderNo()), baseStyle);
        exportUtil.createCell(row, 1, orderDetailDTO.getSettlementPrice(),
            baseStyle);
        exportUtil.createCell(row, 2, orderDetailDTO.getCustomer(),
            baseStyle);
        exportUtil.createCell(row, 3, orderDetailDTO.getConsignee(),
            baseStyle);
        exportUtil.createCell(row, 4, name,
            baseStyle);
        exportUtil.createCell(row, 5, orderDetailDTO.getBrand(),
            baseStyle);
        exportUtil.createCell(row, 6, orderDetailDTO.getProductName(),
            baseStyle);
        exportUtil.createCell(row, 7, orderDetailDTO.getModel(),
            baseStyle);
        exportUtil.createCell(row, 8, orderDetailDTO.getUnit(),
            baseStyle);
        exportUtil.createCell(row, 9, orderDetailDTO.getNum()!=null ?
                CommonlyUseUtil.BigDecimalValue(orderDetailDTO.getNum().toPlainString()) : "0",
            baseStyle);
        exportUtil.createCell(row, 10, orderDetailDTO.getReturnNum() !=null ?
                CommonlyUseUtil.BigDecimalValue(orderDetailDTO.getReturnNum().toPlainString()) : "0",
            baseStyle);
        exportUtil.createCell(row, 11, orderDetailDTO.getSettlementNum() !=null ?
                CommonlyUseUtil.BigDecimalValue(orderDetailDTO.getSettlementNum().toPlainString()) : "0",
            baseStyle);
        exportUtil.createCell(row, 12, orderDetailDTO.getPrice() !=null ?
                CommonlyUseUtil.BigDecimalValue(orderDetailDTO.getPrice().toPlainString()) : "0",
            baseStyle);
        exportUtil.createCell(row, 13, StrUtil.isNotEmpty(orderDetailDTO.getCostPriceTaxRate()) ?
                orderDetailDTO.getCostPriceTaxRate() : "0",
            baseStyle);
        exportUtil.createCell(row, 14, orderDetailDTO.getTaxFreeCbPrice() !=null ?
                CommonlyUseUtil.BigDecimalValue(orderDetailDTO.getTaxFreeCbPrice().toPlainString()) : "0",
            baseStyle);
        exportUtil.createCell(row, 15, orderDetailDTO.getTotalAmount() !=null ?
                CommonlyUseUtil.BigDecimalValue(orderDetailDTO.getTotalAmount().toPlainString()) : "0",
            baseStyle);
        exportUtil.createCell(row, 16, orderDetailDTO.getTotalTaxAmount() !=null ?
                CommonlyUseUtil.BigDecimalValue(orderDetailDTO.getTotalTaxAmount().toPlainString()) : "0",
            baseStyle);
        exportUtil.createCell(row, 17, orderDetailDTO.getTotalAmountIncludingTax() !=null ?
                CommonlyUseUtil.BigDecimalValue(orderDetailDTO.getTotalAmountIncludingTax().toPlainString()) : "0",
            baseStyle);
        startRow += 1;
      }
      ByteArrayOutputStream out = new ByteArrayOutputStream();
      exportUtil.write(out, book);
      return out.toByteArray();
    }
  }

  private String getHandleMan(List<Order> orders) {
    if (CollUtil.isEmpty(orders)) {
      return "-";
    }
    Boolean newOrder = isNewOrder(orders);
    if (Boolean.TRUE.equals(newOrder)) {
      // 签名抬头为总部
      if (TitleOfTheContractEnum.TITLE_OF_HEADQUARTERS.getCode().equals(orders.get(0).getTitleOfTheContract())) {
        return "况容";
      }
      return "王亚";
    }
    Order order = orders.get(0);
    String type = order.getType();
    String supplierId = order.getSupplierId();
    SupplierPerformance supplierPerformance =
        supplierPerformanceDao.getBySupplierIdAndPlatformCode(supplierId, type);
    if (supplierPerformance == null) {
      return "-";
    }
    String dockingPurchaseErpCode = supplierPerformance.getDockingPurchaseErpCode();
    User user = userDao.getUserByCode(dockingPurchaseErpCode);
    if (user == null) {
      return "-";
    }
    return user.getRealName();
  }
  /**
   * 应处理人为所选订单的负责采购如果有多个采购时，取出现次数最多的一个
   * 如果出现次数一致，取最新一次订单的采购员
   * @param orders 供应商订单
   * @return
   */
  private User getSupplierInvoiceHandleMan(List<SupplierOrder> orders) {
    Assert.notEmpty(orders);
    User user = null;
    if (orders.size() == 1) {
      String purchaseCode = orders.get(0).getPurchaseCode();
      if (StrUtil.isBlank(purchaseCode)) {
        throw new CheckException("采购订单未获取到采购员，请联系管理员维护！");
      }
      user = userDao.getUserByCode(purchaseCode);
    }
    if (orders.size() > 1) {
      Map<String, List<SupplierOrder>> map =
          orders.stream().collect(Collectors.groupingBy(SupplierOrder::getPurchaseCode));
      int maxLength = 0;
      List<String> maxKeys = new ArrayList<>();
      for (Map.Entry<String, List<SupplierOrder>> entry : map.entrySet()) {
        int length = entry.getValue().size();
        if (length > maxLength) {
          maxLength = length;
          maxKeys.clear(); // 清空之前的最大key列表
          maxKeys.add(entry.getKey());
        } else if (length == maxLength) {
          //并列第一
          maxKeys.add(entry.getKey());
        }
      }
      //没有并列第一
      if (maxKeys.size() == 1) {
        user = userDao.getUserByCode(maxKeys.get(0));
      }
      if (maxKeys.size() > 1) {
        Long createTime = 0L;
        String finalMaxKey = null;
        for (String maxKey : maxKeys) {
          List<SupplierOrder> supplierOrders = map.get(maxKey);
          supplierOrders.sort(Comparator.comparing(SupplierOrder::getCreateTime).reversed());
          SupplierOrder supplierOrder = supplierOrders.get(0);
          if (supplierOrder.getCreateTime() > createTime) {
            createTime = supplierOrder.getCreateTime();
            finalMaxKey = maxKey;
          }
        }
        if (StrUtil.isNotBlank(finalMaxKey)) {
          user = userDao.getUserByCode(finalMaxKey);
        }
      }
    }
    if (user == null) {
      throw new CheckException("应处理人数据异常，请联系管理员");
    }
    return user;
  }

  /**
   * 应处理人为所选订单对应采购部门的进项票应处理人（默认取第一个订单）
   * @param orders 供应商订单
   * @return
   */
  private String getSupplierInvoiceOperator(List<SupplierOrder> orders) {
    //取第一个订单对应采购部门的进项票应处理人
    Assert.notEmpty(orders);
    String purchaseDeptCode = orders.get(0).getPurchaseDeptCode();
    if (StrUtil.isEmpty(purchaseDeptCode)) {
      throw new CheckException("采购订单未获取到采购部门编码，请联系管理员维护！");
    }
    Group group = groupRepository.findFirstByErpCodeAndState(purchaseDeptCode, Constants.STATE_OK);
    String operator = group.getOperator();
    if (StrUtil.isEmpty(operator)) {
      throw new CheckException("进项票应处理人为空，请联系管理员维护！");
    }
    return operator;
  }


  private List<Order> getOrders(List<String> orderIds) {
    return orderIds.stream().map(id -> {
      return orderService.get(id, () -> CheckException.noFindException(Order.class, id));
    }).collect(Collectors.toList());
  }

  private void checkOrder(List<Order> orders, String dateState) {
    for (Order order : orders) {
      //校验数据，防止重复.
      if (StrUtil.isNotBlank(order.getOrderInvoiceRelationId()) && Objects.equals(
          dateState, Constants.ORDER_INVOICE_STATE_ING) && (!Objects.equals(
          order.getSupplierOpenInvoiceStatus(), Constants.ORDER_INVOICE_STATE_REJECT) && !Objects.equals(
          order.getSupplierOpenInvoiceStatus(), Constants.ORDER_INVOICE_STATE_TEMP))) {
        throw new CheckException(
            "包含已经有开票信息的订单，订单号：" + order.getOrderNo() + "，请刷新列表后重试。");
      }
    }
  }


  private BigDecimal getOrdersAmount(List<Order> orders) {
    //取所有订单的最终结算金额
    return orders.stream().map(order -> {
      return NumberUtil.sub(order.getPrice(), order.getRefundPrice(), order.getCancelPrice());
    }).reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  private BigDecimal getSupplierOrdersAmount(List<SupplierOrder> orders) {
    //取所有订单的最终结算金额
    return orders.stream().map(SupplierOrder :: getFinalPrice).filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  /**
   * 删除字符串中的子字符串
   * 支持的字符串：****\/**** 以/为分割的字符串数组
   *
   * @return 删除后的字符串
   */
  private String deleteAElement(String str, String subStr) {
    String[] split = StrUtil.split(str, "/");
    HashSet<String> invoiceNumSet = new HashSet<>(Arrays.asList(split));
    invoiceNumSet.remove(subStr);
    return CollUtil.join(invoiceNumSet, "/");
  }

  public void deleteOrderAccountInvoiceRelevanceData(List<String> fileIds,
      List<String> orderAccountInvoiceIds) {
    deleteInvoiceFile(fileIds);
    deleteInvoice(orderAccountInvoiceIds);
  }

  /**
   * 删除发票附件
   */
  private void deleteInvoiceFile(List<String> fileIds) {
    if (CollUtil.isEmpty(fileIds)) {
      return;
    }
    fileIds.forEach(id -> {
      fileDao.delete(id);
    });
  }

  /**
   * 删除发票
   */
  private void deleteInvoice(List<String> orderAccountInvoiceIds) {
    if (CollUtil.isEmpty(orderAccountInvoiceIds)) {
      return;
    }
    orderAccountInvoiceIds.forEach(id -> {
      orderAccountInvoiceService.delete(id);
    });
  }



  private DingTaskDTO buildAccountOpenInvoiceDingTask(String supplierName,
      String unionId,List<String> executorIds,String url,String invoiceNos,
      String logisticsInfo){
    DingTaskDTO dingTaskParams = new DingTaskDTO();
    String userId = dingUtils.getUnionId(unionId);
    dingTaskParams.setSubject("落地商【"+supplierName+"】开的进项票，请线下收票后确认");
    dingTaskParams.setCreatorId(userId);
    dingTaskParams.setDescription(StrUtil.EMPTY);
    dingTaskParams.setDueTime(DateUtil.offsetDay(DateTime.of(System.currentTimeMillis()),5).getTime());
    dingTaskParams.setExecutorIds(executorIds);
    dingTaskParams.setParticipantIds(executorIds);
    DetailUrlDTO detailUrl = new DetailUrlDTO();
    detailUrl.setAppUrl(url);
    detailUrl.setPcUrl(url);
    dingTaskParams.setDetailUrl(detailUrl);
    dingTaskParams.setIsOnlyShowExecutor(true);
    dingTaskParams.setPriority(20);
    NotifyConfigsDTO notifyConfigs = new NotifyConfigsDTO();
    notifyConfigs.setDingNotify("1");
    dingTaskParams.setNotifyConfigs(notifyConfigs);
    FiledKeyAndValueDTO filedKeyAndValueDTO = new FiledKeyAndValueDTO();
    filedKeyAndValueDTO.setFieldKey("发票号");
    filedKeyAndValueDTO.setFieldValue(invoiceNos);
    FiledKeyAndValueDTO filedKeyAndValueDTO1 = new FiledKeyAndValueDTO();
    filedKeyAndValueDTO1.setFieldKey("物流信息");
    filedKeyAndValueDTO1.setFieldValue(logisticsInfo);
    dingTaskParams.setContentFieldList(ListUtil.toList(filedKeyAndValueDTO,filedKeyAndValueDTO1));
    return dingTaskParams;
  }





  private DingTaskDTO buildAccountOpenInvoiceDingTask(String Subject,
      String unionId,List<String> executorIds,String url,String invoiceNos){
    DingTaskDTO dingTaskParams = new DingTaskDTO();
    String userId = dingUtils.getUnionId(unionId);
    dingTaskParams.setSubject(Subject);
    dingTaskParams.setCreatorId(userId);
    dingTaskParams.setDescription(StrUtil.EMPTY);
    dingTaskParams.setDueTime(DateUtil.offsetDay(DateTime.of(System.currentTimeMillis()),5).getTime());
    dingTaskParams.setExecutorIds(executorIds);
    dingTaskParams.setParticipantIds(executorIds);
    DetailUrlDTO detailUrl = new DetailUrlDTO();
    detailUrl.setAppUrl(url);
    detailUrl.setPcUrl(url);
    dingTaskParams.setDetailUrl(detailUrl);
    dingTaskParams.setIsOnlyShowExecutor(true);
    dingTaskParams.setPriority(20);
    NotifyConfigsDTO notifyConfigs = new NotifyConfigsDTO();
    notifyConfigs.setDingNotify("1");
    dingTaskParams.setNotifyConfigs(notifyConfigs);
    FiledKeyAndValueDTO filedKeyAndValueDTO = new FiledKeyAndValueDTO();
    filedKeyAndValueDTO.setFieldKey("发票号");
    filedKeyAndValueDTO.setFieldValue(invoiceNos);
    dingTaskParams.setContentFieldList(ListUtil.toList(filedKeyAndValueDTO));
    return dingTaskParams;
  }


  private List<TableMetaDTO> buildTableMeta() {
    return ListUtil.toList(new TableMetaDTO("发票号", "STRING", "invoiceNo", 30),
        new TableMetaDTO("含税金额", "STRING", "price", 30),
        new TableMetaDTO("发票日期", "STRING", "invoiceDate", 40));
  }

  /**
   * 保存对账单开票信息
   */
  private void saveMessageInfo(Map<String, Object> messageInfo, String relevanceId, String type) {
    orderAccountInvoiceDingTalkMessage.saveMessageInfo(messageInfo, relevanceId, type);
  }

  /**
   * 发送对账单开票钉钉消息
   */
  private Map<String, Object> sendDingTalkMessageForOrderAccountInvoice(
      OrderAccountInvoiceDingTalkMessageParams messageParams, String orderAccountId,
      boolean sendOldDingMsg, boolean isSupplierOpenInvoice) {
    return orderAccountInvoiceDingTalkMessage.sendMessage(messageParams, orderAccountId,
        sendOldDingMsg, isSupplierOpenInvoice);
  }

  /**
   * 根据订单信息获取对应采购 - （供应商履约信息对应的下单平台的采购)
   *
   * @param order 订单对象
   * @return 采购人用户对象集合
   */
  private User getPurchaseByInvoice(Order order) {
    //获取对应采购
    if (order == null || order.getSupplier() == null || ObjectUtil.isEmpty(order.getType())) {
      return null;
    }

    String supplierId = order.getSupplier().getId();
    //下单平台
    String platform = order.getType();
    SupplierPerformance supplierPerformance =
        supplierPerformanceDao.getBySupplierIdAndPlatformCode(supplierId, platform);
    if (supplierPerformance == null) {
      return null;
    }
    //采购人编码
    String dockingPurchaseErpCode = supplierPerformance.getDockingPurchaseErpCode();
    if(StringUtils.isNullOrEmpty(dockingPurchaseErpCode)){
      throw new CheckException(order.getSupplier().getEnterpriseName()+"供应商对应"+supplierPerformance.getPlatformName()+"平台的对接采购为空！");
    }
    return userDao.getUserByCode(dockingPurchaseErpCode);
  }

  /**
   * 新增对账单发送消息
   */
  private void sendDingTalkMessageForADD(OrderAccountDingTalkMessageParams messageParams) {
    this.orderAccountDingTalkMessage.sendMessageForAdd(messageParams);
  }

  /**
   * 修改对账单发送消息
   */
  private void sendDingTalkMessageForUpdate(OrderAccountDingTalkMessageParams messageParams) {
    this.orderAccountDingTalkMessage.sendMessageForUpdate(messageParams);
  }

  public void deleteOrderAccountRelevanceData(String orderAccountId) {
    //删除关联表
    List<OrderAccountToOrder> orderAccountToOrders =
        orderAccountToOrderRepository.getAllByOrderAccountIdAndState(orderAccountId,
            Constants.STATE_OK);
    CollUtil.emptyIfNull(orderAccountToOrders).forEach(orderAccountToOrder -> {
      orderAccountToOrder.setState(Constants.STATE_DELETE);
      orderAccountToOrderRepository.save(orderAccountToOrder);
      Order order = orderDao.get(orderAccountToOrder.getOrderId());
      if (order == null) {
        throw CheckException.noFindException(Order.class, orderAccountToOrder.getOrderId());
      }
      order.setAccountNo(null);
      order.setAccountStatus(Constants_order.ORDER_ACCOUNT_STATUS_ALLOW);
      orderDao.save(order);
    });
  }

  @Override
  @SneakyThrows
  public byte[] exportSupplierOrderDetail(List<String> orderIds) {
    LinkedList<ExportSupplierOrderDetailDTO> exportSupplierOrderDetailDTOs = new LinkedList<>();
    for (String orderId : orderIds) {
      SupplierOrder supplierOrder = supplierOrderService.get(orderId,
          () -> CheckException.noFindException(SupplierOrder.class, orderId));
      SupplierOrderToForm supplierOrderToForm =
          supplierOrderToFormService.getDetailedBySupplierOrderId(orderId);
      if (supplierOrderToForm == null) {
        throw new CheckException(orderId + "订单异常，请联系管理员！");
      }
      List<SupplierOrderDetail> currentSupplierOrderDetails =
          supplierOrderDetailService.getByOrderToFormId(supplierOrderToForm.getId());
      if (CollUtil.isEmpty(currentSupplierOrderDetails)) {
        throw new CheckException(orderId + "订单异常，请联系管理员！");
      }
      ExportSupplierOrderDetailDTO exportSupplierOrderDetailDTO =
          new ExportSupplierOrderDetailDTO(currentSupplierOrderDetails, supplierOrder);
      exportSupplierOrderDetailDTOs.add(exportSupplierOrderDetailDTO);
    }
    String file_name = "供应商订单开票明细导出.xlsx";
    try (  InputStream inputStream = downloadThenUpUtil.getInputStreamFromOSS(SUPPLIER_INVOICE_ORDERS);
        Workbook book = ExcelUtil.buildByFile(file_name, inputStream);
    ){
      Sheet sheet = book.getSheetAt(0);
      Row title = sheet.createRow(1);
      CellStyle baseStyle = exportUtil.getBaseStyle(book);
      CellStyle titleStyle = exportUtil.getTitleStyle(book);
      exportUtil.createCell(title, 0, "采购订单号", titleStyle);
      exportUtil.createCell(title, 1, "订单结算金额", titleStyle);
      exportUtil.createCell(title, 2, "收件人", titleStyle);
      exportUtil.createCell(title, 3, "商品名称", titleStyle);
      exportUtil.createCell(title, 4, "规格型号", titleStyle);
      exportUtil.createCell(title, 5, "描述", titleStyle);
      exportUtil.createCell(title, 6, "单位", titleStyle);
      exportUtil.createCell(title, 7, "实际结算数量", titleStyle);
      exportUtil.createCell(title, 8, "单价", titleStyle);
      int startRow = 2;
      for (ExportSupplierOrderDetailDTO detail : exportSupplierOrderDetailDTOs) {
        SupplierOrder supplierOrder = detail.getSupplierOrder();
        List<SupplierOrderDetail> supplierOrderDetails = detail.getSupplierOrderDetails();
        for (SupplierOrderDetail supplierOrderDetail : supplierOrderDetails) {
          Row row = sheet.createRow(startRow);
          exportUtil.createCell(row, 0, StrUtil.emptyIfNull(supplierOrder.getCode()), baseStyle);
          exportUtil.createCell(row, 1, Objects.nonNull(supplierOrder.getFinalPrice()) ?
                  BigDecimalUtil.formatForStandard(supplierOrder.getFinalPrice()).toPlainString() : "",
              baseStyle);
          exportUtil.createCell(row, 2, StrUtil.emptyIfNull(supplierOrder.getReceiveMan()),
              baseStyle);
          if (supplierOrderDetail.getSupplierOrderProduct() == null) {
            throw new CheckException(supplierOrder.getCode() + "订单异常，请联系管理员！");
          }
          exportUtil.createCell(row, 3,
              StrUtil.emptyIfNull(supplierOrderDetail.getSupplierOrderProduct().getName()),
              baseStyle);
          exportUtil.createCell(row, 4,
              StrUtil.emptyIfNull(supplierOrderDetail.getSupplierOrderProduct().getManuCode()),
              baseStyle);
          exportUtil.createCell(row, 5, StrUtil.emptyIfNull(supplierOrderDetail.getDescription()),
              baseStyle);
          exportUtil.createCell(row, 6,
              StrUtil.emptyIfNull(supplierOrderDetail.getSupplierOrderProduct().getUnit()), baseStyle);
          exportUtil.createCell(row, 7, Objects.nonNull(supplierOrderDetail.getSettleQty()) ?
              BigDecimalUtil.formatForStandard(supplierOrderDetail.getSettleQty()).toPlainString() :
              "", baseStyle);
          exportUtil.createCell(row, 8, Objects.nonNull(supplierOrderDetail.getPrice()) ?
              BigDecimalUtil.formatForStandard(supplierOrderDetail.getPrice()).toPlainString() : "", baseStyle);
          startRow += 1;
        }
      }
      ByteArrayOutputStream out = new ByteArrayOutputStream();
      exportUtil.write(out, book);
      return out.toByteArray();
    }
  }



  /**
   * 根据订单id获取客户订单号
   */
  private List<String> getOrderNumber(final List<String> orderId) {
    ArrayList<String> orderNumbers = new ArrayList<>();
    for (String id : orderId) {
      String orderNumber = getOrderNumber(id);
      orderNumbers.add(orderNumber);
    }
    return orderNumbers.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
  }

  /**
   * 根据订单id获取客户订单号
   */
  private String getOrderNumber(final String orderId) {
    Order order = orderDao.get(orderId);
    if (order == null || StrUtil.isBlank(order.getOrderNo())) {
      return "";
    }
    return order.getOrderNo();
  }

  private void checkSupplierOrder(List<SupplierOrder> orders,List<String> excludeOrderCodes) {
    // 校验数据，防止订单重复关联发票单
    for (SupplierOrder order : orders) {
      // 排除需要过滤的订单
      if(CollUtil.contains(excludeOrderCodes,order.getCode())){
        continue;
      }
      if (StrUtil.isNotBlank(order.getOrderInvoiceRelationId())
          && !Objects.equals(order.getSupplierOpenInvoiceState(), Constants.ORDER_INVOICE_STATE_NOT_DONE)) {
        throw new CheckException("包含已经有开票信息的订单，采购单号：" + order.getCode() + "，请刷新列表后重试。");
      }
    }
  }
}
