package com.xhgj.srm.api.dto.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("对账单参数，用于暂存,修改,提交开票")
@NoArgsConstructor
public class AccountParamDTO {

    @ApiModelProperty("对账单id(修改传入)")
    private String id;

    @ApiModelProperty(value = "供应商id", required = true)
    @NotBlank(message = "供应商id不能为空！")
    private String supplierId;

    @ApiModelProperty(value = "提交类型(1--提交开票,2--暂存)", required = true)
    @NotBlank(message = "提交类型不能为空！")
    private String type;

    @ApiModelProperty("订单明细")
    @NotNull(message = "订单明细不能为空")
    private List<OrderParamDTO> orderList;

    @ApiModelProperty("发票信息")
    private List<AddInvoiceInfoParams> addInvoiceInfoParams;
}
