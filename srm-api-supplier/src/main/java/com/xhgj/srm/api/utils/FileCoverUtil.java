package com.xhgj.srm.api.utils;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.GenericResult;
import com.aliyun.oss.model.ProcessObjectRequest;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Component
public class FileCoverUtil {

    private final OSS ossClient;

    private final String bucketName;

    private final String bucketNameFtp;




    public FileCoverUtil(OSS ossClient, SrmConfig srmConfig) {
        this.ossClient = ossClient;
        this.bucketName = srmConfig.getBucketName();
        this.bucketNameFtp = srmConfig.getBucketNameFtp();
    }



    /**
     * 批量处理oss上图片 加水印
     *
     * @param sourceImagePath 原图路径
     * @param sourceImageNames 原图名称集合
     * @param newImagePath 新路径
     */
    public List<String> geTargetPicUrls(
            String sourceImagePath,
            List<String> sourceImageNames,
            String newImagePath) {
        if (StrUtil.isEmpty(sourceImagePath)) {
            return null;
        }
        if (StrUtil.isEmpty(newImagePath)) {
            return null;
        }
        if (CollectionUtils.isEmpty(sourceImageNames)) {
            return null;
        }
        List<String> returnPicUrls = new ArrayList<String>();
        for (String sourceImageName : sourceImageNames) {
            if (StrUtil.isEmpty(sourceImageName)) {
                continue;
            }
            String pic =
                    copyPic(
                            sourceImagePath + sourceImageName,
                            newImagePath + sourceImageName);
            returnPicUrls.add("/" + pic);

        }
        return returnPicUrls;
    }


    /**
     * 对客户空间上传的图片加水印 并存储至商城空间
     *
     * @param sourceImage 原图路径 ，例：0000/20210122/BD0900161/z1.jpg
     * @param newImage 目标图路径，例：productCode/z1.jpg
     * @return 如果操作成功，返回目标图路径，如：productCode/z1.jpg；如果操作失败，返回 null
     */
    public String copyPic(String sourceImage, String newImage) {
        try {
            // 原图存在于客户空间，故取 bucketNameFtp
            addTheTargetPic(sourceImage, newImage, bucketNameFtp, bucketName);
            return newImage;
        } catch (Exception e) {
            log.error("put Object file to OSS {0} failed,", e);
            log.error("sourceImage【" + sourceImage + "】");
            log.error("targetPicImage【" + newImage + "】");
            return null;
        }
    }


    /**
     * 图片加水印
     *
     * @param originalPicUrl 原图路径，例：mdm/temp/upload/productCode/z1.jpg
     * @param targetUrl 目标图路径，例：watermark/productCode/z1.jpg
     * @param originalPicBucket 原图所在 bucket
     * @param targetPicBucket 目标图所在 bucket
     */
    @SneakyThrows
    private void addTheTargetPic(
            String originalPicUrl,
            String targetUrl,
            String originalPicBucket,
            String targetPicBucket) {
        if (com.aliyun.oss.common.utils.StringUtils.isNullOrEmpty(originalPicBucket)) {
            throw new RuntimeException("originalPicBucket 必填！");
        }
        if (com.aliyun.oss.common.utils.StringUtils.isNullOrEmpty(targetPicBucket)) {
            throw new RuntimeException("targetPicBucket 必填！");
        }
        // OSS 路径不允许以 / 开头
        originalPicUrl = StrUtil.removePrefix(originalPicUrl, "/");
        targetUrl = StrUtil.removePrefix(targetUrl, "/");
        StringBuilder sb = new StringBuilder();
        String style = "image/rotate,%s|sys/saveas,o_%s,b_%s";
        String rotate=360+"";
        try (Formatter formatter = new Formatter(sb)) {
            formatter.format(
                    style, rotate,
                    BinaryUtil.toBase64String(targetUrl.getBytes(StandardCharsets.UTF_8)),
                    BinaryUtil.toBase64String(targetPicBucket.getBytes(StandardCharsets.UTF_8)));
        }
        ProcessObjectRequest request =
                new ProcessObjectRequest(originalPicBucket, originalPicUrl, sb.toString());
        try {
            GenericResult result = ossClient.processObject(request);
            result.getResponse().getContent().close();
        } catch (OSSException e) {
            log.error(ExceptionUtil.stacktraceToString(e));
            throw new CheckException(e.getMessage());
        }
    }
}
