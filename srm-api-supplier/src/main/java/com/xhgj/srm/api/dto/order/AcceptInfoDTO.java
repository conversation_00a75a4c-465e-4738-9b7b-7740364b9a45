package com.xhgj.srm.api.dto.order;

import com.xhgj.srm.api.dto.FileDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-02-01 14:19
 */
@Data
public class AcceptInfoDTO {

  @ApiModelProperty("凭证类型")
  private String uploadMan;

  @ApiModelProperty("提交时间")
  private Long createTime;

  @ApiModelProperty("凭证附件")
  private String type;

  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("文件")
  private List<FileDTO> acceptList;

  /**
   * @deprecated 兼容保留
   */
  @ApiModelProperty("审核人")
  private String auditMan;

  /**
   * @deprecated 兼容保留
   */
  @ApiModelProperty("驳回理由")
  private String groundsForRejection;

  /**
   * @deprecated 兼容保留
   */
  @ApiModelProperty("审核状态")
  private String auditState;
}
