package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn.SupplierOrderCancelDetailDTO;
import com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn.SupplierOrderCountDTO;
import com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn.SupplierOrderReturnDetailDTO;
import com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn.SupplierOrderReturnQueryDTO;
import com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn.SupplierOrderReturnTableDTO;
import com.xhgj.srm.api.service.SupplierOrderToFormService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/** <AUTHOR> @ClassName SupplierOrderReturnController */
@Slf4j
@RestController
@RequestMapping("supplierOrderReturn")
@Api(tags = {"供应商订单退换货接口"})
public class SupplierOrderReturnController {
  @Autowired private SupplierOrderToFormService service;

  @ApiOperation(value = "分页获取供应商订单列表")
  @GetMapping("getSupplierOrderReturnPage")
  public ResultBean<PageResult<SupplierOrderReturnTableDTO>> getSupplierOrderReturnPage(
      @Valid SupplierOrderReturnQueryDTO queryDTO, @Valid PageParam pageParam) {
    return new ResultBean<>(service.getSupplierOrderReturnPage(queryDTO, pageParam.toPageable()));
  }

  @ApiOperation(value = "获取退货单详情")
  @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "订单 id", required = true)})
  @GetMapping("getSupplierOrderReturnDetail")
  public ResultBean<SupplierOrderReturnDetailDTO> getSupplierOrderReturnDetail(@RequestParam String id) {
    if (StringUtils.isNullOrEmpty(id)) {
      throw new CheckException("退货单id 为空");
    }
    return new ResultBean<>(service.getSupplierOrderReturnDetail(id));
  }

  @ApiOperation(value = "获取取消单详情")
  @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "订单 id", required = true)})
  @GetMapping("getSupplierOrderCancelDetail")
  public ResultBean<SupplierOrderCancelDetailDTO> getSupplierOrderCancelDetail(
      @RequestParam String id) {
    if (StringUtils.isNullOrEmpty(id)) {
      throw new CheckException("取消单id 为空");
    }
    return new ResultBean<>(service.getSupplierOrderCancelDetail(id));
  }

  @ApiOperation(value = "获取单据类型数量")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "supplierOrderId", value = "供应商订单 id", required = true)})
  @GetMapping(value = "getSupplierOrderCount")
  public ResultBean<SupplierOrderCountDTO> getSupplierOrderCount(
      @RequestParam("supplierOrderId") String supplierOrderId) {
    return new ResultBean<>(service.getSupplierOrderFormCountById(supplierOrderId));
  }
}
