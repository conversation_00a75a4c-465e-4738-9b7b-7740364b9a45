package com.xhgj.srm.api.dto.supplierBrand;

import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
public class BrandPageQueryParam implements BaseDefaultSearchSchemeForm {

  /**
   * 品牌编码
   */
  @ApiModelProperty(value = "品牌编码")
  private String brandCode;

  /**
   * 品牌名称(中文)
   */
  @ApiModelProperty(value = "品牌名称(中文)")
  private String brandNameCn;

  /**
   * 品牌名称(英文)
   */
  @ApiModelProperty(value = "品牌名称(英文)")
  private String brandNameEn;

  /**
   * 审核状态(1-审核中,2-驳回)
   */
  @ApiModelProperty(value = "审核状态(1_审核中 2_驳回)")
  private String mpmState;

  /**
   * 经营类型(1-品牌方,2-集贷商)
   */
  @ApiModelProperty(value = "经营方式(1_品牌方 2_集贷商)")
  private String manageType;

  /**
   * 标签类型(1-我的申请,2-我的品牌)
   */
  @ApiModelProperty(value = "标签类型(1_我的申请 2_我的品牌)")
  private String tabType;

  /**
   * 自定义方案id
   */
  @ApiModelProperty(value = "自定义方案id")
  private String schemeId;

  /**
   * 页数
   */
  @ApiModelProperty(value = "页数")
  private Integer pageNo;

  /**
   * 每页展示数量
   */
  @ApiModelProperty(value = "每页展示数量")
  private Integer pageSize;

  /**
   * 供应商用户 id
   */
  @ApiModelProperty(value = "供应商用户 id ")
  @NotBlank(message = "供应商用户 id 必传")
  private String userId;

  /**
   * 是否过滤放弃数据
   */
  @ApiModelProperty(hidden = true)
  private Boolean filterAbandon;

  public Integer getPageNo() {
    if (pageNo == null || pageNo < 1) {
      pageNo = 1;
    }
    return pageNo;
  }

  public Integer getPageSize() {
    if (pageSize == null || pageSize < 1) {
      pageSize = 25;
    }
    return pageSize;
  }

  /**
   * 默认过滤放弃数据
   */
  public Boolean getFilterAbandon() {
    if (filterAbandon == null) {
      filterAbandon = true;
    }
    return filterAbandon;
  }
}
