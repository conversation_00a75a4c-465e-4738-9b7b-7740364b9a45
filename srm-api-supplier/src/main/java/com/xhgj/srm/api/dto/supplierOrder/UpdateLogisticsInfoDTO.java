package com.xhgj.srm.api.dto.supplierOrder;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import lombok.Data;

/** <AUTHOR> @ClassName UpdateLogisticsInfoDTO */
@Data
public class UpdateLogisticsInfoDTO {
  @NotEmpty(message = "物流 id 必传")
  @ApiModelProperty("物流 id ")
  private String id;

  @NotEmpty(message = "快递公司 必传")
  @ApiModelProperty("快递公司")
  private String logisticsCompany;

  @NotEmpty(message = "快递公司编码 必传")
  @ApiModelProperty("快递公司编码 ")
  private String logisticsCode;

  /**
   * 物流单号 最长 200 字符
   */
  @NotEmpty(message = "物流单号 必传")
  @ApiModelProperty("物流单号 ")
  @Size(max = 200, message = "物流单号最长200字符")
  private String trackNum;
}
