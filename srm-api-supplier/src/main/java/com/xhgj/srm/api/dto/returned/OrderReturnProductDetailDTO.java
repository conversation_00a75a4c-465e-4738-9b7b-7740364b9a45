package com.xhgj.srm.api.dto.returned;

import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.OrderReturnDetail;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class OrderReturnProductDetailDTO {

    @ApiModelProperty("物料编码")
    private String code;
    @ApiModelProperty("品牌")
    private String brand;
    @ApiModelProperty("商品名称")
    private String name;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("单位")
    private String unit;
    @ApiModelProperty("单价")
    private BigDecimal price;
    @ApiModelProperty("退货/取消数量")
    private BigDecimal reCount;

    public OrderReturnProductDetailDTO(OrderReturnDetail orderReturnDetail) {
        this.code = StringUtils.emptyIfNull(orderReturnDetail.getCode());
        this.brand = StringUtils.emptyIfNull(orderReturnDetail.getBrand());
        this.name = StringUtils.emptyIfNull(orderReturnDetail.getName());
        this.model = StringUtils.emptyIfNull(orderReturnDetail.getModel());
        this.unit = StringUtils.emptyIfNull(orderReturnDetail.getUnit());
        this.price = orderReturnDetail.getPrice()!=null?orderReturnDetail.getPrice():BigDecimal.ZERO;
        this.reCount = BigDecimalUtil.formatForStandard(orderReturnDetail.getReturnNum());
    }

}
