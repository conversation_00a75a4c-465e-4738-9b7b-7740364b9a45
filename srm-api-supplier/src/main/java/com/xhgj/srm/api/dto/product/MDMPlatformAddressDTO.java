package com.xhgj.srm.api.dto.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * @description: MDMPlatformAddressDTO
 **/
@NoArgsConstructor
@Data
public class MDMPlatformAddressDTO {

  @ApiModelProperty("返回信息")
  @JsonProperty("msg")
  private String msg;
  @ApiModelProperty("code码")
  @JsonProperty("code")
  private Integer code;
  @ApiModelProperty("数据")
  @JsonProperty("data")
  private List<AddressDTO> data;

  @Data
  public static class AddressDTO {
    @ApiModelProperty("地址编码")
    @JsonProperty("addressCode")
    private String addressCode;

    @ApiModelProperty("地址名称")
    @JsonProperty("addressName")
    private String addressName;
  }
}
