package com.xhgj.srm.api.dto.product.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.xhgj.srm.api.dto.product.DTO.ProductDTO;
import com.xhgj.srm.api.dto.product.DTO.ProductPicDTO;
import com.xhgj.srm.api.dto.product.DTO.SrmProductFieldDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/6/8 10:36
 */
@ApiModel(value = "SRM - 商品结果传输对象")
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductResultVO extends ProductDTO {

  private static final long serialVersionUID = 3910310560303544377L;

  @ApiModelProperty("图片")
  private List<ProductPicDTO> mainUrl;

  @ApiModelProperty("类目名称")
  private String cateName;

  @ApiModelProperty("类目编码集合")
  private List<String> cateCodeList;

  @ApiModelProperty("品牌名称")
  private String brandName;

  @ApiModelProperty("品牌英文名")
  private String brandNameEn;

  @ApiModelProperty("品牌中文名")
  private String brandNameCn;

  @ApiModelProperty("mdmId")
  private String productMdmId;

  @ApiModelProperty("货主名称")
  private String shipperName;

  @ApiModelProperty("单位名称")
  private String basicUnitName;

  @ApiModelProperty("拓展属性列表")
  private List<SrmProductFieldDTO> fieldList;

  @ApiModelProperty("该物料是否正在审核中")
  private String isAssessing;
  @ApiModelProperty("税收分类编码")
  private String taxCategoryCode;
  @ApiModelProperty("税收分类名称")
  private String taxCategoryName;
  /**
   * 税收分类税率
   */
  @ApiModelProperty("税收分类税率")
  private String taxCategoryRate;

  /**
   * 税收分类简称
   */
  @ApiModelProperty("税收分类简称")
  @JSONField(name = "taxCategoryAbbr", alternateNames = {"abbreviation"})
  private String taxCategoryAbbr;

  @ApiModelProperty("项目型字段")
  private List<PlatformProjectValueInfo> projectValueInfoList;

  @ApiModelProperty("详情图")
  private List<String> detailPicList;
}
