package com.xhgj.srm.api.service.impl;

import static com.xhgj.srm.common.Constants.SUPPLIER_PERFORMANCE_STATUS_EFFECT;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import com.xhgj.srm.domain.SupplierWithPerformanceDomain;
import com.xhgj.srm.api.dto.BusinessDetailDto;
import com.xhgj.srm.api.dto.BusinessUpdateParam;
import com.xhgj.srm.api.dto.CooperationAgreementAddParam;
import com.xhgj.srm.api.dto.CooperationAgreementAddParams;
import com.xhgj.srm.api.dto.CooperationAgreementData;
import com.xhgj.srm.api.dto.CooperationAgreementListData;
import com.xhgj.srm.api.dto.EnterpriseDetailDTo;
import com.xhgj.srm.api.dto.SupplierChangeData;
import com.xhgj.srm.api.dto.SupplierDetailExtraFile;
import com.xhgj.srm.api.dto.SupplierDetailFile;
import com.xhgj.srm.api.dto.SupplierLicenseAddParam;
import com.xhgj.srm.api.dto.SupplierLicenseData;
import com.xhgj.srm.api.dto.SupplierLicenseFileAddParam;
import com.xhgj.srm.api.dto.WorkbenchNotice;
import com.xhgj.srm.api.dto.order.OrderPlatformAndItemFieldVo;
import com.xhgj.srm.api.dto.supplier.CooperationAgreementDataDTO;
import com.xhgj.srm.dto.supplier.OrderSupplierDTO;
import com.xhgj.srm.dto.supplier.SupplierStatusDTO;
import com.xhgj.srm.api.service.ExtraFileService;
import com.xhgj.srm.api.service.FileService;
import com.xhgj.srm.api.service.OrderService;
import com.xhgj.srm.api.service.SupplierInGroupService;
import com.xhgj.srm.api.service.SupplierPerformanceService;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.api.service.SupplierTemplateFieldService;
import com.xhgj.srm.api.service.SupplierTemplateService;
import com.xhgj.srm.api.utils.SupplierUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.ConstantsSupplierTemplate;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.constants.SupplierConstants;
import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.common.enums.product.ProductStateEnum;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.jpa.dao.CheckDao;
import com.xhgj.srm.jpa.dao.ExtraFileDao;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.dao.ProductDao;
import com.xhgj.srm.jpa.dao.SupplierChangeInfoDao;
import com.xhgj.srm.jpa.dao.SupplierDao;
import com.xhgj.srm.jpa.dao.SupplierFbDao;
import com.xhgj.srm.jpa.dao.SupplierInGroupDao;
import com.xhgj.srm.jpa.dao.SupplierPerformanceDao;
import com.xhgj.srm.jpa.entity.Check;
import com.xhgj.srm.jpa.entity.ExtraFile;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Product;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierChangeInfo;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.SupplierTemplate;
import com.xhgj.srm.jpa.entity.SupplierTemplateField;
import com.xhgj.srm.jpa.repository.CheckRepository;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.SupplierFbRepository;
import com.xhgj.srm.jpa.repository.SupplierPerformanceRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.request.dto.product.PlatformFieldRuleDTO;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhgj.srm.request.service.third.product.PlatformProductRequest;
import com.xhgj.srm.service.ShareOrderService;
import com.xhgj.srm.service.SharePlatformService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 *
 */
@Slf4j
@Service
public class SupplierServiceImpl implements SupplierService {

  @Autowired
  private SupplierRepository repository;
  @Autowired
  private FileRepository fileRepository;
  @Autowired
  private ProductDao productDao;
  @Autowired
  private FileDao fileDao;
  @Autowired
  private OrderDao dao;
  @Autowired
  private OrderService orderService;
  @Autowired
  private SupplierDao supplierDao;
  @Autowired
  private SupplierChangeInfoDao supplierChangeInfoDao;
  @Autowired
  private SupplierUtil supplierUtil;
  @Autowired
  private CheckRepository checkRepository;
  @Autowired
  private SupplierFbRepository supplierFbRepository;
  @Autowired
  private CheckDao checkDao;
  @Autowired
  private SupplierFbDao supplierFbDao;
  @Autowired
  private ExtraFileDao extraFileDao;
  @Autowired
  private ExtraFileService extraFileService;
  @Autowired
  private SupplierInGroupService supplierInGroupService;
  @Autowired
  private SupplierTemplateFieldService supplierTemplateFieldService;
  @Autowired
  private SupplierTemplateService supplierTemplateService;
  @Autowired
  private FileService fileService;
  @Autowired
  private SupplierPerformanceService supplierPerformanceService;
  @Autowired
  private SupplierPerformanceRepository supplierPerformanceRepository;
  @Resource
  private HttpUtil httpUtil;
  @Resource
  PlatformProductRequest platformProductRequest;
  @Resource private SupplierPerformanceDao supplierPerformanceDao;

  private final String uploadUrl;
  @Resource
  private SupplierInGroupDao supplierInGroupDao;
  @Resource
  private SharePlatformService platformService;
  @Resource
  MPMService mpmService;
  @Resource
  ShareOrderService shareOrderService;


  public SupplierServiceImpl(SrmConfig srmConfig) {
    this.uploadUrl = srmConfig.getUploadUrl();
  }

  @Override
  public BootBaseRepository<Supplier, String> getRepository() {
    return repository;
  }

  @Override
  public EnterpriseDetailDTo getEnterpriseInfo(String supplierId) {
    Supplier supplier = repository.findById(supplierId)
        .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    List<SupplierInGroup> allBySupplier = supplierInGroupDao.getAllBySupplier(supplierId);
    if(CollUtil.isNotEmpty(allBySupplier)){
      supplier.setLicenseUrl(allBySupplier.get(0).getLicenseUrl());
    }
    return new EnterpriseDetailDTo(supplier, uploadUrl);
  }

  @Override
  public BusinessDetailDto getBusinessInfo(String supplierId) {
    Supplier supplier = repository.findById(supplierId)
        .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    // 审核中 给原信息
    SupplierFb supplierFb = supplierFbDao.getSupplierFbBySid(supplier.getId());
    long date = 0;
    long startDate = 0;
    long endDate = 0;
    if (supplierFb == null) {
      date = supplier.getDate() != null && supplier.getDate() > 0 ? supplier.getDate() : 0;
      startDate =
          supplier.getStartDate() != null && supplier.getStartDate() > 0 ? supplier.getStartDate()
              : 0;
      endDate =
          supplier.getEndDate() != null && supplier.getEndDate() > 0 ? supplier.getEndDate() : 0;
    } else {
      date = supplierFb.getDate() != null && supplierFb.getDate() > 0 ? supplierFb.getDate() : 0;
      startDate = supplierFb.getStartDate() != null && supplierFb.getStartDate() > 0
          ? supplierFb.getStartDate() : 0;
      endDate =
          supplierFb.getEndDate() != null && supplierFb.getEndDate() > 0 ? supplierFb.getEndDate()
              : 0;
    }
    supplierFbRepository.flush();
    // 点击详情重置阅读时间
    List<SupplierFb> supplierFbsBySupId = supplierFbDao.getSupplierFbsBySupId(supplier.getId());
    if (!CollUtil.isEmpty(supplierFbsBySupId)) {
      supplierFbsBySupId.stream().forEach(fb -> {
        List<String> unReadSupplierCheckByRidAndType =
            checkDao.getUnReadSupplierCheckByRidAndType(fb.getId());
        if (!CollUtil.isEmpty(unReadSupplierCheckByRidAndType)) {
          unReadSupplierCheckByRidAndType.stream().forEach(s ->{
            Check check = checkRepository.findById(s)
                .orElseThrow(() -> CheckException.noFindException(Check.class, s));
            check.setReadTime(0);
            checkRepository.save(check);
          });
          checkRepository.flush();
        }
      });
    }
    List<SupplierInGroup> allBySupplier = supplierInGroupDao.getAllBySupplier(supplierId);
    if(CollUtil.isNotEmpty(allBySupplier)){
      supplier.setLicenseUrl(allBySupplier.get(0).getLicenseUrl());
    }
    return new BusinessDetailDto(supplier, null, date, startDate, endDate, uploadUrl);
  }

  @Override
  public Supplier updateBusinessInfo(BusinessUpdateParam updateParam) {
    String supplierId = updateParam.getSupplierId();
    Supplier supplier = repository.findById(supplierId)
        .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    if (Constants.AUDIT_STATE_PURCHASEIN.equals(supplier.getAuditState())) {
      throw new CheckException("审核中供应商不能修改");
    }
    // 保存修改前数据
    supplierUtil.UpdateSupplierFb(supplier.getId());
    // 比较修改字段
    supplierUtil.comparToChange(updateParam, supplier);
    updateParam.updateBusinessInfo(supplier);
    repository.saveAndFlush(supplier);
    return supplier;
  }

  @Override
  public SupplierLicenseData getLicenceBySupplier(String supplierId) {
    Supplier supplier = repository.findById(supplierId)
        .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    SupplierLicenseData detail = new SupplierLicenseData();
    // 生产许可
    List<File> productionLicense =
        fileDao.getFileListBySId(supplier.getId(), Constants.FILE_TYPE_SCXK);
    if (CollUtil.isNotEmpty(productionLicense)) {
      detail.setProductionLicense(CollUtil.emptyIfNull(productionLicense).stream()
          .map((file) -> new SupplierDetailFile(file, Constants.FILE_TYPE_SCXK)).limit(1L)
          .collect(Collectors.toList()));
    }
    // 专利证书
    List<File> patentCertificate =
        fileDao.getFileListBySId(supplier.getId(), Constants.FILE_TYPE_ZLZS);
    if (CollUtil.isNotEmpty(patentCertificate)) {
      detail.setPatentCertificate(CollUtil.emptyIfNull(patentCertificate).stream()
          .map((file) -> new SupplierDetailFile(file, Constants.FILE_TYPE_ZLZS)).limit(1L)
          .collect(Collectors.toList()));
    }
    // 商品注册证书
    List<File> commodityRegistrationCertificate =
        fileDao.getFileListBySId(supplier.getId(), Constants.FILE_TYPE_SPZCZS);
    if (CollUtil.isNotEmpty(commodityRegistrationCertificate)) {
      detail.setCommodityRegistrationCertificate(
          CollUtil.emptyIfNull(commodityRegistrationCertificate).stream()
              .map((file) -> new SupplierDetailFile(file, Constants.FILE_TYPE_SPZCZS)).limit(1L)
              .collect(Collectors.toList()));
    }
    // 产品试验报告
    List<File> productTestReport =
        fileDao.getFileListBySId(supplier.getId(), Constants.FILE_TYPE_CPSYBG);
    if (CollUtil.isNotEmpty(productTestReport)) {
      detail.setProductTestReport(CollUtil.emptyIfNull(productTestReport).stream()
          .map((file) -> new SupplierDetailFile(file, Constants.FILE_TYPE_CPSYBG)).limit(1L)
          .collect(Collectors.toList()));
    }
    // 第三方检测报告
    List<File> thirdPartyTestReport =
        fileDao.getFileListBySId(supplier.getId(), Constants.FILE_TYPE_DSFJCBG);
    if (CollUtil.isNotEmpty(thirdPartyTestReport)) {
      detail.setThirdPartyTestReport(CollUtil.emptyIfNull(thirdPartyTestReport).stream()
          .map((file) -> new SupplierDetailFile(file, Constants.FILE_TYPE_DSFJCBG)).limit(1L)
          .collect(Collectors.toList()));
    }
    // ISO质量认证体系
    List<File> qualityCertificationSystem =
        fileDao.getFileListBySId(supplier.getId(), Constants.FILE_TYPE_ISOZLRZTX);
    if (CollUtil.isNotEmpty(qualityCertificationSystem)) {
      detail.setQualityCertificationSystem(CollUtil.emptyIfNull(qualityCertificationSystem).stream()
          .map((file) -> new SupplierDetailFile(file, Constants.FILE_TYPE_ISOZLRZTX)).limit(1L)
          .collect(Collectors.toList()));
    }
    // ISO14001环境管理体系
    List<File> environmentalManagementSystem =
        fileDao.getFileListBySId(supplier.getId(), Constants.FILE_TYPE_ISO14001HJGLTX);
    if (CollUtil.isNotEmpty(environmentalManagementSystem)) {
      detail.setEnvironmentalManagementSystem(
          CollUtil.emptyIfNull(environmentalManagementSystem).stream()
              .map((file) -> new SupplierDetailFile(file, Constants.FILE_TYPE_ISO14001HJGLTX))
              .limit(1L).collect(Collectors.toList()));
    }
    // OHSAS18001职业健康安全管理体系
    List<File> healthyManagementSystem =
        fileDao.getFileListBySId(supplier.getId(), Constants.FILE_TYPE_OHSAS18001ZYJKAQTX);
    if (CollUtil.isNotEmpty(healthyManagementSystem)) {
      detail.setHealthyManagementSystem(CollUtil.emptyIfNull(healthyManagementSystem).stream()
          .map((file) -> new SupplierDetailFile(file, Constants.FILE_TYPE_OHSAS18001ZYJKAQTX))
          .limit(1L).collect(Collectors.toList()));
    }
    // CMS测量管理体系认证证书
    List<File> measurementManagementSystem =
        fileDao.getFileListBySId(supplier.getId(), Constants.FILE_TYPE_CMSZLGLTXRZZS);
    if (CollUtil.isNotEmpty(measurementManagementSystem)) {
      detail.setMeasurementManagementSystem(
          CollUtil.emptyIfNull(measurementManagementSystem).stream()
              .map((file) -> new SupplierDetailFile(file, Constants.FILE_TYPE_CMSZLGLTXRZZS))
              .limit(1L).collect(Collectors.toList()));
    }
    // 自定义
    List<ExtraFile> zdylist = extraFileDao.getFileListByRId(supplier.getId());
    if (CollUtil.isNotEmpty(zdylist)) {
      detail.setZdy(
          CollUtil.emptyIfNull(zdylist).stream().map((file) -> new SupplierDetailExtraFile(file))
              .collect(Collectors.toList()));
    }
    // 设置为已读
    List<File> fileListByS =
        fileDao.getFileListBySupplierId(supplier.getId(), Constants.FILE_TYPE_SCXK);
    List<File> fileListByJ =
        fileDao.getFileListBySupplierId(supplier.getId(), Constants.FILE_TYPE_JYXK);
    if (!CollUtil.isEmpty(fileListByS)) {
      for (File file1 : fileListByS) {
        file1.setIsRead(Constants.YES);
        fileRepository.save(file1);
      }
    }
    if (!CollUtil.isEmpty(fileListByJ)) {
      for (File file1 : fileListByJ) {
        file1.setIsRead(Constants.YES);
        fileRepository.save(file1);
      }
    }
    fileRepository.flush();
    return detail;
  }

  @Transactional
  @Override
  public void addOrUpdateLicenceBySupplier(SupplierLicenseAddParam param) {
    String supplierId = param.getSupplierId();
    Supplier supplier = repository.findById(supplierId)
        .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    List<SupplierLicenseFileAddParam> fileAddParamList = param.getFileAddParamList();
    fileDao.deleteByRelationIdAndRelationType(supplier.getId(), param.getType());
    if (CollUtil.isNotEmpty(fileAddParamList)) {
      for (SupplierLicenseFileAddParam supplierLicenseFileAddParam : fileAddParamList) {
        File file = new File();
        file.setState(Constants.STATE_OK);
        file.setCreateTime(System.currentTimeMillis());
        file.setRelationId(supplierId);
        file.setRelationType(param.getType());
        file.setType(Constants.PLATFORM_TYPE_BEFORE);
        file.setName(supplierLicenseFileAddParam.getFileName());
        file.setUrl(supplierLicenseFileAddParam.getLicenceUrl());
        fileRepository.save(file);
      }
    }
    // 新增自定义文件
    String zdy = param.getZdy();
    if (!StringUtils.isNullOrEmpty(zdy)) {
      extraFileService.addZDYFile(supplier.getId(), zdy);
    }
  }

  @Override
  public WorkbenchNotice getWorkbenchNotice(String supplierId) {
    Supplier supplier = repository.findById(supplierId)
        .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    WorkbenchNotice workbenchNotice = new WorkbenchNotice();
    // 工商信息--有新的审核结果
    workbenchNotice.setBusinessInformation(false);
    List<SupplierFb> supplierFbsBySupId = supplierFbDao.getSupplierFbsBySupId(supplier.getId());
    if (!CollUtil.isEmpty(supplierFbsBySupId)) {
      for (SupplierFb supplierFb : supplierFbsBySupId) {
        List<String> unReadSupplierCheckByRidAndType =
            checkDao.getUnReadSupplierCheckByRidAndType(supplierFb.getId());
        if (!CollUtil.isEmpty(unReadSupplierCheckByRidAndType)) {
          workbenchNotice.setBusinessInformation(true);
          break;
        }
      }
    }
    List<File> fileListBySupplierId =
        fileDao.getFileListBySupplierId(supplier.getId(), Constants.FILE_TYPE_CGXY);
    // 合作协议--供应商等级变更时
    if (SupplierLevelEnum.STRATEGIC.getCode().equals(supplier.getEnterpriseLevel())
        || SupplierLevelEnum.HIGH_QUALITY.getCode().equals(supplier.getEnterpriseLevel())
        || SupplierLevelEnum.GENERAL.getCode().equals(supplier.getEnterpriseLevel())) {
      if (fileListBySupplierId == null) {
        workbenchNotice.setCooperationAgreement(true);
      } else {
        workbenchNotice.setCooperationAgreement(false);
      }
    }
    // 资质证照--供应商等级变更时
    List<File> fileListByS =
        fileDao.getFileListBySupplierId(supplier.getId(), Constants.FILE_TYPE_SCXK);
    List<File> fileListByJ =
        fileDao.getFileListBySupplierId(supplier.getId(), Constants.FILE_TYPE_JYXK);
    if (!(SupplierLevelEnum.STRATEGIC.getCode().equals(supplier.getEnterpriseLevel())
        || SupplierLevelEnum.HIGH_QUALITY.getCode().equals(supplier.getEnterpriseLevel())
        || SupplierLevelEnum.GENERAL.getCode().equals(supplier.getEnterpriseLevel()))) {
      if (fileListByS == null || fileListByJ == null) {
        workbenchNotice.setQualificationCertificate(true);
      } else {
        workbenchNotice.setQualificationCertificate(false);
      }
    }
    // 批量新增--待提交的批量新增物料数量
    workbenchNotice.setBatchAddition(
        productDao.getProductCountByState(ProductStateEnum.TEMPORARY.getKey(), supplier.getId(), ""));
    // 物料审核情况-驳回数量
    List<Product> productListBySupplier =
        productDao.getUnPassProductListBySupplier(supplier.getId());
    workbenchNotice.setMaterialAudit(productListBySupplier.size());
    return workbenchNotice;
  }

  @Override
  public CooperationAgreementListData getCooperationAgreementBySupplier(String supplierId) {
    Supplier supplier = repository.findById(supplierId)
        .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    CooperationAgreementListData data = new CooperationAgreementListData();
    List<CooperationAgreementData> dataList = new ArrayList<>();
    if (!StringUtils.isNullOrEmpty(supplier.getEnterpriseLevel()) && (
        SupplierLevelEnum.STRATEGIC.getCode().equals(supplier.getEnterpriseLevel())
            || SupplierLevelEnum.HIGH_QUALITY.getCode().equals(supplier.getEnterpriseLevel())
            || SupplierLevelEnum.GENERAL.getCode().equals(supplier.getEnterpriseLevel()))) {
      // 采购协议
    }
    // 合作协议
    List<File> fileList = Collections.emptyList();
    if (SupplierLevelEnum.STRATEGIC.getCode().equals(supplier.getEnterpriseLevel())
        || SupplierLevelEnum.HIGH_QUALITY.getCode().equals(supplier.getEnterpriseLevel())
        || SupplierLevelEnum.GENERAL.getCode().equals(supplier.getEnterpriseLevel())) {
      fileList = fileDao.getFileBySupplierId(supplier.getId(), Constants.FILE_TYPE_CGXY);
    }
    if (CollUtil.isNotEmpty(fileList)) {
      for (File file : fileList) {
        CooperationAgreementData cooperationAgreementData = new CooperationAgreementData();
        String licenceUrl = !StringUtils.isNullOrEmpty(file.getUrl()) ? file.getUrl() : "";
        cooperationAgreementData.setCooperationAgreementUrl(
            !StringUtils.isNullOrEmpty(licenceUrl) ? licenceUrl : "");
        cooperationAgreementData.setBaseUrl(uploadUrl);
        cooperationAgreementData.setFileName(
            !StringUtils.isNullOrEmpty(file.getName()) ? file.getName() : "");
        dataList.add(cooperationAgreementData);
      }
      data.setCooperationAgreementState(
          Constants.UPLOAD_STATUS_TO_NAME.get(Constants.UPLOAD_STATUS_UPLOADED));
    } else {
      data.setCooperationAgreementState(
          Constants.UPLOAD_STATUS_TO_NAME.get(Constants.UPLOAD_STATUS_NOT_UPLOADED));
    }
    data.setList(dataList);
    // 设置为已读
    List<File> fileListBySupplierId =
        fileDao.getFileListBySupplierId(supplier.getId(), Constants.FILE_TYPE_CGXY);
    if (!CollUtil.isEmpty(fileListBySupplierId)) {
      for (File file1 : fileListBySupplierId) {
        file1.setIsRead(Constants.YES);
        fileRepository.save(file1);
      }
    }
    fileRepository.flush();
    return data;
  }

  @Override
  public PageResult<SupplierChangeData> getSupplierChangeInfoPage(String supplierId, String pageNo,
      String pageSize) {
    Page<SupplierChangeInfo> page =
        supplierChangeInfoDao.getSupplierChangeInfoPage(supplierId, pageNo, pageSize);
    PageResult<SupplierChangeInfo> pageResult = PageResultBuilder.buildPageResult(page);
    return pageResult.map(SupplierChangeData::new);
  }

  @Transactional
  @Override
  public void addcooperationAgreementBySupplier(CooperationAgreementAddParam param) {
    String supplierId = param.getSupplierId();
    Supplier supplier = repository.findById(supplierId)
        .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    fileDao.deleteByRelationIdAndRelationType(supplier.getId(), param.getType());
    List<SupplierLicenseFileAddParam> fileAddParamList = param.getFileAddParamList();
    if (CollUtil.isNotEmpty(fileAddParamList)) {
      for (SupplierLicenseFileAddParam fileAddParam : fileAddParamList) {
        File file = new File();
        file.setState(Constants.STATE_OK);
        file.setCreateTime(System.currentTimeMillis());
        file.setRelationId(supplierId);
        file.setRelationType(param.getType());
        file.setUrl(fileAddParam.getLicenceUrl());
        file.setName(fileAddParam.getFileName());
        file.setType(Constants.PLATFORM_TYPE_BEFORE);
        fileRepository.save(file);
      }
    }
  }

  @Override
  public PageResult<OrderSupplierDTO> getTakeEffectOrderSupplierPage(String enterpriseName,
      String platform, int pageNo, int pageSize) {
    Page<String> page =
        supplierDao.getTakeEffectOrderSupplierPage(enterpriseName, platform, "1", pageNo, pageSize);
    int totalPages = page.getTotalPages();
    return new PageResult<>(buildOrderSupplierPage(page, pageNo, platform, totalPages),
        page.getTotalElements(), totalPages, pageNo, pageSize);
  }

  private List<OrderSupplierDTO> buildOrderSupplierPage(Page<String> page, int pageNo,
      String platform, int totalPages) {
    List<OrderSupplierDTO> pageDataList = new ArrayList<>();
    if (!(pageNo > totalPages)) {
      List<String> supplierIdList = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      for (String s : supplierIdList) {
        Supplier supplier = get(s);
        OrderSupplierDTO data = new OrderSupplierDTO(supplier);
        data.setPrice(shareOrderService.getSupplierTotalPrice(supplier.getId(), platform));
        pageDataList.add(data);
      }
    }
    return pageDataList;
  }

  @Override
  public List<CooperationAgreementDataDTO> getCooperationAgreementDataDTOList(String supplierId) {
    Assert.notEmpty(supplierId);
    Supplier supplier =
        get(supplierId, () -> CheckException.noFindException(Supplier.class, supplierId));
    // 获取根据 supplierId 获得组织下供应商
    List<SupplierInGroup> supplierInGroupList =
        CollUtil.emptyIfNull(supplierInGroupService.getBySupplierId(supplier.getId()));
    return CollUtil.emptyIfNull(supplierInGroupList.stream().map(supplierInGroup -> {
      // 组织
      Group group = Optional.of(supplierInGroup.getGroup()).orElseThrow(() -> new CheckException(
          "【" + supplierInGroup.getId() + "】该组织下供应商的组织不存在，数据异常请联系管理员！"));
      String groupName = group.getName();
      // 通过组织 id 获取模板判断是否配置了模板
      SupplierTemplate supplierTemplate = supplierTemplateService.getByGroupId(group.getId());
      if (supplierTemplate == null) {
        throw new CheckException("【" + group.getId() + "(" + group.getName() + ")"
            + "】该组织未配置模板，请通知后台管理员配置模板");
      }
      // 获取组织下的
      List<SupplierTemplateField> supplierTemplateFields = CollUtil.emptyIfNull(
          supplierTemplateFieldService.getSupplierTemplateFieldBySupplierTempIdAndType(
              supplierTemplate.getId(), Constants.SUPPLIERTYPE_CHINA));
      // 组织下供应商等级
      String enterpriseLevel = supplierInGroup.getEnterpriseLevel();
      // 协议类型
      String agreementType = getAgreementTypeByLevel(enterpriseLevel);
      if (StrUtil.isBlank(agreementType)) {
        return null;
      }
      List<CooperationAgreementData> files =
          CollUtil.emptyIfNull(fileDao.getFileListBySId(supplierInGroup.getId(), agreementType))
              .stream().map(file -> {
                CooperationAgreementData cooperationAgreementData = new CooperationAgreementData();
                cooperationAgreementData.setCooperationAgreementUrl(file.getUrl());
                cooperationAgreementData.setBaseUrl(uploadUrl);
                cooperationAgreementData.setFileName(file.getName());
                cooperationAgreementData.setUploadTime(file.getCreateTime());
                return cooperationAgreementData;
              }).collect(Collectors.toList());
      CooperationAgreementDataDTO cooperationAgreementDataDTO = new CooperationAgreementDataDTO();
      cooperationAgreementDataDTO.setCooperationAgreementDatas(files);
      cooperationAgreementDataDTO.setLevel(enterpriseLevel);
      // todo 校验这里改动是否正常
      cooperationAgreementDataDTO.setRequire(false);
      cooperationAgreementDataDTO.setGroupName(groupName);
      cooperationAgreementDataDTO.setSupplierInGroupId(supplierInGroup.getId());
      // 设置为已读
      List<File> fileListBySupplierId =
          fileDao.getFileListBySupplierId(supplierInGroup.getId(), Constants.FILE_TYPE_CGXY);
      if (!CollUtil.isEmpty(fileListBySupplierId)) {
        for (File file1 : fileListBySupplierId) {
          file1.setIsRead(Constants.YES);
          fileRepository.save(file1);
        }
      }
      return cooperationAgreementDataDTO;
    }).filter(ObjectUtil::isNotNull).collect(Collectors.toList()));
  }

  @Override
  public void addCooperationAgreementBySupplierInGroupId(CooperationAgreementAddParams param) {
    String supplierInGroupId = param.getSupplierInGroupId();
    SupplierLicenseFileAddParam fileAddParam = param.getFileAddParamList();
    String agreementType = getAgreementTypeByLevel(param.getLevel());
    String licenceUrl = fileAddParam.getLicenceUrl();
    String fileName = fileAddParam.getFileName();
    if (StrUtil.isBlank(agreementType)) {
      throw new CheckException("合作类型非法");
    }
    SupplierInGroup supplierInGroup = supplierInGroupService.get(supplierInGroupId,
        () -> CheckException.noFindException(SupplierInGroup.class, supplierInGroupId));
    File file = new File();
    file.setRelationType(agreementType);
    file.setRelationId(supplierInGroup.getId());
    file.setName(fileName);
    file.setUrl(licenceUrl);
    file.setType(Constants.PLATFORM_TYPE_BEFORE);
    file.setCreateTime(System.currentTimeMillis());
    file.setState(Constants.STATE_OK);
    fileService.save(file);
  }

  @Override
  public List<SupplierDetailFile> getSupplierDetailFileList(String supplierId) {
    Assert.notEmpty(supplierId);
    Assert.notEmpty(supplierId);
    List<SupplierDetailFile> supplierDetailFiles = new ArrayList<>();
    Supplier supplier =
        get(supplierId, () -> CheckException.noFindException(Supplier.class, supplierId));
    // 获取根据 supplierId 获得组织下供应商
    List<SupplierInGroup> supplierInGroupList =
        CollUtil.emptyIfNull(supplierInGroupService.getBySupplierId(supplier.getId()));
    supplierInGroupList.forEach(supplierInGroup -> {
      String groupName = supplierInGroup.getGroup().getName();
      List<SupplierDetailFile> files = fileDao.getByRelationIdAndTypes(supplierInGroup.getId(),
              ListUtil.toList(Constants.FILE_TYPE_SCXK, Constants.FILE_TYPE_ZLZS,
                  Constants.FILE_TYPE_SPZCZS, Constants.FILE_TYPE_CPSYBG, Constants.FILE_TYPE_DSFJCBG,
                  Constants.FILE_TYPE_ISOZLRZTX, Constants.FILE_TYPE_ISO14001HJGLTX,
                  Constants.FILE_TYPE_OHSAS18001ZYJKAQTX, Constants.FILE_TYPE_CMSZLGLTXRZZS)).stream()
          .map(file -> new SupplierDetailFile(file, file.getRelationType(), groupName, uploadUrl))
          .collect(Collectors.toList());
      if (CollUtil.isNotEmpty(files)) {
        supplierDetailFiles.addAll(files);
      }
      // 自定义
      List<SupplierDetailFile> zdylist = extraFileDao.getFileListByRId(supplier.getId()).stream()
          .map(extraFile -> new SupplierDetailFile(extraFile, groupName, uploadUrl))
          .collect(Collectors.toList());
      if (CollUtil.isNotEmpty(zdylist)) {
        supplierDetailFiles.addAll(zdylist);
      }
      // 设置为已读
      List<File> fileListByS =
          fileDao.getFileListBySupplierId(supplier.getId(), Constants.FILE_TYPE_SCXK);
      List<File> fileListByJ =
          fileDao.getFileListBySupplierId(supplier.getId(), Constants.FILE_TYPE_JYXK);
      if (!CollUtil.isEmpty(fileListByS)) {
        for (File file1 : fileListByS) {
          file1.setIsRead(Constants.YES);
          fileService.save(file1);
        }
      }
      if (!CollUtil.isEmpty(fileListByJ)) {
        for (File file1 : fileListByJ) {
          file1.setIsRead(Constants.YES);
          fileService.save(file1);
        }
      }
    });
    return supplierDetailFiles;
  }

  /**
   * 根据供应商等级获取其对应协议的类型
   *
   * @param level 供应商等级
   * @return 如果没有对应的协议类型或 level 传空，则返回空值
   */
  public String getAgreementTypeByLevel(String level) {
    if (StringUtils.isNullOrEmpty(level)) {
      return StrUtil.EMPTY;
    } else if (StrUtil.equalsAny(
        level,
        SupplierLevelEnum.STRATEGIC.getCode(),
        SupplierLevelEnum.HIGH_QUALITY.getCode(),
        SupplierLevelEnum.GENERAL.getCode(),
        // 2022-11-29 经产品确认：零星和电商等级的协议归类为采购协议
        // 目前除【潜在合作】等级以外，所有等级都对应有协议类型
        // 因【潜在合作】在前端页面已不再展示，保守实现需求【所有合作等级的供应商都可以上传合作协议】
        SupplierLevelEnum.SPORADIC.getCode())) {
      // 采购协议
      return Constants.FILE_TYPE_CGXY;
    } else {
      return StrUtil.EMPTY;
    }
  }
  @Override
  public List<OrderPlatformAndItemFieldVo> getOrderPlatformAndItemField(String supplierId) {
    // 取值为供应商账号-接单配置平台中配置生效的项目，与MPM中对接状态为空的项目
    List<OrderPlatformDTO> orderPlatforms =
        getOrderPlatformListBySupplierId(supplierId);
    ArrayList<OrderPlatformAndItemFieldVo> result = new ArrayList<>();

    if (ObjectUtils.isEmpty(orderPlatforms)) {
      return result;
    }
    orderPlatforms.forEach(orderPlatformListDTO -> {
      OrderPlatformAndItemFieldVo vo = new OrderPlatformAndItemFieldVo();
      Optional<List<PlatformFieldRuleDTO>> projectFieldList =
          platformProductRequest.getProjectFieldList(orderPlatformListDTO.getPlatformCode());
      vo.setOrderPlatform(orderPlatformListDTO);
      projectFieldList.ifPresent(vo::setItemFields);
      result.add(vo);
    });
    return result;
  }

  @Override
  public List<OrderPlatformDTO> getOrderPlatformListBySupplierId(String supplierId) {
    List<OrderPlatformDTO> orderPlatformList = getExclusivePlatformListBySupplierId(supplierId);
    return mpmService.getPlatformList(orderPlatformList);
  }

  /**
   * 供应商可下单平台，不包含mdm的下单平台
   * @param supplierId
   * @return
   */
  @Override
  public List<OrderPlatformDTO> getExclusivePlatformListBySupplierId(String supplierId) {
    if (ObjectUtils.isEmpty(supplierId)) {
      throw new CheckException("缺少供应商信息");
    }
    Assert.notEmpty(supplierId);
    List<OrderPlatformDTO> orderPlatformList = new ArrayList<>();
    //没有履约信息就返回固定平台
    List<SupplierPerformance> supplierPerformances =
        supplierPerformanceService.findBySupplierIdAndStatus(supplierId, Constants.STATE_OK);
    if (CollUtil.isEmpty(supplierPerformances)) {
      return orderPlatformList;
    }
    supplierPerformances.forEach(
        supplierPerformance -> {
          OrderPlatformDTO platformDTO =
              platformService.findByCode(supplierPerformance.getPlatformCode());
          if (platformDTO != null
              && Objects.equals(
                  SUPPLIER_PERFORMANCE_STATUS_EFFECT, supplierPerformance.getStatus())) {
            orderPlatformList.add(platformDTO);
          }
        });
    return orderPlatformList;
  }
  /**
   * 供应商可下单平台，后台供应商配置生效的平台
   * @param supplierId
   * @return
   */
  @Override
  public List<OrderPlatformDTO> getPlatformListBySupplierId(String supplierId) {
    if (StrUtil.isBlank(supplierId)) {
      throw new CheckException("缺少供应商信息");
    }
    List<OrderPlatformDTO> orderPlatformList =
        new ArrayList<>();
    List<SupplierPerformance> supplierPerformances =
        supplierPerformanceDao.getListBySupplierId(supplierId);
    if (ObjectUtils.isEmpty(supplierPerformances)) {
      return orderPlatformList;
    }
    String[] array = supplierPerformances.stream().filter(supplierPerformance -> {
      return Objects.equals(supplierPerformance.getStatus(), Constants.STATE_OK);
    }).map(SupplierPerformance::getPlatformCode).toArray(String[]::new);
    //根据编码获取平台信息
    if (array.length == 0) {
      throw new CheckException("您还没有可用平台");
    }
    orderPlatformList.addAll(platformService.batchFindByCode(ListUtil.toList(array)));
    return orderPlatformList.stream().filter(Objects::nonNull).collect(Collectors.toList());
  }

  @Override
  public void addLandingMerchantPerformanceInfo() {
    List<Supplier> allByPlatformNotNullAndState = repository.findAllByState(Constants.STATE_OK);
    List<String> failList = new ArrayList<>();
    Boolean finalOpenSupplierOrder = null;
    Boolean finalOpenOrder = null;
    for (Supplier supplier : allByPlatformNotNullAndState) {
      // 供应商接单权限
      Boolean openSupplierOrder = supplier.getOpenSupplierOrder();
      // 落地商接单权限
      String isOpenOrder = supplier.getIsOpenOrder();
      String cooperateType = supplier.getCooperateType();
      if (BooleanUtil.isTrue(openSupplierOrder) || Constants.COOPERATE_TYPE_ALL.equals(
          cooperateType) || Constants.COOPERATE_TYPE_SUPPLIER.equals(cooperateType)) {
        finalOpenSupplierOrder = Boolean.TRUE;
      }
      if (Constants.YES.equals(isOpenOrder) || Constants.COOPERATE_TYPE_ALL.equals(cooperateType)
          || Constants.COOPERATE_TYPE_LANDER.equals(cooperateType)) {
        finalOpenOrder = Boolean.TRUE;
      }
      if (BooleanUtil.isTrue(finalOpenSupplierOrder) && BooleanUtil.isTrue(finalOpenOrder)) {
        supplier.setCooperateType(Constants.COOPERATE_TYPE_ALL);
        supplier.setIsOpenOrder(Constants.YES);
        supplier.setOpenSupplierOrder(Boolean.TRUE);
      } else {
        if (BooleanUtil.isTrue(finalOpenSupplierOrder)) {
          supplier.setOpenSupplierOrder(Boolean.TRUE);
          supplier.setCooperateType(Constants.COOPERATE_TYPE_SUPPLIER);
        }
        if (BooleanUtil.isTrue(finalOpenOrder)) {
          supplier.setIsOpenOrder(Constants.YES);
          supplier.setCooperateType(Constants.COOPERATE_TYPE_LANDER);
        }
      }
      List<String> savePlatformCode = new ArrayList<>();
      String enterpriseName = supplier.getEnterpriseName();
      String id = supplier.getId();
      try {
        String platform = supplier.getPlatformRemove();
        if (StrUtil.isNotBlank(platform)) {
          List<OrderPlatformDTO> orderPlatformList =
              platformService.batchFindByCode(ListUtil.toList(supplier.getPlatformRemove().split(",")));
          for (OrderPlatformDTO orderPlatformListDTO : orderPlatformList) {
            savePlatformCode.add(orderPlatformListDTO.getPlatformCode());
            if (supplierPerformanceRepository.getFirstByPlatformCodeAndSupplierIdAndStateOrderByUpdateTimeDesc(
                orderPlatformListDTO.getPlatformCode(), supplier.getId(), Constants.STATE_OK) != null) {
              continue;
            }
            SupplierPerformance supplierPerformance = new SupplierPerformance();
            supplierPerformance.setPlatformCode(orderPlatformListDTO.getPlatformCode());
            supplierPerformance.setPlatformName(orderPlatformListDTO.getPlatformName());
            supplierPerformance.setSupplierId(id);
            supplierPerformanceService.save(supplierPerformance);
          }
        }
      } catch (Exception e) {
        failList.add(enterpriseName);
      }
      if (CollUtil.isNotEmpty(savePlatformCode)) {
        String platform = CollUtil.join(savePlatformCode, ",");
        supplier.setPlatform(platform);
      }
      save(supplier);
    }
    if (CollUtil.isNotEmpty(failList)) {
      log.error("新增落地商信息失败的供应商：");
      log.error(JSON.toJSONString(failList));
      throw new CheckException("新增落地商信息失败的供应商【" + CollUtil.join(failList, ",") + "】");
    }
  }
}
