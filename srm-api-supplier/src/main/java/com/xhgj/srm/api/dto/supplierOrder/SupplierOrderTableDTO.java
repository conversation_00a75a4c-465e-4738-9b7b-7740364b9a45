package com.xhgj.srm.api.dto.supplierOrder;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderRefuseState;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> @ClassName SupplierOrderTableDTO */
@Data
@NoArgsConstructor
public class SupplierOrderTableDTO extends BaseSupplierOrderDTO {

  @ApiModelProperty("采购件数")
  private String number;

  @ApiModelProperty("是否有待确认")
  private Boolean confirmState;

  @ApiModelProperty("是否有取消")
  private Boolean cancelState;

  @ApiModelProperty("是否有退货")
  private Boolean returnState;

  @ApiModelProperty("是否有拒单")
  private Boolean refuseState;

  @ApiModelProperty("开票状态")
  private String supplierOpenInvoiceState;

  public SupplierOrderTableDTO(
      SupplierOrder supplierOrder,boolean hasReturnOrder ) {
    super(supplierOrder);
    this.confirmState = supplierOrder.getOrderConfirmState();
    this.cancelState = supplierOrder.getOrderCancelState();
    this.returnState = supplierOrder.getOrderReturnState() && hasReturnOrder;
    if (Boolean.TRUE.equals(this.cancelState) || Boolean.TRUE.equals(this.returnState)) {
      this.confirmState = false;
    }
    // 如果订单履约状态不为待履约则不待确认按钮
    if (!supplierOrder.getOrderState().equals(SupplierOrderState.WAIT.getKey())) {
      this.confirmState = false;
    }
    this.number =
        supplierOrder.getTotalNum() != null
            ? supplierOrder.getTotalNum().stripTrailingZeros().toPlainString()
            : "";
    this.refuseState =
        StrUtil.equals(supplierOrder.getRefuseState(), SupplierOrderRefuseState.REFUSE.getKey());
    this.supplierOpenInvoiceState = supplierOrder.getSupplierOpenInvoiceState();
  }
}
