package com.xhgj.srm.api.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProductSellAreaExternalUpdateParam {
  /**
   * 物料编码
   */
  @ApiModelProperty("物料编码")
  private String productCode;
  /**
   * 可售区域
   */
  @ApiModelProperty("可售区域")
  private String sellArea;

  /**
   * 数量
   */
  @ApiModelProperty("数量")
  private Long stock;

  /**
   * 添加/扣减
   */
  @ApiModelProperty("添加/扣减")
  private Boolean addFlag;

}
