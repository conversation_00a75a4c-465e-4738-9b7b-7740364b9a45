package com.xhgj.srm.api.dto.order;

import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.jpa.entity.OrderDelivery;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DeliveryOrderDetailDTO {

    @ApiModelProperty("发货单id")
    private String id;
    @ApiModelProperty("发货单号")
    private String deliveryNo;
    @ApiModelProperty("发货时间")
    private String deliveryTime;
    @ApiModelProperty("物流公司")
    private String expressCompany;
    @ApiModelProperty("快递单号")
    private String expressNo;
    @ApiModelProperty("客户订单号")
    private String orderNo;
    @ApiModelProperty("下单平台")
    private String platform;
    @ApiModelProperty("客户单位")
    private String customer;
    @ApiModelProperty("联系方式")
    private String mobile;
    @ApiModelProperty("收件人")
    private String consignee;
    @ApiModelProperty("收件地址")
    private String address;
    @ApiModelProperty("发货单状态")
    private String state;
    @ApiModelProperty("发货商品信息")
    private List<OrderDeliveryDetailDTO> deliveryProductList;

    public DeliveryOrderDetailDTO(OrderDelivery orderDelivery){
        this.id = orderDelivery.getId();
        this.deliveryNo =  StringUtils.emptyIfNull(orderDelivery.getDeliveryNo());
        this.deliveryTime = orderDelivery.getCreateTime() > 0 ? DateUtils.formatTimeStampToNormalDateTime(orderDelivery.getCreateTime()) : "";
        this.expressCompany = StringUtils.emptyIfNull(orderDelivery.getExpressCompany());
        this.expressNo = StringUtils.emptyIfNull(orderDelivery.getExpressNo());
        this.orderNo = orderDelivery.getOrder()!=null?StringUtils.emptyIfNull(orderDelivery.getOrder().getOrderNo()):"";
        this.customer = orderDelivery.getOrder()!=null?StringUtils.emptyIfNull(orderDelivery.getOrder().getCustomer()):"";
        this.platform = orderDelivery.getOrder()!=null?StringUtils.emptyIfNull(orderDelivery.getOrder().getType()):"";
        this.mobile = orderDelivery.getOrder()!=null?StringUtils.emptyIfNull(orderDelivery.getOrder().getMobile()):"";
        this.consignee = orderDelivery.getOrder()!=null?StringUtils.emptyIfNull(orderDelivery.getOrder().getConsignee()):"";
        this.address = orderDelivery.getOrder()!=null?StringUtils.emptyIfNull(orderDelivery.getOrder().getAddress()):"";
        this.state = !StringUtils.isNullOrEmpty(orderDelivery.getDeliveryState())? Constants_order.DELIVERY_STATE_MAP.get(orderDelivery.getDeliveryState()):"待收货";
    }

}
