package com.xhgj.srm.api.dto.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("国网物料表格")
@Data
public class GwPlatformProductDTO {
    /**
     * 国网编码
     */
    @ApiModelProperty("国网物料编码")
    private String gwCode;
    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String code;
    /**
     * 国网商品编号
     */
    @ApiModelProperty("国网商品编码")
    private String gwProductSerialNumber;
    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;
    /**
     * 品牌名称
     */
    @ApiModelProperty("品牌名称")
    private String brandName;
    /**
     * 专区价
     */
    @ApiModelProperty("专区价")
    private String regionalPrice;
    /**
     * 最高可改价
     */
    @ApiModelProperty("最高可改价")
    private String highestChangePrice;
    /**
     * 驳回原因
     */
    @ApiModelProperty("驳回原因")
    private String causeOfRejection;
    /**
     * 是否可以调价
     */
    @ApiModelProperty("是否可以调价，true:可以；false:不可以")
    private boolean isAdjustPrice;

    /**
     * 上次是否驳回
     */
    @ApiModelProperty("上次是否驳回，true:是；false:不是")
    private boolean isLastCause;
}
