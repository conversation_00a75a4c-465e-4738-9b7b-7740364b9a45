package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.entity.SearchScheme;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName SchemeDataDTO
 * Create by Liuyq on 2021/6/18 16:36
 **/
@Data
public class SchemeDataDTO {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "检索方案名称")
    private String name;
    @ApiModelProperty(value = "检索方案类型")
    private String type;
    @ApiModelProperty(value = "是否默认")
    private String isDefault;
    @ApiModelProperty(value = "检索方案内容")
    private String content;

    public SchemeDataDTO(SearchScheme searchScheme){
        this.id = searchScheme.getId();
        this.name = searchScheme.getName();
        this.type = searchScheme.getType();
        this.isDefault = searchScheme.getIsDefault();
        this.content = searchScheme.getContent();
    }
}
