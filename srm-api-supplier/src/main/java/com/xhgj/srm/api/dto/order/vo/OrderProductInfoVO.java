package com.xhgj.srm.api.dto.order.vo;

import com.xhgj.srm.api.dto.order.OrderProductDetailDTO;
import com.xhgj.srm.common.vo.order.OrderCancelVO;
import com.xhgj.srm.dto.order.DeliveryDetailDTO;
import com.xhgj.srm.dto.order.ReturnInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import java.util.List;

/**
 * Created by Geng Shy on 2023/11/24
 */
@Data
@AllArgsConstructor
public class OrderProductInfoVO {

  @ApiModelProperty("商品明细")
  private List<OrderProductDetailDTO> productDetailVO;
  @ApiModelProperty("发货明细")
  private List<DeliveryDetailDTO> deliveryDetailVO;
  @ApiModelProperty("退货明细")
  private List<ReturnInfoDTO> returnInfoVO;
  @ApiModelProperty("取消明细")
  private List<OrderCancelVO> cancelInfoVO;

  private String orderId;
  private String orderNo;

}
