package com.xhgj.srm.api.dto.filing;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.OrderFiling;
import com.xhgj.srm.request.utils.OrderFilingUtil;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class FilingPageDTO {

  @ApiModelProperty("报备单id")
  private String id;
  @ApiModelProperty("报备单号")
  private String filingNo;
  @ApiModelProperty("报备时间")
  private String filingTime;
  @ApiModelProperty("有效期")
  private String validity;
  @ApiModelProperty("平台")
  private String platform;
  @ApiModelProperty("金额")
  private BigDecimal price;
  @ApiModelProperty("客户名称")
  private String customer;
  @ApiModelProperty("状态")
  private String state;
  @ApiModelProperty("客户订单号")
  private String orderNo;
  @ApiModelProperty("报备类型")
  private String filingType;

  public FilingPageDTO(OrderFiling orderFiling, String typeName) {
    this.id = orderFiling.getId();
    this.filingNo = orderFiling.getFilingNo();
    this.filingTime = orderFiling.getFilingTime() > 0 ? DateUtils.formatTimeStampToNormalDateTime(
        orderFiling.getFilingTime()) : "";
    validity = OrderFilingUtil.getValidity(orderFiling);
    this.platform = StrUtil.emptyIfNull(typeName);
    this.price = orderFiling.getPrice();
    this.customer = StringUtils.emptyIfNull(orderFiling.getCustomer());
    this.state = orderFiling.getFilingState() == null ? "" : orderFiling.getFilingState();
    this.orderNo = StrUtil.isNotBlank(orderFiling.getOrderNo()) ? orderFiling.getOrderNo() : "-";
    this.filingType = Constants.FILING_TYPE.get(orderFiling.getFilingType()) != null ?
        Constants.FILING_TYPE.get(orderFiling.getFilingType()) : "";
  }
}
