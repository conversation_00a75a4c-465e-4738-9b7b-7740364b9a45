package com.xhgj.srm.api.dto;

import com.xhgj.srm.api.dto.scheme.BaseSchemeSearchDTO;
import java.util.List;
import lombok.Data;

@Data
public class SupplierOrderSchemeDTO extends BaseSchemeSearchDTO {

  /**
   * 客户订单号
   */
  private String orderNo;
  /**
   * 订单状态
   */
  private String orderState;
  /**
   * 下单平台
   */
  private String platform;
  /**
   * 下单金额
   */
  private String price;
  /**
   * 客户名称
   */
  private String customer;
  /**
   * 下单时间
   */
  private String orderStartTime;
  /**
   * 开始时间
   */
  private String beginTime;
  /**
   * 结束时间
   */
  private String endTime;
  /**
   * 开票状态
   */
  private List<String> invoicingState;
  /**
   * 发票类型
   */
  private String type;
  /**
   * 发票抬头
   */
  private String title;
  private String excludeInvoicingState;
  /**
   * 派单时间 - 范围开始时间
   */
  private String dispatchStartTime;
  /**
   * 派单时间 - 范围结束时间
   */
  private String dispatchEndTime;
  /**
   * 发票号码
   */
  private String invoiceNum;
}
