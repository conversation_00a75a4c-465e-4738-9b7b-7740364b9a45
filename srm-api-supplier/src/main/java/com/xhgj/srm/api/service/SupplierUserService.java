package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.*;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.Optional;
import org.springframework.security.core.userdetails.UserDetails;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @ClassName SupplierUserService
 * Create by Liuyq on 2021/6/8 19:02
 **/
public interface SupplierUserService extends BootBaseService<SupplierUser, String> {


    /**
     * 新增账号
     * @Author: liuyq
     * @Date: 2021/6/8 19:10
     * @param addParam
     * @return com.xhgj.srm.jpa.entity.SupplierUser
     **/
    SupplierUser addSupplierUser(SupplierUserAddParam addParam);


    /**
     * 获取账号列表
     * @Author: liuyq
     * @Date: 2021/6/9 9:01
     * @param supplierId
     * @param name
     * @param mobile
     * @param mail
     * @param realName
     * @param schemeId
     * @param pageNo
     * @param pageSize
     * @return com.xhiot.boot.mvc.base.PageResult<com.xhgj.srm.api.dto.SupplierUserPageData>
     **/
    PageResult<SupplierUserPageData> getSupplierUserPage(String supplierId, String name, String mobile, String mail, String realName, String schemeId, String pageNo, String pageSize);


    /**
     * 批量删除账号
     * @Author: liuyq
     * @Date: 2021/6/9 9:17
     * @param deleteParam
     * @return void
     **/
    void deleteSupplierUserById(ArrayBaseParam deleteParam);


    /**
     * 修改账号密码
     * @Author: liuyq
     * @Date: 2021/6/9 9:31
     * @param updateParam
     * @return void
     **/
    void updatePasswordBySupplierUserIds(ArrayBaseParam updateParam);

    /**
     * 移交管理员
     * @Author: liuyq
     * @Date: 2021/6/9 9:38
     * @param transferAdminParam
     * @return void
     **/
    void transferAdminBySupplierUserId(TransferAdminParam transferAdminParam);


    /**
     * 账号登陆
     * @Author: liuyq
     * @Date: 2021/6/9 15:37
     * @param name
     * @param pwd
     * @return com.xhgj.srm.api.dto.SupplierUserLoginData
     **/
    SupplierUserLoginData supplierUserLogin(HttpServletRequest request, HttpServletResponse response,String name, String pwd, String code);


    /**
     * 根据名字获取账号信息
     * @Author: liuyq
     * @Date: 2021/6/9 15:37
     * @param name
     * @return org.springframework.security.core.userdetails.UserDetails
     **/
    UserDetails loadSupplierUserByName(String name);


    /**
     * 获取账号信息
     * @Author: liuyq
     * @Date: 2021/6/9 15:37
     * @param supplierUser
     * @return org.springframework.security.core.userdetails.UserDetails
     **/
    UserDetails loadSupplierUserInfo(SupplierUser supplierUser);


    /**
     * 获取账号详情
     * @Author: liuyq
     * @Date: 2021/6/9 15:38
     * @param supplierUserId
     * @return com.xhgj.srm.api.dto.SupplierUserData
     **/
    SupplierUserData getSupplierUserById(String supplierUserId);


    /**
     * 重置密码--个人中心
     * @Author: liuyq
     * @Date: 2021/6/9 15:45
     * @param updateParam
     * @return void
     **/
    SupplierUser updatePasswordBySupplierUserId(SupplierUserUpdatePasswordParam updateParam);


    /**
     * 生成验证码
     * @Author: liuyq
     * @Date: 2021/6/28 8:55
     * @param response
     * @return void
     **/
    String getCodeImg(HttpServletResponse response);

  /**
   * 查询用户真实姓名
   */
  Optional<String> getSupplierUserRealName(String userId);
}
