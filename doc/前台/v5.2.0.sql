use
srm;
ALTER TABLE t_order
  ADD c_payment_status varchar(2) NULL COMMENT '付款状态';

CREATE TABLE `t_order_payment`
(
  `id`               varchar(32) NOT NULL COMMENT '主键',
  `c_payment_status` varchar(2)      DEFAULT NULL COMMENT '付款状态',
  `c_apply_price`    decimal(20, 10) DEFAULT NULL COMMENT '申请付款金额',
  `c_submit_man`     varchar(100)    DEFAULT NULL COMMENT '提交人',
  `c_submit_id`      varchar(32)     DEFAULT NULL COMMENT '提交人 id',
  `supplier_id`      varchar(32)     DEFAULT NULL COMMENT '供应商 id',
  `c_create_time`    bigint(20) DEFAULT NULL COMMENT '提交时间',
  `c_remark`         varchar(140)    DEFAULT NULL COMMENT '备注',
  `c_payment_no`     varchar(100)    DEFAULT NULL COMMENT '付款单号',
  `c_payment_price`  decimal(20, 10) DEFAULT NULL COMMENT '已回款金额',
  `c_state`  varchar(2) DEFAULT NULL COMMENT '数据状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单付款表';

-- srm.t_order_payment_to_order definition

CREATE TABLE `t_order_payment_to_order`
(
  `id`                 varchar(32) NOT NULL COMMENT '主键',
  `c_order_payment_id` varchar(32)     DEFAULT NULL COMMENT '付款单信息 id',
  `relation_id`        varchar(32)     DEFAULT NULL COMMENT '关联 id',
  `c_type`             varchar(2)      DEFAULT NULL COMMENT '类型',
  `c_create_time`      bigint(20) DEFAULT NULL COMMENT '创建时间',
  `c_state`            varchar(1)      DEFAULT NULL COMMENT '数据状态',
  `c_content`          text COMMENT '内容',
  `c_payment_price`    decimal(20, 10) DEFAULT NULL COMMENT '回款金额',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单付款的中间表';

-- 订单付款表新增订单数量字段
ALTER TABLE t_order_payment ADD c_order_count varchar(10) NULL COMMENT '订单数量';

-- ******** 临时需求 处理历史的付款单状态
use srm;
update t_order set c_payment_status = '2' where (c_payment_status is null  or c_payment_status = '') and c_confirm_voucher_time is not null
    and c_customer_return_progress = '2' and c_account_open_invoice_status = '2';

update t_order set c_payment_status = '1' where (c_payment_status is null  or c_payment_status = '') ;

