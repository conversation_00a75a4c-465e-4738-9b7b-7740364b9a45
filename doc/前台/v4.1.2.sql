use srm;
CREATE TABLE `t_notice_center` (
  `id` varchar(32) NOT NULL,
  `supplierId` varchar(32) DEFAULT NULL COMMENT '供应商id',
  `supplierOrderId` varchar(32) DEFAULT NULL COMMENT '供应商订单id',
  `c_content` varchar(200) DEFAULT NULL COMMENT '通知内容',
  `c_type` varchar(1) DEFAULT NULL COMMENT '通知类型',
  `c_is_read` tinyint(1) DEFAULT NULL COMMENT '是否已读',
  `c_state` varchar(1) DEFAULT NULL COMMENT '数据状态',
  `c_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;