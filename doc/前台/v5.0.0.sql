use srm;
ALTER TABLE t_file
    ADD c_upload_man varchar(50) NULL COMMENT '文件上传人';
ALTER TABLE t_order
    ADD c_first_ship_time BIGINT(20) NULL COMMENT '第一次发货时间';
ALTER TABLE t_order
    ADD c_confirm_voucher_time BIGINT(20) NULL COMMENT '确认验收凭证时间';
ALTER TABLE t_order
    ADD c_complete_time BIGINT(20) NULL COMMENT '订单完成时间';

CREATE TABLE `t_order_invoice`
(
    `id`                varchar(32) NOT NULL COMMENT '主键',
    `order_id`          varchar(32)  DEFAULT NULL COMMENT '落地商订单 id',
    `c_type`            varchar(2)   DEFAULT NULL COMMENT '发票类型',
    `c_title`           varchar(140) DEFAULT NULL COMMENT '发票抬头',
    `c_tax_number`      varchar(20)  DEFAULT NULL COMMENT '税号',
    `c_bank_name`       varchar(140) DEFAULT NULL COMMENT '开户银行',
    `c_bank_account`    varchar(20)  DEFAULT NULL COMMENT '银行账号',
    `c_mobile`          varchar(20)  DEFAULT NULL COMMENT '电话',
    `c_address`         varchar(140) DEFAULT NULL COMMENT '地址',
    `c_content`         varchar(500) DEFAULT NULL COMMENT '票面信息',
    `c_receive_man`     varchar(20)  DEFAULT NULL COMMENT '收件人',
    `c_receive_address` varchar(50)  DEFAULT NULL COMMENT '收件人地址',
    `c_remark`          varchar(500) DEFAULT NULL COMMENT '其它备注',
    `c_receive_mobile`  varchar(50)  DEFAULT NULL COMMENT '收件人联系电话',
    `c_create_time`     bigint(20)   DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='落地商订单发票信息';

-- srm.t_order_open_invoice definition

CREATE TABLE `t_order_open_invoice`
(
    `id`                  varchar(32) NOT NULL COMMENT '主键',
    `order_invoice_id`    varchar(32)     DEFAULT NULL COMMENT '发票信息 id',
    `c_invoice_num`       varchar(50)     DEFAULT NULL COMMENT '发票号',
    `c_invoice_code`      varchar(100)    DEFAULT NULL COMMENT '发票代码',
    `c_invoice_time`      bigint(20)      DEFAULT NULL COMMENT '发票申请时间',
    `c_price`             decimal(20, 10) DEFAULT NULL COMMENT '含税金额',
    `c_logistics_company` varchar(20)     DEFAULT NULL COMMENT '物流公司',
    `c_logistics_num`     varchar(100)    DEFAULT NULL COMMENT '物流单号',
    `file_id`             varchar(32)     DEFAULT NULL COMMENT '发票附件 id',
    `c_create_time`       bigint(20)      DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='落地商订单开票信息';

-- srm.t_order_accept definition

CREATE TABLE `t_order_accept`
(
    `id`                varchar(32) NOT NULL COMMENT '主键',
    `order_id`          varchar(32)  DEFAULT NULL COMMENT '落地商订单 id',
    `c_file_ids`        varchar(500) DEFAULT NULL COMMENT '文件 id 多个，分隔',
    `c_create_time`     bigint(20)   DEFAULT NULL COMMENT '创建时间',
    `c_fist_upload_man` varchar(100) DEFAULT NULL COMMENT '上传人',
    `c_type`            varchar(1)   DEFAULT NULL COMMENT '类型',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='落地商 id';
  
  
  
-- 临时需求第82条旧数据处理，订单状态改为已完成，签收凭证未已确认
UPDATE t_order
SET c_order_state = '4',
c_confirm_voucher_time = 1677231000000
WHERE
	c_order_no IN (
	'PR04549501',
	'PR04549503',
	'PR04548373',
	'PR04504658',
	'PR04503453',
	'PR04501769',
	'PR04501355',
	'PR04505074',
	'PR04510439',
	'PR04510445',
	'PR04510469',
	'PR04505200',
	'PR04505783',
	'PR04526934',
	'PR04536012',
	'PR04535754',
	'PR04535263',
	'PR04535233',
	'PR04553300',
	'PR04553337',
	'PR04556990',
	'PR04555834',
	'PR04555520',
	'PR04557541',
	'PR04559000',
	'PR04563261',
	'PR04566542',
	'PR04566415',
	'PR04566164',
	'PR04604800',
	'PR04607702',
	'PR04639632',
	'PR04639397',
	'PR04639997',
	'PR04640014',
	'PR04608863',
	'PR04608416',
	'PR04613785',
	'PR04613762',
	'PR04626071',
	'PR04631661',
	'PR04630138',
	'PR04642933',
	'PR04642932',
	'PR04642659',
	'PR04642805',
	'PR04643241',
	'PR04643186',
	'PR04644442',
	'PR04648002',
	'PR04582170',
	'PR04597000',
	'PR04640280',
	'PR04640935',
	'PR04629626',
	'PR04632556',
	'PR04633557',
	'PR04536600',
	'PR04621297',
	'PR04621241',
	'PR04600492',
	'PR04632527',
	'PR04605824',
	'PR04531719'
	);
