-- srm.t_order_open_invoice definition
#订单开票申请表和订单表中间表
-- srm.t_order_invoice_to_order definition

CREATE TABLE `t_order_invoice_to_order` (
                                            `id` varchar(32) NOT NULL COMMENT 'id',
                                            `order_invoice_id` varchar(32) NOT NULL COMMENT '落地商订单开票信息id',
                                            `order_id` varchar(32) NOT NULL COMMENT '落地商订单id',
                                            `c_create_time` bigint(20) NOT NULL DEFAULT '-1' COMMENT '创建时间',
                                            `c_state` char(1) NOT NULL COMMENT '状态',
                                            `c_update_time` bigint(20) DEFAULT '-1' COMMENT '修改时间',
                                            `update_user` varchar(32) DEFAULT NULL COMMENT '最后更新的用户id',
                                            `create_user` varchar(32) DEFAULT NULL COMMENT '创建的用户id',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='落地商订单和订单开票信息对应表';


#订单开票申请表增加列
ALTER TABLE srm.t_order_invoice ADD c_invoice_application_number varchar(50) NOT NULL;

# 订单和报备单增加小类（执行七步骤） 20230511
1. alter table t_order add c_sub_type varchar(50) COMMENT '对接平台小类';

2. alter table t_order_filing add c_sub_type varchar(50) COMMENT '对接平台小类';

3. update t_order o set o.c_sub_type = '3201', o.c_type = '32', o.c_type_name = '中国交建' where o.c_type = '3201';

4. update t_order_filing o set o.c_sub_type = '32',o.c_type = '32',c_type_name = '中国交建' where o.c_type = '3201';

5. update t_supplier_performance o set o.c_platform_code = '32',c_platform_name = '中国交建' where o.c_platform_code = '3201';

 # 手动删除一个同一个供应商中 存在两个c_platform_code 为32的数据
6. select *from t_supplier_performance where c_state = '1' and c_platform_code = '32' group by supplier_id having count(supplier_id)>=2  ;

# 查询改语句，然后手动将c_platform字段中3201 改成32
7.select *from t_supplier where c_platform like '%3201%';
#物料表添加下单平台字段
ALTER TABLE t_product ADD c_platform varchar(32) NULL COMMENT '上架项目/下单平台';

-- srm_prod.t_order_payment_collection definition

CREATE TABLE `t_order_payment_collection` (
                                            `id` varchar(32) NOT NULL,
                                            `c_payment_no` varchar(32) DEFAULT NULL COMMENT '付款单号',
                                            `c_business_create_time` bigint(13) DEFAULT NULL COMMENT '业务创建日期',
                                            `c_amount` decimal(20,10) DEFAULT NULL COMMENT '金额',
                                            `c_payment_time` bigint(13) DEFAULT NULL COMMENT '付款日期',
                                            `c_create_time` bigint(13) DEFAULT NULL COMMENT '创建时间',
                                            `c_update_time` bigint(13) DEFAULT NULL COMMENT '更新时间',
                                            `c_create_man` varchar(32) DEFAULT NULL COMMENT '创建人',
                                            `c_update_man` varchar(32) DEFAULT NULL COMMENT '更新人',
                                            `order_payment_id` varchar(32) DEFAULT NULL COMMENT '订单付款单id',
                                            `c_state` varchar(1) DEFAULT NULL COMMENT '数据状态',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='付款单回款信息';



