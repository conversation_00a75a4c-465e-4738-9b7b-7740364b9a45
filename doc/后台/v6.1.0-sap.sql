-- srm_prod.t_purchase_apply_for_order definition

CREATE TABLE `t_purchase_apply_for_order` (
                                            `id` varchar(32) NOT NULL COMMENT 'id',
                                            `c_apply_for_order_no` varchar(10) DEFAULT NULL COMMENT '采购申请单号',
                                            `c_apply_for_type` varchar(4) DEFAULT NULL COMMENT '申请类型',
                                            `c_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                            `c_factory_code` varchar(4) DEFAULT NULL COMMENT '工厂代码',
                                            `c_company_code` varchar(4) DEFAULT NULL COMMENT '公司代码',
                                            `purchase_department` varchar(32) DEFAULT NULL COMMENT '采购部门，可能存t_group的id',
                                            `c_warehouse` varchar(32) DEFAULT NULL COMMENT '仓库',
                                            `c_delivery_address` varchar(500) DEFAULT NULL COMMENT '收货地址',
                                            `c_sold_to_party` varchar(100) DEFAULT NULL COMMENT '售达方',
                                            `c_row_id` varchar(5) DEFAULT NULL COMMENT '物料行id',
                                            `c_product_code` varchar(32) DEFAULT NULL COMMENT '物料编码',
                                            `c_prodcut_name` varchar(100) DEFAULT NULL COMMENT '物料名称',
                                            `c_brand` varchar(100) DEFAULT NULL COMMENT '品牌',
                                            `c_model` varchar(100) DEFAULT NULL COMMENT '规格型号',
                                            `c_unit` varchar(100) DEFAULT NULL COMMENT '单位',
                                            `c_apply_for_number` decimal(20,10) DEFAULT NULL COMMENT '申请数量',
                                            `c_plan_demand_date` bigint(20) DEFAULT NULL COMMENT '计划需求日期',
                                            `c_purchase_man` varchar(100) DEFAULT NULL COMMENT '采购员',
                                            `c_sale_order_no` varchar(100) DEFAULT NULL COMMENT '销售订单号',
                                            `c_sale_order_product_row_id` varchar(4) DEFAULT NULL COMMENT '销售订单物料行id',
                                            `c_order_goods_state` varchar(1) DEFAULT NULL COMMENT '订货状态',
                                            `c_cancellation_state` varchar(1) DEFAULT NULL COMMENT '取消状态',
                                            `c_state` varchar(1) DEFAULT NULL COMMENT '数据状态',
                                            `c_push_down_state` varchar(1) DEFAULT NULL COMMENT '下推状态 0 未下推 1下推成功',
                                            `c_order_goods_number` decimal(20,10) DEFAULT NULL COMMENT '已订货数量',
                                            `c_serial_number` int(11) DEFAULT NULL COMMENT '序号',
                                            `c_description` varchar(500) DEFAULT NULL COMMENT '描述',
                                            `c_create_user` varchar(32) DEFAULT NULL COMMENT '创建人',
                                            `c_update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
                                            `c_update_user` varchar(32) DEFAULT NULL COMMENT '修改人',
                                            PRIMARY KEY (`id`),
                                            KEY `t_purchase_order_c_apply_for_number_IDX` (`c_apply_for_order_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购申请单';

ALTER TABLE t_supplier_order ADD COLUMN `c_order_type` varchar(10) DEFAULT NULL COMMENT '订单类型 1 金蝶采购订单 NB 标准采购（SAP）Z040 寄售 Z010 委外加工';
ALTER TABLE t_supplier_order ADD COLUMN `c_purchase_dept` varchar(50) DEFAULT NULL COMMENT '采购部门';
ALTER TABLE t_supplier_order ADD COLUMN `c_purchase_dept_code` varchar(20) DEFAULT NULL COMMENT '采购部门编码';
ALTER TABLE t_supplier_order ADD COLUMN `c_invoicing_party` varchar(50) DEFAULT NULL COMMENT '开票方';
ALTER TABLE t_supplier_order ADD COLUMN `c_money_code` varchar(10) DEFAULT NULL COMMENT '货币码';
ALTER TABLE t_supplier_order ADD COLUMN `c_order_rate` varchar(10) DEFAULT NULL COMMENT '订单汇率';
ALTER TABLE t_supplier_order ADD COLUMN `c_pay_condition` varchar(10) DEFAULT NULL COMMENT '付款条件 1 货物入库 2供应商开票 3客户回款';
ALTER TABLE t_supplier_order ADD COLUMN `c_reject_reason` varchar(500) DEFAULT NULL COMMENT '驳回原因';
ALTER TABLE t_supplier_order ADD COLUMN `c_create_man` varchar(32) DEFAULT NULL COMMENT '创建人';

ALTER TABLE t_supplier_order ADD COLUMN `c_account_period` varchar(10) DEFAULT NULL COMMENT '账期';
ALTER TABLE t_supplier_order_detail ADD COLUMN `purchase_apply_for_order_id` varchar(32) DEFAULT NULL COMMENT '采购申请单id';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_warehouse` varchar(20) DEFAULT NULL COMMENT '仓库';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_total_amount_including_tax` decimal(20,10) DEFAULT NULL COMMENT '价税合计';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_tax_rate` decimal(10,2) DEFAULT NULL COMMENT '税率';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_free_state` varchar(2) DEFAULT NULL COMMENT '是否免费 0 否 1是';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_project_type` varchar(10) DEFAULT NULL COMMENT '项目类别';
ALTER TABLE t_supplier_order ADD c_customer_invoicing_state varchar(1) NULL COMMENT '客户开票状态';
ALTER TABLE t_supplier_order ADD c_customer_payment_collection_state varchar(1) NULL COMMENT '客户回款状态';
ALTER TABLE t_supplier_order_detail ADD c_purchase_deliver_time bigint(20) NULL COMMENT '采购交货时间';
ALTER TABLE t_supplier_order_detail MODIFY COLUMN c_deliver_time bigint(20) NULL COMMENT '供应商交货时间';

ALTER TABLE t_supplier_order_detail ADD COLUMN `c_entrust_detail_id` varchar(32) DEFAULT NULL COMMENT '委外加工 详情id';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_sap_row_id` varchar(10) DEFAULT NULL COMMENT 'sap行id';
ALTER TABLE t_supplier_order_to_form ADD COLUMN `c_product_voucher` varchar(50) DEFAULT NULL COMMENT '物料凭证';
ALTER TABLE t_supplier_order_to_form ADD COLUMN `c_product_voucher_year` varchar(20) DEFAULT NULL COMMENT '物料凭证年度';
ALTER TABLE t_supplier_order_to_form ADD COLUMN `c_return_reason` varchar(225) DEFAULT NULL COMMENT '退库原因';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_in_warehouse_id` varchar(32) DEFAULT NULL COMMENT '入库单id';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_in_warehouse_name` varchar(20) DEFAULT NULL COMMENT '入库单名称';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_batch_no` varchar(20) DEFAULT NULL COMMENT '批号';

ALTER TABLE t_supplier_order_detail ADD COLUMN `c_product_rate` decimal(10,2) DEFAULT NULL COMMENT '物料税率';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_markup_coefficient` decimal(10,2) DEFAULT NULL COMMENT '加价系数';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_transfer_price` decimal(10,2) DEFAULT NULL COMMENT 'mpm参考调拨价';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_surcharge` decimal(10,2) DEFAULT NULL COMMENT '附加费';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_settlement_price` decimal(10,2) DEFAULT NULL COMMENT '结算单价';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_total_settlement_price` decimal(10,2) DEFAULT NULL COMMENT '结算总价';


ALTER TABLE srm_prod.t_supplier_order_to_form ADD c_sap_reversal_no varchar(50) NULL COMMENT 'SAP 冲销物料凭证号';

ALTER TABLE srm_prod.t_supplier_order_to_form ADD c_deliver_form_id varchar(32) NULL COMMENT '关联发货单 id';
ALTER TABLE srm_prod.t_supplier_order_detail ADD c_sap_reversal_row_no varchar(10) NULL COMMENT '冲销凭证行号';

ALTER TABLE t_supplier_in_group ADD c_block_range varchar(1) NULL COMMENT '拉黑范围， 1不允许做账；2不允许做单；3不允许做账和做单';

ALTER TABLE t_assess ADD c_block_range varchar(1) NULL COMMENT '拉黑范围， 1不允许做账；2不允许做单；3不允许做账和做单';
ALTER TABLE t_supplier ADD COLUMN `c_region` varchar(20) DEFAULT NULL COMMENT '区域';
ALTER TABLE t_supplier ADD COLUMN `c_abbreviation` varchar(10) DEFAULT NULL COMMENT '企业简称';
ALTER TABLE t_supplier_temp ADD COLUMN `c_abbreviation` varchar(10) DEFAULT NULL COMMENT '企业简称';
ALTER TABLE t_supplier_temp ADD COLUMN `c_region` varchar(20) DEFAULT NULL COMMENT '区域';
ALTER TABLE t_supplier_in_group ADD COLUMN `c_abbreviation` varchar(10) DEFAULT NULL COMMENT '企业简称';
ALTER TABLE t_supplier_in_group_temp ADD COLUMN `c_abbreviation` varchar(10) DEFAULT NULL COMMENT '企业简称';

ALTER TABLE t_supplier MODIFY COLUMN `c_enterpriseName` varchar(250) DEFAULT NULL ;
ALTER TABLE t_supplier ADD COLUMN `c_pay_type` varchar(50) DEFAULT NULL COMMENT '付款方式';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_open_invoice_state` varchar(2) DEFAULT NULL COMMENT '入库单物料行开票状态 0未开票 1已开票';
ALTER TABLE t_supplier_order_detail ADD COLUMN `c_invoicable_num` decimal(20,10) DEFAULT NULL COMMENT '入库单物料行可开票数量';

ALTER TABLE t_order_invoice_relation  ADD COLUMN `c_invoice_voucher_number` varchar(20) DEFAULT NULL COMMENT 'sap发票凭证号';
ALTER TABLE t_order_invoice_relation  ADD COLUMN `c_accounting_year` varchar(20)  DEFAULT NULL COMMENT 'sap会计年度';
ALTER TABLE t_order_invoice_relation  ADD COLUMN `c_financial_vouchers` varchar(20)  DEFAULT NULL COMMENT 'sap财务凭证号';

=========================旧数据处理===============================

update t_supplier set c_region = '东北' WHERE c_province like '%黑龙江%' or c_province = '%吉林%' or c_province = '%辽宁%';
update t_supplier set c_region = '华东' WHERE c_province like '%上海%' or c_province like '%江苏%' or c_province like '%浙江%' or c_province like '%安徽%' or c_province like '%福建%' or c_province like '%江西%' or c_province like '%山东%' or c_province like '%台湾%';
update t_supplier set c_region = '华北' WHERE c_province like '%北京%' or c_province like '%天津%' or c_province like '%山西%' or c_province like '%河北%' or c_province like '%内蒙古%';
update t_supplier set c_region = '华中' WHERE c_province like '%河南%' or c_province like '%湖北%' or c_province like '%湖南%';
update t_supplier set c_region = '华南' WHERE c_province like '%广东%' or c_province like '%广西%' or c_province like '%海南%' or c_province like '%香港%'  or c_province like '%澳门%';
update t_supplier set c_region = '西南' WHERE c_province like '%四川%' or c_province like '%贵州%' or c_province like '%云南%' or c_province like '%重庆%'  or c_province like '%西藏%';
update t_supplier set c_region = '西北' WHERE c_province like '%陕西%' or c_province like '%甘肃%' or c_province like '%青海%' or c_province like '%宁夏%'  or c_province like '%新疆%';


================================================================

CREATE TABLE `t_supplier_invoice_to_detail` (
                                              `id` varchar(32) NOT NULL COMMENT 'id',
                                              `c_invoice_number` varchar(32) DEFAULT NULL COMMENT '发票号',
                                              `c_detail_id` varchar(32) DEFAULT NULL COMMENT '订单明细id',
                                              `c_invoice_num` decimal(20,10) DEFAULT NULL COMMENT '本次开票数量',
                                              `c_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                              `c_tax_free_amount` decimal(10,2) DEFAULT NULL COMMENT '本次去税金额',
                                              `c_open_tax_amount` decimal(10,2) DEFAULT NULL COMMENT '本次开票金额',
                                              `c_tax_amount` decimal(10,2) DEFAULT NULL COMMENT '本次开票税额',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票与入库单关联表';

ALTER TABLE t_supplier_order_detail ADD purchase_order_id varchar(32) NULL COMMENT '采购订单id';
ALTER TABLE t_order_invoice_relation  ADD COLUMN `c_manage_flag` varchar(2)  DEFAULT NULL COMMENT '是否来源后台 0否 1是';
ALTER TABLE t_order_invoice_relation  ADD COLUMN `c_association_type` varchar(10)  DEFAULT NULL COMMENT '关联类型 订单 入库单 明细';
ALTER TABLE t_order_supplier_invoice  ADD COLUMN `c_remark` varchar(255)  DEFAULT NULL COMMENT '发票备注';
ALTER TABLE t_order_invoice_relation  ADD COLUMN `c_group_code` varchar(20)  DEFAULT NULL COMMENT '组织编码';
ALTER TABLE t_order_invoice_relation ADD c_offset_accounting_year varchar(32) NULL COMMENT 'sap冲销会计年度';
ALTER TABLE t_order_invoice_relation ADD c_reversal_date varchar(32) NULL COMMENT 'sap冲销日期';
ALTER TABLE t_order_invoice_relation ADD c_reversal_voucher_no varchar(32) NULL COMMENT 'sap财务冲销凭证号';

ALTER TABLE t_order_invoice_relation ADD COLUMN `c_account_period` varchar(10) DEFAULT NULL COMMENT '账期';
ALTER TABLE t_order_invoice_relation ADD COLUMN `c_pay_type` varchar(50) DEFAULT NULL COMMENT '付款方式';

CREATE TABLE `t_financial_vouchers` (
                                      `id` varchar(32) NOT NULL COMMENT 'id',
                                      `c_financial_voucher_no` varchar(32) DEFAULT NULL COMMENT 'sap财务凭证号',
                                      `c_accounting_year` varchar(32) DEFAULT NULL COMMENT 'sap会计年度',
                                      `c_voucher_type` varchar(1) DEFAULT NULL COMMENT '凭证类型',
                                      `c_invoice_order_no` varchar(32) DEFAULT NULL COMMENT '进项发票单号',
                                      `c_voucher_price` decimal(20,10) DEFAULT NULL COMMENT '凭证金额',
                                      `c_voucher_payment_state` varchar(1) DEFAULT NULL COMMENT '凭证付款状态(与sap交互)',
                                      `c_base_date` bigint(20) DEFAULT NULL COMMENT '基准日期',
                                      `c_account_period` varchar(100) DEFAULT NULL COMMENT '账期',
                                      `c_expected_payment_date` bigint(20) DEFAULT NULL COMMENT '预计付款日期',
                                      `c_payment_type` varchar(20) DEFAULT NULL COMMENT '付款方式',
                                      `c_payment_freeze_status` varchar(1) DEFAULT NULL COMMENT '付款冻结状态',
                                      `purchase_order_no` varchar(32) DEFAULT NULL COMMENT '采购订单号',
                                      `supplier_id` varchar(32) DEFAULT NULL COMMENT '供应商id',
                                      `c_group_code` varchar(50) DEFAULT NULL COMMENT '组织编码',
                                      `c_payment_terms` varchar(10) DEFAULT NULL COMMENT '付款条件',
                                      `c_customer_collection_state` varchar(1) DEFAULT NULL COMMENT '1== 已完成 0 == 未完成',
                                      `c_order_amount` decimal(20,10) DEFAULT NULL COMMENT '订货金额',
                                      `c_related_amount` decimal(20,10) DEFAULT NULL COMMENT '关联金额',
                                      `c_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                      `c_update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
                                      `c_create_man` varchar(32) DEFAULT NULL COMMENT '创建人',
                                      `c_update_man` varchar(32) DEFAULT NULL COMMENT '更新人',
                                      `c_state` varchar(1) DEFAULT NULL COMMENT '数据状态',
                                      `c_sap_pay_type` varchar(10) DEFAULT NULL COMMENT 'sap返回的付款状态',
                                      `c_sap_pay_vouchers` varchar(20) DEFAULT NULL COMMENT 'sap返回的付款凭证编号',
                                      `c_sap_pay_expire` varchar(30) DEFAULT NULL COMMENT 'sap返回的付款凭证过期日期',
                                      `c_sap_apply_code` varchar(10) DEFAULT NULL COMMENT 'sap返回的支付申请状态编码',
                                      `c_sap_apply_name` varchar(20) DEFAULT NULL COMMENT 'sap返回的支付申请状态名称',
                                      `c_voucher_payment_state_name` varchar(20) DEFAULT NULL COMMENT '凭证付款状态名称',
                                      `c_file_url` varchar(500) DEFAULT NULL COMMENT '银行回单下载地址',
                                      PRIMARY KEY (`id`),
                                      KEY `t_financial_vouchers_supplier_id_IDX` (`supplier_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
CREATE TABLE `t_payment_apply_record` (
                                        `id` varchar(32) NOT NULL COMMENT '主键',
                                        `c_payment_apply_no` varchar(32) DEFAULT NULL COMMENT '申请单号',
                                        `c_apply_type` varchar(2) DEFAULT NULL COMMENT '申请类型 1 预付申请 2延长申请 3加急申请 4提款申请 5冻结申请 6退款申请 7 解冻申请',
                                        `c_reject_reason` varchar(500) DEFAULT NULL COMMENT '驳回理由',
                                        `c_supplier_order_no` varchar(500) DEFAULT NULL COMMENT '申请订单号 多个',
                                        `c_invoice_number` varchar(500) DEFAULT NULL COMMENT '进项票发票号 多个',
                                        `c_apply_man` varchar(20) DEFAULT NULL COMMENT '申请人',
                                        `c_apply_time` bigint(32) DEFAULT NULL COMMENT '申请时间',
                                        `c_audit_time` bigint(32) DEFAULT NULL COMMENT '审核时间',
                                        `c_update_time` bigint(32) DEFAULT NULL COMMENT '修改时间',
                                        `c_state` varchar(2) DEFAULT NULL COMMENT '数据状态',
                                        `c_apply_state` varchar(2) DEFAULT NULL COMMENT '申请状态  1 审核中 2 通过 3 驳回 4 已放弃',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='付款申请记录表';



CREATE TABLE `t_purchase_info_record`
(
  `id`                   varchar(32) NOT NULL COMMENT '主键',
  `c_erp_code`           varchar(10)    DEFAULT NULL COMMENT 'ERP 编码（SAP 记录编号）',
  `c_org_code`           varchar(20)    DEFAULT NULL COMMENT '采购组织编码（该表的数据根据组织编码隔离）',
  `c_dept_code`          varchar(20)    DEFAULT NULL COMMENT '采购部门编码',
  `c_supplier_code`      varchar(10)    DEFAULT NULL COMMENT '供应商编码',
  `c_product_code`       varchar(40)    DEFAULT NULL COMMENT '物料编码',
  `c_order_code` varchar(20) DEFAULT NULL COMMENT '订单号（该条记录是根据采购订单生成时会绑定订单号）',
  `c_delivery_days`      int(11)        DEFAULT NULL COMMENT '计划交货时间',
  `c_valid_date_begin`   bigint(20)     DEFAULT NULL COMMENT '有效期起始日（记录当日 00:00）',
  `c_valid_date_end`     bigint(20)     DEFAULT NULL COMMENT '有效期截止日（记录当日 23:59）',
  `c_newest`             bool           DEFAULT NULL COMMENT '是否是该物料最新产生的一条采购信息记录（即当前有效的那条）',
  `c_dept_name`         varchar(50)     DEFAULT NULL COMMENT '采购部门名称（快照）',
  `c_product_name`       varchar(100)   DEFAULT NULL COMMENT '物料名称',
  `c_product_brand_name` varchar(200)   DEFAULT NULL COMMENT '物料品牌名',
  `c_product_manu_code`  varchar(100)   DEFAULT NULL COMMENT '物料规格型号',
  `c_product_tax_rate`   varchar(5)     DEFAULT NULL COMMENT '物料税率（13%）',
  `c_product_tax_price` decimal(20, 10) DEFAULT NULL COMMENT '物料含税单价',
  `c_product_unit`       varchar(5)     DEFAULT NULL COMMENT '物料单位（个、包）',
  `c_product_currency`   varchar(10)    DEFAULT NULL COMMENT '物料币别（人民币、美元）',
  `c_create_man`         varchar(32)    DEFAULT NULL COMMENT '创建人',
  `c_update_man`         varchar(32)    DEFAULT NULL COMMENT '修改人',
  `c_create_time`        bigint(20)     DEFAULT NULL COMMENT '申请时间',
  `c_update_time`        bigint(20)     DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='采购信息记录（采购价格库）';

ALTER TABLE t_payment_apply_record MODIFY COLUMN c_invoice_number varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '进项票发票号 多个';

ALTER TABLE t_purchase_apply_for_order ADD c_mpm_reference_settlement_price decimal(20,10) NULL COMMENT 'mpm参考结算价';
ALTER TABLE t_purchase_apply_for_order ADD c_sales_demand_quantity decimal(20,10) NULL COMMENT '销售需求数量';
ALTER TABLE t_purchase_apply_for_order ADD c_sales_unit_price decimal(20,10) NULL COMMENT '销售单价';
ALTER TABLE t_purchase_apply_for_order ADD c_material_line_remarks varchar(500) NULL COMMENT '物料行备注';
ALTER TABLE t_purchase_apply_for_order ADD c_material_description varchar(500) NULL COMMENT '物料描述';
ALTER TABLE t_purchase_apply_for_order ADD c_contact_information varchar(100) NULL COMMENT '联系方式';
ALTER TABLE t_purchase_apply_for_order ADD c_consignee varchar(100) NULL COMMENT '收货人';
ALTER TABLE t_purchase_apply_for_order ADD c_delivery_type varchar(100) NULL COMMENT '发货方式';
ALTER TABLE t_purchase_apply_for_order ADD c_application_form_remarks varchar(500) NULL COMMENT '申请单备注';
ALTER TABLE t_purchase_apply_for_order ADD c_salesman varchar(100) NULL COMMENT '业务员';
ALTER TABLE t_purchase_apply_for_order ADD c_sales_organization varchar(100) NULL COMMENT '销售组织';
ALTER TABLE t_purchase_apply_for_order ADD c_purchasing_organization varchar(100) NULL COMMENT '采购组织';

ALTER TABLE t_order_supplier_invoice ADD c_offset varchar(1) NULL COMMENT '是否冲销，1是 0 否';

ALTER TABLE t_supplier MODIFY COLUMN c_account_period varchar(20) DEFAULT NULL COMMENT '账期';
ALTER TABLE t_supplier_in_group MODIFY COLUMN c_account_period varchar(20) DEFAULT NULL COMMENT '账期';
ALTER TABLE t_supplier_in_group_temp MODIFY COLUMN c_account_period varchar(20) DEFAULT NULL COMMENT '账期';
ALTER TABLE t_purchase_apply_for_order ADD c_order_time bigint(20) NULL COMMENT '申请单创建时间';
ALTER TABLE t_purchase_apply_for_order MODIFY COLUMN c_apply_for_type varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '申请类型';
ALTER TABLE t_purchase_apply_for_order MODIFY COLUMN c_sale_order_product_row_id varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '销售订单物料行id';
ALTER TABLE t_purchase_apply_for_order ADD c_unit_name varchar(32) NULL COMMENT '单位名称';
ALTER TABLE t_supplier_order_to_form ADD c_source varchar(32) NULL COMMENT '来源';
ALTER TABLE t_order_invoice_relation MODIFY COLUMN c_reversal_date bigint(20) NULL COMMENT 'sap冲销日期';
ALTER TABLE t_order_invoice_relation ADD c_invoice_offset_voucher_no varchar(32) NULL COMMENT 'sap发票冲销凭证号';
CREATE INDEX t_supplier_order_c_code_IDX USING BTREE ON srm_prod.t_supplier_order (c_code);
ALTER TABLE t_purchase_apply_for_order ADD c_customer_order_no varchar(32) NULL COMMENT '客户订单号';
ALTER TABLE t_purchase_apply_for_order ADD c_follow_man_name varchar(32) NULL COMMENT '跟单人姓名';
ALTER TABLE t_purchase_apply_for_order ADD c_project_name varchar(100) NULL COMMENT '大票项目名称';
ALTER TABLE t_purchase_apply_for_order ADD c_project_no varchar(100) NULL COMMENT '大票项目号';


ALTER TABLE t_supplier_order ADD COLUMN `c_sup_contacts` varchar(50) DEFAULT NULL COMMENT '供方联系人';
ALTER TABLE t_supplier_order ADD COLUMN `c_sup_mobile` varchar(50) DEFAULT NULL COMMENT '联系方式';
ALTER TABLE t_supplier_order ADD COLUMN `c_sup_email` varchar(50) DEFAULT NULL COMMENT '电子邮件';
ALTER TABLE t_supplier_order ADD COLUMN `c_sup_fax` varchar(50) DEFAULT NULL COMMENT '传真';
ALTER TABLE t_supplier_order_to_form ADD COLUMN c_execution_status varchar(2) DEFAULT NULL COMMENT 'wms仓库执行状态 0 未执行 1已执行';

ALTER TABLE t_supplier_order ADD c_project_name varchar(1000) NULL COMMENT '大票项目名称';
ALTER TABLE t_supplier_order ADD c_project_no varchar(1000) NULL COMMENT '大票项目号';
ALTER TABLE t_supplier_order ADD c_sale_order_no varchar(1000) NULL COMMENT '销售订单号';

ALTER TABLE t_financial_vouchers ADD c_refund_amount decimal(20,10) NULL COMMENT '已退款金额';
ALTER TABLE t_financial_vouchers ADD c_offset_prepaid_amount decimal(20,10) NULL COMMENT '抵消预付金额';
ALTER TABLE t_financial_vouchers ADD c_withdrawn_amount decimal(20,10) NULL COMMENT '已提款金额';
ALTER TABLE t_financial_vouchers ADD c_remaining_refundable_amount decimal(20,10) NULL COMMENT '剩余可退款金额';

-- srm_prod.t_payment_apply_detail definition

CREATE TABLE `t_payment_apply_detail` (
                                        `id` varchar(32) NOT NULL COMMENT '主键',
                                        `payment_apply_record_id` varchar(32) DEFAULT NULL COMMENT '申请id',
                                        `c_max_advance_price` decimal(10,2) DEFAULT NULL COMMENT '最大可预付金额',
                                        `c_apply_advance_price` decimal(10,2) DEFAULT NULL COMMENT '申请预付金额',
                                        `c_advance_period` varchar(20) DEFAULT NULL COMMENT '预付账期',
                                        `c_advance_date` bigint(20) DEFAULT NULL COMMENT '预计付款日期',
                                        `c_pay_type` varchar(20) DEFAULT NULL COMMENT '付款方式',
                                        `c_bank` varchar(100) DEFAULT NULL COMMENT '开户行',
                                        `c_bank_code` varchar(50) DEFAULT NULL COMMENT '联行号',
                                        `c_bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
                                        `c_account_name` varchar(50) DEFAULT NULL COMMENT '账户名称',
                                        `c_remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                        `c_financial_vouchers` varchar(32) DEFAULT NULL COMMENT 'sap财务凭证号',
                                        `c_accounting_year` varchar(20) DEFAULT NULL COMMENT 'sap会计年度',
                                        `c_voucher_type` varchar(20) DEFAULT NULL COMMENT '凭证类型',
                                        `c_invoice_numer` varchar(32) DEFAULT NULL COMMENT '进项票发票号',
                                        `c_voucher_price` decimal(10,2) DEFAULT NULL COMMENT '凭证金额',
                                        `c_reference_date` bigint(20) DEFAULT NULL COMMENT '基准日期',
                                        `c_period` varchar(10) DEFAULT NULL COMMENT '账期',
                                        `c_update_period` varchar(10) DEFAULT NULL COMMENT '调整好的账期',
                                        `c_update_advance_date` bigint(20) DEFAULT NULL COMMENT '调整后的预计付款日期',
                                        `c_supplier_name` varchar(50) DEFAULT NULL COMMENT '供应商',
                                        `c_supplier_order_no` varchar(32) DEFAULT NULL COMMENT '采购订单号',
                                        `c_draw_money` varchar(10) DEFAULT NULL COMMENT '提款总金额',
                                        `financial_vouchers_id` varchar(500) DEFAULT NULL COMMENT '财务凭证id',
                                        `c_this_amount` json DEFAULT NULL COMMENT '本次操作金额',
                                        `c_create_time` bigint(13) DEFAULT NULL COMMENT '创建日期',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='付款申请记录详情';

ALTER TABLE t_supplier_order_detail ADD COLUMN `c_payment_amount` decimal(10,2) DEFAULT NULL COMMENT '付汇金额';

ALTER TABLE t_supplier_order_detail ADD COLUMN `c_freight` decimal(10,2) DEFAULT NULL COMMENT '物料运费';

ALTER TABLE t_supplier_order ADD COLUMN `c_freight` decimal(10,2) DEFAULT NULL COMMENT '订单运费';

ALTER TABLE t_supplier_order ADD COLUMN `c_invoice_type` varchar(10) DEFAULT NULL COMMENT '发票类型 1增值税专用 2增值税普通 3其他';

ALTER TABLE t_supplier_order_detail ADD COLUMN `c_tariff_amount` decimal(10,2) DEFAULT NULL COMMENT '关税金额';

ALTER TABLE t_supplier_order ADD COLUMN `c_free_state` tinyint(1) DEFAULT NULL COMMENT '单子是否赠品订单';

ALTER TABLE t_supplier_order ADD COLUMN `c_self_state` tinyint(1) DEFAULT NULL COMMENT '单子是否自采';

ALTER TABLE t_order_invoice_relation ADD COLUMN `c_tail_difference` varchar(255) DEFAULT NULL COMMENT '尾差原因';
ALTER TABLE srm_prod.t_supplier_order_detail ADD c_project_no varchar(32) NULL COMMENT '大票项目号';

ALTER TABLE t_payment_apply_detail ADD COLUMN `new_financial_vouchers_id` varchar(500) DEFAULT NULL COMMENT '新生成的提款/退款财务凭证id';

ALTER TABLE srm_prod.t_supplier_invoice_to_detail ADD c_seller varchar(50) NULL COMMENT '销方名称';



ALTER TABLE t_group ADD COLUMN `c_parent_code` varchar(20) DEFAULT NULL COMMENT '上级组织编码';

ALTER TABLE t_group ADD COLUMN `c_parent_name` varchar(50) DEFAULT NULL COMMENT '上级组织名称';

ALTER TABLE t_group ADD COLUMN `c_full_name` varchar(200) DEFAULT NULL COMMENT '组织全称';


ALTER TABLE t_group MODIFY COLUMN c_code varchar(50) NULL COMMENT 'SRM编码';

ALTER TABLE t_supplier_order ADD COLUMN `c_update_man` varchar(32) DEFAULT NULL COMMENT '修改人';

ALTER TABLE t_order ADD c_erp_type varchar(10) NULL COMMENT 'erp类型';

update t_order set c_erp_type = "金蝶订单" where c_erp_order_id is not null;
update t_order set c_erp_type = "SAP订单" where c_erp_order_id is null;

ALTER TABLE t_order_detail ADD c_row_no varchar(32) NULL COMMENT '销售订单物料行id';




-- 处理采购申请数据权限
INSERT INTO srm_prod.t_permission_type
(id, c_permission_code, user_id, c_type, c_state, c_create_time)
select replace(uuid(),_utf8'-',_utf8'') ,'1',id,'6','1',1708185600000 from t_user where c_state = '1' and c_role = '2';
-- 供应商区域
update t_supplier set c_region = '东北' WHERE c_province like '%黑龙江%' or c_province = '%吉林%' or c_province = '%辽宁%';
update t_supplier set c_region = '华东' WHERE c_province like '%上海%' or c_province like '%江苏%' or c_province like '%浙江%' or c_province like '%安徽%' or c_province like '%福建%' or c_province like '%江西%' or c_province like '%山东%' or c_province like '%台湾%';
update t_supplier set c_region = '华北' WHERE c_province like '%北京%' or c_province like '%天津%' or c_province like '%山西%' or c_province like '%河北%' or c_province like '%内蒙古%';
update t_supplier set c_region = '华中' WHERE c_province like '%河南%' or c_province like '%湖北%' or c_province like '%湖南%';
update t_supplier set c_region = '华南' WHERE c_province like '%广东%' or c_province like '%广西%' or c_province like '%海南%' or c_province like '%香港%'  or c_province like '%澳门%';
update t_supplier set c_region = '西南' WHERE c_province like '%四川%' or c_province like '%贵州%' or c_province like '%云南%' or c_province like '%重庆%'  or c_province like '%西藏%';
update t_supplier set c_region = '西北' WHERE c_province like '%陕西%' or c_province like '%甘肃%' or c_province like '%青海%' or c_province like '%宁夏%'  or c_province like '%新疆%';
-- 供应商 结算币别
update t_supplier set c_settleCurrency = 'CNY' WHERE c_settleCurrency = 'PRE001';
update t_supplier set c_settleCurrency = 'HKD' WHERE c_settleCurrency = 'PRE002';
update t_supplier set c_settleCurrency = 'EUR' WHERE c_settleCurrency = 'PRE003';
update t_supplier set c_settleCurrency = 'JPY' WHERE c_settleCurrency = 'PRE004';
update t_supplier set c_settleCurrency = 'TWD' WHERE c_settleCurrency = 'PRE005';
update t_supplier set c_settleCurrency = 'GBP' WHERE c_settleCurrency = 'PRE006';
update t_supplier set c_settleCurrency = 'USD' WHERE c_settleCurrency = 'PRE007';

update t_supplier_in_group set c_settle_currency = 'CNY' WHERE c_settle_currency = 'PRE001';
update t_supplier_in_group set c_settle_currency = 'HKD' WHERE c_settle_currency = 'PRE002';
update t_supplier_in_group set c_settle_currency = 'EUR' WHERE c_settle_currency = 'PRE003';
update t_supplier_in_group set c_settle_currency = 'JPY' WHERE c_settle_currency = 'PRE004';
update t_supplier_in_group set c_settle_currency = 'TWD' WHERE c_settle_currency = 'PRE005';
update t_supplier_in_group set c_settle_currency = 'GBP' WHERE c_settle_currency = 'PRE006';
update t_supplier_in_group set c_settle_currency = 'USD' WHERE c_settle_currency = 'PRE007';

-- 数据处理

update t_supplier set c_useCode = '1064' WHERE c_useCode = '2050101';
update t_supplier set c_useCode = '1064' WHERE c_useCode = '1050101';
update t_supplier set c_useCode = '1044' WHERE c_useCode = '2020105';
update t_supplier set c_useCode = '1067' WHERE c_useCode = '20504';
update t_supplier set c_useCode = '1011' WHERE c_useCode = '1030206';
update t_supplier set c_useCode = '1073' WHERE c_useCode = '1030305';
update t_supplier set c_useCode = '1020' WHERE c_useCode = '1030113';
update t_supplier set c_useCode = '1042' WHERE c_useCode = '20211';
update t_supplier set c_useCode = '1013' WHERE c_useCode = '10305';
update t_supplier set c_useCode = '1012' WHERE c_useCode = '1030205';
update t_supplier set c_useCode = '1018' WHERE c_useCode = '900';
update t_supplier set c_useCode = '1026' WHERE c_useCode = '800';
update t_supplier set c_useCode = '1064' WHERE c_useCode = '700';
update t_supplier set c_useCode = '1005' WHERE c_useCode = '500';
update t_supplier set c_useCode = '1072' WHERE c_useCode = '300';
update t_supplier set c_useCode = '1007' WHERE c_useCode = '2070201';
update t_supplier set c_useCode = '1043' WHERE c_useCode = '20602';
update t_supplier set c_useCode = '1019' WHERE c_useCode = '2060103';
update t_supplier set c_useCode = '1009' WHERE c_useCode = '2060102';
update t_supplier set c_useCode = '1008' WHERE c_useCode = '206010101';
update t_supplier set c_useCode = '1056' WHERE c_useCode = '20509';
update t_supplier set c_useCode = '1068' WHERE c_useCode = '20508';
update t_supplier set c_useCode = '1063' WHERE c_useCode = '20502';
update t_supplier set c_useCode = '1004' WHERE c_useCode = '20304';
update t_supplier set c_useCode = '1072' WHERE c_useCode = '2030302';
update t_supplier set c_useCode = '1018' WHERE c_useCode = '2030203';
update t_supplier set c_useCode = '1005' WHERE c_useCode = '2030201';
update t_supplier set c_useCode = '1003' WHERE c_useCode = '203010601';
update t_supplier set c_useCode = '1016' WHERE c_useCode = '203010401';
update t_supplier set c_useCode = '1002' WHERE c_useCode = '203010101';
update t_supplier set c_useCode = '1037' WHERE c_useCode = '2021001';
update t_supplier set c_useCode = '1041' WHERE c_useCode = '2020902';
update t_supplier set c_useCode = '1040' WHERE c_useCode = '2020901';
update t_supplier set c_useCode = '1038' WHERE c_useCode = '202080202';
update t_supplier set c_useCode = '1062' WHERE c_useCode = '2020801';
update t_supplier set c_useCode = '1057' WHERE c_useCode = '2020702';
update t_supplier set c_useCode = '1039' WHERE c_useCode = '2020701';
update t_supplier set c_useCode = '1054' WHERE c_useCode = '2020603';
update t_supplier set c_useCode = '1052' WHERE c_useCode = '2020602';
update t_supplier set c_useCode = '1036' WHERE c_useCode = '2020601';
update t_supplier set c_useCode = '1035' WHERE c_useCode = '2020502';
update t_supplier set c_useCode = '1034' WHERE c_useCode = '2020501';
update t_supplier set c_useCode = '1051' WHERE c_useCode = '2020303';
update t_supplier set c_useCode = '1050' WHERE c_useCode = '2020302';
update t_supplier set c_useCode = '1032' WHERE c_useCode = '2020301';
update t_supplier set c_useCode = '1060' WHERE c_useCode = '20202030201';
update t_supplier set c_useCode = '1049' WHERE c_useCode = '202020301';
update t_supplier set c_useCode = '1047' WHERE c_useCode = '2020202';
update t_supplier set c_useCode = '1031' WHERE c_useCode = '2020201';
update t_supplier set c_useCode = '1046' WHERE c_useCode = '2020104';
update t_supplier set c_useCode = '1059' WHERE c_useCode = '202010303';
update t_supplier set c_useCode = '1053' WHERE c_useCode = '202010301';
update t_supplier set c_useCode = '1045' WHERE c_useCode = '2020102';
update t_supplier set c_useCode = '1030' WHERE c_useCode = '2020101';
update t_supplier set c_useCode = '1000' WHERE c_useCode = '20101';
update t_supplier set c_useCode = '1004' WHERE c_useCode = '200';
update t_supplier set c_useCode = '1007' WHERE c_useCode = '10702';
update t_supplier set c_useCode = '1001' WHERE c_useCode = '106031';
update t_supplier set c_useCode = '1008' WHERE c_useCode = '1060101';
update t_supplier set c_useCode = '1069' WHERE c_useCode = '1030301';
update t_supplier set c_useCode = '1017' WHERE c_useCode = '1030111';
update t_supplier set c_useCode = '1003' WHERE c_useCode = '1030106';
update t_supplier set c_useCode = '1016' WHERE c_useCode = '1030104';
update t_supplier set c_useCode = '1014' WHERE c_useCode = '1030102';
update t_supplier set c_useCode = '1002' WHERE c_useCode = '1030101';
update t_supplier set c_useCode = '1025' WHERE c_useCode = '100';
update t_supplier set c_useCode = '1027' WHERE c_useCode = '20503';

﻿update t_supplier set c_createCode = '1064' WHERE c_createCode = '2050101';
update t_supplier set c_createCode = '1064' WHERE c_createCode = '1050101';
update t_supplier set c_createCode = '1044' WHERE c_createCode = '2020105';
update t_supplier set c_createCode = '1067' WHERE c_createCode = '20504';
update t_supplier set c_createCode = '1011' WHERE c_createCode = '1030206';
update t_supplier set c_createCode = '1073' WHERE c_createCode = '1030305';
update t_supplier set c_createCode = '1020' WHERE c_createCode = '1030113';
update t_supplier set c_createCode = '1042' WHERE c_createCode = '20211';
update t_supplier set c_createCode = '1013' WHERE c_createCode = '10305';
update t_supplier set c_createCode = '1012' WHERE c_createCode = '1030205';
update t_supplier set c_createCode = '1018' WHERE c_createCode = '900';
update t_supplier set c_createCode = '1026' WHERE c_createCode = '800';
update t_supplier set c_createCode = '1064' WHERE c_createCode = '700';
update t_supplier set c_createCode = '1005' WHERE c_createCode = '500';
update t_supplier set c_createCode = '1072' WHERE c_createCode = '300';
update t_supplier set c_createCode = '1007' WHERE c_createCode = '2070201';
update t_supplier set c_createCode = '1043' WHERE c_createCode = '20602';
update t_supplier set c_createCode = '1019' WHERE c_createCode = '2060103';
update t_supplier set c_createCode = '1009' WHERE c_createCode = '2060102';
update t_supplier set c_createCode = '1008' WHERE c_createCode = '206010101';
update t_supplier set c_createCode = '1056' WHERE c_createCode = '20509';
update t_supplier set c_createCode = '1068' WHERE c_createCode = '20508';
update t_supplier set c_createCode = '1063' WHERE c_createCode = '20502';
update t_supplier set c_createCode = '1004' WHERE c_createCode = '20304';
update t_supplier set c_createCode = '1072' WHERE c_createCode = '2030302';
update t_supplier set c_createCode = '1018' WHERE c_createCode = '2030203';
update t_supplier set c_createCode = '1005' WHERE c_createCode = '2030201';
update t_supplier set c_createCode = '1003' WHERE c_createCode = '203010601';
update t_supplier set c_createCode = '1016' WHERE c_createCode = '203010401';
update t_supplier set c_createCode = '1002' WHERE c_createCode = '203010101';
update t_supplier set c_createCode = '1037' WHERE c_createCode = '2021001';
update t_supplier set c_createCode = '1041' WHERE c_createCode = '2020902';
update t_supplier set c_createCode = '1040' WHERE c_createCode = '2020901';
update t_supplier set c_createCode = '1038' WHERE c_createCode = '202080202';
update t_supplier set c_createCode = '1062' WHERE c_createCode = '2020801';
update t_supplier set c_createCode = '1057' WHERE c_createCode = '2020702';
update t_supplier set c_createCode = '1039' WHERE c_createCode = '2020701';
update t_supplier set c_createCode = '1054' WHERE c_createCode = '2020603';
update t_supplier set c_createCode = '1052' WHERE c_createCode = '2020602';
update t_supplier set c_createCode = '1036' WHERE c_createCod e = '2020601';
update t_supplier set c_createCode = '1035' WHERE c_createCode = '2020502';
update t_supplier set c_createCode = '1034' WHERE c_createCode = '2020501';
update t_supplier set c_createCode = '1051' WHERE c_createCode = '2020303';
update t_supplier set c_createCode = '1050' WHERE c_createCode = '2020302';
update t_supplier set c_createCode = '1032' WHERE c_createCode = '2020301';
update t_supplier set c_createCode = '1060' WHERE c_createCode = '20202030201';
update t_supplier set c_createCode = '1049' WHERE c_createCode = '202020301';
update t_supplier set c_createCode = '1047' WHERE c_createCode = '2020202';
update t_supplier set c_createCode = '1031' WHERE c_createCode = '2020201';
update t_supplier set c_createCode = '1046' WHERE c_createCode = '2020104';
update t_supplier set c_createCode = '1059' WHERE c_createCode = '202010303';
update t_supplier set c_createCode = '1053' WHERE c_createCode = '202010301';
update t_supplier set c_createCode = '1045' WHERE c_createCode = '2020102';
update t_supplier set c_createCode = '1030' WHERE c_createCode = '2020101';
update t_supplier set c_createCode = '1000' WHERE c_createCode = '20101';
update t_supplier set c_createCode = '1004' WHERE c_createCode = '200';
update t_supplier set c_createCode = '1007' WHERE c_createCode = '10702';
update t_supplier set c_createCode = '1001' WHERE c_createCode = '106031';
update t_supplier set c_createCode = '1008' WHERE c_createCode = '1060101';
update t_supplier set c_createCode = '1069' WHERE c_createCode = '1030301';
update t_supplier set c_createCode = '1017' WHERE c_createCode = '1030111';
update t_supplier set c_createCode = '1003' WHERE c_createCode = '1030106';
update t_supplier set c_createCode = '1016' WHERE c_createCode = '1030104';
update t_supplier set c_createCode = '1014' WHERE c_createCode = '1030102';
update t_supplier set c_createCode = '1002' WHERE c_createCode = '1030101';
update t_supplier set c_createCode = '1025' WHERE c_createCode = '100';
update t_supplier set c_createCode = '1027' WHERE c_createCode = '20503';



ALTER TABLE srm_prod.t_order_delivery_detail ADD c_batch_no varchar(32) NULL COMMENT 'sap 批号';
ALTER TABLE t_group ADD COLUMN `operator` varchar(32) DEFAULT NULL COMMENT '应处理人';
ALTER TABLE t_order_detail ADD COLUMN `c_purchase_order_row_no` varchar(32) DEFAULT NULL COMMENT '采购订单物料行id';

ALTER TABLE t_order ADD c_large_ticket_project varchar(1000) NULL COMMENT '大票项目号';

ALTER TABLE t_group MODIFY COLUMN c_code varchar(50) DEFAULT NULL COMMENT 'SRM编码';
ALTER TABLE t_group MODIFY COLUMN c_groupCode varchar(50) DEFAULT NULL COMMENT '所属组织编码';


ALTER TABLE t_supplier_order_detail MODIFY COLUMN `c_total_settlement_price` decimal(20,2) DEFAULT NULL COMMENT '结算总价';
ALTER TABLE t_supplier_order_detail MODIFY COLUMN `c_settlement_price` decimal(20,2) DEFAULT NULL COMMENT '结算单价';
ALTER TABLE t_supplier_order_detail MODIFY COLUMN `c_tariff_amount` decimal(20,2) DEFAULT NULL COMMENT '关税金额';
ALTER TABLE t_supplier_order_detail MODIFY COLUMN `c_payment_amount` decimal(20,2) DEFAULT NULL COMMENT '付汇金额';
ALTER TABLE t_supplier_order_detail MODIFY COLUMN `c_transfer_price` decimal(20,2) DEFAULT NULL COMMENT 'mpm参考调拨价'

ALTER TABLE t_supplier_order_detail ADD COLUMN `c_invoiced_num` decimal(20,10) DEFAULT NULL COMMENT '入库单物料行可开票数量';

ALTER TABLE t_supplier_order_detail ADD COLUMN `c_tariff` decimal(10,2) DEFAULT NULL COMMENT '关税';



