use srm;

ALTER TABLE t_order
    ADD c_customer_return_progress varchar(1) DEFAULT '0' COMMENT '回款进度';

-- srm_zhens.t  _customer_receivable definition

CREATE TABLE `t_customer_receivable` (
  `id` varchar(32)  NOT NULL COMMENT '主键',
  `order_id` varchar(32) DEFAULT NULL COMMENT '落地商订单 id',
  `c_project_no` varchar(32)  DEFAULT NULL COMMENT '大票项目编号',
  `c_invoice_no` varchar(32)  DEFAULT NULL COMMENT '发票号码',
  `c_invoice_time` bigint(20) DEFAULT NULL COMMENT '发票日期',
  `c_price` decimal(20,10) DEFAULT NULL COMMENT '价税合计',
  `c_return_state` varchar(1)  DEFAULT NULL COMMENT '回款状态',
  `c_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `c_state` varchar(1)  DEFAULT NULL COMMENT '数据状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='客户收款表';

-- srm_zhens.t_customer_return definition

CREATE TABLE `t_customer_return` (
  `id` varchar(32)  NOT NULL COMMENT '主键',
  `customer_receivable_id` varchar(32)  DEFAULT NULL COMMENT '收款 id',
  `c_price` decimal(20,10) DEFAULT NULL COMMENT '回款金额',
  `c_return_time` bigint(20) DEFAULT NULL COMMENT '回款日期',
  `c_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `c_state` varchar(1)  DEFAULT NULL COMMENT '数据状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='客户回款表';

-- 修改发票号码和大票项目字段长度
use srm;
ALTER TABLE t_customer_receivable MODIFY COLUMN c_invoice_no varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '发票号码';
ALTER TABLE t_customer_receivable MODIFY COLUMN c_project_no varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '大票项目编号';


-- 客户收款表新增订单编号字段
ALTER TABLE t_customer_receivable ADD c_order_no varchar(50) NULL COMMENT '订单编号';
