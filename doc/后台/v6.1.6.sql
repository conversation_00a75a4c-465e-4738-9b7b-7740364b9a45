ALTER TABLE srm_prod.t_supplier_order ADD c_scp TINYINT(1) NULL;
update t_supplier_order set c_scp = true where c_order_type = '1' and c_scp is null ;

update t_supplier_order o
  left join t_supplier s
on o.supplier_id = s.id
  set c_scp = true
where s.c_open_supplier_order = true
  and c_scp is null ;

update t_supplier_order set c_scp = false where c_scp is null ;


ALTER TABLE `t_supplier_order_to_form`
  ADD COLUMN `c_sap_return_number` varchar(200) NULL DEFAULT NULL COMMENT 'SAP退库单采购订单号' AFTER `c_need_red_ticket`;

ALTER TABLE `t_supplier_order_detail`
  ADD COLUMN `c_warehouse_name` varchar(255) NULL DEFAULT NULL COMMENT '仓库名称' AFTER `c_return_amount`;


UPDATE t_supplier_order_detail od_target
  JOIN (
  SELECT DISTINCT od_inner.id
  FROM t_supplier_order so
  INNER JOIN t_supplier_order_to_form otf ON so.id = otf.supplier_order_id
  INNER JOIN t_supplier_order_detail od_inner ON od_inner.order_to_form_id = otf.id
  WHERE so.c_group_code = '1025'
  AND so.c_state = '1'
  AND od_inner.c_warehouse = '1009'
  ) od_source ON od_target.id = od_source.id
  SET od_target.c_warehouse_name = '万聚样品库';



UPDATE t_supplier_order_detail od_target
  JOIN (
  SELECT DISTINCT od_inner.id
  FROM t_supplier_order so
  INNER JOIN t_supplier_order_to_form otf ON so.id = otf.supplier_order_id
  INNER JOIN t_supplier_order_detail od_inner ON od_inner.order_to_form_id = otf.id
  WHERE so.c_group_code = '1025'
  AND so.c_state = '1'
  AND od_inner.c_warehouse = '1001'
  ) od_source ON od_target.id = od_source.id
  SET od_target.c_warehouse_name = '万聚成品库';



UPDATE t_supplier_order_detail od_target
  JOIN (
  SELECT DISTINCT od_inner.id
  FROM t_supplier_order so
  INNER JOIN t_supplier_order_to_form otf ON so.id = otf.supplier_order_id
  INNER JOIN t_supplier_order_detail od_inner ON od_inner.order_to_form_id = otf.id
  WHERE so.c_group_code = '1025'
  AND so.c_state = '1'
  AND od_inner.c_warehouse = '1004'
  ) od_source ON od_target.id = od_source.id
  SET od_target.c_warehouse_name = '万聚待检库';



UPDATE t_supplier_order_detail od_target
  JOIN (
  SELECT DISTINCT od_inner.id
  FROM t_supplier_order so
  INNER JOIN t_supplier_order_to_form otf ON so.id = otf.supplier_order_id
  INNER JOIN t_supplier_order_detail od_inner ON od_inner.order_to_form_id = otf.id
  WHERE so.c_group_code = '1025'
  AND so.c_state = '1'
  AND od_inner.c_warehouse = '1005'
  ) od_source ON od_target.id = od_source.id
  SET od_target.c_warehouse_name = '万聚待处理库';

UPDATE t_supplier_order_detail od_target
  JOIN (
  SELECT DISTINCT od_inner.id
  FROM t_supplier_order so
  INNER JOIN t_supplier_order_to_form otf ON so.id = otf.supplier_order_id
  INNER JOIN t_supplier_order_detail od_inner ON od_inner.order_to_form_id = otf.id
  WHERE so.c_group_code = '1025'
  AND so.c_state = '1'
  AND od_inner.c_warehouse = '1101'
  ) od_source ON od_target.id = od_source.id
  SET od_target.c_warehouse_name = '嘉峪关分仓';


UPDATE t_supplier_order_detail od_target
  JOIN (
  SELECT DISTINCT od_inner.id
  FROM t_supplier_order so
  INNER JOIN t_supplier_order_to_form otf ON so.id = otf.supplier_order_id
  INNER JOIN t_supplier_order_detail od_inner ON od_inner.order_to_form_id = otf.id
  WHERE so.c_group_code = '1025'
  AND so.c_state = '1'
  AND od_inner.c_warehouse = '1201'
  ) od_source ON od_target.id = od_source.id
  SET od_target.c_warehouse_name = '惠州分仓';


UPDATE t_supplier_order_detail od_target
  JOIN (
  SELECT DISTINCT od_inner.id
  FROM t_supplier_order so
  INNER JOIN t_supplier_order_to_form otf ON so.id = otf.supplier_order_id
  INNER JOIN t_supplier_order_detail od_inner ON od_inner.order_to_form_id = otf.id
  WHERE so.c_group_code = '1025'
  AND so.c_state = '1'
  AND od_inner.c_warehouse = '5000'
  ) od_source ON od_target.id = od_source.id
  SET od_target.c_warehouse_name = '万聚借出库';


UPDATE t_supplier_order_detail od_target
  JOIN (
  SELECT DISTINCT od_inner.id
  FROM t_supplier_order so
  INNER JOIN t_supplier_order_to_form otf ON so.id = otf.supplier_order_id
  INNER JOIN t_supplier_order_detail od_inner ON od_inner.order_to_form_id = otf.id
  WHERE so.c_group_code = '1025'
  AND so.c_state = '1'
  AND od_inner.c_warehouse = '6000'
  ) od_source ON od_target.id = od_source.id
  SET od_target.c_warehouse_name = '万聚直销库';

UPDATE t_supplier_order_detail od_target
  JOIN (
  SELECT DISTINCT od_inner.id
  FROM t_supplier_order so
  INNER JOIN t_supplier_order_to_form otf ON so.id = otf.supplier_order_id
  INNER JOIN t_supplier_order_detail od_inner ON od_inner.order_to_form_id = otf.id
  WHERE so.c_group_code = '1025'
  AND so.c_state = '1'
  AND od_inner.c_warehouse = '8000'
  ) od_source ON od_target.id = od_source.id
  SET od_target.c_warehouse_name = '万聚残次品库';

UPDATE t_supplier_order_detail od_target
  JOIN (
  SELECT DISTINCT od_inner.id
  FROM t_supplier_order so
  INNER JOIN t_supplier_order_to_form otf ON so.id = otf.supplier_order_id
  INNER JOIN t_supplier_order_detail od_inner ON od_inner.order_to_form_id = otf.id
  WHERE so.c_group_code = '1025'
  AND so.c_state = '1'
  AND od_inner.c_warehouse = '1999'
  ) od_source ON od_target.id = od_source.id
  SET od_target.c_warehouse_name = '走流程库';