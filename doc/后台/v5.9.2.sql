ALTER TABLE t_supplier_order_detail ADD c_description varchar(255) NULL COMMENT '描述';
ALTER TABLE t_order_invoice_relation ADD c_logistics_num varchar(50) NULL COMMENT '物流单号';
ALTER TABLE t_order_invoice_relation ADD c_logistics_company varchar(20) NULL COMMENT '物流公司';
ALTER TABLE t_order_invoice_relation MODIFY COLUMN c_logistics_company varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '物流公司';

-- 删除备用字段
ALTER TABLE t_order_account DROP COLUMN c_standby;
ALTER TABLE t_order DROP COLUMN c_standby;

ALTER TABLE t_ding_card_info CHANGE c_state c_type varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '类型，1：卡片 2：待办';
ALTER TABLE t_ding_card_info MODIFY COLUMN c_type varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '类型，1：卡片 2：待办';
ALTER TABLE t_ding_card_info ADD c_update_state varchar(1) NULL COMMENT '更新状态（卡片或待办是否已经更新）0：未更新 1：已更新';
update t_ding_card_info set c_update_state = "1";



