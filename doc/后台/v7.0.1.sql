# 增加库存安全列表
CREATE TABLE `t_inventory_safety`
(
  `id`                      varchar(32) NOT NULL COMMENT '唯一id',
  `c_group_code`            varchar(100)   DEFAULT NULL COMMENT '组织编码',
  `c_product_code`          varchar(100)   DEFAULT NULL COMMENT '物料编码',
  `c_warehouse`             varchar(100)   DEFAULT NULL COMMENT '库房编码',
  `c_warehouse_id`          varchar(32)    DEFAULT NULL COMMENT '库房id',
  `c_brand_name_cn`         varchar(100)   DEFAULT NULL COMMENT '品牌中文名称',
  `c_brand_name_en`         varchar(100)   DEFAULT NULL COMMENT '品牌英文名称',
  `c_brand_name`            varchar(200)   DEFAULT NULL COMMENT '品牌名全称',
  `c_name`                  varchar(200)   DEFAULT NULL COMMENT '物料名称',
  `c_model`                 varchar(100)   DEFAULT NULL COMMENT '型号',
  `c_unit`                  varchar(100)   DEFAULT NULL COMMENT '单位',
  `c_min_safety_stock`      decimal(18, 2) DEFAULT NULL COMMENT '库存安全最小数量',
  `c_notified_person`       varchar(100)   DEFAULT NULL COMMENT '通知人',
  `c_notified_person_id`    varchar(100)   DEFAULT NULL COMMENT '通知人id',
  `c_notified_person_code`  varchar(100)   DEFAULT NULL COMMENT '通知人工号',
  `c_notified_person_phone` varchar(100)   DEFAULT NULL COMMENT '通知人手机号',
  `c_last_reminder_time`    bigint(20) DEFAULT NULL COMMENT '上次提醒时间',
  `c_next_reminder_time`    bigint(20) DEFAULT NULL COMMENT '下次提醒时间',
  `c_status`                tinyint(2) DEFAULT NULL COMMENT '通知状态 (1开启 -1关闭)',
  `c_create_time`           bigint(20) DEFAULT NULL COMMENT '创建时间',
  `c_update_time`           bigint(20) DEFAULT NULL COMMENT '更新时间',
  `c_state`                 varchar(1)     DEFAULT '1' COMMENT '数据状态',
  PRIMARY KEY (`id`),
  KEY                       `c_index_warehouse` (`c_warehouse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存安全管理';

--库位管理表
CREATE TABLE `t_inventory_location` (
                             `id` varchar(32) NOT NULL COMMENT '唯一id',
                             `c_group_code` varchar(10) DEFAULT NULL COMMENT '组织编码',
                             `c_group_name` varchar(50) DEFAULT NULL COMMENT '组织名称',
                             `c_warehouse_name` varchar(20) DEFAULT NULL COMMENT '库房名称',
                             `c_warehouse` varchar(10) DEFAULT NULL COMMENT '库房编码',
                             `c_is_wms` varchar(1) DEFAULT NULL COMMENT '是否涉及WMS:1-是，0-否',
                             `c_business_type` varchar(10) DEFAULT NULL COMMENT '涉及WMS的业务类型：1-采购订单，2-货物移动',
                             `c_create_time` bigint(20)  DEFAULT NULL COMMENT '创建时间',
                             `c_update_time` bigint(20)  DEFAULT NULL COMMENT '更新时间',
                             `c_state`       varchar(1)  DEFAULT '1' COMMENT '数据状态',
                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='库位管理表';



CREATE TABLE `t_inventory_safety_log`
(
  `id`                      varchar(32) NOT NULL COMMENT '唯一id',
  `c_json`                  text COMMENT '消息json',
  `c_notified_person`       varchar(100) DEFAULT NULL COMMENT '提醒用户名称',
  `c_notified_person_id`    varchar(100) DEFAULT NULL COMMENT '提醒用户id',
  `c_notified_person_code`  varchar(100) DEFAULT NULL COMMENT '提醒用户的code',
  `c_notified_person_phone` varchar(100) DEFAULT NULL COMMENT '提醒用户手机号',
  `c_confirm`               tinyint(1) DEFAULT '0' COMMENT '是否确认',
  `c_create_time`           bigint(20) DEFAULT NULL COMMENT '创建时间',
  `c_state`                 varchar(1)   DEFAULT NULL COMMENT '数据状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存安全检查日志';



--管理员添加库存安全列表权限
INSERT INTO t_permission_type
(id, c_permission_code, user_id, c_type, c_state, c_create_time)
VALUES(replace(uuid(),_utf8'-',_utf8''), '1', '402881756ce75bf8016ce75e01eb0003', '20', '1', 1739345779000);


