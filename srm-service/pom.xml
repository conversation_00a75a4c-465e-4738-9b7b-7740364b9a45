<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>srm-boot</artifactId>
    <groupId>com.xhgj</groupId>
    <version>3.0.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>srm-service</artifactId>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-map-struct</artifactId>
      <version>3.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-v2</artifactId>
      <version>3.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-jpa</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-request</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhiot.xhiot-boot</groupId>
      <artifactId>boot-repeat</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
    </dependency>
      <dependency>
          <groupId>com.xhgj</groupId>
          <artifactId>srm-open-spi</artifactId>
          <version>3.0.0-SNAPSHOT</version>
          <scope>compile</scope>
      </dependency>
      <dependency>
          <groupId>com.xhgj</groupId>
          <artifactId>srm-mission-dispatcher</artifactId>
      </dependency>
    <dependency>
      <groupId>com.xhiot.xhiot-boot.modules-core</groupId>
      <artifactId>boot-dict-core</artifactId>
    </dependency>
  </dependencies>
</project>