package com.xhgj.srm.component;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.dto.OrderAccountInvoiceDingTalkMessageParams;
import com.xhgj.srm.jpa.dao.DingCardInfoDao;
import com.xhgj.srm.jpa.entity.DingCardInfo;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> <PERSON> @date 2023/6/29
 */
@Component
public class OrderAccountInvoiceDingTalkMessage {
  @Resource
  private DingUtils dingUtils;
  @Resource
  private DingCardInfoDao dingCardInfoDao;

  /**
   * 发送对账单开票信息的钉钉通知
   * @param messageParams 消息参数
   * @return  发送给钉钉的消息信息
   */
  public Map<String, Object> sendMessage(OrderAccountInvoiceDingTalkMessageParams messageParams,
      String orderAccountId,boolean
      sendOldDingMsg, boolean isSupplierOpenInvoice) {
    //发送钉钉通知
    Map<String, Object> map = new HashMap<>();
    map.put("title", messageParams.getTitle());
//    map.put("expressName", StrUtil.join("、", messageParams.getExpressNames()));
//    map.put("expressNo", StrUtil.join("、", messageParams.getExpressNos()));
    map.put("platform", StrUtil.join("、", messageParams.getPlatforms()));
    map.put("orderNo", StrUtil.join("、", messageParams.getOrderInfos().keySet()));
    map.put("remark", messageParams.getRemark());
//    map.put("id", messageParams.getId());
//    map.put("accountNo", messageParams.getOrderAccountNo());
    map.put("detailLink", messageParams.getDetailLink());
    map.put("assess_button", messageParams.getButtonShow());
    map.put("sys_full_json_obj", JSON.toJSONString(messageParams.getSys_full_json_obj()));
    dingUtils.sendOpenInvoiceNotice(messageParams.getMobileList(), map, messageParams.getId(),
        orderAccountId,sendOldDingMsg, isSupplierOpenInvoice);
    return map;
  }

  @Transactional(propagation = REQUIRES_NEW)
  public void saveMessageInfo(Map<String, Object> messageInfo, String relevanceId,String type) {
    DingCardInfo dingCardInfo = new DingCardInfo();
    dingCardInfo.setInfo(JSON.toJSONString(messageInfo));
    dingCardInfo.setType(type);
    dingCardInfo.setCreateTime(System.currentTimeMillis());
    dingCardInfo.setRelevanceId(relevanceId);
    dingCardInfoDao.save(dingCardInfo);
  }

  public boolean checkOldOrder(Map<String, String> map) {
    for (String key : map.keySet()) {
      String saleOrderNo = map.get(key);
      if (StrUtil.isBlank(saleOrderNo)) {
        return false;
      }
    }
    return true;
  }
}
