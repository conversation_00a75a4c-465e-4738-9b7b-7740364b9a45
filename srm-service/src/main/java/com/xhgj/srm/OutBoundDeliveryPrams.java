package com.xhgj.srm;

import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class OutBoundDeliveryPrams implements Serializable {

  private static final long serialVersionUID = -3433775365777237754L;

  @ApiModelProperty("勾选的数据行")
  private List<String> ids;

  @ApiModelProperty("搜索方案 id")
  private String schemeId;

  @ApiModelProperty(value = "采购订单号")
  private String orderCode;

  @ApiModelProperty(value = "SAP物料凭证号")
  private String sapProductVoucherNo;

  @ApiModelProperty(value = "SAP退库单采购订单号")
  private String returnOrderCode;

  @ApiModelProperty(value = "批号")
  private String batchNo;

  @ApiModelProperty(value = "已开红票数量")
  private Integer invoiceQuantity;

  @ApiModelProperty(value = "已开红票数量操作符")
  private LogicalOperatorsEnums invoiceQuantityOperator;

  @ApiModelProperty(value = "是否需要开红票 0否 1是")
  private Integer isNeedRedInvoice;

  @ApiModelProperty(value = "退库时间起")
  private String returnTimeStart;

  @ApiModelProperty(value = "退库时间止")
  private String returnTimeEnd;

  @ApiModelProperty(value = "退库原因")
  private String returnReason;

  @ApiModelProperty(value = "退库仓库")
  private String returnWarehouse;

  @ApiModelProperty(value = "仓库执行状态 0否 1是")
  private String warehouseExecutionStatus;

  @ApiModelProperty(value = "快递单号")
  private String expressNo;

  @ApiModelProperty(value = "物流公司")
  private String expressCompany;

  @ApiModelProperty(value = "物料编码")
  private String productCode;

  @ApiModelProperty(value = "品牌")
  private String brand;

  @ApiModelProperty(value = "物料名称")
  private String productName;

  @ApiModelProperty(value = "规格型号")
  private String specification;

  @ApiModelProperty(value = "退库数量")
  private BigDecimal returnQuantity;

  @ApiModelProperty(value = "单退库数量操作符")
  private LogicalOperatorsEnums returnQuantityOperator;

  @ApiModelProperty(value = "关联发票号")
  private String invoiceNo;

  @ApiModelProperty(value = "冲销状态")
  private String writeOffState;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("采购员")
  private String purchaseMan;

  @ApiModelProperty("采购部门")
  private String purchaseDept;
}
