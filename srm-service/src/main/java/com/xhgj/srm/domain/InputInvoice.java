package com.xhgj.srm.domain;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.dto.invoice.InvoiceIdentifyResultDTO;
import com.xhgj.srm.common.dto.invoice.InvoiceOcrRecognitionResultDTO;
import com.xhgj.srm.common.dto.invoice.InvoiceOcrRecognitionResultDTO.Result;
import com.xhgj.srm.common.dto.invoice.InvoiceVerificationInfo;
import com.xhgj.srm.common.dto.invoice.InvoiceVerificationParam;
import com.xhgj.srm.jpa.entity.InvoiceOcrRecognition;
import com.xhgj.srm.jpa.repository.InvoiceOcrRecognitionRepository;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class InputInvoice {

  private final String HEAD_APP_ID = "3c9c1f61603aecc61e9c48777d5d7db0";
  private final String HEAD_SECRET_CODE = "74fec8754809ab2fd9615e462477cf55";
  /**
   * 验真url
   */
  private final String VERIFICATION_URL = "https://api.textin.com/robot/v1.0/api/verify_vat";
  /**
   * 增值税发票识别提取
   */
  private final String IDENTIFY_URL = "https://api.textin.com/robot/v1.0/api/vat_invoice";
  private final int OCR_RECOGNITION_SUCCESS_CODE = 200;
  @Resource
  private InvoiceOcrRecognitionRepository invoiceOcrRecognitionRepository;
  @Resource
  private DownloadThenUpUtil downloadThenUpUtil;



/*  private Boolean verification(final InvoiceVerificationParam param) {
    InvoiceVerificationInfo invoiceVerificationInfo = doVerification(param);
    if (invoiceVerificationInfo == null || invoiceVerificationInfo.getResult() == null) {
      return false;
    }
    String success_code = "001";
    String code = invoiceVerificationInfo.getResult().getCode();
    if (!Objects.equals(success_code, code)) {
      log.error("合合信息发票验真服务响应异常：{}", JSONUtil.toJsonStr(invoiceVerificationInfo));
      throw new RuntimeException("发票验真服务响应异常");
    }
    return true;
  }*/

/*
  public InvoiceVerificationInfo getVerificationInfo(final InvoiceVerificationParam param) throws RuntimeException{
    InvoiceVerificationInfo invoiceVerificationInfo = doVerification(param);
    String success_code = "001";
    String code = invoiceVerificationInfo.getResult().getCode();
    if (!Objects.equals(success_code, code)) {
      log.error("合合信息发票验真服务响应异常：{}", JSONUtil.toJsonStr(invoiceVerificationInfo));
      throw new RuntimeException("发票验真服务响应异常");
    }
    return invoiceVerificationInfo;
  }
*/




  private InvoiceVerificationInfo doVerification(final InvoiceVerificationParam param) {
    Objects.requireNonNull(param);
    HttpRequest request = HttpUtil.createPost(VERIFICATION_URL).header("x-ti-app-id", HEAD_APP_ID)
        .header("x-ti-secret-code", HEAD_SECRET_CODE)
        .body(com.alibaba.fastjson.JSON.toJSONString(param));
    log.info("请求发票验真服务入参：{}", JSONUtil.toJsonStr(param));
    InvoiceVerificationInfo invoiceVerificationInfo = null;
    try {
      HttpResponse response = request.execute();
      String result = response.body();
      invoiceVerificationInfo = com.alibaba.fastjson.JSON.parseObject(result,
          new TypeReference<InvoiceVerificationInfo>() {});
    } catch (Exception e) {
      log.error("请求发票验真服务出现异常，入参：{}", JSONUtil.toJsonStr(param));
    }
    return invoiceVerificationInfo;
  }


  /**
   * 校验发票状态是否正常
   */
  public boolean doVerification(final InvoiceVerificationInfo invoiceVerificationInfo) {
    if (invoiceVerificationInfo == null || invoiceVerificationInfo.getResult() == null) {
      return false;
    }
    String success_code = "001";
    String code = invoiceVerificationInfo.getResult().getCode();
    return Objects.equals(success_code, code);
  }

  public Optional<InvoiceOcrRecognitionResultDTO> ocrRecognition(String fileUrl) {
    Assert.notBlank(fileUrl);
    try(InputStream inputStream = downloadThenUpUtil.getInputStreamFromOSS(fileUrl);) {
      if (inputStream == null) {
        return Optional.empty();
      }
      byte[] bytes = IoUtil.readBytes(inputStream);
      InvoiceOcrRecognitionResultDTO invoiceOcrRecognitionResultDTO = ocrRecognition(bytes);
      if (invoiceOcrRecognitionResultDTO == null || !Objects.equals(invoiceOcrRecognitionResultDTO.getCode(),
          OCR_RECOGNITION_SUCCESS_CODE)) {
        return Optional.empty();
      }
      return Optional.of(invoiceOcrRecognitionResultDTO);
    } catch (IOException e) {
        throw new RuntimeException(e);
    }
  }





  /**
   * 发票提取服务
   *
   * @param bytes 发票文件
   * @return 提取后映射的信息对象
   */
  public InvoiceOcrRecognitionResultDTO ocrRecognition(byte[] bytes) {
    InvoiceOcrRecognitionResultDTO InvoiceOcrRecognitionResultDTO = null;
    String body = null;
    try {
      body = HttpUtil.createPost(IDENTIFY_URL).header("x-ti-app-id", HEAD_APP_ID)
          .header("x-ti-secret-code", HEAD_SECRET_CODE)
          .header("Content-Type", "application/octet" + "-stream").charset("UTF-8").body(bytes)
          .execute().body();
      InvoiceOcrRecognitionResultDTO = JSON.parseObject(body, new TypeReference<InvoiceOcrRecognitionResultDTO>() {});
      log.info("合合信息发票识别服务响应：{}", body);
    } catch (Exception e) {
      log.info("请求发票提取服务出现异常！", e);
    }
    return InvoiceOcrRecognitionResultDTO;
  }

  public InvoiceOcrRecognition saveOcrRecognitionResult(InvoiceOcrRecognitionResultDTO invoiceOcrRecognitionResultDTO) {
    String invoiceNumber = invoiceOcrRecognitionResultDTO.getInvoiceNumber();
    if (StrUtil.isBlank(invoiceNumber)){
      throw new CheckException("未识别到发票号，请更换更清晰的附件");
    }
    InvoiceOcrRecognition invoiceOcrRecognitionOld =
        invoiceOcrRecognitionRepository.findFirstByInvoiceNumber(invoiceNumber);
    //发票上没有数量字段的默认赋值0
    Optional.ofNullable(invoiceOcrRecognitionResultDTO.getResult()).map(Result::getProduct_list)
        .orElse(new ArrayList<>()).stream().flatMap(Collection::stream).forEach(product -> {
          if ("vat_invoice_electrans_quantity".equals(product.getKey()) && StrUtil.isBlank(
              product.getValue())) {
            product.setValue("0");
          }
        });
    if (invoiceOcrRecognitionOld != null) {
      invoiceOcrRecognitionOld.setOcrInfo(JSON.toJSONString(invoiceOcrRecognitionResultDTO));
      invoiceOcrRecognitionRepository.save(invoiceOcrRecognitionOld);
      return invoiceOcrRecognitionOld;
    }
    InvoiceOcrRecognition invoiceOcrRecognition = new InvoiceOcrRecognition();
    invoiceOcrRecognition.setInvoiceNumber(invoiceNumber);
    invoiceOcrRecognition.setOcrInfo(JSON.toJSONString(invoiceOcrRecognitionResultDTO));
    invoiceOcrRecognitionRepository.save(invoiceOcrRecognition);
    return invoiceOcrRecognition;
  }

  public InvoiceIdentifyResultDTO getInvoiceOcrRecognitionResultVO(String invoiceNumber) {
      InvoiceOcrRecognition invoiceOcrRecognition =
              invoiceOcrRecognitionRepository.findFirstByInvoiceNumber(invoiceNumber);
      if (invoiceOcrRecognition == null) {
          return null;
      }
      InvoiceOcrRecognitionResultDTO invoiceOcrRecognitionResultDTO;
      try {
          invoiceOcrRecognitionResultDTO = JSON.parseObject(invoiceOcrRecognition.getOcrInfo(),
                  new TypeReference<InvoiceOcrRecognitionResultDTO>() {
                  });
      } catch (Exception e) {
          log.error("发票识别结果解析异常", e);
          throw new RuntimeException("发票识别结果解析异常");
      }
    return invoiceOcrRecognitionResultDTO.buildVoObject(null, null);
  }





}
