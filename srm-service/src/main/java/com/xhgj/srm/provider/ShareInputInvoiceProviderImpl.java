package com.xhgj.srm.provider;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.SupplierInvoiceToDetail;
import com.xhgj.srm.jpa.repository.OrderInvoiceRelationRepository;
import com.xhgj.srm.jpa.repository.SupplierInvoiceToDetailRepository;
import com.xhgj.srm.service.ShareInputInvoiceService;
import com.xhgj.srm.v2.provider.ShareInputInvoiceProvider;
import com.xhgj.srm.v2.dto.InputInvoiceOrderWithDetailV2;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ShareInputInvoiceProviderImpl
 */
@Service
public class ShareInputInvoiceProviderImpl implements ShareInputInvoiceProvider {


  @Resource
  SupplierInvoiceToDetailRepository supplierInvoiceToDetailRepository;
  @Resource
  private OrderInvoiceRelationRepository orderInvoiceRelationRepository;

  @Override
  public List<InputInvoiceOrderWithDetailV2> getOrderInvoiceRelationListByDetailIdsRef(List<String> supplierOrderDetailIds) {
    if (CollUtil.isEmpty(supplierOrderDetailIds)) {
      return new ArrayList<>();
    }
    Set<InputInvoiceOrderWithDetailV2> resultSet = new HashSet<>(); // 使用Set来去重
    int batchSize = 1000; // 设置每批处理的大小
    List<List<String>> batches = Lists.partition(supplierOrderDetailIds, batchSize); // 按批次分割

    for (List<String> batch : batches) {
      // 根据订单详情id查询供应商开票关联表
      List<SupplierInvoiceToDetail> supplierInvoiceToDetails = supplierInvoiceToDetailRepository.findAllByDetailIdIn(batch)
          .stream()
          .filter(item -> item.getInputInvoiceOrderId() != null)
          .collect(Collectors.toList());

      // 根据进项票id分组
      Map<String, List<SupplierInvoiceToDetail>> inputOrder2Details = supplierInvoiceToDetails.stream()
          .collect(Collectors.groupingBy(SupplierInvoiceToDetail::getInputInvoiceOrderId));
      // 获取进项票id
      List<String> inputInvoiceIds = supplierInvoiceToDetails.stream()
          .map(SupplierInvoiceToDetail::getInputInvoiceOrderId)
          .distinct()
          .collect(Collectors.toList());

      inputInvoiceIds.add("-1"); // 防止列表为空的情况
      List<InputInvoiceOrder> inputInvoiceOrders = orderInvoiceRelationRepository.findAllById(inputInvoiceIds);

      // 使用Map来避免重复
      for (InputInvoiceOrder item : inputInvoiceOrders) {
        InputInvoiceOrderWithDetailV2 inputInvoiceOrderWithDetail = new InputInvoiceOrderWithDetailV2();
        inputInvoiceOrderWithDetail.setInputInvoiceOrder(item);
        List<SupplierInvoiceToDetail> linkDetails = Optional.ofNullable(inputOrder2Details.get(item.getId())).orElse(new ArrayList<>());
        inputInvoiceOrderWithDetail.setSupplierInvoiceToDetails(linkDetails);

        if (CollUtil.isNotEmpty(inputInvoiceOrderWithDetail.getSupplierInvoiceToDetails())) {
          resultSet.add(inputInvoiceOrderWithDetail); // 添加到Set中以去重
        }
      }
    }

    return new ArrayList<>(resultSet); // 转换Set为List返回
  }

}
