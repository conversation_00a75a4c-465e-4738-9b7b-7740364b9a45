package com.xhgj.srm.factory;/**
 * @since 2025/2/25 16:37
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.common.enums.WarehouseEnum;
import com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderItemType;
import com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderStatus;
import com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderType;
import com.xhgj.srm.common.enums.transferOrder.TransferOrderStatus;
import com.xhgj.srm.common.utils.asmDisOrder.AsmDisOrderBatchNoCleanerAndGenerator;
import com.xhgj.srm.common.utils.asmDisOrder.AsmDisOrderCleanerAndGenerator;
import com.xhgj.srm.common.vo.asmDisOrder.AsmDisOrderItemDTO;
import com.xhgj.srm.common.vo.asmDisOrder.AsmDisOrderListVO;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderSaveForm;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderSaveForm.AsmDisOrderItemSaveForm;
import com.xhgj.srm.jpa.entity.*;
import com.xhgj.srm.jpa.repository.*;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.vo.asmDisOrder.AsmDisOrderItemVO;
import com.xhgj.srm.vo.asmDisOrder.AsmDisOrderVO;
import com.xhiot.boot.core.common.exception.CheckException;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/2/25 16:37:41
 *@description 组装拆卸单工厂
 */
@Component
public class AsmDisOrderFactory {

  @Resource
  private AsmDisOrderRepository asmDisOrderRepository;
  @Resource
  private GroupRepository groupRepository;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private AsmDisOrderItemRepository asmDisOrderItemRepository;
  @Resource
  private SAPService sapService;
  @Resource
  private InventoryLocationRepository inventoryLocationRepository;
  @Resource
  private PurchaseApplyForOrderRepository purchaseApplyForOrderRepository;
  @Resource
  private RedissonClient RedissonClient;

  /**
   *  #check 校验仓库
   * @param form
   */
  public void checkWarehouse(AsmDisOrderSaveForm form) {
    String warehouseId = form.getFp().getWarehouseId();
    List<AsmDisOrderItemSaveForm> sub = form.getSub();
    for (AsmDisOrderItemSaveForm one : sub) {
      if (!StrUtil.equals(one.getWarehouseId(), warehouseId)) {
        throw new CheckException("仓库不一致，请修改");
      }
    }
  }

  /**
   * #check 校验成品与子件不能出现相同的物料编码
   */
  public void checkProductCode(AsmDisOrderSaveForm form) {
    String fpProductCode = form.getFp().getProductCode();
    List<AsmDisOrderItemSaveForm> sub = form.getSub();
    for (AsmDisOrderItemSaveForm one : sub) {
      if (StrUtil.equals(one.getProductCode(), fpProductCode)) {
        throw new CheckException("成品与子件不能出现相同的物料编码");
      }
    }
  }


  /**
   * #check 校验暂存时,原单状态
   */
  public void checkTemporary(AsmDisOrderSaveForm saveForm, AsmDisOrder origin) {
    if (saveForm.getSaveType() == 1) {
      // 驳回状态 和 暂存状态 可以暂存
      if (!AsmDisOrderStatus.REJECT.getCode().equals(origin.getStatus())
          && !AsmDisOrderStatus.TEMPORARY.getCode().equals(origin.getStatus())) {
        throw new CheckException("只有驳回状态和暂存状态的单据可以暂存");
      }
    }
  }

  /**
   * #check 校验直销库
   * @param form
   */
  public void checkDirectSale(AsmDisOrderSaveForm form) {
    String warehouseId = form.getFp().getWarehouseId();
    InventoryLocation inventoryLocation = inventoryLocationRepository.findById(warehouseId)
        .orElseThrow(() -> new CheckException("仓库不存在"));
    if (WarehouseEnum.HAI_NING_DIRECT_SALES.getCode().equals(inventoryLocation.getWarehouse())) {
      // 如果是组装单 则判断子件是否填写批次;成品关联销售订单是否填写
      if (AsmDisOrderType.ASSEMBLY_ORDER.getCode() == form.getType()) {
        for (AsmDisOrderItemSaveForm one : form.getSub()) {
          if (StrUtil.isBlank(one.getBatchNo())) {
            throw new CheckException("组装单直销库时，子件批次不能为空");
          }
        }
        if (StrUtil.isBlank(form.getFp().getSaleOrderNo()) || StrUtil.isBlank(form.getFp().getSaleOrderProductRowId())) {
          throw new CheckException("组装单直销库时，销售订单号和销售订单行号不能为空");
        }
      }
      // 如果是拆卸单 则判断成品是否填写批次
      if (AsmDisOrderType.DISASSEMBLY_ORDER.getCode() == form.getType()) {
        if (StrUtil.isBlank(form.getFp().getBatchNo())) {
          throw new CheckException("拆卸单直销库时，成品批次不能为空");
        }
      }
    }
  }


  public AsmDisOrder createAsmDisOrder(AsmDisOrderSaveForm saveForm) {
    // 查询group
    Group group =
        groupRepository.findFirstByErpCodeAndState(saveForm.getGroupCode(), Constants.STATE_OK);
    if (group == null) {
      throw new CheckException("创建组织不存在");
    }
    // 查询部门
    Group purchaseDept = groupRepository.findById(saveForm.getDeptId())
        .orElseThrow(() -> new CheckException("创建部门不存在"));
    AsmDisOrder asmDisOrder = null;
    if (StrUtil.isBlank(saveForm.getId())) {
      asmDisOrder = new AsmDisOrder();
      asmDisOrder.setCode(AsmDisOrderCleanerAndGenerator.INSTANCE.generate(saveForm.getType()));
      asmDisOrder.setType(saveForm.getType());

      asmDisOrder.setCreateMan(saveForm.getCreateMan());
      asmDisOrder.setCreateManCode(saveForm.getCreateManCode());
      asmDisOrder.setCreateManName(saveForm.getCreateManName());
      asmDisOrder.setUpdateTime(System.currentTimeMillis());
      asmDisOrder.setCreateTime(System.currentTimeMillis());
      asmDisOrder.setState(Constants.STATE_OK);
    } else {
      asmDisOrder = asmDisOrderRepository.findById(saveForm.getId())
          .orElseThrow(() -> new CheckException("组装拆卸单不存在"));
      asmDisOrder.setUpdateTime(System.currentTimeMillis());
      // #check 是否可暂存
      checkTemporary(saveForm, asmDisOrder);
    }
    asmDisOrder.setGroupCode(group.getCode());
    asmDisOrder.setGroupName(group.getName());
    asmDisOrder.setDeptId(purchaseDept.getId());
    asmDisOrder.setDeptCode(purchaseDept.getErpCode());
    asmDisOrder.setDeptName(purchaseDept.getName());
    asmDisOrder.setStartTime(saveForm.getStartTime());
    asmDisOrder.setEndTime(saveForm.getEndTime());
    asmDisOrder.setRemark(saveForm.getRemark());
    asmDisOrder.setStatus(AsmDisOrderStatus.AUDITING.getCode());
    String originReviewId = asmDisOrder.getReviewId();
    // 订单状态为审核中时清空审核人等信息
    if (TransferOrderStatus.AUDITING.getCode().equals(asmDisOrder.getStatus())) {
      asmDisOrder.setReviewId(null);
      asmDisOrder.setReviewer(null);
      asmDisOrder.setReviewTime(null);
    }
    if (saveForm.getSaveType() == 1) {
      // 驳回 -> 暂存 保留审核id
      asmDisOrder.setStatus(TransferOrderStatus.TEMPORARY.getCode());
      asmDisOrder.setReviewId(originReviewId);
    }
    // 设置库位的业务类型
    if (saveForm.getFp() != null) {
      String warehouseId = saveForm.getFp().getWarehouseId();
      InventoryLocation inventoryLocation = inventoryLocationRepository.findById(warehouseId)
          .orElseThrow(() -> new CheckException("仓库不存在"));
      asmDisOrder.setBusinessType(inventoryLocation.getBusinessType());
    }
    return asmDisOrder;
  }



  public AsmDisOrderVO buildVO(AsmDisOrder asmDisOrder, List<AsmDisOrderItem> asmDisOrderItems) {
    AsmDisOrderVO res = MapStructFactory.INSTANCE.toAsmDisOrderVO(asmDisOrder);
    AsmDisOrderItemVO fp = asmDisOrderItems.stream().filter(item -> item.getType().equals(AsmDisOrderItemType.FP.getCode())).findFirst()
        .map(MapStructFactory.INSTANCE::toAsmDisOrderItemVO).orElse(null);
    if (fp == null) {
      throw new CheckException("成品明细不存在");
    }
    String warehouseId = fp.getWarehouseId();
    InventoryLocation inventoryLocation = inventoryLocationRepository.findById(warehouseId)
        .orElseThrow(() -> new CheckException("仓库不存在"));
    fp.setWarehouseCode(inventoryLocation.getWarehouse());
    fp.setWarehouseName(inventoryLocation.getWarehouseName());
    List<AsmDisOrderItemVO> sub = asmDisOrderItems.stream().filter(item -> item.getType().equals(AsmDisOrderItemType.SUB.getCode()))
        .map(MapStructFactory.INSTANCE::toAsmDisOrderItemVO).collect(Collectors.toList());
    sub.forEach(one -> {
      one.setWarehouseCode(inventoryLocation.getWarehouse());
      one.setWarehouseName(inventoryLocation.getWarehouseName());
    });
    res.setFp(fp);
    res.setSub(sub);
    res.setHasMovement(asmDisOrder.hasMovement());
    return res;
  }

  public List<AsmDisOrderListVO> buildListVos(List<AsmDisOrder> asmDisOrders) {
    if (CollUtil.isEmpty(asmDisOrders)) {
      return new ArrayList<>();
    }
    // 获取ids
    List<String> ids = asmDisOrders.stream().map(AsmDisOrder::getId).collect(Collectors.toList());
    List<AsmDisOrderItem> asmDisOrderItems =
        asmDisOrderItemRepository.findByAsmDisIdInAndState(ids, Constants.STATE_OK);
    List<String> warehouseIds = asmDisOrderItems.stream().map(AsmDisOrderItem::getWarehouseId)
        .distinct().filter(StrUtil::isNotBlank).collect(Collectors.toList());
    warehouseIds.add("-1");
    List<InventoryLocation> inventoryLocations = inventoryLocationRepository.findAllById(warehouseIds);
    Map<String, InventoryLocation> id2InventoryLocation =
        inventoryLocations.stream().collect(Collectors.toMap(InventoryLocation::getId,
            Function.identity(), (k1, k2) -> k1));
    // 获取明细
    Map<String, List<AsmDisOrderItem>> id2AsmDisOrderItem =
        asmDisOrderItems.stream()
            .collect(Collectors.groupingBy(AsmDisOrderItem::getAsmDisId));
    return asmDisOrders.stream()
        .map(item -> this.buildListVo(item, id2AsmDisOrderItem.get(item.getId()),
            id2InventoryLocation))
        .collect(Collectors.toList());
  }

  private AsmDisOrderListVO buildListVo(AsmDisOrder asmDisOrder,
      List<AsmDisOrderItem> asmDisOrderItems, Map<String, InventoryLocation> id2InventoryLocation) {
    AsmDisOrderListVO vo = new AsmDisOrderListVO();
    asmDisOrderItems = CollUtil.emptyIfNull(asmDisOrderItems);
    vo.setId(asmDisOrder.getId());
    vo.setCode(asmDisOrder.getCode());
    vo.setType(asmDisOrder.getType());
    vo.setStatus(asmDisOrder.getStatus());
    AsmDisOrderItem fpItem = asmDisOrderItems.stream()
        .filter(item -> item.getType().equals(AsmDisOrderItemType.FP.getCode())).findFirst()
        .orElse(null);
    List<AsmDisOrderItem> subItems = asmDisOrderItems.stream()
        .filter(item -> item.getType().equals(AsmDisOrderItemType.SUB.getCode())).collect(Collectors.toList());
    if (fpItem != null) {
      vo.setFp(this.buildDTO(fpItem, asmDisOrder,
          id2InventoryLocation.get(fpItem.getWarehouseId())));
    }
    vo.setSub(new ArrayList<>());
    if (CollUtil.isNotEmpty(subItems)) {
      vo.setSub(subItems.stream().map(item -> this.buildDTO(item, asmDisOrder,
          id2InventoryLocation.get(item.getWarehouseId()))).collect(Collectors.toList()));
    }
    return vo;
  }

  private AsmDisOrderItemDTO buildDTO(AsmDisOrderItem item, AsmDisOrder asmDisOrder,
      InventoryLocation inventoryLocation) {
    AsmDisOrderItemDTO dto = new AsmDisOrderItemDTO();
    dto.setId(item.getId());
    dto.setRowId(item.getRowId());
    dto.setType(item.getType());
    dto.setProductCode(item.getProductCode());
    dto.setBrand(item.getBrand());
    dto.setProductName(item.getProductName());
    dto.setModel(item.getModel());
    dto.setUnit(item.getUnit());
    dto.setUnitCode(item.getUnitCode());
    dto.setNum(item.getNum());
    dto.setWarehouse(item.getWarehouseId());
    dto.setWarehouseName(inventoryLocation.getWarehouseName());
    dto.setBatchNo(item.getBatchNo());
    dto.setCreateManCode(asmDisOrder.getCreateManCode());
    dto.setCreateManName(asmDisOrder.getCreateManName());
    dto.setCreateTime(asmDisOrder.getCreateTime());
    dto.setReviewer(asmDisOrder.getReviewer());
    dto.setReviewTime(asmDisOrder.getReviewTime());
    dto.setTax(item.getTax());
    dto.setOriginNetPrice(item.getOriginNetPrice());
    if (item.getType().equals(AsmDisOrderItemType.FP.getCode())) {
      dto.setWarehouseOperator(asmDisOrder.getWarehouseOperatorFp());
      dto.setWarehouseTime(asmDisOrder.getWarehouseTimeFp());
      dto.setProductVoucher(asmDisOrder.getProductVoucherFp());
      dto.setProductVoucherYear(asmDisOrder.getProductVoucherYearFp());
    } else {
      dto.setWarehouseOperator(asmDisOrder.getWarehouseOperatorSub());
      dto.setWarehouseTime(asmDisOrder.getWarehouseTimeSub());
      dto.setProductVoucher(asmDisOrder.getProductVoucherSub());
      dto.setProductVoucherYear(asmDisOrder.getProductVoucherYearSub());
    }
    return dto;
  }

  private AsmDisOrderItem updateAsmDisOrderItem(
      AsmDisOrderItemSaveForm saveForm,
      AsmDisOrder asmDisOrder,
      AsmDisOrderItem origin,
      Byte itemType,
      Map<String, PurchaseApplyForOrder> purchaseApplyForOrderMap
  ) {
    AsmDisOrderItem asmDisOrderItem = MapStructFactory.INSTANCE.toAsmDisOrderItem(saveForm);
    asmDisOrderItem.setCreateTime(System.currentTimeMillis());
    asmDisOrderItem.setType(itemType);
    asmDisOrderItem.setUpdateTime(System.currentTimeMillis());
    asmDisOrderItem.setState(Constants.STATE_OK);
    asmDisOrderItem.setAsmDisId(asmDisOrder.getId());
    asmDisOrderItem.setAsmDisCode(asmDisOrder.getCode());
    asmDisOrderItem.setAsmDisType(asmDisOrder.getType());
    if (StrUtil.isNotBlank(saveForm.getPurchaseApplyForOrderId())) {
      PurchaseApplyForOrder purchaseApplyForOrder =
          purchaseApplyForOrderMap.computeIfAbsent(saveForm.getPurchaseApplyForOrderId(),
              k -> purchaseApplyForOrderRepository.findById(saveForm.getPurchaseApplyForOrderId())
                  .orElseThrow(() -> new CheckException("采购申请单不存在")));
      asmDisOrderItem.setSaleOrderNo(purchaseApplyForOrder.getSaleOrderNo());
      asmDisOrderItem.setSaleOrderProductRowId(purchaseApplyForOrder.getSaleOrderProductRowId());
      asmDisOrderItem.setPurchaseApplyForOrderId(purchaseApplyForOrder.getId());
      asmDisOrderItem.setPurchaseApplyForOrderCode(
          StrUtil.format("{}-{}", purchaseApplyForOrder.getApplyForOrderNo(),
              purchaseApplyForOrder.getSerialNumber()));
      lockPurchaseApplyForOrder(asmDisOrder.getType(), asmDisOrderItem.getNum(), purchaseApplyForOrder.getId(), itemType, purchaseApplyForOrderMap);
    }
    asmDisOrderItem.setType(itemType);
    if (origin != null) {
      asmDisOrderItem.setCreateTime(origin.getCreateTime());
      asmDisOrderItem.setState(origin.getState());
      asmDisOrderItem.setId(origin.getId());
    }
    return asmDisOrderItem;

  }

  public List<AsmDisOrderItem> createAsmDisOrderItemFp(AsmDisOrderSaveForm form,
      AsmDisOrder asmDisOrder, Map<String, PurchaseApplyForOrder> purchaseApplyForOrderMap) {
    List<AsmDisOrderItem> res = new ArrayList<>();
    List<AsmDisOrderItem> origins =
        asmDisOrderItemRepository.findByAsmDisIdAndTypeAndState(asmDisOrder.getId(),
            AsmDisOrderItemType.FP.getCode(), Constants.STATE_OK);
    AsmDisOrderItemSaveForm fp = form.getFp();
    AsmDisOrderItem origin = null;
    if (StrUtil.isNotBlank(fp.getId())) {
      origin = origins.stream().filter(item -> StrUtil.equals(item.getId(), fp.getId())).findFirst()
          .orElseThrow(() -> new CheckException("成品明细不存在"));
    }
    AsmDisOrderItem asmDisOrderItem =
        updateAsmDisOrderItem(fp, asmDisOrder, origin, AsmDisOrderItemType.FP.getCode(),
            purchaseApplyForOrderMap);
    res.add(asmDisOrderItem);
    // 删除原有的成品
    List<AsmDisOrderItem> deleteOnes = origins.stream()
        .filter(item -> !StrUtil.equals(item.getId(), fp.getId())).collect(Collectors.toList());
    if (CollUtil.isNotEmpty(deleteOnes)) {
      deleteOnes.forEach(item -> {
        item.setState(Constants.STATE_DELETE);
        item.setUpdateTime(System.currentTimeMillis());
      });
      res.addAll(deleteOnes);
    }
    return res;
  }


  public List<AsmDisOrderItem> createAsmDisOrderItemSub(AsmDisOrderSaveForm form,
      AsmDisOrder asmDisOrder, Map<String, PurchaseApplyForOrder> purchaseApplyForOrderMap) {
    List<AsmDisOrderItem> res = new ArrayList<>();
    List<AsmDisOrderItemSaveForm> items = form.getSub();
    List<AsmDisOrderItem> origins =
        asmDisOrderItemRepository.findByAsmDisIdAndTypeAndState(asmDisOrder.getId(),
            AsmDisOrderItemType.SUB.getCode(), Constants.STATE_OK);
    Map<String, AsmDisOrderItem> originMap = origins.stream()
        .collect(Collectors.toMap(AsmDisOrderItem::getId, item -> item));
    // patch更新
    // 过滤出新增的
    List<AsmDisOrderItemSaveForm> addItems = items.stream().filter(item -> StrUtil.isBlank(item.getId()))
        .collect(Collectors.toList());
    // 过滤出更新的
    List<AsmDisOrderItemSaveForm> updateItems = items.stream().filter(item -> StrUtil.isNotBlank(item.getId()))
        .collect(Collectors.toList());
    // 过滤出删除的
    List<AsmDisOrderItem> deleteItems = origins.stream()
        .filter(origin -> items.stream().noneMatch(item -> StrUtil.equals(origin.getId(), item.getId())))
        .collect(Collectors.toList());
    // 处理新增
    if (CollUtil.isNotEmpty(addItems)) {
      List<AsmDisOrderItem> addNewOnes = addItems.stream().map(item -> updateAsmDisOrderItem(item
          , asmDisOrder, null, AsmDisOrderItemType.SUB.getCode(), purchaseApplyForOrderMap)).collect(Collectors.toList());
      res.addAll(addNewOnes);
    }
    // 处理更新
    if (CollUtil.isNotEmpty(updateItems)) {
      List<AsmDisOrderItem> updateOnes = updateItems.stream().map(item -> {
        AsmDisOrderItem origin = originMap.get(item.getId());
        if (origin == null) {
          throw new CheckException("更新的子件明细不存在");
        }
        return updateAsmDisOrderItem(item, asmDisOrder, origin, AsmDisOrderItemType.SUB.getCode()
            , purchaseApplyForOrderMap);
      }).collect(Collectors.toList());
      res.addAll(updateOnes);
    }
    // 处理删除
    if (CollUtil.isNotEmpty(deleteItems)) {
      deleteItems.forEach(item -> {
        item.setState(Constants.STATE_DELETE);
        item.setUpdateTime(System.currentTimeMillis());
      });
      res.addAll(deleteItems);
    }
    return res;
  }
  /**
   * 处理 PurchaseApplyForOrder,释放数量
   */
  public void releasePurchaseApplyForOrder(Byte orderType,
      BigDecimal num,
      String purchaseApplyForOrderId,
      Byte itemType,
      Map<String, PurchaseApplyForOrder> purchaseApplyForOrderMap) {
    if (StrUtil.isNotBlank(purchaseApplyForOrderId)) {
      PurchaseApplyForOrder purchaseApplyForOrder =
          purchaseApplyForOrderMap.computeIfAbsent(purchaseApplyForOrderId, k -> purchaseApplyForOrderRepository.findById(
                  purchaseApplyForOrderId)
              .orElseThrow(() -> new CheckException("采购申请单不存在")));
      BigDecimal orderGoodsNumber = Optional.ofNullable(purchaseApplyForOrder.getOrderGoodsNumber())
          .orElse(BigDecimal.ZERO);
      if (itemType.equals(AsmDisOrderItemType.FP.getCode())) {
        // 成品
        if (AsmDisOrderType.ASSEMBLY_ORDER.getCode() == orderType) {
          // 组装单时 扣减采购申请已订货数量
          purchaseApplyForOrder.setOrderGoodsNumber(orderGoodsNumber.subtract(num));
        }else{
          // 拆卸单时 增加采购申请已订货数量
          purchaseApplyForOrder.setOrderGoodsNumber(orderGoodsNumber.add(num));
        }
      }
      if (itemType.equals(AsmDisOrderItemType.SUB.getCode())) {
        // 子件
        if (AsmDisOrderType.ASSEMBLY_ORDER.getCode() == orderType) {
          // 组装单时 增加采购申请已订货数量
          purchaseApplyForOrder.setOrderGoodsNumber(orderGoodsNumber.add(num));
        }else{
          // 拆卸单时 扣减采购申请已订货数量
          purchaseApplyForOrder.setOrderGoodsNumber(orderGoodsNumber.subtract(num));
        }
      }
      // 更新orderGoodsState
      purchaseApplyForOrder.updateOrderGoodsStateCheckLock();
    }
  }

  /**
   * 处理 PurchaseApplyForOrder,锁定数量
   */
  public void lockPurchaseApplyForOrder(Byte orderType,
      BigDecimal num,
      String purchaseApplyForOrderId,
      Byte itemType,
      Map<String, PurchaseApplyForOrder> purchaseApplyForOrderMap) {
    if (StrUtil.isNotBlank(purchaseApplyForOrderId)) {
      PurchaseApplyForOrder purchaseApplyForOrder =
          purchaseApplyForOrderMap.computeIfAbsent(purchaseApplyForOrderId,
              k -> purchaseApplyForOrderRepository.findById(purchaseApplyForOrderId)
                  .orElseThrow(() -> new CheckException("采购申请单不存在")));
      if (SimpleBooleanEnum.NO.getKey().equals(purchaseApplyForOrder.getOrderGoodsState())) {
        throw new CheckException("采购申请单处于不可订货状态");
      }
      BigDecimal orderGoodsNumber = Optional.ofNullable(purchaseApplyForOrder.getOrderGoodsNumber())
          .orElse(BigDecimal.ZERO);
      // 订货总数量
      BigDecimal applyForNumber = Optional.ofNullable(purchaseApplyForOrder.getApplyForNumber())
          .orElse(BigDecimal.ZERO);
      // 计算未订货数量
      BigDecimal unOrderGoodsNumber = applyForNumber.subtract(orderGoodsNumber);
      // 判断是否超过未订货数量
      if (NumberUtil.isGreater(num, unOrderGoodsNumber)) {
        throw new CheckException(StrUtil.format("{}超出采购申请的可订货数量，请核实！", purchaseApplyForOrder.getProductCode()));
      }
      if (itemType.equals(AsmDisOrderItemType.FP.getCode())) {
        // 成品
        if (AsmDisOrderType.ASSEMBLY_ORDER.getCode() == orderType) {
          // 组装单时 增加采购申请已订货数量
          purchaseApplyForOrder.setOrderGoodsNumber(orderGoodsNumber.add(num));
        } else {
          // 拆卸单时 扣减采购申请已订货数量
          purchaseApplyForOrder.setOrderGoodsNumber(orderGoodsNumber.subtract(num));
        }
      }
      if (itemType.equals(AsmDisOrderItemType.SUB.getCode())) {
        // 子件
        if (AsmDisOrderType.ASSEMBLY_ORDER.getCode() == orderType) {
          // 组装单时 扣减采购申请已订货数量
          purchaseApplyForOrder.setOrderGoodsNumber(orderGoodsNumber.subtract(num));
        } else {
          // 拆卸单时 增加采购申请已订货数量
          purchaseApplyForOrder.setOrderGoodsNumber(orderGoodsNumber.add(num));
        }
      }
      // 更新orderGoodsState
      purchaseApplyForOrder.updateOrderGoodsStateCheckLock();
    }
  }

  /**
   * 批量生成批次并更新
   */
  public List<AsmDisOrderItem> batchGenerateBatchNoAndUpdate(AsmDisOrder asmDisOrder) {
    List<AsmDisOrderItem> res = new ArrayList<>();
    // 如果是组装单，则生成成品
    if (AsmDisOrderType.ASSEMBLY_ORDER.getCode() == asmDisOrder.getType()) {
      res = asmDisOrderItemRepository.findByAsmDisIdAndTypeAndState(asmDisOrder.getId(),
          AsmDisOrderItemType.FP.getCode(), Constants.STATE_OK);
    }
    // 如果是拆卸单，则生成子件
    if (AsmDisOrderType.DISASSEMBLY_ORDER.getCode() == asmDisOrder.getType()) {
      res = asmDisOrderItemRepository.findByAsmDisIdAndTypeAndState(asmDisOrder.getId(),
          AsmDisOrderItemType.SUB.getCode(), Constants.STATE_OK);
    }
    if (CollUtil.isNotEmpty(res)) {
      res.forEach(one -> {
        one.setBatchNo(AsmDisOrderBatchNoCleanerAndGenerator.INSTANCE.generate(RedissonClient));
        one.setUpdateTime(System.currentTimeMillis());
      });
    }
    return res;
  }
}
