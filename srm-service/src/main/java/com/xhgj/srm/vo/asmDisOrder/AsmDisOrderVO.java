package com.xhgj.srm.vo.asmDisOrder;/**
 * @since 2025/2/26 15:11
 */

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderStatus;
import com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderType;
import lombok.Data;
import java.util.List;

/**
 * 组装拆卸单VO
 */
@Data
public class AsmDisOrderVO {

  /**
   * id
   */
  private String id;

  /**
   * 组装拆卸单号
   */
  private String code;

  /**
   * 单据类型 1.组装单 2.拆卸单
   * @see com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderType
   */
  private Byte type;

  /**
   * 单据类型value
   */
  private String typeValue;

  /**
   * 订单状态
   * @see com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderStatus
   */
  private Byte status;

  /**
   * 调拨状态value
   */
  private String statusValue;

  /**
   * 开始时间
   */
  private Long startTime;

  /**
   * 结束时间
   */
  private Long endTime;

  /**
   * 创建组织code
   */
  private String groupCode;

  /**
   * 创建组织名称
   */
  private String groupName;

  /**
   * 业务类型
   * @see com.xhgj.srm.common.enums.transferOrder.WarehouseBusinessType
   */
  private String businessType;

  /**
   * 创建部门id
   */
  private String deptId;

  /**
   * 创建部门
   */
  private String deptCode;

  /**
   * 创建部门名称
   */
  private String deptName;

  /**
   * 创建人id
   */
  private String createMan;

  /**
   * 创建人code
   */
  private String createManCode;

  /**
   * 创建人名称
   */
  private String createManName;

  /**
   * 创建人mix
   */
  private String createManMix;

  /**
   * 备注
   */
  private String remark;

  /**
   * 审核人
   */
  private String reviewer;

  /**
   * 审核id
   */
  private String reviewId;

  /**
   * 审核人code
   */
  private String reviewerCode;

  /**
   * 审核时间
   */
  private Long reviewTime;


  /**
   * sap财务凭证号 - 成品
   */
  private String productVoucherFp;

  /**
   * sap财务凭证年份 - 成品
   */
  private String productVoucherYearFp;

  /**
   * 仓库操作人 - 成品
   */
  private String warehouseOperatorFp;

  /**
   * 仓库操作时间 - 成品
   */
  private Long warehouseTimeFp;

  /**
   * sap财务凭证号 - 子件
   */
  private String productVoucherSub;

  /**
   * sap财务凭证年份 - 子件
   */
  private String productVoucherYearSub;

  /**
   * 仓库操作人 - 子件
   */
  private String warehouseOperatorSub;

  /**
   * 仓库操作时间 - 子件
   */
  private Long warehouseTimeSub;

  /**
   * 更新时间
   */
  private Long updateTime;

  /**
   * 创建时间
   */
  private Long createTime;

  /**
   * 是否涉及wms-货物移动
   */
  private Boolean hasMovement;

  /**
   * 成品明细
   */
  private AsmDisOrderItemVO fp;

  /**
   * 子件明细
   */
  private List<AsmDisOrderItemVO> sub;

  /**
   * 单据类型value
   * @return
   */
  public String getTypeValue() {
    return AsmDisOrderType.getNameByCode(this.type);
  }

  /**
   * 调拨状态value
   * @return
   */
  public String getStatusValue() {
    return AsmDisOrderStatus.getNameByCode(this.status);
  }

  /**
   * 创建人mix
   * @return
   */
  public String getCreateManMix() {
    if (StrUtil.isNotBlank(createManCode)
        && createManCode.length() > 4
        && StrUtil.isNotBlank(createManName)
    ) {
      return createManCode.substring(createManCode.length() - 4) + createManName;
    }
    return null;
  }
}
