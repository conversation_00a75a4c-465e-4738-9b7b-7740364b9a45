package com.xhgj.srm.vo.transferOrder;/**
 * @since 2025/2/26 15:18
 */

import com.xhgj.srm.common.enums.transferOrder.TransferOrderType;
import lombok.Data;
import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/2/26 15:18:19
 *@description 调拨单明细VO
 */
@Data
public class TransferOrderItemVO {
  /**
   * 调拨单明细id
   */
  private String id;

  /**
   * 行id
   */
  private String rowId;

  /**
   * 物料编码
   */
  private String productCode;

  /**
   * 品牌
   */
  private String brand;

  /**
   * 物料名称
   */
  private String productName;

  /**
   * 描述
   */
  private String desc;

  /**
   * 型号规格
   */
  private String model;

  /**
   * 单位
   */
  private String unit;

  /**
   * 单位编码
   */
  private String unitCode;

  /**
   * 调拨数量
   */
  private BigDecimal num;

  /**
   * 调出库位id
   */
  private String warehouseOut;

  /**
   * 调出仓库编码
   */
  private String warehouseOutCode;

  /**
   * 调出仓库名称
   */
  private String warehouseOutName;

  /**
   * 调入库位id
   */
  private String warehouseIn;

  /**
   * 调入仓库编码
   */
  private String warehouseInCode;


  /**
   * 调入仓库名称
   */
  private String warehouseInName;

  /**
   * 批号
   */
  private String batchNo;

  /**
   * 备注
   */
  private String remark;

  /**
   * 库存属性 attr  1标准  2寄售
   * @see com.xhgj.srm.common.enums.transferOrder.TransferOrderType
   */
  private Byte stockAttr;

  /**
   * 库存属性值
   */
  private String stockAttrValue;

  /**
   * 获取库存属性值
   */
  public String getStockAttrValue() {
    return TransferOrderType.getNameByCode(this.stockAttr);
  }
}
