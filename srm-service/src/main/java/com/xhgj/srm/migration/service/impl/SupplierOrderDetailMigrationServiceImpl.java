package com.xhgj.srm.migration.service.impl;/**
 * @since 2025/5/30 16:17
 */

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.jpa.entity.SupplierInvoiceToDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.migration.MigrationRecord;
import com.xhgj.srm.jpa.repository.MigrationRecordRepository;
import com.xhgj.srm.jpa.repository.SupplierInvoiceToDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.util.LazyLoaderContext;
import com.xhgj.srm.migration.service.MigrationService;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/5/30 16:17:10
 *@description
 */
@Service
@Slf4j
public class SupplierOrderDetailMigrationServiceImpl implements MigrationService {
  public static final String TABLE = "t_supplier_order_detail";
  @Resource
  MigrationRecordRepository migrationRecordRepository;
  @Resource
  private SupplierInvoiceToDetailRepository supplierInvoiceToDetailRepository;
  // ------v1--------
  @Resource
  private SupplierOrderDetailRepository supplierOrderDetailRepository;
  // -----v2--------
  @Resource
  private SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;
  @Resource
  private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;

  @Override
  public String migrateTable(String originId, String relationId, String batchNo, Map<String, Object> ext) {
    // 1.判断V1版本是否存在
    SupplierOrderDetail supplierOrderDetail = supplierOrderDetailRepository.findById(originId)
        .orElseThrow(() -> new CheckException("订单明细不存在"));
    // 2.迁移v1版本到v2版本
    SupplierOrderDetailV2 supplierOrderDetailV2 = MapStructFactory.INSTANCE.toSupplierOrderDetailV2(supplierOrderDetail);
    SupplierOrderToFormV2 supplierOrderToFormV2 =
        supplierOrderToFormV2Repository.findById(relationId)
            .orElseThrow(() -> new CheckException("订单表单不存在"));
    // 3.其他规则
    String index = Convert.toStr(ext.get("index"));
    String supplierOrderId = Convert.toStr(ext.get("supplierOrderId"));
    // #rule 物料明细form
    if (supplierOrderToFormV2.getType().equals(SupplierOrderFormType.DETAILED.getType())) {
      // 待申请入库数量 = 订货数量-取消数量-入库数量-退库数量
      supplierOrderDetailV2.setWaitQty(NumberUtil.sub(
          supplierOrderDetailV2.getNum(),
          supplierOrderDetailV2.getCancelQty(),
          supplierOrderDetailV2.getStockInputQty()
      ));
      // 已申请入库数量 = 展示入库数量
      supplierOrderDetailV2.setShipQty(supplierOrderDetailV2.getStockInputQty());
    }
    // #rule 入库单
    if (supplierOrderToFormV2.getType().equals(SupplierOrderFormType.WAREHOUSING.getType())) {
      // 交给SupplierOrderDetailSpecialMigrationServiceImpl处理InWareHouseApplyId
      supplierOrderDetailV2.setInWareHouseApplyId(null);
      supplierOrderDetailV2.setWarehouse(supplierOrderToFormV2.getWarehouseCode());
      supplierOrderDetailV2.setWarehouseName(supplierOrderToFormV2.getWarehouseName());
    }
    // #rule 退货单
    if (supplierOrderToFormV2.getType().equals(SupplierOrderFormType.RETURN.getType())) {
      // 退库单的入库单序号固定 GR2505010001--- id交给主方法中的handleReturnForm处理
      supplierOrderDetailV2.setInWareHouseId(null);
      supplierOrderDetailV2.setWarehouse(supplierOrderToFormV2.getWarehouseCode());
      supplierOrderDetailV2.setWarehouseName(supplierOrderToFormV2.getWarehouseName());
    }
    // 4.save
    supplierOrderDetailV2.setOrderToFormId(relationId);
    supplierOrderDetailV2.setSupplierOrderProduct(null);
    supplierOrderDetailV2.setDetailed(null);
    supplierOrderDetailV2.setOrderToFormType(supplierOrderToFormV2.getType());
    supplierOrderDetailV2.setIndex(index);
    supplierOrderDetailV2.setPurchaseOrderId(supplierOrderId);
    supplierOrderDetailV2Repository.saveAndFlush(supplierOrderDetailV2);
    supplierOrderDetail.setState(Constants.STATE_DELETE);
    supplierOrderDetailRepository.saveAndFlush(supplierOrderDetail);
    // 5.记录日志
    this.recordMigration(originId, supplierOrderDetailV2.getId(), batchNo, migrationRecordRepository);
    return supplierOrderDetailV2.getId();
  }

  @Override
  public void refreshLinkTable(String originId, String newId) {
    // 查询detailId有关联的
    List<SupplierOrderDetailV2> detailV2List =
        supplierOrderDetailV2Repository.findAllByDetailedIdAndState(originId, Constants.STATE_OK);
    detailV2List.forEach(detailV2 -> {
      detailV2.setDetailedId(newId);
      supplierOrderDetailV2Repository.saveAndFlush(detailV2);
    });
    // 查询entrustDetailId有关联的
    List<SupplierOrderDetailV2> entrustDetailV2List =
        supplierOrderDetailV2Repository.findAllByEntrustDetailIdAndState(originId, Constants.STATE_OK);
    entrustDetailV2List.forEach(detailV2 -> {
      detailV2.setEntrustDetailId(newId);
      supplierOrderDetailV2Repository.saveAndFlush(detailV2);
    });
    // 进项票关联明细
    List<SupplierInvoiceToDetail> allByDetailIdIn =
        supplierInvoiceToDetailRepository.findAllByDetailIdIn(Collections.singletonList(originId));
    allByDetailIdIn.forEach(item -> {
      item.setDetailId(newId);
      supplierInvoiceToDetailRepository.save(item);
    });
  }

  @Override
  public void rollback(MigrationRecord migrationRecord) {
    // 查询v2版本
    SupplierOrderDetailV2 supplierOrderDetailV2 = LazyLoaderContext.lazyLoad(
        () -> supplierOrderDetailV2Repository.findById(migrationRecord.getNewId())
            .orElseThrow(() -> new CheckException("订单明细不存在")));
    // 删除v2版本
    supplierOrderDetailV2.setState(Constants.STATE_DELETE);
    supplierOrderDetailV2Repository.saveAndFlush(supplierOrderDetailV2);
    // 恢复v1版本
    SupplierOrderDetail supplierOrderDetail = LazyLoaderContext.lazyLoad(
        () -> supplierOrderDetailRepository.findById(migrationRecord.getOriginId())
            .orElseThrow(() -> new CheckException("订单明细不存在")));
    supplierOrderDetail.setState(Constants.STATE_OK);
    supplierOrderDetailRepository.saveAndFlush(supplierOrderDetail);
    // 恢复相关关联关系
    this.refreshLinkTableRollback(migrationRecord.getOriginId(), migrationRecord.getNewId());
    migrationRecord.setState(Constants.STATE_DELETE);
    migrationRecordRepository.saveAndFlush(migrationRecord);
  }

  @Override
  public void refreshLinkTableRollback(String originId, String newId) {
    // 进项票关联明细
    List<SupplierInvoiceToDetail> allByDetailIdIn =
        supplierInvoiceToDetailRepository.findAllByDetailIdIn(Collections.singletonList(newId));
    allByDetailIdIn.forEach(item -> {
      item.setDetailId(originId);
      supplierInvoiceToDetailRepository.save(item);
    });
  }


  @Override
  public String getTableName() {
    return TABLE;
  }

  @Override
  public List<String> getOriginIds(String relationId) {
    List<SupplierOrderDetail> supplierOrderDetails =
        supplierOrderDetailRepository.findByOrderToFormIdAndState(relationId, Constants.STATE_OK);
    return supplierOrderDetails.stream().map(SupplierOrderDetail::getId).collect(Collectors.toList());
  }
}
