package com.xhgj.srm.migration.service;/**
 * @since 2025/5/30 16:56
 */

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Batch;
import com.xhgj.srm.common.component.LockUtils;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.utils.FileUtils;
import com.xhgj.srm.common.utils.MissionUtil;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.migration.MigrationRecord;
import com.xhgj.srm.jpa.repository.MigrationRecordRepository;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.util.SnowflakeIdGenerator;
import com.xhgj.srm.migration.factory.SupplierOrderMigrationFactory;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.dispatcher.MissionDispatcher;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.upload.util.OssUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *<AUTHOR>
 *@date 2025/5/30 16:56:36
 *@description
 */
@Service
public class SupplierOrderMigrationCoordinator {

  @Resource
  LockUtils lockUtils;
  @Resource
  private SupplierOrderMigrationFactory supplierOrderMigrationFactory;
  @Resource
  private PlatformTransactionManager transactionManager;
  @Resource
  private MigrationRecordRepository migrationRecordRepository;
  @Resource
  private OssUtil ossUtil;
  @Resource
  private MissionRepository missionRepository;
  @Resource
  private MissionUtil missionUtil;
  @Resource
  private MissionDispatcher missionDispatcher;
  @Resource
  private SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;
  @Resource
  private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;

  /**
   * 迁移单个供应商订单及相关数据
   */
  public void migrateSupplierOrder(String supplierOrderId) {
    // 开启事务
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    transactionTemplate.execute(status -> {
      // 1.查询迁移记录表，判断是否已经迁移过
      MigrationRecord migrationRecord =
          migrationRecordRepository.findFirstByTableNameAndOriginIdAndState("t_supplier_order",
              supplierOrderId, Constants.STATE_OK);
      // 2.生成批号
      SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator();
      String batchNo = Convert.toStr(snowflakeIdGenerator.getSnowflakeGenerator().generateKey());
      if (migrationRecord != null) {
        throw new CheckException("迁移失败，订单已迁移");
      }
      // 3.执行迁移
      List<String> totalOriginDetailIds = new ArrayList<>();
      List<String> totalNewDetailIds = new ArrayList<>();
      List<String> totalOriginProductIds = new ArrayList<>();
      List<String> totalNewProductIds = new ArrayList<>();
      Map<String, Object> formExt = new HashMap<>();
      // 记录新旧入库单id映射关系
      Map<String, String> originId2NewIdForWarehouse = new HashMap<>();
      // 主表
      String newOrderId = supplierOrderMigrationFactory.getMigrationService("t_supplier_order").migrateTable(supplierOrderId, null, batchNo, formExt);
      // 付款条件表
      List<String> purchaseOrderPaymentTerms = supplierOrderMigrationFactory.getMigrationService("t_purchase_order_payment_terms").getOriginIds(supplierOrderId);
      for (String purchaseOrderPaymentTerm : purchaseOrderPaymentTerms) {
        supplierOrderMigrationFactory.getMigrationService("t_purchase_order_payment_terms").migrateTable(purchaseOrderPaymentTerm, newOrderId, batchNo, formExt);
      }
      // form
      List<String> originFormIds = supplierOrderMigrationFactory.getMigrationService("t_supplier_order_to_form").getOriginIds(supplierOrderId);
      // 存储流水号
      formExt.put(SupplierOrderFormType.WAREHOUSING.getType(), 1);
      formExt.put(SupplierOrderFormType.RETURN.getType(), 1);
      formExt.put(SupplierOrderFormType.DELIVER.getType(), 1);
      for (String originFormId : originFormIds) {
        String newFormId = supplierOrderMigrationFactory.getMigrationService("t_supplier_order_to_form").migrateTable(originFormId, newOrderId, batchNo, formExt);
        if (newFormId == null) {
          continue;
        }
        // 判断newFormId是否为入库单
        if (formExt.get("type").equals(SupplierOrderFormType.WAREHOUSING.getType())) {
          originId2NewIdForWarehouse.put(originFormId, newFormId);
        }
        // detail
        List<String> originDetailIds = supplierOrderMigrationFactory.getMigrationService("t_supplier_order_detail").getOriginIds(originFormId);
        totalOriginDetailIds.addAll(originDetailIds);
        int index = 1;
        for (String originDetailId : originDetailIds) {
          formExt.put("index", index++);
          formExt.put("supplierOrderId", newOrderId);
          String newDetailId = supplierOrderMigrationFactory.getMigrationService("t_supplier_order_detail").migrateTable(originDetailId, newFormId, batchNo, formExt);
          totalNewDetailIds.add(newDetailId);
          // product
          List<String> originProductIds = supplierOrderMigrationFactory.getMigrationService("t_supplier_order_product").getOriginIds(originDetailId);
          totalOriginProductIds.addAll(originProductIds);
          for (String originProductId : originProductIds) {
            String newProductId = supplierOrderMigrationFactory.getMigrationService("t_supplier_order_product").migrateTable(originProductId, newDetailId, batchNo, new HashMap<>());
            totalNewProductIds.add(newProductId);
          }
        }
      }
      // 刷新明细关联表
      supplierOrderMigrationFactory.getMigrationService("t_supplier_order").refreshLinkTable(supplierOrderId, newOrderId);
      // 刷新明细的相关的关联表
      if (totalOriginDetailIds.size() != totalNewDetailIds.size()) {
        throw new CheckException("明细数据迁移异常：原始明细数量与新明细数量不匹配");
      }
      for (int i = 0; i < totalOriginDetailIds.size(); i++) {
        String originDetailId = totalOriginDetailIds.get(i);
        String newDetailId = totalNewDetailIds.get(i);
        supplierOrderMigrationFactory.getMigrationService("t_supplier_order_detail").refreshLinkTable(originDetailId, newDetailId);
      }
      // 刷新产品的相关关联表
      if (totalOriginProductIds.size() != totalNewProductIds.size()) {
        throw new CheckException("产品数据迁移异常：原始产品数量与新产品数量不匹配");
      }
      for (int i = 0; i < totalOriginProductIds.size(); i++) {
        String originProductId = totalOriginProductIds.get(i);
        String newProductId = totalNewProductIds.get(i);
        supplierOrderMigrationFactory.getMigrationService("t_supplier_order_product").refreshLinkTable(originProductId, newProductId);
      }
      // 特殊处理 入库单创建入库申请单
      List<String> needCreateWarehouseApplyIds = supplierOrderMigrationFactory.getMigrationService("t_supplier_order_to_form_special").getOriginIds(newOrderId);
      for (String needCreateWarehouseApplyId : needCreateWarehouseApplyIds) {
        String specialFormId = supplierOrderMigrationFactory.getMigrationService("t_supplier_order_to_form_special").migrateTable(needCreateWarehouseApplyId, newOrderId, batchNo, formExt);
        List<String> specialDetailIds = supplierOrderMigrationFactory.getMigrationService("t_supplier_order_detail_special").getOriginIds(needCreateWarehouseApplyId);
        for (String specialDetailId : specialDetailIds) {
          supplierOrderMigrationFactory.getMigrationService("t_supplier_order_detail_special").migrateTable(specialDetailId, specialFormId, batchNo, formExt);
        }
      }
      // 特殊处理，刷新相关退库单上
      this.handleReturnForm(newOrderId, originId2NewIdForWarehouse);
      return null;
    });
  }

  /**
   * 处理退库单明细
   * @param newOrderId
   * @param originId2NewIdForWarehouse
   */
  private void handleReturnForm(String newOrderId, Map<String, String> originId2NewIdForWarehouse) {
    List<SupplierOrderDetailV2> returnDetails =
        supplierOrderDetailV2Repository.findAllByPurchaseOrderIdAndOrderToFormTypeAndState(
            newOrderId, SupplierOrderFormType.RETURN.getType(), Constants.STATE_OK);
    for (SupplierOrderDetailV2 returnDetail : returnDetails) {
      String originId = returnDetail.getInWareHouseId();
      if (StrUtil.isEmpty(originId)) {
        continue;
      }
      String newId = originId2NewIdForWarehouse.get(originId);
      if (newId == null) {
        throw new CheckException("未找到对应的入库单映射关系，原始ID：" + originId);
      }
      SupplierOrderToFormV2 supplierOrderToFormV2 = supplierOrderToFormV2Repository.findById(newId)
          .orElseThrow(() -> new CheckException("未找到入库单，ID：" + newId));
      returnDetail.setInWareHouseId(newId);
      returnDetail.setInWareHouseName(supplierOrderToFormV2.getFormCode());
      supplierOrderDetailV2Repository.saveAndFlush(returnDetail);
    }
  }

  /**
   * 迁移单个供应商订单回滚
   */
  public void migrateSupplierOrderRollback(String supplierOrderId, String newSupplierOrderId) {
    // 开启事务
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    transactionTemplate.execute(status -> {
      // 1.查询迁移记录表，判断是否已经迁移过
      MigrationRecord migrationRecord =
          migrationRecordRepository.findFirstByTableNameAndOriginIdAndState("t_supplier_order",
              supplierOrderId, Constants.STATE_OK);
      MigrationRecord newMigrationRecord =
          migrationRecordRepository.findFirstByTableNameAndNewIdAndState("t_supplier_order",
              newSupplierOrderId, Constants.STATE_OK);
      if (migrationRecord == null && newMigrationRecord == null) {
        throw new CheckException("迁移失败，订单未迁移");
      }
      String batchNo = migrationRecord != null ? migrationRecord.getBatchNo() : newMigrationRecord.getBatchNo();
      // 查询相关的同一批次的迁移记录
      List<MigrationRecord> migrationRecords =
          migrationRecordRepository.findAllByBatchNoAndState(batchNo, Constants.STATE_OK);
      // 2.回滚
      for (MigrationRecord record : migrationRecords) {
        supplierOrderMigrationFactory.getMigrationService(record.getTableName()).rollback(record);
      }
      return null;
    });
  }

  public void importFile(MultipartFile file, User user) {
    String savePath = null;
    try {
      savePath = this.saveExcel(file);
    } catch (IOException e) {
      throw new CheckException("文件保存失败：" + e.getMessage(), e);
    }
    // 新增任务
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(user.getCode()),
        MissionTypeEnum.BATCH_TASK_MIGRATION_PURCHASE_ORDER.getTypeName(),
        user.getId(),
        Constants.PLATFORM_TYPE_AFTER,
        null,
        ""
    );
    Map<String, Object> mapParam = new HashMap<>(3);
    mapParam.put("filePath", savePath);
    mapParam.put("fileName", file.getOriginalFilename());
    missionRepository.saveAndFlush(mission);
    missionDispatcher.doDispatch(mission.getId(), JSON.toJSONString(mapParam),
        MissionTypeEnum.BATCH_TASK_MIGRATION_PURCHASE_ORDER);
  }

  /** 将需要导入的 excel 保存 */
  public String saveExcel(MultipartFile file) throws IOException {
    if (file != null) {
      String fileName = file.getOriginalFilename();
      if (fileName == null || fileName.isEmpty()) {
        throw new CheckException("文件异常,请查看文件");
      }
      // 导入文件上传至 OSS
      String fileUrl =
          ossUtil.putOneFile(
              file.getInputStream(),
              FileUtils.getFileNameWithInsertStr(
                  file.getOriginalFilename(), String.valueOf(System.currentTimeMillis())),
              Constants_Batch.UPLOAD_FILE_DIR_TASK_IMPORT
                  + DateUtils.formatTimeStampToPureDate(System.currentTimeMillis()));
      if (StringUtils.isNullOrEmpty(fileUrl)) {
        throw new CheckException("文件上传异常！");
      }
      return fileUrl;
    } else {
      throw new CheckException("无上传文件");
    }
  }
}
