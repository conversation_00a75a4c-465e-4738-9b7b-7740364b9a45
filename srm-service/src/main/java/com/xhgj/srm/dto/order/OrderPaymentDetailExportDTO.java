package com.xhgj.srm.dto.order;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.enums.BooleanEnum;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.order.OrderReceiptPaymentMethodEnum;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderPayment;
import com.xhgj.srm.jpa.entity.OrderReceiptRecord;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhiot.boot.core.common.util.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * OrderPaymentDetailExportDTO
 */
@Data
@NoArgsConstructor
public class OrderPaymentDetailExportDTO {

  @ApiModelProperty("付款单号")
  private String paymentNo;

  @ApiModelProperty("付款状态")
  private String paymentStatus;

  @ApiModelProperty("订单数量")
  private String orderCount;

  @ApiModelProperty("申请付款金额")
  private String applyPrice;

  @ApiModelProperty("已付金额")
  private String paymentPrice;

  @ApiModelProperty("提交人")
  private String submitMan;

  @ApiModelProperty("提交时间")
  private String createTime;

  @ApiModelProperty("是否自动提款")
  private String autoDraw;

  @ApiModelProperty("客户订单号")
  private String orderNo;


  @ApiModelProperty("下单平台")
  private String platform;


  @ApiModelProperty("实际订货金额")
  private String finalPrice;


  @ApiModelProperty("供应商开票状态")
  private String orderInvoiceStatus;

  @ApiModelProperty("客户回款")
  private String customerPayback;

//  @ApiModelProperty("回款比例")
//  private String paymentProportion;

//  @ApiModelProperty("核销时间")
//  private String writeOffTime;

//  @ApiModelProperty("回款方式")
//  private String paymentType;

  @ApiModelProperty("签收凭证")
  private String signVoucherState;

  /**
   * 客户回款方式
   **/
  @ApiModelProperty("客户回款方式")
  private String customerPaymentType;

  @ApiModelProperty("回款展示比例")
  private String showRate;

  @ApiModelProperty("银行流水号")
  private String bankSerialNo;

  @ApiModelProperty("汇票号")
  private String billNo;

  /**
   * {@link com.xhgj.srm.common.enums.contract.LandingMerchantContractPaymentConditionEnum} 发起付款条件
   */
  @ApiModelProperty("付款发起条件（多选）")
  private String paymentCondition;

  @ApiModelProperty("付款满足日期")
  private String paymentConditionTime;

  @ApiModelProperty("账期（单位：天）")
  private Integer accountingPeriod;

  @ApiModelProperty("预计付款日期")
  private String paymentDate;

  @ApiModelProperty("付款方式")
  private String paymentMethod;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("本次提款金额")
  private String withdrawalAmount;


  public OrderPaymentDetailExportDTO(OrderPayment orderPayment) {
    this.paymentNo = orderPayment.getPaymentNo();
    this.paymentStatus =
        Constants_order.ORDER_PAYMENT_STATUS_MAP.get(orderPayment.getPaymentStatus());
    this.orderCount = orderPayment.getOrderCount();
    this.applyPrice = BigDecimalUtil.formatForStandard(orderPayment.getApplyPrice()).toPlainString();
    this.paymentPrice =
        BigDecimalUtil.formatForStandard(orderPayment.getPaymentPrice()).toPlainString();
    this.submitMan = orderPayment.getSubmitMan();
    this.createTime = orderPayment.getCreateTime() == null ? StrUtil.EMPTY
        : DateUtils.formatTimeStampToNormalDateTime(orderPayment.getCreateTime());
    this.autoDraw = orderPayment.getAutoDraw() ? BooleanEnum.YES.getDescription()
        : BooleanEnum.NO.getDescription();
  }

  public OrderPaymentDetailExportDTO(OrderPayment orderPayment,Order order,String platformName,
      OrderReceiptRecord orderReceiptRecord,BigDecimal showRate,BigDecimal withdrawalAmount) {
    this.paymentNo = orderPayment.getPaymentNo();
    this.paymentStatus =
        Constants_order.ORDER_PAYMENT_STATUS_NEW_MAP.get(orderPayment.getPaymentStatus());
    this.orderCount = orderPayment.getOrderCount();
    this.applyPrice = BigDecimalUtil.formatForStandard(orderPayment.getApplyPrice()).toPlainString();
    this.paymentPrice =
        BigDecimalUtil.formatForStandard(orderPayment.getPaymentPrice()).toPlainString();
    this.submitMan = orderPayment.getSubmitMan();
    this.createTime = orderPayment.getCreateTime() == null ? StrUtil.EMPTY
        : DateUtils.formatTimeStampToNormalDateTime(orderPayment.getCreateTime());
    this.autoDraw = orderPayment.getAutoDraw() ? BooleanEnum.YES.getDescription()
        : BooleanEnum.NO.getDescription();
    //付款方式
    this.paymentMethod =PayTypeSAPEnums.getNameByCode(orderPayment.getPayType());

    if (order != null) {
      this.orderNo = order.getOrderNo();
      this.signVoucherState = order.getConfirmVoucherAuditStatus();
      this.orderInvoiceStatus =
          StrUtil.isNotBlank(order.getSupplierOpenInvoiceStatus())
              ? Constants.ORDER_SUPPLIER_INVOICE_STATE_TYPE.get(order.getSupplierOpenInvoiceStatus())
              : "未开票";
      this.finalPrice = BigDecimalUtil.formatForStandard(
              NumberUtil.sub(order.getPrice(), order.getRefundPrice(), order.getCancelPrice()))
          .toPlainString();
//      this.customerPayback = Constants_order.getCustomerPaybackStateNameByReturnProgress(
//          order.getCustomerReturnProgress(), Constants_order.CUSTOMER_PAYBACK_UN);
      this.paymentCondition = order.getPaymentCondition();
      this.paymentConditionTime = order.getPaymentConditionTime() == null ? StrUtil.EMPTY
          : DateUtils.formatTimeStampToNormalDateTime(order.getPaymentConditionTime());
      this.accountingPeriod = order.getAccountingPeriod();
      this.paymentDate = order.getPredictPaymentTime() == null ? StrUtil.EMPTY
          : DateUtils.formatTimeStampToNormalDateTime(order.getPredictPaymentTime());
      this.supplierName = Optional.ofNullable(order.getSupplier()).map(Supplier::getEnterpriseName).orElse(StrUtil.EMPTY);;
    }
    this.platform = StrUtil.emptyIfNull(platformName);
    this.withdrawalAmount = BigDecimalUtil.formatForStandard(withdrawalAmount).toPlainString();
    this.showRate = BigDecimalUtil.formatForStandard(showRate).toPlainString();
    if (orderReceiptRecord != null) {
      this.bankSerialNo =orderReceiptRecord.getBankSerialNo();
      this.billNo =orderReceiptRecord.getBillNo();
//      this.paymentMethod = OrderReceiptPaymentMethodEnum.getDescFromCode(orderReceiptRecord.getPaymentMethod());
      //客户回款方式
      this.customerPaymentType =OrderReceiptPaymentMethodEnum.getDescFromCode(orderReceiptRecord.getPaymentMethod());

    }

  }

}
