package com.xhgj.srm.dto.asmDisOrder;/**
 * @since 2025/3/5 15:25
 */

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.constraints.NotBlank;

/**
 *<AUTHOR>
 *@date 2025/3/5 15:25:25
 *@description
 */
@Data
public class AsmDisOrderImportForm {

  /**
   * 文件
   */
  MultipartFile file;
  /**
   * 用户组织
   */
  @NotBlank(message = "用户组织不能为空")
  private String userGroup;

}
