package com.xhgj.srm.dto.filedConfig.purchaseOrderV2;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ButtonFieldConverter {

  /**
   * 库内扁平化结构转换嵌套结构
   * @param dtos
   * @return
   */
  public static List<ButtonFieldVo> convert(List<ButtonFieldDto> dtos) {
    List<ButtonFieldVo> items = new ArrayList<>();
    // 1. 按page分组
    Map<String, List<ButtonFieldDto>> pageGrouped =
        dtos.stream().collect(Collectors.groupingBy(ButtonFieldDto::getPage));
    int h1 = 1;
    for (Map.Entry<String, List<ButtonFieldDto>> pageEntry : pageGrouped.entrySet()) {
      String page = pageEntry.getKey();
      List<ButtonFieldDto> pageDtos = pageEntry.getValue();
      // 2. 创建Item对象
      ButtonFieldVo item = new ButtonFieldVo();
      item.setPage(page);
      item.setPageEnum(h1++);
      // 3. 按condition分组
      Map<String, List<ButtonFieldDto>> conditionGrouped = pageDtos.stream()
          .filter(dto -> dto.getCondition() != null && !dto.getCondition().isEmpty())
          .collect(Collectors.groupingBy(ButtonFieldDto::getCondition));
      List<ButtonFieldVo.Condition> conditions = new ArrayList<>();
      // 4. 处理有条件的按钮
      for (Map.Entry<String, List<ButtonFieldDto>> conditionEntry : conditionGrouped.entrySet()) {
        String condition = conditionEntry.getKey();
        List<ButtonFieldDto> conditionDtos = conditionEntry.getValue();
        ButtonFieldVo.Condition voCondition = new ButtonFieldVo.Condition();
        voCondition.setCondition(condition);
        voCondition.setConditionEnum(SupplierOrderState.findOrderStateByValue(condition));
        voCondition.setFields(convertToFields(conditionDtos));
        conditions.add(voCondition);
      }
      // 5. 处理无条件的按钮（当page存在但condition为空时）
      List<ButtonFieldDto> noConditionDtos = pageDtos.stream()
          .filter(dto -> dto.getCondition() == null || dto.getCondition().isEmpty())
          .collect(Collectors.toList());
      if (!noConditionDtos.isEmpty()) {
        ButtonFieldVo.Condition voCondition = new ButtonFieldVo.Condition();
        // 空条件标识
        voCondition.setCondition("");
        voCondition.setFields(convertToFields(noConditionDtos));
        conditions.add(voCondition);
      }
      // 6. 设置条件列表和是否有条件标识
      item.setConditions(conditions);
      boolean hasCondition =
          conditions.stream().anyMatch(condition -> StrUtil.isNotBlank(condition.getCondition()));
      item.setHasCondition(hasCondition ? Constants.STATE_OK : Constants.STATE_NO);
      items.add(item);
    }
    return items;
  }

  private static List<ButtonFieldVo.Field> convertToFields(List<ButtonFieldDto> dtos) {
    return dtos.stream().map(dto -> {
      ButtonFieldVo.Field field = new ButtonFieldVo.Field();
      field.setButtonName(dto.getButtonName());
      field.setIsChecked(dto.getIsChecked());
      field.setIsUpdate(dto.getIsUpdate());
      return field;
    }).collect(Collectors.toList());
  }
}