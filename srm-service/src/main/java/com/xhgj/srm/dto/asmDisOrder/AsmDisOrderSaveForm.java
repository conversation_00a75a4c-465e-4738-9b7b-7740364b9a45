package com.xhgj.srm.dto.asmDisOrder;/**
 * @since 2025/2/25 16:10
 */

import com.xhgj.srm.jpa.annotations.NumberLengthAndScale;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/2/25 16:10:30
 *@description
 */
@Data
public class AsmDisOrderSaveForm {

  /**
   * 组装拆卸单id,修改时传
   */
  private String id;

  /**
   * 保存类型  1暂存  2提交
   */
  private int saveType;

  /**
   * 组装拆卸单类型 1组装  2拆卸
   * @see com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderType
   */
  @NotNull(message = "组装拆卸单类型不能为空")
  private Byte type;

  /**
   * 组织code
   */
  @NotBlank(message = "组织code不能为空")
  private String groupCode;

//  /**
//   * 部门code
//   */
//  @NotBlank(message = "部门code不能为空")
//  private String deptCode;

  /**
   * 部门id
   */
  @NotBlank(message = "部门id不能为空")
  private String deptId;

  /**
   * 创建人id
   */
  private String createMan;

  /**
   * 创建人code
   */
  @NotBlank(message = "创建人code不能为空")
  private String createManCode;

  /**
   * 创建人名称
   */
  @NotBlank(message = "创建人名称不能为空")
  private String createManName;

  /**
   * 开始时间
   */
  @NotNull(message = "开始时间不能为空")
  private Long startTime;

  /**
   * 结束时间
   */
  @NotNull(message = "结束时间不能为空")
  private Long endTime;

  /**
   * 备注
   */
  private String remark;


  /**
   * 成品明细
   */
  @NotNull(message = "成品明细不能为空")
  @Valid
  private AsmDisOrderItemSaveForm fp;

  /**
   * 子件明细
   */
  @Valid
  @NotEmpty(message = "子件明细不能为空")
  private List<AsmDisOrderItemSaveForm> sub;

  @Data
  public static class AsmDisOrderItemSaveForm {
    /**
     * 明细id,修改时传
     */
    private String id;

    /**
     * 行id (子件明细需要从2开始)
     */
    private String rowId;

    /**
     * 物料编码
     */
    @NotBlank(message = "物料编码不能为空")
    private String productCode;

    /**
     * 物料品牌
     */
    private String brand;

    /**
     * 描述
     */
    private String desc;

    /**
     * 规格型号
     */
    private String model;

    /**
     * 物料名称
     */
    private String productName;

    /**
     * 物料单位
     */
    private String unit;

    /**
     * 物料单位编码
     */
    private String unitCode;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @DecimalMax(value = "999999.999",message = "调拨数量最大值为999999.999")
    @NumberLengthAndScale(length = 9,fraction = 3,message = "数量不能为空最大长度为9位，小数位数最大为3")
    private BigDecimal num;

    /**
     * 仓库id
     */
    @NotBlank(message = "仓库id不能为空")
    private String warehouseId;

    /**
     * 未税单价
     */
    private BigDecimal originNetPrice;

    /**
     * 税率
     */
    private BigDecimal tax;

    /**
     * 含税结算单价
     */
    private BigDecimal price;

    /**
     * 批次 (直销库必填)
     */
    private String batchNo;

    /**
     * 销售订单号
     */
    private String saleOrderNo;

    /**
     * 销售订单行项目号
     */
    private String saleOrderProductRowId;

    /**
     * 关联采购申请id (直销库必填)
     */
    private String purchaseApplyForOrderId;

    /**
     * 未税单价
     */
    public BigDecimal getOriginPrice() {
      // 等于 含税单价 / (1 + 税率)
      if (originNetPrice == null || tax == null) {
        return BigDecimal.ZERO;
      }
      return originNetPrice.multiply(tax.add(BigDecimal.ONE)).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 含税结算总价
     * 含税结算单价*数量
     */
    public BigDecimal getPriceTotal() {
      if (num == null || price == null) {
        return BigDecimal.ZERO;
      }
      return price.multiply(num).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 备注
     */
    private String remark;

    /**
     * 含税成本总价
     * 含税单价*数量
     */
    public BigDecimal getOriginPriceTotal() {
      if (num == null || getOriginPrice() == null) {
        return BigDecimal.ZERO;
      }
      return getOriginPrice().multiply(num).setScale(2, RoundingMode.HALF_UP);
    }
  }
}
