package com.xhgj.srm.dto.entryregistration;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class ApprovalResultCallbackParam {

  @ApiModelProperty("报备单号")
  @NotBlank
  private String auditId;
  @ApiModelProperty("审核结果， 0驳回，1通过")
  @NotBlank
  private String approvalResult;
  @ApiModelProperty("驳回理由，驳回时为必须参数")
  private String rejection;
}
