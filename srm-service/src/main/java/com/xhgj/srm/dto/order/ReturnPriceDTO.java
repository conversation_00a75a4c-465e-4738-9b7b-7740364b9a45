package com.xhgj.srm.dto.order;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/** <AUTHOR> @ClassName ReturnPriceDTO */
@Data
public class ReturnPriceDTO {
  @ApiModelProperty("业务日期")
  private String time;

  @ApiModelProperty("创建时间")
  private Long createTime;

  @ApiModelProperty("erp付款单号")
  private  String erpPaymentNo;

  @ApiModelProperty("回款金额")
  private BigDecimal price;

  @ApiModelProperty("erp付款单id")
  private String  id;

  public ReturnPriceDTO(String time, BigDecimal price,String erpPaymentNo,String id) {
    this.time = time;
    this.price = price;
    this.erpPaymentNo = erpPaymentNo;
    this.id  = id;
  }
}
