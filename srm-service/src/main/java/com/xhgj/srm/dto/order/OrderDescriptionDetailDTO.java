package com.xhgj.srm.dto.order;

import com.xhgj.srm.dto.FileDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * OrderDescriptionDetailDTO
 */
@Data
public class OrderDescriptionDetailDTO {
  @ApiModelProperty("订单说明富文本字段")
  @NotBlank(message = "订单说明富文本字段不能为空")
  private String orderDescriptionTextField;

  @ApiModelProperty("订单Id")
  @NotBlank(message = "订单订单Id不能为空")
  private String orderId;

  @ApiModelProperty("保存订单说明附件集合")
  private List<FileDTO> orderDescriptionFileList;
}
