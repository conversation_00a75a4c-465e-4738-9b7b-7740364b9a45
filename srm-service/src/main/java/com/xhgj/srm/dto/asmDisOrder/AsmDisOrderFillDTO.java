package com.xhgj.srm.dto.asmDisOrder;/**
 * @since 2025/2/26 10:09
 */

import lombok.Data;

import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/2/26 10:09:48
 *@description
 */
@Data
public class AsmDisOrderFillDTO {

  /**
   * 行id
   */
  private String rowId;

  /**
   * 物料编码
   */
  private String productCode;

  /**
   * 品牌
   */
  private String brand;

  /**
   * 物料名称
   */
  private String productName;

  /**
   * 描述
   */
  private String desc;

  /**
   * 规格型号
   */
  private String model;

  /**
   * 单位
   */
  private String unit;

  /**
   * 税率
   */
  private String tax;

  /**
   * 未税单价
   */
  private BigDecimal originNetPrice;

  /**
   * 数量
   */
  private BigDecimal num;

  /**
   * 含税结算单价
   */
  private BigDecimal price;

  /**
   * 仓库编码
   */
  private String warehouseCode;

  /**
   * 仓库id
   */
  private String warehouseId;


  /**
   * 批次
   */
  private String batchNo;

  /**
   * 备注
   */
  private String remark;

  /**
   * 错误信息
   */
  private String errorMsg;

}
