package com.xhgj.srm.dto.order;

import com.xhgj.srm.jpa.entity.OrderRefundCollection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class OrderPaymentRefundDTO {

  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("退款申请单号")
  private String refundNo;

  @ApiModelProperty("期望退款日期")
  private Long expectedRefundTime;

  @ApiModelProperty("退款方式")
  private String paymentType;

  @ApiModelProperty("退款总金额")
  private BigDecimal refundAmount;

  @ApiModelProperty("退款状态：1 审核中 2 通过 3 驳回")
  private String refundState;

  @ApiModelProperty("创建时间")
  private Long createTime;

  @ApiModelProperty("退款驳回理由")
  private String rejectReason;

  @ApiModelProperty("开户银行")
  private String bankName;

  @ApiModelProperty("开户银行对应联行号")
  private String bankCode;

  @ApiModelProperty("银行账号")
  private String bankAccount;

  @ApiModelProperty("账户名称")
  private String accountName;

  @ApiModelProperty("备注")
  private String remark;

  public OrderPaymentRefundDTO(OrderRefundCollection refundCollection) {
    this.id =refundCollection.getId();
    this.refundNo = refundCollection.getRefundNo();
    this.expectedRefundTime = refundCollection.getExpectedRefundTime();
    this.paymentType = refundCollection.getPaymentType();
    this.refundAmount = refundCollection.getRefundAmount();
    this.refundState = refundCollection.getRefundState();
    this.createTime = refundCollection.getCreateTime();
    this.rejectReason = refundCollection.getRejectReason();
    this.bankName = refundCollection.getBankName();
    this.bankCode = refundCollection.getBankCode();
    this.bankAccount = refundCollection.getBankAccount();
    this.accountName = refundCollection.getAccountName();
    this.remark = refundCollection.getRemark();
  }
}