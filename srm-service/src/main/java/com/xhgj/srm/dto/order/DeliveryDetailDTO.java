package com.xhgj.srm.dto.order;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.dto.FileDTO;
import com.xhgj.srm.jpa.entity.OrderDelivery;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class DeliveryDetailDTO {

  @ApiModelProperty("发货单id")
  private String id;

  @ApiModelProperty("发货时间")
  private String deliveryTime;

  @ApiModelProperty("物流公司")
  private String expressCompany;

  @ApiModelProperty("物流公司编码")
  private String expressCode;

  @ApiModelProperty("快递单号")
  private String expressNo;

  @ApiModelProperty("发货单状态")
  private String state;

  @ApiModelProperty("发货商品信息")
  private List<OrderDeliveryDetailDTO> deliveryProductList;

  @ApiModelProperty("erp入库单号")
  private String erpNo;

  @ApiModelProperty("订单的采购单号")
  private String orderErpNo;

  @ApiModelProperty("订单的采购单号")
  private String number;

  @ApiModelProperty("附件")
  private List<FileDTO> fileList;

  public DeliveryDetailDTO(OrderDelivery orderDelivery) {
    this.id = orderDelivery.getId();
    this.deliveryTime =
        orderDelivery.getCreateTime() > 0
            ? DateUtils.formatTimeStampToNormalDateTime(orderDelivery.getCreateTime())
            : "";
    this.expressCompany = StringUtils.emptyIfNull(orderDelivery.getExpressCompany());
    this.expressNo = StringUtils.emptyIfNull(orderDelivery.getExpressNo());
    this.expressCode = StringUtils.emptyIfNull(orderDelivery.getExpressCode());
    this.state =
        !StringUtils.isNullOrEmpty(orderDelivery.getDeliveryState())
            ? Constants_order.DELIVERY_STATE_MAP.get(orderDelivery.getDeliveryState())
            : "待收货";
    this.erpNo = orderDelivery.getErpNo();
    this.orderErpNo = StrUtil.emptyIfNull(orderDelivery.getOrderErpNo());
    this.number = StrUtil.emptyIfNull(orderDelivery.getNumber());
  }
}
