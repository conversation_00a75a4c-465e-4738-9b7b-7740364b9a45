package com.xhgj.srm.dto.order.invoice;

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhiot.boot.core.common.exception.CheckException;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class PurchaseOrderInvoiceVO {
  @ApiModelProperty("采购单id/入库单id/入库单详情id")
  private String id;
  @ApiModelProperty("订单id")
  private String orderId;
  @ApiModelProperty("采购订单号")
  private String purchaseOrderNo;
  @ApiModelProperty("入库单sap号")
  private String warehousingEntrySapNo;
  @ApiModelProperty("物料编码")
  private String productCode;
  @ApiModelProperty("物料名称")
  private String productName;
  @ApiModelProperty("型号")
  private String manuCode;
  @ApiModelProperty("型号")
  private String model;
  @ApiModelProperty("规格")
  private String specification;
  @ApiModelProperty("本次开票数量")
  private BigDecimal openInvoiceNumber;
  @ApiModelProperty("去税单价")
  private BigDecimal taxRemovalUnitPrice;
  @ApiModelProperty("含税单价")
  private BigDecimal unitPrice;
  @ApiModelProperty("税率")
  private BigDecimal taxRate;
  @ApiModelProperty("含税金额")
  private BigDecimal amountIncludingTax;
  @ApiModelProperty("可开票数量")
  private BigDecimal invoiceQuantity;
  @ApiModelProperty("本次去税金额")
  private BigDecimal taxFreeAmount;
  @ApiModelProperty("本次开票金额")
  private BigDecimal openTaxAmount;
  @ApiModelProperty("本次开票税额")
  private BigDecimal taxAmount;
  @ApiModelProperty("发票类型")
  private String invoiceType;
  @ApiModelProperty("物料单价")
  private BigDecimal price;

  @ApiModelProperty("detailId")
  private String detailId;
  /**
   * 实际入库数量
   */
  @ApiModelProperty("实际入库数量")
  private BigDecimal totalNum;

  /**
   * 实际入库金额
   */
  @ApiModelProperty("实际入库金额")
  private BigDecimal finalPrice;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  /**
   * 是否为退换货订单明细
   */
  @ApiModelProperty("是否为退换货订单明细")
  private Boolean returnFlag;

  /**
   * version
   * @see com.xhgj.srm.jpa.sharding.enums.VersionEnum
   */
  @ApiModelProperty("版本")
  private String version;

  @ApiModelProperty("发票关联明细id")
  private String supplierInvoiceToDetailId;

  /**
   * 获取实例
   * @param detail
   * @param openInvoiceNumber
   * @param id
   * @return
   */
  public static PurchaseOrderInvoiceVO getInstance(SupplierOrderDetail detail,
      BigDecimal openInvoiceNumber, String id) {
    Assert.notNull(detail);
    SupplierOrderProduct product = detail.getSupplierOrderProduct();
    if (product == null) {
      throw new CheckException("数据异常，请联系管理员！");
    }
    PurchaseOrderInvoiceVO vo = new PurchaseOrderInvoiceVO();
    vo.setId(id);
    vo.setProductCode(product.getCode());
    vo.setProductName(product.getName());
    vo.setManuCode(product.getManuCode());
    vo.setModel(product.getModel());
    vo.setSpecification(product.getSpecification());
    vo.setOpenInvoiceNumber(openInvoiceNumber != null ? openInvoiceNumber.stripTrailingZeros() :
        BigDecimal.ZERO);
    vo.setPrice(detail.getPrice());
    vo.setReturnFlag(detail.getReturnFlag());
    return vo;
  }
}
