package com.xhgj.srm.dto.filedConfig.purchaseOrderV2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * @Author: fanghuanxu
 * @Date: 2025/5/9 9:53
 * @Description: 采购订单按钮配置参数 - 按钮配置项 vo 嵌套接口
 */
@Data
public class ButtonFieldVo {

  @ApiModelProperty("所属页面")
  private String page;

  @ApiModelProperty("页面枚举 - 仅前端使用")
  private Integer pageEnum;

  @ApiModelProperty("是否含有条件")
  private String hasCondition;

  @ApiModelProperty("条件项")
  private List<Condition> conditions;

  @Data
  public static class Condition {

    @ApiModelProperty("条件")
    private String condition;

    @ApiModelProperty("h2条件枚举 - 仅前端使用")
    private String conditionEnum;

    @ApiModelProperty("字段项")
    private List<Field> fields;
  }

  @Data
  public static class Field {

    @ApiModelProperty("按钮名称")
    private String buttonName;

    @ApiModelProperty("是否选中 0-否 1-是")
    private String isChecked;
    @ApiModelProperty("是否可修改 0-否 1-是")
    private String isUpdate;
  }
}
