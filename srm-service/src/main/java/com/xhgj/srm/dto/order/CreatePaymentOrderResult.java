package com.xhgj.srm.dto.order;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON> Shy on 2023/9/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreatePaymentOrderResult {

  /**
   * 弹窗时间
   */
  private Long time;
  /**
   * 总应付金额
   */
  private BigDecimal countAmount;
  /**
   * 申请付款中金额
   */
  private BigDecimal countApplyAmount;
  /**
   * 本次申请付款金额
   */
  private BigDecimal applyAmount;
  /**
   * 提交结果
   */
  private boolean result;
  /**
   * 付款单id
   */
  private String paymentId;
}
