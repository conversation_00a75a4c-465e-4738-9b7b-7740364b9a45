package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.dao.UserToGroupDao;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.UserToGroup;
import com.xhgj.srm.jpa.repository.UserToGroupRepository;
import com.xhgj.srm.service.ShareGroupService;
import com.xhgj.srm.service.ShareUserToGroupService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/9/5 20:52
 */
@Service
public class ShareUserToGroupServiceImpl implements ShareUserToGroupService {
  @Autowired private UserToGroupRepository repository;
  @Autowired private UserToGroupDao dao;
  @Autowired private ShareGroupService shareGroupService;

  @Override
  public BootBaseRepository<UserToGroup, String> getRepository() {
    return repository;
  }

  @Override
  public UserToGroup getUserToGroupByUserIdAndGroupId(String userId, String groupId) {
    if (StrUtil.isBlank(userId) || StrUtil.isBlank(groupId)) {
      return null;
    } else {
      return dao.getUserToGroupByUserIdAndGroupId(userId, groupId);
    }
  }

  @Override
  public List<UserToGroup> getUserToGroupList(String userId) {
    return CollUtil.emptyIfNull(dao.getUserToGroupList(userId));
  }

  @Override
  public List<String> getDeptErpCodeListByUser(String userId) {
    return CollUtil.emptyIfNull(dao.getUserToGroupList(userId)).stream()
        .map(UserToGroup::getDeptId)
        .distinct()
        .map(shareGroupService::get)
        .filter(ObjectUtil::isNotNull)
        .map(Group::getErpCode)
        .collect(Collectors.toList());
  }

  @Override
  public List<String> getUserIdListByDepartIds(List<String> groupIds, String param) {
    return dao.getUserIdListByDepartIds(groupIds, param);
  }
}
