package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.service.ShareSupplierUserService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierUser.SupplierUserSource;
import com.xhgj.srm.common.utils.PasswordUtil;
import com.xhgj.srm.jpa.dao.SupplierUserDao;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.repository.SupplierUserRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON> <PERSON>hy on 2023/10/13
 */
@Service
public class ShareSupplierUserServiceImpl implements ShareSupplierUserService {
  @Resource
  private SupplierUserRepository repository;
  @Resource
  private SupplierUserDao dao;

  @Override
  public BootBaseRepository<SupplierUser, String> getRepository() {
    return repository;
  }

  @Override
  public Optional<List<SupplierUser>> getSupplierUserListBySupplierIdAsc(String supplierId) {
    List<SupplierUser> supplierUsers = dao.getSupplierUserListBySidAsc(supplierId);
    if (CollUtil.isEmpty(supplierUsers)) {
      return Optional.empty();
    }
    return Optional.of(supplierUsers.stream().filter(Objects::nonNull).collect(Collectors.toList()));
  }

  @Override
  public boolean createIfNotExist(Supplier supplier, String userName, String mobile,
      String emailAddress, String pwd, String createMan) {
    List<SupplierUser> supplierUsers =
        this.getSupplierUserListBySupplierIdAsc(supplier.getId()).orElse(new ArrayList<>());
    if (CollUtil.isNotEmpty(supplierUsers)) return false;
    SupplierUser supplierUser = new SupplierUser();
    supplierUser.setSupplier(supplier);
    supplierUser.setSupplierId(supplier.getId());
    supplierUser.setName(supplier.getEnterpriseName());
    supplierUser.setRealName(userName);
    supplierUser.setMobile(mobile);
    supplierUser.setMail(emailAddress);
    supplierUser.setRole(Constants.SUPPLIER_USER_ROLE_ADMIN);
    supplierUser.setState(Constants.STATE_OK);
    supplierUser.setCreateTime(System.currentTimeMillis());
    supplierUser.setUpdateTime(System.currentTimeMillis());
    supplierUser.setSource(SupplierUserSource.BACKEND.getCode());
    supplierUser.setCreateMan(createMan);
    String permission = supplierUser.makePermission(supplier);
    supplierUser.setPermission(permission);
    save(supplierUser);
    String encryptPwd = PasswordUtil.sha1("xhiot", supplierUser.getId(),pwd);
    supplierUser.setPassword(encryptPwd);
    save(supplierUser);
    return true;
  }
}
