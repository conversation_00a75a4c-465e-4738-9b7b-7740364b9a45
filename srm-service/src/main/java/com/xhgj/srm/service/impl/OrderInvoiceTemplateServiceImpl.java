package com.xhgj.srm.service.impl;

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.dto.order.OrderDetailInvoiceDTO;
import com.xhgj.srm.jpa.entity.OrderInvoiceTemplate;
import com.xhgj.srm.jpa.repository.OrderInvoiceTemplateRepository;
import com.xhgj.srm.service.OrderInvoiceTemplateService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-06-04 16:04
 */
@Service
public class OrderInvoiceTemplateServiceImpl implements OrderInvoiceTemplateService {

  @Autowired private OrderInvoiceTemplateRepository repository;
  @Override
  public BootBaseRepository<OrderInvoiceTemplate, String> getRepository() {
    return repository;
  }

  @Override
  public Optional<OrderInvoiceTemplate> getByOrderId(String orderId){
    Assert.notBlank(orderId);
    return repository.getFirstByOrderId(orderId);
  }

  @Override
  public void saveOrderInvoiceTemplate(String orderId,
      OrderDetailInvoiceDTO orderDetailInvoiceDTO) {
    if (orderDetailInvoiceDTO!=null) {
      OrderInvoiceTemplate orderInvoiceTemplate = getByOrderId(orderId).orElseGet(()->{
        OrderInvoiceTemplate orderInvoiceTemplateTemp = new OrderInvoiceTemplate();
        orderInvoiceTemplateTemp.setOrderId(orderId);
        return orderInvoiceTemplateTemp;
      });
      orderInvoiceTemplate.setInvoiceTypeStr(orderDetailInvoiceDTO.getInvoiceTypeStr());
      orderInvoiceTemplate.setInvoiceType(orderDetailInvoiceDTO.getInvoiceType());
      orderInvoiceTemplate.setInvoiceTitle(orderDetailInvoiceDTO.getInvoiceTitle());
      orderInvoiceTemplate.setTaxCode(orderDetailInvoiceDTO.getTaxCode());
      orderInvoiceTemplate.setBankName(orderDetailInvoiceDTO.getBankName());
      orderInvoiceTemplate.setBankAccount(orderDetailInvoiceDTO.getBankAccount());
      orderInvoiceTemplate.setOpenInvoiceMobile(orderDetailInvoiceDTO.getOpenInvoiceMobile());
      orderInvoiceTemplate.setInvoiceAddress(orderDetailInvoiceDTO.getInvoiceAddress());
      orderInvoiceTemplate.setInvoiceFaceInfo(orderDetailInvoiceDTO.getInvoiceFaceInfo());
      orderInvoiceTemplate.setReceiveMan(orderDetailInvoiceDTO.getReceiveMan());
      orderInvoiceTemplate.setMobile(orderDetailInvoiceDTO.getMobile());
      orderInvoiceTemplate.setReceiveAddress(orderDetailInvoiceDTO.getReceiveAddress());
      save(orderInvoiceTemplate);
    }
  }
}
