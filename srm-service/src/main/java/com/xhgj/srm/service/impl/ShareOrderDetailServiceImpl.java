package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.repository.OrderDetailRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.request.dto.erp.OpenOrCloseOrderParams.OpenOrCloseOrderIdsDTO;
import com.xhgj.srm.request.enums.OpenOrCloseOrderType;
import com.xhgj.srm.request.service.third.erp.ERPRequest;
import com.xhgj.srm.service.ShareOrderDetailService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.config.BootConfig;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ShareOrderDetailServiceImpl implements ShareOrderDetailService {

  @Resource
  OrderRepository orderRepository;
  @Resource
  OrderDetailRepository orderDetailRepository;
  @Resource
  private ERPRequest erpRequest;
  @Resource
  BootConfig bootConfig;

  /**
   * erp终止行操作
   * @param orderId 订单id
   */
  public void stopOrderProduct(String orderId){
    Order order = orderRepository.findById(orderId)
        .orElseThrow(() -> CheckException.noFindException(Order.class, orderId));
    String erpOrderId = order.getErpOrderId();
    if (StrUtil.isBlank(erpOrderId)) {
      return;
    }
    List<OrderDetail> orderDetails =
        orderDetailRepository.findAllByOrderIdAndState(orderId, Constants.STATE_OK);
    if (CollUtil.isEmpty(orderDetails)) {
      return;
    }
    LinkedList<String> list = new LinkedList<>();
    for (OrderDetail orderDetail : orderDetails) {
      //有未发货数量
      if (NumberUtil.isGreater(orderDetail.getUnshipNum(), new BigDecimal(0))) {
        list.add(orderDetail.getErpRowId());
      }
    }
    if (CollUtil.isEmpty(list)) {
      return;
    }
    OpenOrCloseOrderIdsDTO idsDTO = new OpenOrCloseOrderIdsDTO();
    idsDTO.setId(erpOrderId);
    idsDTO.setEntryIds(list);
    String result = null;
    try {
      result = erpRequest.openOrCloseOrder(OpenOrCloseOrderType.DETAIL_CLOSE, idsDTO);
    } catch (Exception e) {
      String env = bootConfig.getEnv();
      DingUtils.sendMsgByWarningRobot(
          "【" + env + "环境 " + bootConfig.getAppName() + "】 【" + order.getOrderNo() + "】调用ERP "
              + "终止行操作失败：" + "请求参数：" + JSON.toJSONString(idsDTO) + "请求结果：" + result
              + " ，请及时处理！", env);
    }
  }
}
