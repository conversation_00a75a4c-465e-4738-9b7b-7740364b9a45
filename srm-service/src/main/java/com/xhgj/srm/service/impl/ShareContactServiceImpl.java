package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.service.ShareContactService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.Contact;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.repository.ContactRepository;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import com.xhgj.srm.registration.repository.EntryRegistrationRepository;
import com.xhgj.srm.service.ShareSupplierService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class ShareContactServiceImpl implements ShareContactService {
  @Resource
  private ContactRepository contactRepository;
  @Resource
  private ShareSupplierService supplierService;
  @Resource
  EntryRegistrationRepository entryRegistrationRepository;



  @Override
  public void addContactIfNotExist(String contacts, String supplierId, String mobile,
      String userId, String groupId) {
    if (StringUtils.isNullOrEmpty(supplierId)) {
      throw new CheckException("供应商为空");
    }
    if (StringUtils.isNullOrEmpty(userId)) {
      throw new CheckException("用户id为空");
    }
    if (StringUtils.isNullOrEmpty(mobile)) {
      throw new CheckException("供应商联系方式为空");
    }
    if (StringUtils.isNullOrEmpty(contacts)) {
      throw new CheckException("供应商联系人为空");
    }
    Supplier supplier = supplierService.get(supplierId);
    if (supplier == null) {
      throw new CheckException("供应商不存在，无法新增联系人");
    }
    List<com.xhgj.srm.jpa.entity.Contact> contactList =
        contactRepository.findAllBySupplierIdAndSupplierInGroupIdAndState(supplierId, groupId,
            Constants.STATE_OK);
    Contact selectContact = CollUtil.emptyIfNull(contactList).stream().
        filter(contact -> Objects.equals(contact.getName(), contacts)).findFirst().orElse(null);
    if (selectContact == null) {
      Contact contact = new Contact();
      contact.setCreateMan(userId);
      contact.setName(contacts);
      contact.setSex("2");
      contact.setPhone(mobile);
      contact.setCreateTime(System.currentTimeMillis());
      contact.setSupplier(supplier);
      contact.setSupplierId(supplier.getId());
      contact.setState(Constants.STATE_OK);
      contactRepository.save(contact);
    }



  }

  @Override
  public void addContactByEntryRegistrationOrder(EntryRegistrationOrder entryRegistrationOrder,
      Supplier supplier, String supplierInGroupId) {
    EntryRegistrationEntity
        entity = new EntryRegistrationEntity(entryRegistrationRepository, entryRegistrationOrder);
    List<com.xhgj.srm.jpa.entity.Contact> contactList =
        contactRepository.findAllBySupplierIdAndSupplierInGroupIdAndState(supplier.getId(), supplierInGroupId,
            Constants.STATE_OK);
    Contact contact = CollUtil.emptyIfNull(contactList).stream().
        filter(contactItem -> Objects.equals(contactItem.getName(),
        entryRegistrationOrder.getCooperationContactName())).findFirst().orElse(null);
    if (contact == null) {
      contact = new Contact();
    }
    // 合作联系人、联系电话、职务、邮箱、联系地址
    contact.setName(entryRegistrationOrder.getCooperationContactName());
    contact.setPhone(entryRegistrationOrder.getCooperationContactPhone());
    contact.setDuty(entryRegistrationOrder.getPosition());
    if (entity.getMerchant() != null) {
      contact.setMail(entity.getMerchant().getEmailAddress());
    }
    contact.setContactAddress(entryRegistrationOrder.getContactAddress());

    contact.setArea(entryRegistrationOrder.getCooperationRegion());
    contact.setCreateTime(System.currentTimeMillis());
    contact.setUpdateTime(System.currentTimeMillis());
    contact.setSupplier(supplier);
    contact.setSupplierId(supplier.getId());
    contact.setState(Constants.STATE_OK);
    contact.setSupplierInGroupId(supplierInGroupId);
    contact.setCreateMan(supplier.getCreateMan());
    contactRepository.save(contact);
  }
}
