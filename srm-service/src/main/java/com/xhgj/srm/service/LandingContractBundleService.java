package com.xhgj.srm.service;/**
 * @since 2024/11/28 11:35
 */

import com.xhgj.srm.dto.bundle.SupplierBundleMatchQueryForm;
import com.xhgj.srm.handle.LandingContractBundleBatchGetHandler;
import com.xhgj.srm.jpa.dto.landingContract.LandingContractBundleSaveForm;
import com.xhgj.srm.jpa.entity.LandingContractBundle;
import com.xhgj.srm.vo.bundle.PlatformSupplierBundle;
import com.xhgj.srm.vo.bundle.SupplierBundleMatchBatchVO;
import com.xhgj.srm.vo.bundle.SupplierBundleMatchVO;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2024/11/28 11:35:27
 *@description 合同绑品信息
 */
public interface LandingContractBundleService {
  /**
   * 新增 合同绑品信息
   */
  void patchUpdate(List<LandingContractBundleSaveForm> saveForm, String landingContractId, String userId);

  /**
   * 批量获取合同的绑品信息
   */
  List<LandingContractBundle> getLandingContractBundle(List<String> landingContractIds);

  /**
   * 批量获取合同的绑品信息,实时刷新名称
   */
  List<LandingContractBundle> getLandingContractBundle(List<String> landingContractIds, boolean refresh);

  /**
   * 根据平台code查询绑品信息
   */
  List<LandingContractBundle> getLandingContractBundleByPlatformCode(String platformCode, boolean refresh);

  /**
   * 根据平台code查询绑品信息
   */
  List<LandingContractBundle> getLandingContractBundleByPlatformCode(String platformCode);

  /**
   * 获取平台供应商绑品信息列表
   */
  List<PlatformSupplierBundle> getPlatformSupplierBundle(String platformCode);

  /**
   * 获取供应商绑品匹配信息
   * @param form
   * @return
   */
  List<SupplierBundleMatchVO> matchSupplierBundle(SupplierBundleMatchQueryForm form);

  /**
   * 获取供应商绑品匹配信息 批量
   * @param forms
   * @return
   */
  List<SupplierBundleMatchBatchVO> matchSupplierBundleBatch(List<SupplierBundleMatchQueryForm> forms);

  /**
   * 批量获取绑品关联信息
   */
  void batchGetInfo(List<String> landingContractIds, LandingContractBundleBatchGetHandler handler);
}
