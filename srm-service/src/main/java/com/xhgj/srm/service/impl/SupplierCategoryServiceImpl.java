package com.xhgj.srm.service.impl;/**
 * @since 2024/12/17 9:48
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.dto.supplierCategory.SupplierCategorySaveForm;
import com.xhgj.srm.jpa.entity.SupplierCategory;
import com.xhgj.srm.jpa.repository.SupplierCategoryRepository;
import com.xhgj.srm.map.domain.IgnoreFieldContext;
import com.xhgj.srm.request.dto.supplierCategory.CategoryFindDto;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhgj.srm.service.SupplierCategoryService;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SupplierCategoryServiceImpl implements SupplierCategoryService {
  @Resource
  private SupplierCategoryRepository supplierCategoryRepository;
  @Resource
  private MPMService mpmService;

  @Override
  public void patchUpdate(List<SupplierCategorySaveForm> saveForm, String supplierId, String supplierInGroupId, String userId) {
    this.patchUpdate(saveForm, supplierId, supplierInGroupId, userId, "-1");
  }

  @Override
  public void patchUpdate(List<SupplierCategorySaveForm> saveForm, String supplierId, String supplierInGroupId, String userId, String originSupplierInGroupId) {
    if (StrUtil.isBlank(supplierId)) {
      return;
    }
    // saveForm如果传null，不做处理
    if (saveForm == null) {
      return;
    }
    // patch更新供应商经营类目
    // 查询供应商原有的经营类目
    List<SupplierCategory> origin =
        supplierCategoryRepository.findAllBySupplierInGroupIdAndState(supplierInGroupId, Constants.STATE_OK);
    // originSupplierInGroupId对应的数据
    List<SupplierCategory> origin2 =
        supplierCategoryRepository.findAllBySupplierInGroupIdAndState(originSupplierInGroupId, Constants.STATE_OK);
    List<String> originCodes =
        origin.stream().map(SupplierCategory::getCategoryCode).collect(Collectors.toList());
    // 筛选出新增的
    List<SupplierCategorySaveForm> newOnes =
        saveForm.stream().filter(item -> !originCodes.contains(item.getCategoryCode()))
            .collect(Collectors.toList());
    // 筛选出更新的
    List<SupplierCategorySaveForm> updateOnes =
        saveForm.stream().filter(item -> originCodes.contains(item.getCategoryCode()))
            .collect(Collectors.toList());
    // 筛选出删除的 origin中存在，updateOnes中不存在的
    List<SupplierCategory> deleteOnes = origin.stream().filter(
            item -> saveForm.stream().noneMatch(update -> update.getCategoryCode().equals(item.getCategoryCode())))
        .collect(Collectors.toList());
    // 删除
    if (CollUtil.isNotEmpty(deleteOnes)) {
      deleteOnes.forEach(item -> {
        item.setUpdateTime(System.currentTimeMillis());
        item.setUpdateMan(userId);
        item.setState(Constants.STATE_DELETE);
      });
      supplierCategoryRepository.saveAll(deleteOnes);
      supplierCategoryRepository.flush();
    }
    // 新增
    if (CollUtil.isNotEmpty(newOnes)) {
      Map<String, SupplierCategory> origin2Map =
          origin2.stream().collect(Collectors.toMap(SupplierCategory::getCategoryCode, item -> item));
      List<SupplierCategory> newBatch = newOnes.stream().map(item -> {
        SupplierCategory supplierCategory = MapStructFactory.INSTANCE.toSupplierCategory(item);
        supplierCategory.setId(null);
        supplierCategory.setSupplierId(supplierId);
        supplierCategory.setSupplierInGroupId(supplierInGroupId);
        this.refreshCategory(supplierCategory);
        if (supplierCategory.getSort() == null) {
          supplierCategory.setSort(100);
        }
        supplierCategory.setCreateTime(System.currentTimeMillis());
        supplierCategory.setCreateMan(userId);
        supplierCategory.setUpdateTime(System.currentTimeMillis());
        supplierCategory.setUpdateMan(userId);
        supplierCategory.setState(Constants.STATE_OK);
        SupplierCategory supplierCategoryOrigin = origin2Map.get(item.getCategoryCode());
        if (supplierCategoryOrigin != null) {
          supplierCategory.setCreateMan(supplierCategoryOrigin.getCreateMan());
          supplierCategory.setCreateTime(supplierCategoryOrigin.getCreateTime());
        }
        return supplierCategory;
      }).collect(Collectors.toList());
      supplierCategoryRepository.saveAll(newBatch);
      supplierCategoryRepository.flush();
    }
    // 更新
    if (CollUtil.isNotEmpty(updateOnes)) {
      Map<String, SupplierCategory> originMap =
          origin.stream().collect(Collectors.toMap(SupplierCategory::getCategoryCode, item -> item));
      List<SupplierCategory> updateBatch = updateOnes.stream().map(item -> {
        SupplierCategory supplierCategory = originMap.get(item.getCategoryCode());
        MapStructFactory.INSTANCE.updateSupplierCategory(item, supplierCategory, IgnoreFieldContext.create("id"));
        supplierCategory.setUpdateTime(System.currentTimeMillis());
        supplierCategory.setUpdateMan(userId);
        this.refreshCategory(supplierCategory);
        return supplierCategory;
      }).collect(Collectors.toList());
      supplierCategoryRepository.saveAll(updateBatch);
      supplierCategoryRepository.flush();
    }
  }

  /**
   * 刷新类目相关信息
   * @param supplierCategory
   */
  private void refreshCategory(SupplierCategory supplierCategory) {
    CategoryFindDto findDto = mpmService.findCategory(supplierCategory.getCategoryCode());
    if (findDto == null) {
      // 第一次查询不到，清除缓存再查询一次
      mpmService.clearCategoryListTreeCache();
      findDto = mpmService.findCategory(supplierCategory.getCategoryCode());
      if (findDto == null) {
        throw new CheckException("类目编码不存在");
      }
    }
    supplierCategory.setCategoryName(findDto.getName());
    supplierCategory.setCategoryPath(findDto.makeNamesString());
    supplierCategory.setCategoryPathName(findDto.makePathString());
  }

  @Override
  public List<SupplierCategory> getSupplierCategory(List<String> supplierInGroupIds) {
    return this.getSupplierCategory(supplierInGroupIds, false);
  }

  @Override
  public List<SupplierCategory> getSupplierCategory(List<String> supplierInGroupIds, boolean refresh) {
    if (CollUtil.isEmpty(supplierInGroupIds)) {
      return Collections.emptyList();
    }
    List<SupplierCategory> supplierCategories =
        supplierCategoryRepository.findAllBySupplierInGroupIdInAndStateOrderByCreateTimeAscIdAsc(supplierInGroupIds,
        Constants.STATE_OK);
    if (refresh) {
      try {
        // 多线程刷新类目信息
        supplierCategories.parallelStream().forEach(this::refreshCategory);
      }catch (Exception e) {
        // 刷新类目信息失败
        log.info("getSupplierCategory中刷新类目信息失败: {}", e.getMessage(), e);
      }
    }
    return supplierCategories;
  }

  @Override
  public void deleteBySupplierInGroupId(String supplierInGroupId, String userId) {
    if (StrUtil.isBlank(supplierInGroupId)) {
      return;
    }
    List<SupplierCategory> supplierCategories =
        supplierCategoryRepository.findAllBySupplierInGroupIdAndState(supplierInGroupId,
            Constants.STATE_OK);
    if (CollUtil.isEmpty(supplierCategories)) {
      return;
    }
    supplierCategories.forEach(item -> {
      item.setUpdateTime(System.currentTimeMillis());
      item.setUpdateMan(userId);
      item.setState(Constants.STATE_DELETE);
    });
    supplierCategoryRepository.saveAll(supplierCategories);
  }


  @Override
  public void deleteBySupplierId(String supplierId, String userId) {
    if (StrUtil.isBlank(supplierId)) {
      return;
    }
    List<SupplierCategory> supplierCategories =
        supplierCategoryRepository.findAllBySupplierIdAndState(supplierId, Constants.STATE_OK);
    if (CollUtil.isEmpty(supplierCategories)) {
      return;
    }
    supplierCategories.forEach(item -> {
      item.setUpdateTime(System.currentTimeMillis());
      item.setUpdateMan(userId);
      item.setState(Constants.STATE_DELETE);
    });
    supplierCategoryRepository.saveAll(supplierCategories);
  }
}
