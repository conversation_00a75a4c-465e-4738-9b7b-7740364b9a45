package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.jpa.entity.Platform;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.PlatformRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.service.SharePlatformService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * Created by Geng Shy on 2023/10/18
 */
@Service("servicePlatformServiceImpl")
public class PlatformServiceImpl implements SharePlatformService {

  @Resource
  private PlatformRepository repository;
  @Resource private UserRepository userRepository;
  @Override
  public BootBaseRepository<Platform, String> getRepository() {
    return repository;
  }

  @Override
  public List<OrderPlatformDTO> findAll() {
    List<Platform> platforms = getAll();
    if (CollUtil.isEmpty(platforms)) {
      return new ArrayList<>();
    }
    return platforms.stream().map(platform ->
        new OrderPlatformDTO(platform.getName(), platform.getCode(),
            platform.getPlatformAbbreviation(),null,platform.getProjectCategory())).collect(Collectors.toList());
  }

  @Override
  public String findNameByCode(String code) {
    if (StrUtil.isBlank(code)) {
      return null;
    }
    Optional<Platform> platformOptional =
        repository.findFirstByCodeAndState(code, Constants.STATE_OK);
    return platformOptional.map(Platform::getName).orElse(null);
  }

  @Override
  public String findCodeByName(String name) {
    if (StrUtil.isBlank(name)) {
      return null;
    }
    Optional<Platform> platformOptional =
        repository.findFirstByNameAndState(name, Constants.STATE_OK);
    return platformOptional.map(Platform::getName).orElse(null);
  }

  @Override
  public OrderPlatformDTO findByCode(String code) {
    if (StrUtil.isBlank(code)) {
      return null;
    }
    Optional<Platform> platformOptional =
        repository.findFirstByCodeAndState(code, Constants.STATE_OK);
    return platformOptional.map(
        platform -> new OrderPlatformDTO(platform.getName(), platform.getCode(),
            platform.getPlatformAbbreviation(),null,platform.getProjectCategory())).orElse(null);
  }

  @Override
  public OrderPlatformDTO findByName(String name) {
    if (StrUtil.isBlank(name)) {
      return null;
    }
    Optional<Platform> platformOptional =
        repository.findFirstByNameAndState(name, Constants.STATE_OK);
    return platformOptional.map(
        platform -> new OrderPlatformDTO(platform.getName(), platform.getCode(),
            platform.getPlatformAbbreviation(),null,platform.getProjectCategory())).orElse(null);
  }

  @Override
  public Platform getEntityByCode(String code) {
    Assert.notBlank(code);
    Optional<Platform> platformOptional =
        repository.findFirstByCodeAndState(code, Constants.STATE_OK);
    return platformOptional.orElse(null);
  }

  @Override
  public List<OrderPlatformDTO> batchFindByCode(Collection<String> codes) {
    if (CollUtil.isEmpty(codes)) return Collections.emptyList();
    LinkedList<OrderPlatformDTO> result = new LinkedList<>();
    for (String code : codes) {
      result.add(findByCode(code));
    }
    return result;
  }

  @Override
  public List<Platform> findByNameIn(List<String> platformNames) {
    if (CollUtil.isEmpty(platformNames)) {
      return Collections.emptyList();
    }
    List<Platform> platforms = repository.findByNameInAndState(platformNames, Constants.STATE_OK);
    // 去重
    Map<String, Platform> firstMap = platforms.stream().collect(
        Collectors.toMap(Platform::getName,
            platformDTO -> platformDTO,
            (existing, replacement) -> existing));
    return new ArrayList<>(firstMap.values());

  }

  @Override
  public String findDefaultPurchaseByCode(String code) {
    if (StrUtil.isBlank(code)) {
      return null;
    }
    Optional<Platform> platformOptional =
        repository.findFirstByCodeAndState(code, Constants.STATE_OK);
    return platformOptional.map(Platform::getDefaultPurchase)
        .flatMap(userId -> userRepository.findById(userId))
        .map(User::getRealName)
        .orElse(null);
  }
}
