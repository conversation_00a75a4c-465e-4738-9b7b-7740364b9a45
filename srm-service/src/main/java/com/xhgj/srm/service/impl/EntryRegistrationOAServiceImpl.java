package com.xhgj.srm.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.dto.entryregistration.ApprovalResultCallbackParam;
import com.xhgj.srm.dto.entryregistration.EntryRegistrationDetail;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.service.EntryRegistrationOAService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.RandomPasswordGeneratorUtil;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.enums.AssessStateEnum;
import com.xhgj.srm.common.enums.BooleanEnum;
import com.xhgj.srm.common.enums.ShortMessageEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationStatusEnum;
import com.xhgj.srm.jpa.entity.*;
import com.xhgj.srm.jpa.repository.EntryRegistrationLandingMerchantRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationOrderRepository;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.SupplierToMenuRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDetailDTO;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import com.xhgj.srm.registration.repository.EntryRegistrationRepository;
import com.xhgj.srm.request.dto.partner.PartnerDTO;
import com.xhgj.srm.request.service.third.xhgj.XhgjDockRequest;
import com.xhgj.srm.request.service.third.xhgj.XhgjSMSRequest;
import com.xhgj.srm.service.ShareContactService;
import com.xhgj.srm.service.ShareEntryRegistrationLandingMerchantService;
import com.xhgj.srm.service.ShareEntryRegistrationService;
import com.xhgj.srm.service.ShareLandingMerchantContractService;
import com.xhgj.srm.service.ShareSupplierInGroupService;
import com.xhgj.srm.service.ShareSupplierService;
import com.xhgj.srm.service.ShareSupplierUserService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;;
import java.util.Date;
import java.util.HashMap;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class EntryRegistrationOAServiceImpl implements EntryRegistrationOAService {

  @Resource private EntryRegistrationOrderRepository entryRegistrationOrderRepository;

  @Resource
  private EntryRegistrationLandingMerchantRepository entryRegistrationLandingMerchantRepository;
  @Resource private SupplierRepository supplierRepository;
  @Resource private GroupRepository groupRepository;

  @Resource
  private ShareEntryRegistrationLandingMerchantService shareEntryRegistrationLandingMerchantService;

  @Resource private ShareSupplierService supplierService;
  @Resource private FileRepository fileRepository;
  @Resource
  private ShareContactService contactService;
  @Resource
  private UserRepository userRepository;
  @Resource
  private ShareSupplierInGroupService shareSupplierInGroupService;
  @Resource
  private ShareLandingMerchantContractService shareLandingMerchantContractService;
  @Resource
  private SupplierToMenuRepository supplierToMenuRepository;
  @Resource
  private ShareSupplierUserService supplierUserService;
  @Resource
  XhgjSMSRequest xhgjSMSRequest;
  @Resource
  EntryRegistrationRepository entryRegistrationRepository;
  @Resource
  XhgjDockRequest xhgjDockRequest;
  @Resource
  ShareEntryRegistrationService shareEntryRegistrationService;
  @Override
  public BootBaseRepository<EntryRegistrationOrder, String> getRepository() {
    return entryRegistrationOrderRepository;
  }
  private final String baseUrl;
  public EntryRegistrationOAServiceImpl(SrmConfig config) {
    this.baseUrl = config.getUploadUrl();
  }
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean approvalResultCallback(ApprovalResultCallbackParam param) {
    checkApprovalResultCallbackParam(param);
    EntryRegistrationOrder entryRegistrationOrder =
        entryRegistrationOrderRepository.findFirstByOaAuditIdAndState(
            param.getAuditId(), Constants.STATE_OK);
    if (Objects.isNull(entryRegistrationOrder)) {
      throw new CheckException("报备单不存在");
    }
    EntryRegistrationEntity entity =
        new EntryRegistrationEntity(entryRegistrationRepository, entryRegistrationOrder);
    // 设置oa审核更新时间
    entity.setOaCompletionTime(System.currentTimeMillis());
    entity.setMdmCompletionTime(System.currentTimeMillis());
    // 临时需求，驳回后允许OA再次审核
    //    checkStatus(entity.getRegistrationStatus());
    if (Objects.equals(param.getApprovalResult(), BooleanEnum.NO.getKey())) {
      entity.setRegistrationStatus(EntryRegistrationStatusEnum.REJECTED.getKey());
      entity.setReasonForRejection(param.getRejection());
      entity.save();
    }
    if (Objects.equals(param.getApprovalResult(), BooleanEnum.YES.getKey())) {
      entity.setRegistrationStatus(EntryRegistrationStatusEnum.APPROVED.getKey());
      entity.save();
      handleSupplierAuditRef(entity);
    }
    return true;
  }

  @Transactional(rollbackFor = Exception.class)
  public void handleSupplierAuditRef(EntryRegistrationEntity entity) {
    EntryRegistrationLandingMerchant merchant = entity.getMerchant();
    if (merchant == null) {
      throw new CheckException("未找到落地商信息");
    }
    Supplier originSupplier =
        supplierRepository
            .findFirstByEnterpriseNameAndState(
                entity.getMerchant().getEnterpriseName(), Constants.STATE_OK)
            .orElse(null);
    Group headquartersGroup = groupRepository.findFirstByCodeAndState(Constants.HEADQUARTERS_CODE,
        Constants.STATE_OK);
    PartnerDTO partnerDTO = null;
    // 标志位是否已经创建供应商
    Boolean isCreate = originSupplier != null;
    // 1.判断是否有供应商信息，没有则创建，并同步至mdm
    if (!isCreate) {
      originSupplier = shareEntryRegistrationLandingMerchantService.convertToSupplier(merchant);
      try {
        supplierService.createSupplier(originSupplier, headquartersGroup);
        // 如果去mdm创建新的供应商，那么睡眠600ms，防止下一行代码去mdm及时查询，偶尔会查不到
        Thread.sleep(600);
      } catch (Exception e) {
        log.info("创建供应商失败:{}", e.getMessage(), e);
      }
    }
    try {
      partnerDTO = supplierService.getPartnerDTO(originSupplier.getEnterpriseName(), originSupplier.getSupType());
    } catch (Exception e) {
      log.info("获取合作商信息失败:{}", e.getMessage(), e);
      throw new CheckException(e.getMessage());
    }
    Supplier newSupplier = MapStructFactory.INSTANCE.toSupplier(originSupplier);
    // 2.更新supplier
    if (partnerDTO != null) {
      newSupplier.setMdmCode(partnerDTO.getMdmCode());
    }
    newSupplier.setState(Constants.STATE_OK);
    //（跟随电商供应商所有权限）
    newSupplier.openOrderPermission();
    // 处理platform
    newSupplier.addPlatform(entity.getPlatformCodes());
    supplierRepository.save(newSupplier);
    // 3.更新merchant
    merchant.setSupplierId(newSupplier.getId());
    merchant.setSupplierAuditStatus(AssessStateEnum.PASS.getKey());
    if (partnerDTO != null) {
      merchant.setMdmCode(newSupplier.getMdmCode());
    }
    entryRegistrationLandingMerchantRepository.save(merchant);
    // 4.创建或更新合同信息
    shareLandingMerchantContractService.addByEntryRegistrationLandingMerchant(entity,newSupplier);
    // 5.开通前台权限
    supplierToMenuRepository.deleteAllBySupplierId(newSupplier.getId());
    SupplierInGroup supplierInGroup =
        shareSupplierInGroupService.createIfNotExist(Constants.HEADQUARTERS_CODE,
            newSupplier.getId(), merchant, entity);
    // 6.添加供应商联系人(供应商未创建时)
    if (!isCreate) {
      contactService.addContactByEntryRegistrationOrder(entity, newSupplier, supplierInGroup.getId());
    }
    // 7.生成供应商账号
    // 生产6位随机密码 英文大小写+数字
    String pwd = RandomPasswordGeneratorUtil.generateRandomPassword(6);
    boolean createFlag =
        supplierUserService.createIfNotExist(newSupplier, merchant.getAccountUser(),
            merchant.getAccountUserPhone(), merchant.getEmailAddress(), pwd, entity.getUpdateMan());
    // 7.1根据是否创建供应商账号发送不同的短信模版
    if (StrUtil.isBlank(merchant.getAccountUser())) {
      return;
    }
    String sendSmsMobile = merchant.getAccountUserPhone();
    // 同步履约信息至OMS,清空原有履约信息
    shareEntryRegistrationService.batchUpdateDiscountToOmsClear(entity);
    if (createFlag) {
      //发送短信
      xhgjSMSRequest.sendSms(
          ShortMessageEnum.APPROVED_SEND_MESSAGE_TO_THE_LANDER_NEW_SUPPLIER,
          sendSmsMobile,
          new HashMap<String, String>() {
            {
              put("name", merchant.getAccountName());
              put("Password", pwd);
            }
          });
    }else{
      //发送短信
      xhgjSMSRequest.sendSms(
          ShortMessageEnum.APPROVED_SEND_MESSAGE_TO_THE_LANDER_EXIST_SUPPLIER,
          sendSmsMobile,
          new HashMap<String, String>() {
            {
              put("project", entity.getProjectName());
            }
          });
    }
    // 8 同步MDM任务，然后MDM同步至SAP
    xhgjDockRequest.createSupplierAddTask(
        shareSupplierInGroupService.buildDockSupplier(newSupplier.getId(), headquartersGroup, null));
  }

  private void checkStatus(String registrationStatus) {
    if (!Objects.equals(registrationStatus, EntryRegistrationStatusEnum.UNDER_REVIEW.getKey())) {
      throw new CheckException("该报备单非待审核状态");
    }
  }

  private void checkApprovalResultCallbackParam(ApprovalResultCallbackParam param) {
    if (Objects.equals(param.getApprovalResult(), BooleanEnum.NO.getKey())) {
      if (StrUtil.isBlank(param.getRejection())) {
        throw new CheckException("请说明驳回理由");
      }
    }
  }


  /**
   * 入驻报备单供 H5 详情
   *
   * @param id
   * @return
   */
  @Override
  public EntryRegistrationDetail settlementRegistrationFormForH5Details(String id) {
    EntryRegistrationEntity entity = entryRegistrationRepository.byId(id);
    if (entity == null) {
      throw new RuntimeException("未找到该条数据");
    }
    if (entity.getLinkGenerationTime() == null
        || DateUtil.between(
        new Date(entity.getLinkGenerationTime()),
        new Date(),
        DateUnit.DAY)
        > 7) {
     throw  new CheckException("链接已失效");
    }

    EntryRegistrationDetailDTO entryRegistrationDetailDTO = entity.toEntryRegistrationDetailDTO(baseUrl);
    return MapStructFactory.INSTANCE.toEntryRegistrationDetail(entryRegistrationDetailDTO);
  }
}
