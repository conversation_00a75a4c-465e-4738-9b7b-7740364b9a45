package com.xhgj.srm.service.impl;/**
 * @since 2025/3/17 15:34
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.dto.OmsOrderReceiptDto;
import com.xhgj.srm.common.dto.OmsOrderReceiptTestDto;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.order.OrderReceiptPaymentMethodEnum;
import com.xhgj.srm.common.enums.order.OrderReceiptType;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderReceiptRecord;
import com.xhgj.srm.jpa.repository.OrderReceiptRecordRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.service.OrderNeedPaymentService;
import com.xhgj.srm.service.OrderReceiptRecordService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * 履约回款记录业务方法
 */
@Service
@Slf4j
public class OrderReceiptRecordServiceImpl implements OrderReceiptRecordService {

  @Resource
  private OrderReceiptRecordRepository orderReceiptRecordRepository;
  @Resource
  private OrderNeedPaymentService orderNeedPaymentService;
  @Resource
  private OrderRepository orderRepository;

  @Override
  public void saveOrderReceiptRecord(OmsOrderReceiptDto form, Order order) {
    OrderReceiptRecord record = MapStructFactory.INSTANCE.toOrderReceiptRecord(form);
    OrderReceiptRecord dbRecord =
        orderReceiptRecordRepository.findFirstByOmsUniqueIdAndOrderIdAndState(record.getOmsUniqueId(),
            order.getId(),
            Constants.STATE_OK);
    if (!Objects.isNull(dbRecord)) {
      log.error("订单id{}履约回款记录已存在，omsUniqueId:{}，跳过", order.getId(),
          record.getOmsUniqueId());
      return;
    }
    record.setOrderId(order.getId());
    record.setCreateTime(System.currentTimeMillis());
    record.setUpdateTime(System.currentTimeMillis());
    record.setState(Constants.STATE_OK);
    orderReceiptRecordRepository.saveAndFlush(record);
    orderNeedPaymentService.save(record.getId(), order);
  }

  @Override
  public List<OrderReceiptRecord> findByIds(List<String> ids) {
    if (CollUtil.isEmpty(ids)) {
      return Collections.emptyList();
    }
    return orderReceiptRecordRepository.findAllById(ids);
  }

  @Override
  public List<OrderReceiptRecord> findByOrderIds(List<String> ids) {
    if (CollUtil.isEmpty(ids)) {
      return Collections.emptyList();
    }
    return orderReceiptRecordRepository.findByOrderIdInAndState(ids, Constants.STATE_OK);
  }

  @Override
  public List<String> findPaymentMethodsByOrderId(String orderId) {
    return this.findByOrderIds(Collections.singletonList(orderId)).stream()
        .map(OrderReceiptRecord::getPaymentMethod).distinct()
        .map(OrderReceiptPaymentMethodEnum::getDescFromCode)
        .filter(StrUtil::isNotBlank)
        .collect(Collectors.toList());
  }

  @Override
  public void testData(OmsOrderReceiptTestDto dto, String orderId) {
    Order order =
        orderRepository.findById(orderId).orElseThrow(() -> new CheckException("订单不存在"));
    order.setCustomerReturnProgress(Constants_order.RETURN_PROGRESS_ALL);
    orderRepository.saveAndFlush(order);
    OmsOrderReceiptDto one = new OmsOrderReceiptDto();
    one.setId(dto.getId());
    one.setProjectNo(dto.getProjectNo());
    one.setPaymentMethod(dto.getPaymentMethod());
    one.setPaymentType(dto.getPaymentType());
    one.setBankSerialNo(dto.getBankSerialNo());
    one.setBillNo(dto.getBillNo());
    one.setClearingTime(new Date());
    one.setReceivedTime(new Date());
    one.setPaymentRatio(dto.getPaymentRatio());
    one.setPaymentAmount(dto.getPaymentAmount());
    this.saveOrderReceiptRecord(one, order);
  }
}

