package com.xhgj.srm.service;/**
 * @since 2025/2/25 11:38
 */

import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.vo.asmDisOrder.AsmDisOrderListVO;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderApplyLinkDTO;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderExportForm;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderFillDTO;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderImportForm;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderSaveForm;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderSearchForm;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSAsmDisOrderReturn;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSAsmDisOrderReturnResult;
import com.xhgj.srm.vo.asmDisOrder.AsmDisOrderVO;
import com.xhiot.boot.mvc.base.PageResult;
import java.io.IOException;
import java.util.List;

/**
 * 组装拆卸单服务
 */
public interface AsmDisOrderService {

  /**
   * 组装拆卸单保存
   * @return 组装拆卸单id
   */
  String save(AsmDisOrderSaveForm form);

  /**
   * 组装拆卸单分页列表
   */
  PageResult<AsmDisOrderListVO> getPage(AsmDisOrderSearchForm form);

  /**
   * 组装拆卸单导出
   */
  void export(AsmDisOrderExportForm form);

  /**
   * 调拨单详情
   */
  AsmDisOrderVO getDetail(String id);

  /**
   * 组装拆卸单删除
   */
  void delete(String id, Boolean sap);

  /**
   * 组装拆卸单废弃
   */
  void invalid(String id);

  /**
   * 飞搭审核回调
   */
  void auditCallBack(ApprovalResult approvalResult, byte status);

  /**
   * 下载组装拆卸单导入模板
   * @param fillDTOList
   * @return
   */
  byte[] downloadDetail(List<AsmDisOrderFillDTO> fillDTOList);

  /**
   * 导入调拨单
   * @param form
   * @return
   */
  List<AsmDisOrderFillDTO> fill(AsmDisOrderImportForm form) throws IOException;

  /**
   * WMS仓库执行调拨结果回传处理
   * @param param
   * @return
   */
  WMSAsmDisOrderReturnResult wmsCallback(WMSAsmDisOrderReturn param);

  /**
   * sap执行 079，并更新财务凭证号与凭证年份
   */
  void sapProcess(String id, String wmsOperationType, Long time);

  /**
   * 通过采购申请ids查询相关的组装拆卸单
   */
  List<AsmDisOrderApplyLinkDTO> getPurchaseApplyLink(List<String> purchaseApplyForOrderIds);

  /**
   * 同步SAP079
   *
   * @param id 组装拆卸单id
   * @param time 过账日期
   */
  void syncSap079(String id, Long time);
}
