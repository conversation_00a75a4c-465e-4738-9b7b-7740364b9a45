package com.xhgj.srm.service;

import com.xhgj.srm.dto.landingContract.LandingMerchantContractDownloadQuery;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.Map;

/**
 * Created by Geng Shy on 2023/11/8
 */
public interface ShareLandingMerchantContractService extends BootBaseService<LandingMerchantContract,String> {

  /**
   * ocr识别服务，传入文件半路径，返回ocr所识别到的内容
   * @param fileUrl
   * @return
   */
  Map<String, String> handleFileDiscern(String fileUrl);


  void addByEntryRegistrationLandingMerchant(EntryRegistrationEntity entity,
      Supplier supplier);

  /**
   * 生成合同 同时写入履约信息状态为开启
   * @param entryRegistrationOrder 报备单
   * @param landingMerchant 报备单关联的电商供应商信息
   * @param supplier 供应商
   */
  void addByEntryRegistrationOrderAndLandingMerchantAndSupplier(EntryRegistrationOrder entryRegistrationOrder,
      EntryRegistrationLandingMerchant landingMerchant, Supplier supplier);

  String getBusinessLeaderId(String salesmanId);

  /**
   * 下载合同
   * @param params
   * @return
   */
  byte[] downloadTheContract(LandingMerchantContractDownloadQuery params);
}
