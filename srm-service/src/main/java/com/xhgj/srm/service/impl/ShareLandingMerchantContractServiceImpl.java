package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.dto.LandingContract.LandingMerchantContractPut2Pdf;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.PurchaseOrderInvoiceType;
import com.xhgj.srm.common.enums.contract.LandingMerchantContractKeywordEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationCooperationTypeEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationDiscountTypeEnum;
import com.xhgj.srm.common.enums.landingContract.ContractStatus;
import com.xhgj.srm.common.utils.FileUtil;
import com.xhgj.srm.common.utils.MoneyUtil;
import com.xhgj.srm.common.utils.WordPoiUtils;
import com.xhgj.srm.common.utils.ZIPUtils;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.domain.Contract;
import com.xhgj.srm.dto.contract.ContractExtractionResponse;
import com.xhgj.srm.dto.landingContract.LandingMerchantContractDownloadQuery;
import com.xhgj.srm.dto.oa.OAUserPageDTO;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.dao.LandingMerchantContractDao;
import com.xhgj.srm.jpa.entity.EntryRegistrationDiscount;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.Platform;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.EntryRegistrationDiscountRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationLandingMerchantRepository;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.LandingMerchantContractRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.jpa.repository.PlatformRepository;
import com.xhgj.srm.jpa.repository.SupplierPerformanceRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import com.xhgj.srm.registration.repository.EntryRegistrationRepository;
import com.xhgj.srm.request.dto.ocr.AccurateDTO;
import com.xhgj.srm.request.dto.ocr.GetAccurateParams;
import com.xhgj.srm.request.dto.ocr.WordsResultDTO;
import com.xhgj.srm.request.service.third.xhgj.XhgjOCRRequest;
import com.xhgj.srm.request.utils.AccurateUtils;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhgj.srm.service.OAUserService;
import com.xhgj.srm.service.ShareLandingMerchantContractService;
import com.xhgj.srm.service.ShareSupplierPerformanceService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import static com.xhgj.srm.common.enums.PayTypeSAPEnums.getContractDownloadPayType;

/**
 * Created by Geng Shy on 2023/11/8
 */
@Service
@Slf4j
public class ShareLandingMerchantContractServiceImpl implements
    ShareLandingMerchantContractService {
  @Resource
  private XhgjOCRRequest ocrRequest;
  @Resource
  private EntryRegistrationLandingMerchantRepository entryRegistrationLandingMerchantRepository;
  @Resource
  private PlatformRepository platformRepository;
  @Resource
  private LandingMerchantContractRepository landingMerchantContractRepository;
  @Resource
  private GroupRepository groupRepository;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private Contract contract;
  @Resource
  private OrderRepository orderRepository;
  @Resource
  private ShareSupplierPerformanceService shareSupplierPerformanceService;

  @Autowired
  private EntryRegistrationDiscountRepository entryRegistrationDiscountRepository;
  @Autowired
  private SupplierPerformanceRepository supplierPerformanceRepository;
  @Resource
  private UserRepository userRepository;
  @Resource
  private OAUserService oaUserService;
  @Resource
  private DownloadThenUpUtil downloadThenUpUtil;
  @Resource
  private LandingMerchantContractDao landingMerchantContractDao;
  private static final String DOWNLOAD_THE_CONTRACT_TEMPLATE =
      "srm/model/电商供应商合作框架协议模版V3.2.docx";
  /**
   * 国能平台合同模板
   */
  private static final String GN_DOWNLOAD_THE_CONTRACT_TEMPLATE =
      "srm/model/电商供应商合作框架协议模版V3.2(国能最新).docx";
  /**
   * 国铁平台合同模板
   */
  private static final String GT_DOWNLOAD_THE_CONTRACT_TEMPLATE =
      "srm/model/电商供应商合作框架协议模版V3.2(国铁).docx";
  @Resource
  private EntryRegistrationRepository entryRegistrationRepository;

  @Override
  public BootBaseRepository<LandingMerchantContract, String> getRepository() {
    return landingMerchantContractRepository;
  }
  @Override
  public Map<String, String> handleFileDiscern(String fileUrl) {
    if (!FileUtil.isPermitType(fileUrl, Constants.FILE_TYPE_HTFJ)) {
      throw new CheckException("合同附件类型不支持");
    }
    ContractExtractionResponse extraction;
    List<String> keys = Arrays.stream(LandingMerchantContractKeywordEnum.values())
        .map(LandingMerchantContractKeywordEnum::getKeyword).collect(Collectors.toList());
    try (InputStream finleInputStream = downloadThenUpUtil.getInputStreamFromOSS(fileUrl);) {
      String fileBase64 = FileUtil.toBase64(finleInputStream);
      extraction = contract.extraction(keys, fileBase64, FileUtil.getFileType(fileUrl));
    } catch (Exception e) {
      log.error("合同提取服务异常", e);
      throw new CheckException("合同提取服务异常");
    }
    if (extraction.getResult() == null || CollUtil.isEmpty(extraction.getResult().getItem_list())) {
      throw new CheckException("未识别到合同内容");
    }
    //处理识别出来的信息
    HashMap<String, String> resultMap = extraction.getResult().getItem_list().stream().collect(
        Collectors.toMap(itemList -> LandingMerchantContractKeywordEnum.
                throwIfNotFindByKeyword(itemList.getKey()).getCode(), itemList -> {
              LandingMerchantContractKeywordEnum keywordEnum =
                  LandingMerchantContractKeywordEnum.throwIfNotFindByKeyword(itemList.getKey());
              return LandingMerchantContractKeywordEnum.getAfterProcessing(keywordEnum,
                  itemList.getValue());
            }, (v1, v2) -> v1,
            HashMap::new));
    for (LandingMerchantContractKeywordEnum value : LandingMerchantContractKeywordEnum.values()) {
      //保证金未识别到视为未缴纳
      if (value == LandingMerchantContractKeywordEnum.SMALL_DEPOSIT) {
        continue;
      }
      if (StrUtil.isBlank(resultMap.get(value.getCode()))) {
        throw new CheckException("未识别到合同重要字段：" + value.getName());
      }
    }
    log.info("合同识别到的信息：" + JSON.toJSONString(resultMap));
    String cooperationPeriod = resultMap.get(LandingMerchantContractKeywordEnum.COOPERATION_PERIOD.getCode());
    long cooperationPeriodStartTime = Contract.getCooperationPeriodStartTime(cooperationPeriod);
    long cooperationPeriodEndTime = Contract.getCooperationPeriodEndTime(cooperationPeriod);
    resultMap.put("startTime",
        DateUtil.format(DateUtil.date(cooperationPeriodStartTime), "yyyy-MM-dd"));
    resultMap.put("endTime",
        DateUtil.format(DateUtil.date(cooperationPeriodEndTime), "yyyy-MM-dd"));
    return resultMap;
  }
  @Override
  public void addByEntryRegistrationLandingMerchant(EntryRegistrationEntity entity,
      Supplier supplier) {
    Assert.notNull(entity);
    EntryRegistrationLandingMerchant merchant = entity.getMerchant();
    LandingMerchantContract contract = entity.getContract();
    if (contract == null) {
      contract = new LandingMerchantContract();
    }
    Map<String, String> codeToName =
        validatePlatformCode(entity.getPlatform());
    // 项目缩写 用于生成合同号
    //20250102不校验项目大类了，现在大类是会变动的
    String abbreviation =
        platformRepository.findFirstByProjectCategoryAndState(entity.getProjectCategory(),
            Constants.STATE_OK).map(Platform::getPlatformAbbreviation).orElse(StrUtil.EMPTY);
    LandingMerchantContract landingMerchantContract = saveLandingMerchantContract(entity, contract, abbreviation, merchant);
    // 保存折扣信息
    List<EntryRegistrationDiscount> entryRegistrationDiscountList =
        entryRegistrationDiscountRepository.findByEntryRegistrationOrderIdAndState(
            entity.getId(), Constants.STATE_OK);
    // 折扣信息关联合同
    entryRegistrationDiscountList.forEach(item -> entryRegistrationDiscountRepository.save(
        bulidEntryRegistrationDiscount(item,landingMerchantContract.getId())));
    // 更新履约信息
    relatedContractPerformance(supplier.getId(), contract.getId(), codeToName,merchant,
        entity.getCooperationRegion(),getBusinessLeaderId(entity.getSalesmanId()));
  }

  /**
   * @description: 获取业务负责人id
   * @param: salesmanId
   **/
  public String getBusinessLeaderId(String salesmanId) {
    String businessLeaderId = StrUtil.EMPTY;
    User user = userRepository.findById(salesmanId).orElse(null);
    if (user == null || StrUtil.hasBlank(user.getRealName(), user.getMobile())) {
      return businessLeaderId;
    }
    try {
      OAUserPageDTO oaUserDTO =
          oaUserService.getOAUserByMobileAndName(user.getMobile(), user.getRealName());
      if (oaUserDTO != null) {
        businessLeaderId = oaUserDTO.getOaId();
      }
    } catch (Exception e) {
      log.error("根据报备单的业务员名称：{}查询oa服务数据错误",user.getRealName());
    }
    return businessLeaderId;
  }

  @Override
  @Deprecated
  public void addByEntryRegistrationOrderAndLandingMerchantAndSupplier(
      EntryRegistrationOrder entryRegistrationOrder,
      EntryRegistrationLandingMerchant landingMerchant, Supplier supplier) {
    LandingMerchantContract contract = new LandingMerchantContract();
    Map<String, String> codeToName = validatePlatformCode(entryRegistrationOrder.getPlatform());
    //20250102不校验项目大类了，现在大类是会变动的
    String abbreviation =
        platformRepository.findFirstByProjectCategoryAndState(entryRegistrationOrder.getProjectCategory(),
            Constants.STATE_OK).map(Platform::getPlatformAbbreviation).orElse(StrUtil.EMPTY);
    // 保存合同
    LandingMerchantContract landingMerchantContract =
        saveLandingMerchantContract(entryRegistrationOrder, contract, abbreviation,
            landingMerchant);
    // 保存折扣信息
    List<EntryRegistrationDiscount> entryRegistrationDiscountList =
        entryRegistrationDiscountRepository.findByEntryRegistrationOrderIdAndState(
            entryRegistrationOrder.getId(), Constants.STATE_OK);
    // 折扣信息关联合同
    entryRegistrationDiscountList.forEach(item -> entryRegistrationDiscountRepository.save(
        bulidEntryRegistrationDiscount(item,landingMerchantContract.getId())));
    // 关联履约信息
    relatedContractPerformance(supplier.getId(), contract.getId(), codeToName,landingMerchant,
        entryRegistrationOrder.getCooperationRegion(),null);
  }

  @Override
  @SneakyThrows
  public byte[] downloadTheContract(LandingMerchantContractDownloadQuery params) {
    List<byte[]> contracts = new ArrayList<>();
    List<String> ids = params.getIds();
    for (String id : ids) {
      LandingMerchantContract landingMerchantContract = landingMerchantContractDao.get(id);
      if (landingMerchantContract == null) {
        throw new CheckException("合同不存在！");
      }
      EntryRegistrationEntity entryRegistrationOrder =
          entryRegistrationRepository.byId(landingMerchantContract.getEntryRegistrationOrderId());
      if (entryRegistrationOrder == null) {
        throw new CheckException(
            landingMerchantContract.getContractNo() + "对应的入驻报备单不存在，不可下载！");
      }
      EntryRegistrationLandingMerchant entryRegistrationLandingMerchant =
          entryRegistrationLandingMerchantRepository.findFirstByEntryRegistrationOrderIdAndState(
              entryRegistrationOrder.getId(), Constants.STATE_OK);
      if (Boolean.FALSE.equals(entryRegistrationOrder.isRegistrationApprovedReal())) {
        throw new CheckException(landingMerchantContract.getContractNo() + "对应的入驻报备单"
            + entryRegistrationOrder.getRegistrationNumber() + "未审核通过，不可下载！");
      }
      String contractTemplate = getTemplateByPlatform(entryRegistrationOrder.getOriginPlatformList());
      try(InputStream inputStreamFromOSS = downloadThenUpUtil.getInputStreamFromOSS(contractTemplate);) {
        if (inputStreamFromOSS == null) {
          throw new CheckException("未找到模板，请联系管理员放置模板");
        }
        Map<String, String> fillInTemplateContent = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 1.合同编号
        LandingMerchantContractPut2Pdf put2Pdf = new LandingMerchantContractPut2Pdf();
        put2Pdf.setContractNo(landingMerchantContract.getContractNo());
        // 2.签约时间(创建时间)
        put2Pdf.setSignDate(sdf.format(new Date(landingMerchantContract.getCreateTime())));
        // 3.对方签约主体
        Supplier supplier =
            supplierRepository.findById(landingMerchantContract.getSecondSigningSupplierId())
                .orElse(null);
        if (supplier != null) {
          String enterpriseName = supplier.getEnterpriseName();
          // 乙方（供应商）
          put2Pdf.setEnterpriseName(enterpriseName);
        }
        // 4.户名
        put2Pdf.setAccountName(entryRegistrationLandingMerchant.getAccountName());
        // 5.税号
        put2Pdf.setTaxNo(entryRegistrationLandingMerchant.getUscc());
        // 6.开户行
        put2Pdf.setBankName(entryRegistrationLandingMerchant.getBankName());
        // 7.账号
        put2Pdf.setBankAccount(entryRegistrationLandingMerchant.getBankAccountNumber());
        // 8.地址
        put2Pdf.setBankAddress(entryRegistrationLandingMerchant.getBankAddress());
        // 9.电话
        put2Pdf.setPhone(entryRegistrationLandingMerchant.getPhone());
        // 10.保证金
        put2Pdf.setPerformanceBond(StrUtil.blankToDefault(deleAllZeros(landingMerchantContract.getDeposit()),"/"));
        // 11.人民币 大写
        put2Pdf.setPerformanceBondUpper(StrUtil.blankToDefault(MoneyUtil.toChinese(landingMerchantContract.getDeposit()),"/"));
        // 12.甲方人员
        // 13.甲方职务
        // 14.甲方联系电话
        // 15.甲方邮箱
        // 16.甲方联系地址
        // 17.乙方人员
        put2Pdf.setContactName(entryRegistrationOrder.getCooperationContactName());
        // 18.乙方职务
        put2Pdf.setContactPosition(entryRegistrationOrder.getPosition());
        // 19.乙方联系电话
        put2Pdf.setContactPhone(entryRegistrationOrder.getCooperationContactPhone());
        // 20.乙方邮箱
        if (entryRegistrationOrder.getMerchant() != null) {
          put2Pdf.setContactEmail(entryRegistrationOrder.getMerchant().getEmailAddress());
        }
        // 21.合作联系地址
        put2Pdf.setContactAddress(entryRegistrationOrder.getContactAddress());

        Date effectiveStart = new Date(landingMerchantContract.getEffectiveStart());
        Date effectiveEnd = new Date(landingMerchantContract.getEffectiveEnd());
        // 协议合作期限开始 年
        put2Pdf.setCooperationStartDateYear(Convert.toStr(DateUtil.year(effectiveStart)));
        // 协议合作期限开始 月
        put2Pdf.setCooperationStartDateMonth(Convert.toStr(DateUtil.month(effectiveStart) + 1));
        // 协议合作期限开始 日
        put2Pdf.setCooperationStartDateDay(Convert.toStr(DateUtil.dayOfMonth(effectiveStart)));
        // 协议合作期限结束 年
        put2Pdf.setCooperationEndDateYear(Convert.toStr(DateUtil.year(effectiveEnd)));
        // 协议合作期限结束 月
        put2Pdf.setCooperationEndDateMonth(Convert.toStr(DateUtil.month(effectiveEnd) + 1));
        // 协议合作期限结束 日
        put2Pdf.setCooperationEndDateDay(Convert.toStr(DateUtil.dayOfMonth(effectiveEnd)));
        // 乙方盖章--同对方签约主体
        // 乙方法人代表
        // 乙方授权代表
        // 乙方联系方式
        // 乙方名字
        // 签订时间 年
        put2Pdf.setSignDateYear(Convert.toStr(DateUtil.year(new Date(landingMerchantContract.getCreateTime()))));
        // 签订时间 月
        put2Pdf.setSignDateMonth(Convert.toStr(DateUtil.month(new Date(landingMerchantContract.getCreateTime())) + 1));
        // 签订时间 日
        put2Pdf.setSignDateDay(Convert.toStr(DateUtil.dayOfMonth(new Date(landingMerchantContract.getCreateTime()))));
        // 合同编号
        put2Pdf.setContractNo(landingMerchantContract.getContractNo());
        // 甲方为xx项目
        put2Pdf.setCooperationProject(entryRegistrationOrder.getProjectName());
        // 合作区域
        put2Pdf.setCooperationArea(entryRegistrationOrder.getCooperationRegion());
        // 合作类型
        Optional.ofNullable(EntryRegistrationCooperationTypeEnum.valueOfByKey(
            landingMerchantContract.getTypeOfCooperation())).ifPresent(item -> put2Pdf.setCooperationType(item.getDescription()));
        // 合作产品品类
        put2Pdf.setCooperationCategory(landingMerchantContract.getCooperationBrand());
        // 保底金额
        Optional.ofNullable(landingMerchantContract.getGuaranteedAmount())
            .ifPresent(item -> put2Pdf.setGuaranteedAmount(item.stripTrailingZeros().toPlainString()));
        // 违约金
        if (landingMerchantContract.getPenalty() != null) {
          List<String> split = StrUtil.split(landingMerchantContract.getPenalty(), ',');
          if (!split.isEmpty()) {
            put2Pdf.setPenalty(split.get(0));
          }
          if (split.size() > 1) {
            put2Pdf.setPenaltyPercent(split.get(1));
          }
        }
        // 商品结算价格 合作比例 1 - 折扣比例
        List<EntryRegistrationDiscount> entryRegistrationDiscountList =
            entryRegistrationDiscountRepository.findByLandingContractIdAndState(landingMerchantContract.getId(),
                Constants.STATE_OK);
        BigDecimal initialDiscountRatio = entryRegistrationDiscountList.stream()
            .filter(entryDiscount -> StrUtil.equals(EntryRegistrationDiscountTypeEnum.STEP_DISCOUNT_RATIO.getKey(), entryDiscount.getType())
                && NumberUtil.equals(BigDecimal.ZERO, BigDecimalUtil.formatForStandard(entryDiscount.getPerformanceAmount())))
            .map(EntryRegistrationDiscount::getDiscountRatio)
            .findFirst()
            .orElse(BigDecimal.ZERO);

        put2Pdf.setCooperationRatio(
            new BigDecimal(100).subtract(initialDiscountRatio).setScale(2, RoundingMode.HALF_UP)
                .toString());
        //有多少条数据，就有多少条履约金额和折扣比例（按照履约金额正序排列），最大5条，超过5条只取前5条
        //        handleAmountAndDiscount(entryRegistrationDiscountList, put2Pdf);

        List<EntryRegistrationDiscount> allRegistrationDiscountList =
            entryRegistrationDiscountList.stream()
                .filter(entryDiscount -> StrUtil.equals(
                    EntryRegistrationDiscountTypeEnum.STEP_DISCOUNT_RATIO.getKey(),
                    entryDiscount.getType()))
                .sorted(Comparator.comparing(EntryRegistrationDiscount::getPerformanceAmount,
                    Comparator.nullsFirst(BigDecimal::compareTo)))
                .skip(1)  // 跳过第一条记录
                .collect(Collectors.toList());

        // 其他备注
        put2Pdf.setOtherRemark(entryRegistrationOrder.getOtherRemarks());
        // 对账天数
        put2Pdf.setAccountingPeriod(Convert.toStr(landingMerchantContract.getAccountingPeriod()));
        // 如果是背对背，默认为15日
        if (Boolean.TRUE.equals(landingMerchantContract.getBackToBack())) {
          put2Pdf.setAccountingPeriod(LandingMerchantContractPut2Pdf.DEFAULT_ACCOUNTING_PERIOD);
        }
        // 开票税率
        put2Pdf.setTaxRate(
            Optional.ofNullable(landingMerchantContract.getTaxRate())
                .map(taxRate -> taxRate.setScale(2, RoundingMode.HALF_UP).toString())
                .orElse("")
        );
        // 开票类型
        Optional.ofNullable(PurchaseOrderInvoiceType.fromKey(landingMerchantContract.getInvoiceType()))
            .ifPresent(item -> put2Pdf.setInvoiceType(item.getDescription()));
        // 约定x个月
        put2Pdf.setPaymentMethodMonth(landingMerchantContract.getPaymentTypeInput());
        // 的x方式
        put2Pdf.setPaymentMethod(PayTypeSAPEnums.getNameByCode(landingMerchantContract.getPaymentType()));
        // 乙方支付x%的贷款
        put2Pdf.setPaymentRatio(landingMerchantContract.getPaymentRatio());
        // 付款方式拼接长文--背靠背
        boolean isBackToBack = Boolean.TRUE.equals(landingMerchantContract.getBackToBack());
        if (isBackToBack) {
          put2Pdf.setPaymentMethodDesc(LandingMerchantContractPut2Pdf.DEFAULT_PAYMENT_METHOD_DESC);
        } else {
          //甲方已就该笔订单向甲方终端客户开具完毕整单发票，并且该笔订单在平台（网址：  ）的订单状态显示为“已经妥投完成”。
          String platformCode = supplierPerformanceRepository.findAllByLandingContractIdAndState(
                  landingMerchantContract.getId(), Constants.STATE_OK).stream()
              .map(SupplierPerformance::getPlatformCode).findFirst().orElse(StrUtil.EMPTY);
          put2Pdf.setPaymentMethodDesc(String.format(
              LandingMerchantContractPut2Pdf.DEFAULT_PAYMENT_METHOD_DESC_NOT_BACK_TO_BACK_URL,
              Constants.PLATFORM_CODE_AND_URL_MAP.getOrDefault(platformCode, "/")));
          put2Pdf.setNotBackToBack(String.format(
              LandingMerchantContractPut2Pdf.DEFAULT_PAYMENT_METHOD_DESC_NOT_BACK_TO_BACK,
              getContractDownloadPayType(landingMerchantContract.getPaymentType(), landingMerchantContract.getPaymentTypeInput()),
              landingMerchantContract.getPaymentRatio()));
        }
        // 反射获取属性名，并通过fillInTemplateContent.put
        Field[] fields = ReflectUtil.getFields(put2Pdf.getClass());
        for (Field field : fields) {
          field.setAccessible(true);
          String fieldName = field.getName();
          String value = Convert.toStr(ReflectUtil.getFieldValue(put2Pdf, fieldName));
          // ${}
          fillInTemplateContent.put(String.format("${%s}", fieldName), StrUtil.emptyIfNull(value));
        }
        try (XWPFDocument doc = new XWPFDocument(inputStreamFromOSS);) {
          // 替换段落里面的变量
          WordPoiUtils.replaceInParaRef(doc, fillInTemplateContent);
          // 替换表格里面的变量
          WordPoiUtils.replaceInTableRef(doc, fillInTemplateContent);
          //根据折扣列表更新或删除Word文档中的指定表格
          updateOrRemoveTable(doc, allRegistrationDiscountList, initialDiscountRatio);
          ByteArrayOutputStream out = new ByteArrayOutputStream();
          doc.write(out);
          ByteArrayInputStream inputStream = new ByteArrayInputStream(out.toByteArray());
          byte[] bytesOfPdf;
          try {
            bytesOfPdf = FileUtil.docConvertPdf(inputStream);
          } catch (Exception e) {
            log.error("生成pdf文件异常", e);
            throw new CheckException("生成pdf文件异常！");
          }
          contracts.add(bytesOfPdf);
        }
      }
    }
    return downloadZipMoreFile(contracts);
  }

  private String getTemplateByPlatform(List<String> platformCodes) {
    if (CollUtil.contains(platformCodes, Constants_order.ORDER_PLATFORM_GTSC_CODE)) {
      return GT_DOWNLOAD_THE_CONTRACT_TEMPLATE;
    }
    if (CollUtil.contains(platformCodes, Constants_order.ORDER_PLATFORM_GN)) {
      return GN_DOWNLOAD_THE_CONTRACT_TEMPLATE;
    }
    return DOWNLOAD_THE_CONTRACT_TEMPLATE;
  }

  /**
   * 根据折扣列表更新或删除Word文档中的指定表格。
   *
   * @param doc 文档对象
   * @param allRegistrationDiscountList 折扣列表
   * @param initialDiscountRatio 初始折扣率
   */
  public void updateOrRemoveTable(XWPFDocument doc, List<EntryRegistrationDiscount> allRegistrationDiscountList, BigDecimal initialDiscountRatio) {
    if (CollUtil.isEmpty(allRegistrationDiscountList)) {
      // 如果allRegistrationDiscountList为空，则尝试找到并删除包含"完成金额"的表格
      Optional<XWPFTable> targetTableOpt = doc.getTables().stream()
          .filter(table -> table.getRow(0).getCell(0).getText().trim().contains("完成金额"))
          .findFirst();
      targetTableOpt.ifPresent(targetTable -> doc.removeBodyElement(doc.getPosOfTable(targetTable)));
    } else {
      XWPFTable table = null;
      boolean tableExists = doc.getTables().stream().anyMatch(tbl -> tbl.getRow(0).getCell(0).getText().trim().contains("完成金额"));
      if (tableExists) {
        table = doc.getTables().stream()
            .filter(tbl -> tbl.getRow(0).getCell(0).getText().trim().contains("完成金额"))
            .findFirst().orElseThrow(() -> new RuntimeException("未找到预期的折扣比例表格"));
      }
      while (table.getRows().size() > 1) { // 保留标题行
        table.removeRow(1);
      }
      int size = allRegistrationDiscountList.size();
      for (int i = 0; i < size; i++) {
        EntryRegistrationDiscount discount = allRegistrationDiscountList.get(i);
        XWPFTableRow row = table.createRow(); // 使用createRow保证顺序正确
        StringBuilder amountRange = new StringBuilder();
        StringBuilder priceDescription = new StringBuilder("在此范围内的订单按照甲方与甲方指定收货人商品结算价格*");
        if (size == 1) {
          // 单一记录的处理
          amountRange.append("大于0元, 小于等于")
              .append(NumberUtil.toBigDecimal(discount.getPerformanceAmount())
                  .setScale(2, RoundingMode.HALF_UP)).append("元");
          priceDescription.append(NumberUtil.sub(new BigDecimal(100),
                  NumberUtil.toBigDecimal(initialDiscountRatio))
              .setScale(2, RoundingMode.HALF_UP)).append(" %");

          // 添加最后一行表示无上限
          XWPFTableRow lastRow = table.createRow();
          StringBuilder lastAmountRange = new StringBuilder();
          StringBuilder lastPriceDescription = new StringBuilder("在此范围内的订单按照甲方与甲方指定收货人商品结算价格*");

          lastAmountRange.append("大于").append(NumberUtil.toBigDecimal(discount.getPerformanceAmount())
              .setScale(2, RoundingMode.HALF_UP)).append("元");
          lastPriceDescription.append(NumberUtil.sub(new BigDecimal(100),
                  NumberUtil.toBigDecimal(discount.getDiscountRatio()))
              .setScale(2, RoundingMode.HALF_UP)).append(" %");
          lastRow.getCell(0).setText(lastAmountRange.toString());
          lastRow.getCell(1).setText(lastPriceDescription.toString());
        } else if (i == 0) {
          // 第一条记录特殊处理，从0开始到第一个y值
          amountRange.append("大于0元, 小于等于")
              .append(NumberUtil.toBigDecimal(discount.getPerformanceAmount())
                  .setScale(2, RoundingMode.HALF_UP)).append("元");
          priceDescription.append(NumberUtil.sub(new BigDecimal(100),
                  NumberUtil.toBigDecimal(initialDiscountRatio))
              .setScale(2, RoundingMode.HALF_UP)).append(" %");
        } else if (i == size - 1) {
          // 最后一条记录，补齐倒数第二条数据
          amountRange.append("大于").append(NumberUtil.toBigDecimal(
                  allRegistrationDiscountList.get(i - 1).getPerformanceAmount())
              .setScale(2, RoundingMode.HALF_UP)).append("元, 小于等于").append(
              NumberUtil.toBigDecimal(
                      allRegistrationDiscountList.get(i).getPerformanceAmount())
                  .setScale(2, RoundingMode.HALF_UP)).append("元");
          priceDescription.append(NumberUtil.sub(new BigDecimal(100), NumberUtil.toBigDecimal(
                  allRegistrationDiscountList.get(i - 1).getDiscountRatio()))
              .setScale(2, RoundingMode.HALF_UP)).append(" %");

          XWPFTableRow lastRow = table.createRow();
          StringBuilder lastAmountRange = new StringBuilder();
          StringBuilder lastPriceDescription = new StringBuilder("在此范围内的订单按照甲方与甲方指定收货人商品结算价格*");

          // 最后一条记录，没有y值，即无上限
          lastAmountRange.append("大于").append(NumberUtil.toBigDecimal(
                  allRegistrationDiscountList.get(size - 1).getPerformanceAmount())
              .setScale(2, RoundingMode.HALF_UP)).append("元");
          lastPriceDescription.append(NumberUtil.sub(new BigDecimal(100),
                  NumberUtil.toBigDecimal(discount.getDiscountRatio()))
              .setScale(2, RoundingMode.HALF_UP)).append(" %");
          lastRow.getCell(0).setText(lastAmountRange.toString());
          lastRow.getCell(1).setText(lastPriceDescription.toString());
        } else {
          // 中间记录
          amountRange.append("大于").append(NumberUtil.toBigDecimal(
                  allRegistrationDiscountList.get(i - 1).getPerformanceAmount())
              .setScale(2, RoundingMode.HALF_UP)).append("元, 小于等于").append(
              NumberUtil.toBigDecimal(
                      allRegistrationDiscountList.get(i).getPerformanceAmount())
                  .setScale(2, RoundingMode.HALF_UP)).append("元");
          priceDescription.append(NumberUtil.sub(new BigDecimal(100), NumberUtil.toBigDecimal(
                  allRegistrationDiscountList.get(i - 1).getDiscountRatio()))
              .setScale(2, RoundingMode.HALF_UP)).append(" %");
        }
        row.getCell(0).setText(amountRange.toString());
        row.getCell(1).setText(priceDescription.toString());

      }
    }
  }

  private String deleAllZeros(BigDecimal number) {
    if (number == null || number.compareTo(BigDecimal.ZERO) == 0) {
      return "";
    } else {
      String numberStr = String.valueOf(number);
      if (StrUtil.isNotBlank(numberStr)) {
        String decimalPart =
            numberStr.split("\\.").length > 1 ? numberStr.split("\\.")[1] : ""; // 小数部分
        if (decimalPart.chars().allMatch(ch -> ch == '0')) {
          return numberStr.split("\\.")[0]; // 整数部分
        }
        return numberStr;
      }
      return "";
    }
  }

  @SneakyThrows
  public byte[] downloadZipMoreFile(List<byte[]> contractList) {
    if (CollUtil.isEmpty(contractList)) {
      return new byte[] {};
    }
    Map<String, byte[]> contracNameMapByte = new HashMap<>();
    for (byte[] contract : contractList) {
      contracNameMapByte.put("下载合同" + UUID.randomUUID() + ".pdf", contract);
    }
    return ZIPUtils.batchFileToZIP(contracNameMapByte);
  }

  /**
   * 保存电商供应商合同信息
   *
   * @param entryRegistrationOrder 报备单
   * @param contract 合同
   * @param platformAbbreviation 项目缩写
   * @param entryRegistrationLandingMerchant 报备单关联的电商供应商信息
   */
  private LandingMerchantContract saveLandingMerchantContract(EntryRegistrationOrder entryRegistrationOrder,
      LandingMerchantContract contract, String platformAbbreviation,
      EntryRegistrationLandingMerchant entryRegistrationLandingMerchant) {
    contract.setContractNo(generateContractNumber(platformAbbreviation));
    contract.setCreateTime(System.currentTimeMillis());
    contract.setCreateMan(entryRegistrationLandingMerchant.getCreateMan());
    contract.setAssociationStatus(Constants.CONTRACT_ASSOCIATION_PERFORMANCE_STATUS_NO);
    contract.setSignatureStatus(Constants.SIGNATURE_STATUS_NO);
    contract.setState(Constants.STATE_OK);
    contract.setPaymentType(entryRegistrationOrder.getPaymentType());
    Group group =
        groupRepository.findFirstByCodeAndState(Constants.HEADQUARTERS_CODE, Constants.STATE_OK);
    contract.setFirstSigningGroupId(group.getId());
    Supplier supplier = supplierRepository.findFirstByEnterpriseNameAndState(
        entryRegistrationLandingMerchant.getEnterpriseName(), Constants.STATE_OK).orElseThrow(
        () -> new CheckException("数据异常，准入新增合同时未查询到此供应商信息，新增失败！"));
    contract.setSecondSigningSupplierId(supplier.getId());
    contract.setEffectiveStart(entryRegistrationOrder.getCooperationStartTime());
    contract.setEffectiveEnd(entryRegistrationOrder.getCooperationEndTime());
    // 签订方式 - 此版本全为纸质合同
    contract.setSigningType(Constants.PAPER_CONTRACT);
    // 合同类型 - 此版本全部为框架合同
    contract.setType(Constants.FRAME_CONTRACT);
    contract.setAccountingPeriod(entryRegistrationOrder.getAccountPeriod());
    contract.setBackToBack(entryRegistrationOrder.getBackToBack());
    contract.setPaymentRatio(StrUtil.blankToDefault(entryRegistrationOrder.getPaymentRatio(),"100"));
    contract.setPaymentCondition(entryRegistrationOrder.getPaymentTerms());
    // 合同来源默认为SRM新增。
    contract.setSourceType(Constants.CONTRACT_SOURCE_SRM);
    contract.setEntryRegistrationOrderId(entryRegistrationOrder.getId());
    contract.setDeposit(entryRegistrationOrder.getDeposit());
    contract.setTypeOfCooperation(entryRegistrationOrder.getTypeOfCooperation());
    contract.setCooperationRRegion(entryRegistrationOrder.getCooperationRegion());
    contract.setCooperationBrand(entryRegistrationOrder.getCooperationBrand());
    contract.setDepositState(false);
    //    contract.setFileReviewState(FileReviewStateEnum.UPLOAD.getKey());
    contract.setContractStatus(ContractStatus.VOID.getCode());
    // 6.5.1版本去除
//    contract.setRegularSupply(entryRegistrationOrder.getRegularSupply());
    contract.setStorage(
        StrUtil.emptyToDefault(entryRegistrationOrder.getStorage(), Constants.STATE_NO));
    contract.setStorageAddress(entryRegistrationOrder.getStorageAddress());
    contract.setStorageArea(entryRegistrationOrder.getStorageArea());
    contract.setGuaranteedAmount(entryRegistrationOrder.getGuaranteedAmount());
    contract.setPenalty(entryRegistrationOrder.getPenalty());
    contract.setInvoiceType(entryRegistrationLandingMerchant.getInvoiceType());
    contract.setTaxRate(entryRegistrationLandingMerchant.getTaxRate());
    contract.setNeedBundle(false);
    contract.setPaymentTypeInput(entryRegistrationOrder.getPaymentTypeInput());
    contract.setProjectCategory(entryRegistrationOrder.getProjectCategory());
    contract.setProjectName(entryRegistrationOrder.getProjectName());

    //新增合同的时候根据下单平台和供应商去找订单中付款条件为空的数据
    savePaymentConditionInfo(entryRegistrationOrder, contract);
    return landingMerchantContractRepository.save(contract);

  }

  public EntryRegistrationDiscount bulidEntryRegistrationDiscount(
      EntryRegistrationDiscount oldEntity, String landingContractId) {
    EntryRegistrationDiscount entity = MapStructFactory.INSTANCE.toEntryRegistrationDiscount(oldEntity);
    entity.setCreateTime(System.currentTimeMillis());
    entity.setUpdateTime(System.currentTimeMillis());
    entity.setLandingContractId(landingContractId);
    entity.setId(null);
    entity.setEntryRegistrationOrderId(null);
    return entity;
  }

  private void deleteEntryRegistrationDiscount(String contractId) {
    List<EntryRegistrationDiscount> entryRegistrationDiscounts =
        entryRegistrationDiscountRepository.findByLandingContractIdAndState(
            contractId, Constants.STATE_OK);
    if (CollUtil.isEmpty(entryRegistrationDiscounts)) {
      return;
    }
    for (EntryRegistrationDiscount entryRegistrationDiscount : entryRegistrationDiscounts) {
      entryRegistrationDiscount.setState(Constants.STATE_DELETE);
      entryRegistrationDiscountRepository.save(entryRegistrationDiscount);
    }
  }
  /**
   * 根据下单平台、供应商、合同关联履约信息
   * @param supplierId 供应商id
   * @param landingContractId 合同id
   * @param codeToName 下单平台 平台编码-名称
   */
  private void relatedContractPerformance(String supplierId, String landingContractId, Map<String
      , String> codeToName,EntryRegistrationLandingMerchant merchant,String cooperationRegion,
      String businessLeaderId) {
    AtomicReference<User> defaultPurchase = new AtomicReference<>(
        userRepository.findFirstByRealNameAndState(Constants.USER_NAME_GUO_JIA_LEI,
            Constants.STATE_OK));
    // 为每一个codeToName生成一条履约信息.存在则更新，不存在则新增
    codeToName.forEach((code, name) -> {
      // 同一供应商同一平台其他合同状态变成失效
      List<LandingMerchantContract> landingMerchantContracts =
          landingMerchantContractDao.findBySecSupplierAndPlatformCode(supplierId, code);
      landingMerchantContracts.forEach(item -> {
        if (!item.getId().equals(landingContractId)) {
          item.setContractStatus(ContractStatus.INVALID.getCode());
          item.setUpdateMan(merchant.getCreateMan());
          item.setUpdateTime(System.currentTimeMillis());
          landingMerchantContractRepository.save(item);
        }
      });
      // 对接采购 初始取值平台管理中的默认采购
      Platform platform = platformRepository.findFirstByCodeAndState(code, Constants.STATE_OK)
          .orElseThrow(() -> new CheckException("平台信息不存在"));
      String defaultPurchaseId = Optional.ofNullable(platform.getDefaultPurchase()).orElse("-1");
      userRepository.findById(defaultPurchaseId).ifPresent(defaultPurchase::set);
      SupplierPerformance existPerformance =
          supplierPerformanceRepository.findFirstByPlatformCodeAndSupplierIdAndState(code, supplierId, Constants.STATE_OK);
      if (ObjectUtil.isNull(existPerformance)) {
        existPerformance  = new SupplierPerformance();
        // 新增时赋默认采购
        existPerformance.setDockingPurchaseErpCode(defaultPurchase.get().getCode());
      }
      existPerformance.setSupplierId(supplierId);
      existPerformance.setState(Constants.STATE_OK);
      existPerformance.setCreateTime(System.currentTimeMillis());
      existPerformance.setStatus(Constants.SUPPLIER_PERFORMANCE_STATUS_EFFECT);
      existPerformance.setPlatformCode(code);
      existPerformance.setPlatformName(name);
      existPerformance.setUpdateStateTime(System.currentTimeMillis());
      // 根据合同id可找更多信息
      existPerformance.setLandingContractId(landingContractId);
      existPerformance.setUpdateTime(System.currentTimeMillis());
      existPerformance.setContacts(merchant.getAccountUser());
      existPerformance.setMobile(merchant.getAccountUserPhone());
      existPerformance.setArea(cooperationRegion);
      if (StrUtil.isNotBlank(businessLeaderId)) {
        existPerformance.setBusinessLeader(businessLeaderId);
      }
      shareSupplierPerformanceService.save(existPerformance);
    });
  }

  /**
   * 校验平台编码，并返回code - name
   * @param platformCodes
   * @return
   */
  private Map<String,String> validatePlatformCode(String platformCodes) {
    //下单平台code,可多条数据,逗号隔开
    Map<String,String> map = new HashMap<>();
    List<String> platformCodeList = StrUtil.splitTrim(platformCodes, StrUtil.COMMA);
    if (CollUtil.isEmpty(platformCodeList)) {
      throw new CheckException("平台信息不存在");
    }
    for (String platformCode : platformCodeList) {
      Platform platform =
          platformRepository.findFirstByCodeAndState(platformCode, Constants.STATE_OK)
              .orElseThrow(() -> new CheckException("平台信息不存在"));
      map.put(platformCode,platform.getName());
    }
    return map;
  }

  /* 保存付款发起条件，账期信息 **/
  private void savePaymentConditionInfo(EntryRegistrationOrder entryRegistrationOrder,
      LandingMerchantContract contract) {
    List<String> platformCodeList = StrUtil.splitTrim(entryRegistrationOrder.getPlatform(), StrUtil.COMMA);
    for (String platformCode : platformCodeList) {
      Optional.ofNullable(
          entryRegistrationLandingMerchantRepository.findFirstByEntryRegistrationOrderIdAndState(
              entryRegistrationOrder.getId(), Constants.STATE_OK)).ifPresent(landingMerchant -> {
        String supplierId = landingMerchant.getSupplierId();
        if (supplierId != null) {
          List<Order> orderList =
              orderRepository.getAllByTypeAndSupplierIdAndStateAndPaymentConditionIsNull(platformCode,
                  supplierId, Constants.STATE_OK);
          orderList.forEach(item -> {
            item.setAccountingPeriod(contract.getAccountingPeriod());
            item.setBackToBack(contract.getBackToBack());
            item.setPaymentCondition(contract.getPaymentCondition());
            //1.货物验收->签收凭证通过时间
            //2.对方开票->供应商开票时间
            //3.客户回款->客户回款日期
            //付款条件满足时间:付款发起条件对应的时间中最晚的时间，如果有一个付款条件的时间为空，满足日期展示“-”
            Long latestPaymentTriggerTime =
                com.xhgj.srm.common.utils.DateUtil.getLatestPaymentTriggerTime(contract.getPaymentCondition(),
                    item.getConfirmVoucherTime(),
                    item.getConfirmAccountOpenInvoiceTime(), item.getCustomerReturnSignTime());
            item.setPaymentConditionTime(latestPaymentTriggerTime);
            //付款条件满足日期+账期-7天（PS：背靠背视为10天），如果计算后的时间早于今天，就自动取今天。如果以上三个字段有空，这里就是空的
            //预计付款时间
            if (latestPaymentTriggerTime != null) {
              Long predictPaymentTime =
                  com.xhgj.srm.common.utils.DateUtil.getPredictPaymentTimeByCondition(latestPaymentTriggerTime,
                      contract.getBackToBack(), contract.getAccountingPeriod());
              item.setPredictPaymentTime(predictPaymentTime);
            }
            orderRepository.save(item);
          });
        }
      });
    }
  }

  private String generateContractNumber(String platformAbbreviations) {
    String prefix = "XH-" + platformAbbreviations + "-";
    String toDay = DateUtil.format(new Date(), "yyyyMMdd");
    String firstNumber = "001";
    List<LandingMerchantContract> contracts =
        landingMerchantContractRepository.findAllByStateAndContractNoLikeOrderByCreateTimeDesc(Constants.STATE_OK,
            "%" + platformAbbreviations + "-" + toDay + "%");
    if (CollUtil.isEmpty(contracts)) {
      return prefix + toDay + firstNumber;
    }
    String contractNo = contracts.get(0).getContractNo();
    int serialNumber =
        Integer.parseInt(StrUtil.sub(contractNo, contractNo.length() - 3, contractNo.length()));
    return prefix + toDay + StrUtil.fillBefore(Integer.toString(++serialNumber), '0', 3);
  }

  private List<WordsResultDTO> handleFileOcrTransform(String url) throws IOException {
    if (ObjectUtils.isEmpty(url)) {
      return null;
    }
    String FILE_TYPE_PDF = "PDF";
    String fileType = FileUtil.getFileType(url);
    GetAccurateParams accurateParams = new GetAccurateParams();
    try (InputStream inputStreamFromOSS = downloadThenUpUtil.getInputStreamFromOSS(url);) {
      String accurateParamsPart = AccurateUtils.buildImageOrPdfParams(inputStreamFromOSS);
      int START_PAGE = 1;
      if (FILE_TYPE_PDF.equalsIgnoreCase(fileType)) {
        //pdf文件
        accurateParams.setPdf(accurateParamsPart);
        accurateParams.setPdfFileNum(START_PAGE);
        AccurateDTO accurate = ocrRequest.getAccurate(accurateParams);
        List<WordsResultDTO> wordsResultDTOS = new ArrayList<>(accurate.getWordsResultListDto());
        String pdfFileSize = accurate.getPdfFileSize();
        if (!ObjectUtils.isEmpty(pdfFileSize) && Integer.parseInt(pdfFileSize) > 1) {
          int totalPages = Integer.parseInt(pdfFileSize);
          for (int i = START_PAGE + 1; i <= totalPages; i++) {
            accurateParams.setPdfFileNum(i);
            AccurateDTO currAccurate = ocrRequest.getAccurate(accurateParams);
            if (ObjectUtils.isEmpty(currAccurate)) {
              throw new RuntimeException("OCR服务异常");
            }
            wordsResultDTOS.addAll(currAccurate.getWordsResultListDto());
          }
        }
        return wordsResultDTOS;
      } else {
        //非pdf文件就是图片文件
        accurateParams.setImage(accurateParamsPart);
      }
    }
    AccurateDTO accurate = ocrRequest.getAccurate(accurateParams);
    if (ObjectUtils.isEmpty(accurate)) {
      throw new RuntimeException("OCR服务异常");
    }
    return accurate.getWordsResultListDto();
  }

}
