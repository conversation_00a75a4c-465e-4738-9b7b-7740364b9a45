package com.xhgj.srm.service.impl;

import static java.util.stream.Collectors.toList;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.enums.OrderPaymentTypeEnum;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.TitleOfTheContractEnum;
import com.xhgj.srm.common.enums.VoucherPaymentStateEnum;
import com.xhgj.srm.common.enums.order.OrderPaymentSource;
import com.xhgj.srm.common.enums.order.OrderReceiptPaymentMethodEnum;
import com.xhgj.srm.common.event.SrmEventPublisher;
import com.xhgj.srm.common.event.newEvent.OderPaymentReLockEvent;
import com.xhgj.srm.common.event.newEvent.OrderPaymentAbandonEvent;
import com.xhgj.srm.common.event.newEvent.OrderPaymentDoneEvent;
import com.xhgj.srm.common.utils.PaymentRefundNumUtil;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.common.component.LockUtils;
import com.xhgj.srm.dto.order.CreatePaymentOrderResult;
import com.xhgj.srm.dto.order.OrderInfoDTO;
import com.xhgj.srm.dto.order.OrderPaymentCollectionVO;
import com.xhgj.srm.dto.order.OrderPaymentInfoDTO;
import com.xhgj.srm.dto.order.OrderPaymentListDTO;
import com.xhgj.srm.dto.order.OrderPaymentListQuery;
import com.xhgj.srm.dto.order.OrderPaymentOrderInfoDTO;
import com.xhgj.srm.dto.order.OrderPaymentRefundDTO;
import com.xhgj.srm.dto.order.ProductInfoDTO;
import com.xhgj.srm.dto.order.SubmitPaymentOrderApiParams;
import com.xhgj.srm.dto.order.SubmitPaymentOrderParams;
import com.xhgj.srm.dto.order.SubmitPaymentOrderParams.SubmitOrderItem;
import com.xhgj.srm.dto.order.SubmitPaymentRefundParams;
import com.xhgj.srm.dto.order.SubmitPaymentVoucherParams;
import com.xhgj.srm.dto.order.UpdatePaymentVoucherParams;
import com.xhgj.srm.dto.order.needPayment.NeedPaymentPreCheckForm;
import com.xhgj.srm.dto.order.needPayment.NeedPaymentPreCheckForm.NeedPaymentForm;
import com.xhgj.srm.factory.OrderPaymentFactory;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.dao.OrderPaymentToOrderDao;
import com.xhgj.srm.jpa.dao.OrderSupplierInvoiceDao;
import com.xhgj.srm.jpa.dao.SupplierPerformanceDao;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.dto.order.OrderPaymentStatistics;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderAccountToOrder;
import com.xhgj.srm.jpa.entity.OrderNeedPayment;
import com.xhgj.srm.jpa.entity.OrderPartialPayment;
import com.xhgj.srm.jpa.entity.OrderPayment;
import com.xhgj.srm.jpa.entity.OrderPaymentCollection;
import com.xhgj.srm.jpa.entity.OrderPaymentToOrder;
import com.xhgj.srm.jpa.entity.OrderPaymentToOrderLink;
import com.xhgj.srm.jpa.entity.OrderReceiptRecord;
import com.xhgj.srm.jpa.entity.OrderRefundCollection;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.OrderAccountToOrderRepository;
import com.xhgj.srm.jpa.repository.OrderInvoiceRelationRepository;
import com.xhgj.srm.jpa.repository.OrderNeedPaymentRepository;
import com.xhgj.srm.jpa.repository.OrderPartialPaymentRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentCollectionRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentDao;
import com.xhgj.srm.jpa.repository.OrderPaymentRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentToOrderLinkRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentToOrderRepository;
import com.xhgj.srm.jpa.repository.OrderReceiptRecordRepository;
import com.xhgj.srm.jpa.repository.OrderRefundCollectionRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.request.service.third.erp.sap.SapAdvanceApplyRequest;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyParam.DATADTO.HEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapPayStateParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapPayStateParam.ITEMDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapPayStateResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapPayStateResult.RETURNDTO;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.service.OrderAcceptService;
import com.xhgj.srm.service.OrderNeedPaymentService;
import com.xhgj.srm.service.OrderPaymentService;
import com.xhgj.srm.service.OrderPaymentToOrderService;
import com.xhgj.srm.service.SharePlatformService;
import com.xhgj.srm.service.TempOrderService;
import com.xhgj.srm.service.TempSearchSchemeService;
import com.xhgj.srm.service.TempSupplierUserService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * <AUTHOR>
 * @since 2023-03-16 15:08
 */
@Service
@Slf4j
public class OrderPaymentServiceImpl implements OrderPaymentService {

  @Autowired private OrderPaymentRepository repository;
  @Autowired private OrderPaymentDao dao;
  @Autowired private TempSupplierUserService supplierUserService;
  @Autowired private TempSearchSchemeService searchSchemeService;
  @Autowired private OrderPaymentToOrderService orderPaymentToOrderService;
  @Autowired private TempOrderDetailServiceImpl orderDetailService;
  @Autowired private TempOrderService orderService;
  @Autowired private DingUtils dingUtils;
  @Autowired private SrmConfig srmConfig;
  @Autowired private UserRepository userRepository;
  @Autowired private OrderAccountToOrderRepository orderAccountToOrderRepository;
  @Autowired private OrderSupplierInvoiceDao orderAccountInvoiceDao;
  @Autowired private OrderRepository orderRepository;
  @Resource private OrderPaymentToOrderRepository orderPaymentToOrderRepository;
  @Resource private SupplierPerformanceDao supplierPerformanceDao;
  @Resource private UserDao userDao;
  @Resource private OrderAcceptService orderAcceptService;
  @Resource
  private SharePlatformService platformService;
  @Resource
  private OrderPaymentToOrderDao orderPaymentToOrderDao;
  @Resource
  private OrderPaymentCollectionRepository orderPaymentCollectionRepository;
  @Resource
  private OrderPaymentRepository orderPaymentRepository;
  @Resource
  private SapAdvanceApplyRequest sapAdvanceApplyRequest;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private OrderInvoiceRelationRepository orderInvoiceRelationRepository;
  @Resource
  private SAPService sapService;
  @Resource
  private OrderRefundCollectionRepository orderRefundCollectionRepository;
  @Resource
  private OrderPartialPaymentRepository orderPartialPaymentRepository;
  @Resource
  OrderDao orderDao;
  public static final String LOCK_FOR_ORDER_NEED_PAYMENT = "srm:lock_for_order_need_payment"
      + ":{}";
  @Resource
  LockUtils lockUtils;
  @Resource
  OrderPaymentFactory orderPaymentFactory;
  @Resource
  OrderPaymentToOrderLinkRepository orderPaymentToOrderLinkRepository;
  @Resource
  private OrderNeedPaymentService orderNeedPaymentService;
  @Resource
  private OrderNeedPaymentRepository orderNeedPaymentRepository;
  @Resource
  private OrderReceiptRecordRepository orderReceiptRecordRepository;
  @Resource
  SrmEventPublisher srmEventPublisher;
  @Resource
  private PlatformTransactionManager transactionManager;

  @Override
  public BootBaseRepository<OrderPayment, String> getRepository() {
    return repository;
  }


  @Override
  public Page<OrderPaymentListDTO> getOrderPaymentPage(
      OrderPaymentListQuery query, Pageable pageable) {
    String userId = query.getUserId();
    User user = userRepository.findById(userId).orElse(null);
    String supplierId = "";
    if (user == null) {
      SupplierUser supplierUser =
          supplierUserService.get(
              userId, () -> CheckException.noFindException(SupplierUser.class, userId));
      supplierId = supplierUser.getSupplierId();
    }
    String schemeId = query.getSchemeId();
    if (StrUtil.isBlank(schemeId)) {
      SearchScheme search =
          searchSchemeService.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_ORDER_PAYMENT);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    long startTime = 0, endTime = 0;
    String paymentStartTime = query.getPaymentRequestStartTime();
    String paymentEndTime = query.getPaymentRequestEndTime();
    if (StrUtil.isNotEmpty(paymentStartTime)) {
      startTime = DateUtil.parse(paymentStartTime, "yyyy-MM-dd").getTime();
    }
    if (StrUtil.isNotEmpty(paymentEndTime)) {
      long aDay = 60 * 60 * 24 * 1000;
      endTime = DateUtil.parse(paymentEndTime, "yyyy-MM-dd").getTime() + aDay;
    }
    if (StrUtil.isNotEmpty(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        OrderPaymentListQuery orderPaymentListQuery =
            JSON.parseObject(search.getContent(), new TypeReference<OrderPaymentListQuery>() {});
        if (orderPaymentListQuery != null) {
          query.setPaymentNo(
              StrUtil.blankToDefault(query.getPaymentNo(), orderPaymentListQuery.getPaymentNo()));
          query.setPaymentStatus(
              StrUtil.blankToDefault(
                  query.getPaymentStatus(), orderPaymentListQuery.getPaymentStatus()));
          query.setOrderCount(
              StrUtil.blankToDefault(query.getOrderCount(), orderPaymentListQuery.getOrderCount()));
          query.setApplyPrice(
              StrUtil.blankToDefault(query.getApplyPrice(), orderPaymentListQuery.getApplyPrice()));
          query.setPaymentPrice(
              StrUtil.blankToDefault(
                  query.getPaymentPrice(), orderPaymentListQuery.getPaymentPrice()));
          query.setSubmitMan(
              StrUtil.blankToDefault(query.getSubmitMan(), orderPaymentListQuery.getSubmitMan()));
          query.setAutoDraw(
              ObjectUtil.defaultIfNull(query.getAutoDraw(), orderPaymentListQuery.getAutoDraw()));
          String requestStartTime =
              ObjectUtil.defaultIfNull(
                  paymentStartTime, orderPaymentListQuery.getPaymentRequestStartTime());
          if (StrUtil.isNotEmpty(requestStartTime)) {
            startTime = DateUtil.beginOfDay(DateUtil.parse(requestStartTime)).getTime();
          }
          String requestEndTime =
              ObjectUtil.defaultIfNull(
                  query.getPaymentRequestEndTime(),
                  orderPaymentListQuery.getPaymentRequestEndTime());
          if (StrUtil.isNotEmpty(requestEndTime)) {
            endTime = DateUtil.endOfDay(DateUtil.parse(requestEndTime)).getTime();
          }
        }
      }
    }
    return findPage(
            supplierId,
            query.getPaymentNo(),
            query.getPaymentStatus(),
            query.getSubmitMan(),
            query.getOrderCount(),
            query.getApplyPrice(),
            query.getPaymentPrice(),
            startTime,
            endTime,
        query.getAutoDraw(),
            pageable)
        .map(orderPayment->{
          OrderPaymentListDTO orderPaymentListDTO =
              new OrderPaymentListDTO(orderPayment, BigDecimalUtil.formatForStandard(orderPayment.getApplyPrice()));
          OrderPaymentToOrder orderPaymentToOrder =
              orderPaymentToOrderRepository.findFirstByOrderPaymentIdAndTypeAndState(
                  orderPayment.getId(), Constants_order.ORDER_TO_PAYMENT_TYPE_ORDER_ID,
                  Constants.STATE_OK);
          // 排除查询不到的数据，正常逻辑是不会，有时候后端会在数据库改数据
          if(orderPaymentToOrder!=null){
            orderRepository.findById(orderPaymentToOrder.getRelationId()).ifPresent(order -> {
              TitleOfTheContractEnum titleOfTheContractEnum =
                  TitleOfTheContractEnum.getEnumByCode(order.getTitleOfTheContract());
              String titleOfTheContract =
                  titleOfTheContractEnum == null ? StrUtil.EMPTY : titleOfTheContractEnum.getName();
              orderPaymentListDTO.setTitleOfTheContract(titleOfTheContract);
            });
          }
          return orderPaymentListDTO;
        });
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_ORDER_PAYMENT)
  public PageResult<OrderPaymentListDTO> getOrderPaymentPageRef(
      OrderPaymentListQuery query) {
    String userId = query.getUserId();
    User user = userRepository.findById(userId).orElse(null);
    String supplierId = "";
    if (user == null) {
      SupplierUser supplierUser =
          supplierUserService.get(
              userId, () -> CheckException.noFindException(SupplierUser.class, userId));
      supplierId = supplierUser.getSupplierId();
    }
    Page<OrderPayment> pageRef = dao.findPageRef(query.toQueryMap(supplierId));
    List<OrderPayment> content = pageRef.getContent();
    if (CollUtil.isEmpty(content)) {
      return new PageResult<>(new ArrayList<>(), pageRef.getTotalElements(),
          pageRef.getTotalPages(), query.getPageNo(), query.getPageSize());
    }
    // paymentIds
    List<String> paymentIds = content.stream().map(OrderPayment::getId).collect(Collectors.toList());
    List<OrderPaymentToOrderLink> paymentToOrderLinkList = orderPaymentToOrderLinkRepository.findByPaymentIdIn(paymentIds);
    // orderNeedPaymentIds
    List<String> orderNeedPaymentIds =
        paymentToOrderLinkList.stream().map(OrderPaymentToOrderLink::getOrderNeedPaymentId)
            .collect(toList());
    orderNeedPaymentIds.add("-1");
    Map<String, OrderNeedPayment> orderNeedPaymentMap =
        orderNeedPaymentRepository.findAllById(orderNeedPaymentIds).stream()
            .collect(Collectors.toMap(OrderNeedPayment::getId, orderNeedPayment -> orderNeedPayment,
                (k1, k2) -> k1));
    // orderIds
    List<String> orderIds =
        paymentToOrderLinkList.stream().map(OrderPaymentToOrderLink::getOrderId).collect(toList());
    orderIds.add("-1");
    Map<String, Order> orderMap = orderRepository.findAllByIdIn(orderIds).stream()
        .collect(Collectors.toMap(Order::getId, order -> order, (order1, order2) -> order1));
    // orderReceiptIds
    List<String> orderReceiptIds =
        paymentToOrderLinkList.stream().map(OrderPaymentToOrderLink::getReceiptRecordId)
            .collect(toList());
    orderReceiptIds.add("-1");
    Map<String, OrderReceiptRecord> orderReceiptMap =
        orderReceiptRecordRepository.findAllById(orderReceiptIds).stream()
            .collect(Collectors.toMap(OrderReceiptRecord::getId, orderReceiptRecord -> orderReceiptRecord,
                (k1, k2) -> k1));
    // paymentToOrderLinkMap
    Map<String, List<OrderPaymentToOrderLink>> paymentToOrderLinkMap = paymentToOrderLinkList.stream().collect(Collectors.groupingBy(OrderPaymentToOrderLink::getPaymentId));

    List<OrderPaymentListDTO> result = content.stream().map(item -> {
      OrderPaymentListDTO orderPaymentListDTO = new OrderPaymentListDTO(item, BigDecimalUtil.formatForStandard(item.getApplyPrice()));
      List<OrderPaymentToOrderLink> orderPaymentToOrderLinks = paymentToOrderLinkMap.get(item.getId());
      if (CollUtil.isEmpty(orderPaymentToOrderLinks)) {
        orderPaymentListDTO.setPaymentOrderInfoDTOS(new ArrayList<>());
        return orderPaymentListDTO;
      }
      OrderPaymentToOrderLink firstOne = orderPaymentToOrderLinks.get(0);
      Order order = orderMap.get(firstOne.getOrderId());
      this.setContractTitle(orderPaymentListDTO, order);
      List<OrderPaymentOrderInfoDTO> orderInfoDTOList =
          orderPaymentToOrderLinks.stream().map(link -> {
            Order newOneOrder = orderMap.get(link.getOrderId());
            OrderReceiptRecord orderReceiptRecord = orderReceiptMap.get(link.getReceiptRecordId());
            String platformName = newOneOrder!=null?newOneOrder.getTypeName():StrUtil.EMPTY;
            OrderNeedPayment orderNeedPayment = orderNeedPaymentMap.get(link.getOrderNeedPaymentId());
            return new OrderPaymentOrderInfoDTO(newOneOrder, platformName, orderReceiptRecord, orderNeedPayment, link.getAmount());
          }).collect(toList());
      orderPaymentListDTO.setPaymentOrderInfoDTOS(orderInfoDTOList);
      return orderPaymentListDTO;
    }).collect(toList());
    return new PageResult<>(result, pageRef.getTotalElements(), pageRef.getTotalPages(),
        query.getPageNo(), query.getPageSize());
  }

  private OrderPaymentOrderInfoDTO getOrderPaymentOrderInfoDTO(OrderPaymentToOrder paymentToOrder) {
    return orderRepository.findById(paymentToOrder.getRelationId())
        .map(order -> {
          String platformName = platformService.findNameByCode(order.getType());
          return new OrderPaymentOrderInfoDTO(order, platformName);
        })
        .orElse(null);
  }

  private void setContractTitle(OrderPaymentListDTO orderPaymentListDTO, Order order) {
    TitleOfTheContractEnum titleOfTheContractEnum =
        TitleOfTheContractEnum.getEnumByCode(order.getTitleOfTheContract());
    String titleOfTheContract =
        (titleOfTheContractEnum != null) ? titleOfTheContractEnum.getName() : StrUtil.EMPTY;
    orderPaymentListDTO.setTitleOfTheContract(titleOfTheContract);
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_ORDER_PAYMENT)
  public OrderPaymentStatistics getOrderPaymentStatistics(
      OrderPaymentListQuery query) {
    String userId = query.getUserId();
    User user = userRepository.findById(userId).orElse(null);
    String supplierId = "";
    if (user == null) {
      SupplierUser supplierUser =
          supplierUserService.get(
              userId, () -> CheckException.noFindException(SupplierUser.class, userId));
      supplierId = supplierUser.getSupplierId();
    }
//    List<OrderPayment> orderPayments = dao.findStatistics(query.toQueryMap(supplierId));
//    BigDecimal paymentPrice =
//        orderPayments.stream().map(OrderPayment::getPaymentPrice).filter(Objects::nonNull)
//            .reduce(BigDecimal.ZERO, BigDecimal::add);
//    BigDecimal applyPrice =
//        orderPayments.stream().map(OrderPayment::getApplyPrice).filter(Objects::nonNull)
//            .reduce(BigDecimal.ZERO, BigDecimal::add);
//    Integer orderCount = orderPayments.stream().map(item -> Convert.toInt(item.getOrderCount())).reduce(0, Integer::sum);
//
//    OrderPaymentStatistics orderPaymentStatistics = new OrderPaymentStatistics();
//    orderPaymentStatistics.setOrderCount(orderCount);
//    orderPaymentStatistics.setApplyPrice(applyPrice.doubleValue());
//    orderPaymentStatistics.setPaymentPrice(paymentPrice.doubleValue());
//    return orderPaymentStatistics;
    return dao.findStatistics2(query.toQueryMap(supplierId));
  }

  @Override
  public OrderPaymentInfoDTO getOrderPaymentInfoById(String paymentId) {
    OrderPayment orderPayment =
        get(paymentId, () -> CheckException.noFindException(OrderPayment.class, paymentId));
    //    updateOrderPriceAndPayState(orderPayment, totalReturnPrice, paymentDate);
    //如果付款单已经完成，那么金额取值ApplyPrice，否则动态取值
    //6.7.0不区分付款状态，直接取申请付款金额
    BigDecimal finalOrderPaymentPrice = orderPayment.getApplyPrice();
    OrderPaymentInfoDTO vo =
        new OrderPaymentInfoDTO(orderPayment, getOrderInfoDTOList(paymentId),
            getProductInfoList(paymentId), false, finalOrderPaymentPrice);
    List<OrderPaymentCollection> orderPaymentCollections =
        orderPaymentCollectionRepository.findAllByOrderPaymentIdAndState(paymentId,
            Constants.STATE_OK);
    List<OrderPaymentCollectionVO> orderPaymentCollectionVOS =
        CollUtil.emptyIfNull(orderPaymentCollections).stream().map(OrderPaymentCollectionVO::of)
            .collect(toList());
    vo.setPaymentCollections(orderPaymentCollectionVOS);
    return vo;
  }

  @Override
  public OrderPaymentInfoDTO getOrderPaymentInfoByIdAdmin(String paymentId) {
    OrderPayment orderPayment =
        get(paymentId, () -> CheckException.noFindException(OrderPayment.class, paymentId));
    //如果付款单已经完成，那么金额取值ApplyPrice，否则动态取值
    //6.7.0不区分付款状态，直接取申请付款金额
    BigDecimal finalOrderPaymentPrice = orderPayment.getApplyPrice();
    OrderPaymentInfoDTO vo =
        new OrderPaymentInfoDTO(orderPayment, getOrderInfoDTOList(paymentId),
            getProductInfoList(paymentId), false, finalOrderPaymentPrice);
    List<OrderPaymentCollection> orderPaymentCollections =
        orderPaymentCollectionRepository.findAllByOrderPaymentIdAndState(paymentId,
            Constants.STATE_OK).stream().peek(this::setPayAmountAndTime).collect(toList());
    List<OrderPaymentCollectionVO> orderPaymentCollectionVOS =
        CollUtil.emptyIfNull(orderPaymentCollections).stream().map(OrderPaymentCollectionVO::of)
            .collect(toList());
    orderPaymentCollectionVOS.forEach(item->{
      String refundState =
          orderRefundCollectionRepository.findFirstByOrderPaymentCollectionIdAndState(item.getId(),
              Constants.STATE_OK).map(OrderRefundCollection::getRefundState).orElse(StrUtil.EMPTY);
      item.setRefundState(refundState);
    });

    vo.setPaymentCollections(orderPaymentCollectionVOS);
    return vo;
  }

  private List<OrderPaymentOrderInfoDTO> getOrderInfoDTOList(String paymentId) {
    //付款明细
    OrderPayment orderPayment =
        get(paymentId, () -> CheckException.noFindException(OrderPayment.class, paymentId));
    List<OrderPaymentToOrderLink> paymentToOrderLinkList =
        orderPaymentToOrderLinkRepository.findByPaymentId(paymentId);
    List<OrderPaymentOrderInfoDTO> orderInfoDTOList = new ArrayList<>();
    if (CollUtil.isNotEmpty(paymentToOrderLinkList)) {
      orderInfoDTOList.addAll(paymentToOrderLinkList.stream().map(link -> {
        Order order = link.getOrderId() != null ?
            orderRepository.findById(link.getOrderId()).orElse(null) : null;
        OrderReceiptRecord orderReceiptRecord = link.getReceiptRecordId() != null ?
            orderReceiptRecordRepository.findById(link.getReceiptRecordId()).orElse(null) : null;
        String platformName = order != null ? platformService.findNameByCode(order.getType()) : StrUtil.EMPTY;
        OrderNeedPayment orderNeedPayment = null;
        if (StrUtil.isNotBlank(link.getOrderNeedPaymentId())) {
          orderNeedPayment = orderNeedPaymentRepository.findById(link.getOrderNeedPaymentId()).get();
        }
        OrderPaymentOrderInfoDTO orderInfoDTO =
            new OrderPaymentOrderInfoDTO(order, platformName, orderReceiptRecord, orderNeedPayment,
                link.getAmount());
        return orderInfoDTO;
      }).collect(Collectors.toList()));
    }
    return orderInfoDTOList;
  }

  private List<ProductInfoDTO> getProductInfoList(String paymentId) {
    List<String> orderIdList = orderPaymentToOrderService.getOrderIdListByPaymentId(paymentId);
    List<ProductInfoDTO> productInfoList = new ArrayList<>();
    orderIdList.forEach(
        orderId ->
            productInfoList.addAll(
                orderDetailService.getOrderDetailByOrderId(orderId).stream()
                    .map(ProductInfoDTO::new)
                    .collect(Collectors.toList())));
    return productInfoList;
  }

  private List<OrderInfoDTO> getOrderInfoList(String paymentId) {
    return orderPaymentToOrderRepository.getAllByOrderPaymentIdAndStateAndType(paymentId,
            Constants.STATE_OK, Constants_order.ORDER_TO_PAYMENT_TYPE_ORDER_ID).stream()
        .map(
            orderPaymentToOrder -> {
              BigDecimal applyPrice = orderPaymentToOrder.getApplyPrice();
              Order order = orderService.get(orderPaymentToOrder.getRelationId(),
                  () -> CheckException.noFindException(Order.class, orderPaymentToOrder.getRelationId()));
              // 对账单id
             String orderAccountId =  Optional.ofNullable(orderAccountToOrderRepository.getFirstByOrderIdAndState(order.getId(),Constants.STATE_OK)).map(OrderAccountToOrder::getOrderAccountId).orElseGet(()->"");
              // 进项票发票号
             List<String> inputTicketNumList =  Optional.ofNullable(
                  orderAccountToOrderRepository.getFirstByOrderIdAndState(order.getId(),
                      Constants.STATE_OK)).map(OrderAccountToOrder::getOrderAccountId)
                  .map(id -> orderAccountInvoiceDao.getOrderAccountInvoiceByAccount(id))
                  .orElse(ListUtil.toList()).stream().map(OrderSupplierInvoice::getInvoiceNum)
                  .collect(toList());
             String inputTicketNumStr = CollUtil.isNotEmpty(inputTicketNumList)
                 ? StrUtil.join(",", inputTicketNumList)
                 : "";
              //签收凭证
              String acceptState = orderAcceptService.getAcceptState(order.getId());

              String platformName = platformService.findNameByCode(order.getType());
              return new OrderInfoDTO(order, inputTicketNumStr,orderAccountId, acceptState,
                  platformName,
                  Optional.ofNullable(orderInvoiceRelationRepository.findFirstByOrderNumsLikeAndState(order.getOrderNo(),
                  Constants.STATE_OK)).map(InputInvoiceOrder::getId).orElse(StrUtil.EMPTY),applyPrice);
            })
        .collect(Collectors.toList());
  }

  @Override
  public CreatePaymentOrderResult submitPaymentOrderForApi(SubmitPaymentOrderApiParams params) {
    // todo 需要修改为维度为订单
    List<String> orderIds = params.getOrderIds();
    List<OrderNeedPayment> orderNeedPayments =
        orderNeedPaymentRepository.findAllByOrderIdInAndState(orderIds, Constants.STATE_OK);
    if (CollUtil.isEmpty(orderNeedPayments)) {
      throw new CheckException("没有需要付款的订单");
    }
    // 检查是否所有传入的订单ID都有对应的需付款记录
    Set<String> foundOrderIds = orderNeedPayments.stream()
        .map(OrderNeedPayment::getOrderId)
        .collect(Collectors.toSet());
    List<String> missingOrderIds = orderIds.stream()
        .filter(id -> !foundOrderIds.contains(id))
        .collect(Collectors.toList());
    if (!missingOrderIds.isEmpty()) {
      throw new CheckException("以下订单没有找到需付款记录：" + String.join(", ", missingOrderIds));
    }
    SubmitPaymentOrderParams newParams = new SubmitPaymentOrderParams();
    // 获取第一条订单的付款方式
//    String oderReceiptId = orderNeedPayments.stream().map(OrderNeedPayment::getOrderReceiptId)
//        .filter(StrUtil::isNotBlank).findFirst().orElseThrow( () -> new CheckException("未找到回款记录"));
//    OrderReceiptRecord orderReceiptRecord =
//        orderReceiptRecordRepository.findById(oderReceiptId).orElseThrow(() -> new CheckException("未找到回款记录"));
//    String paymentMethod = orderReceiptRecord.getPaymentMethod();
    String oderReceiptId = orderNeedPayments.stream().map(OrderNeedPayment::getOrderReceiptId)
        .filter(StrUtil::isNotBlank).findFirst().orElse(null);
    PayTypeSAPEnums sapEnum = null;
    String payType = null;
    if (StrUtil.isNotBlank(oderReceiptId)) {
      OrderReceiptRecord orderReceiptRecord = orderReceiptRecordRepository.findById(oderReceiptId)
          .orElseThrow(() -> new CheckException("未找到回款记录"));
      String paymentMethod = orderReceiptRecord.getPaymentMethod();
      sapEnum = OrderReceiptPaymentMethodEnum.getSapEnumFromCode(paymentMethod);
      if (sapEnum == null) {
        throw new CheckException("未找到对应的付款方式");
      }
      payType = sapEnum.getCode();
    } else {
      Supplier supplier = supplierRepository.findById(params.getSupplierId())
          .orElseThrow(() -> new CheckException("供应商不存在"));
      payType = PayTypeSAPEnums.getCodeByName(supplier.getPayType());
    }
    newParams.setPayType(payType);
    newParams.setPayOtherDesc("");
    newParams.setRemark(params.getRemark());
    newParams.setSupplierId(params.getSupplierId());
    List<SubmitOrderItem> submitOrderItems = new ArrayList<>();
    for (OrderNeedPayment orderNeedPayment : orderNeedPayments) {
      Order order = orderRepository.findById(orderNeedPayment.getOrderId()).orElseThrow(() -> new CheckException(
          "订单不存在"));
      SubmitOrderItem submitOrderItem = new SubmitOrderItem();
      submitOrderItem.setNeedPaymentId(orderNeedPayment.getId());
      BigDecimal realOrderPrice =
          NumberUtil.sub(order.getPrice(), order.getRefundPrice(), order.getCancelPrice());
      BigDecimal realAmount = realOrderPrice.multiply(orderNeedPayment.getRate())
          .subtract(orderNeedPayment.getPaidAmount())
          .subtract(orderNeedPayment.getPendingAmount())
          .add(orderNeedPayment.getReturnAmount());
      // 获取实际可以金额
      submitOrderItem.setAmount(realAmount.setScale(2, RoundingMode.HALF_UP));
      if (realAmount.compareTo(BigDecimal.ZERO) > 0) {
        submitOrderItems.add(submitOrderItem);
      }
    }
    if (CollUtil.isEmpty(submitOrderItems)) {
      throw new CheckException("没有需要付款的订单");
    }
    newParams.setItems(submitOrderItems);
    newParams.setUserId(params.getUserId());
    newParams.setSource(params.getSource());
    return save(newParams);
  }

  @Override
  public CreatePaymentOrderResult submitNewPaymentOrderForHtApi(SubmitPaymentOrderParams params) {
    params.setSource(OrderPaymentSource.BACKEND.getCode());
    return save(params);
  }

  private CreatePaymentOrderResult save(SubmitPaymentOrderParams params) {
    List<String> needPaymentIds =
        params.getItems().stream().map(SubmitOrderItem::getNeedPaymentId).collect(toList());
    List<RLock> locks =
        lockUtils.lockAll(needPaymentIds, LOCK_FOR_ORDER_NEED_PAYMENT);
    // 开启事务
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      return transactionTemplate.execute(status -> {
        Supplier supplier = supplierRepository.findById(params.getSupplierId())
            .orElseThrow(() -> new CheckException("供应商不存在"));
        List<Order> orders = new ArrayList<>();
        List<String> invoiceCodeList = new ArrayList<>();
        // 0.释放原有的申请数量
        orderPaymentFactory.releaseOrderPayment(params.getPaymentId());
        // ------------------ 校验 ------------------
        // #check 提交预校验
        NeedPaymentPreCheckForm preCheckForm = new NeedPaymentPreCheckForm();
        List<NeedPaymentForm> needPaymentForms = new ArrayList<>();
        for (SubmitOrderItem submitOrderItem : params.getItems()) {
          NeedPaymentForm needPaymentForm = new NeedPaymentForm();
          needPaymentForm.setNeedPaymentId(submitOrderItem.getNeedPaymentId());
          needPaymentForm.setAmount(submitOrderItem.getAmount());
          needPaymentForms.add(needPaymentForm);
        }
        preCheckForm.setNeedPaymentForms(needPaymentForms);
        preCheckForm.setPaymentApplyId(params.getPaymentId());
        preCheckForm.setSource(params.getSource());
        orderNeedPaymentService.preCheck(preCheckForm);
        // ----------------- 保存 ------------------
        // 1.保存付款单
        OrderPayment orderPayment = orderPaymentFactory.createOrderPayment(params, orders, supplier);
        orderPaymentRepository.saveAndFlush(orderPayment);
        // 2.生成相应的orderPaymentToOrder
        List<OrderPaymentToOrder> orderPaymentToOrder = orderPaymentFactory.createOrderPaymentToOrder(params, orderPayment);
        orderPaymentToOrderRepository.saveAll(orderPaymentToOrder);
        // 3.生成相应的orderPaymentToOrderLink
        List<OrderPaymentToOrderLink> orderPaymentToOrderLink =
            orderPaymentFactory.createOrderPaymentToOrderLink(params, orderPayment, orderPaymentToOrder);
        orderPaymentToOrderLinkRepository.saveAll(orderPaymentToOrderLink);
        // 4.占用数量
        orderPaymentFactory.lockOrderPayment(orderPaymentToOrderLink);
        // todo 5.履约状态变更
        for (Order order : orders) {
          invoiceCodeList.addAll(Optional.ofNullable(
                  orderAccountToOrderRepository.getFirstByOrderIdAndState(order.getId(),
                      Constants.STATE_OK)).map(OrderAccountToOrder::getOrderAccountId)
              .map(id -> orderAccountInvoiceDao.getOrderAccountInvoiceByAccount(id))
              .orElse(ListUtil.toList()).stream().map(OrderSupplierInvoice::getInvoiceNum)
              .collect(toList()));
          // 如果已经在付款中则不修改
          if (!Constants_order.CONDUCT_PAYMENT_TYPE.equals(order.getPaymentStatus())) {
            orderDao.optimisticLockUpdateOrder(order, o -> {
              // 设置原始付款状态
              o.setExtraJsonOriginPaymentStatus(o.getPaymentStatus());
              // 设置履约单付款状态(付款中)
              o.setPaymentStatus(Constants_order.CONDUCT_PAYMENT_TYPE);
              // 设置履约单付款申请时间
              o.setPaymentCreateTime(System.currentTimeMillis());
            });
          }
        }
        // ----------------- 调用sap ------------------
        if (orderPayment.getAutoDraw()) {
          //  调用 SAP 034 接口
          sapService.sapAdvanceApplyWithLockGroup(sapAdvanceApplyRequest.buildOrderPaymentSAPParams(orderPayment, orderPaymentToOrder, orders,supplier));
          OrderPaymentCollection orderPaymentCollection = new OrderPaymentCollection();
          orderPaymentCollection.setOrderPaymentId(orderPayment.getId());
          long createTime = System.currentTimeMillis();
          orderPaymentCollection.setCreateTime(createTime);
          orderPaymentCollection.setCreateVoucherType(Constants.VOUCHER_TYPE_BY_SY);
          orderPaymentCollection.setBusinessCreateTime(createTime);
          orderPaymentCollectionRepository.save(orderPaymentCollection);
          updateOrderPaymentState(orderPayment.getId());
        }
        // ----------------- 发送钉钉消息 ------------------
        //发送钉钉消息
        try {
          // 获取第一条order的platformCode
          String platformCode = orders.get(0).getType();
          List<String> orderCodeList = orders.stream().map(Order::getOrderNo).collect(toList());
          sendDingDingMessage(supplier, platformCode, orderPayment, orderCodeList, new ArrayList<>());
        } catch (Exception e) {
          log.error(ExceptionUtil.stacktraceToString(e,-1));
        }
        return new CreatePaymentOrderResult();
      });
    } finally {
      lockUtils.unlockAllLocks(locks);
    }
  }

  private void sendDingDingMessage(Supplier supplier, String platformCode,
      OrderPayment orderPayment, List<String> orderCodeList, List<String> invoiceCodeList) {
    SupplierPerformance supplierPerformance =
        supplierPerformanceDao.getBySupplierIdAndPlatformCode(supplier.getId(), platformCode);
    if (supplierPerformance == null) {
      throw new CheckException("您没有配置此订单所属平台的履约权限，请联系管理员配置!");
    }
    List<String> mobileList = new ArrayList<>(srmConfig.getPaymentNoticeList());
    String code = supplierPerformance.getDockingPurchaseErpCode();
    if (StrUtil.isNotBlank(code)) {
      User user = userDao.getUserByCode(code);
      if (user != null && StrUtil.isNotBlank(user.getMobile())) {
        mobileList.add(user.getMobile());
      }
    }
    log.info(supplier.getId() + ":" + supplier.getEnterpriseName() + ":" + platformCode + ":"
        + "付款申请发送的手机号:{}", mobileList);
    for (String mobile : CollUtil.emptyIfNull(mobileList)) {
      try {
        dingUtils.sendApplyPaymentNotice(supplier.getEnterpriseName(), orderPayment.getPaymentNo(),
            orderPayment.getApplyPrice(), orderCodeList, invoiceCodeList, mobile);
      } catch (Exception e) {
        log.error(ExceptionUtil.stacktraceToString(e, -1));
      }
    }
  }

  /**
   * @param supplierId 供应商id
   * @return 供应商付款中的付款单申请金额合计
   */
  private BigDecimal getApplyPaymentAmountBySupplierId(String supplierId) {
    Assert.notBlank(supplierId);
    List<OrderPayment> orderPayments =
        repository.findBySupplierIdAndStateAndPaymentStatus(supplierId, Constants.STATE_OK,
            Constants_order.CONDUCT_PAYMENT_TYPE);
    if (CollUtil.isEmpty(orderPayments)) {
      return BigDecimal.ZERO;
    }
    return orderPayments.stream().map(OrderPayment::getApplyPrice).filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }




  @Override
  public Page<OrderPayment> findPage(
      String supplierId,
      String paymentNo,
      String paymentStatus,
      String submitMan,
      String orderCount,
      String applyPrice,
      String paymentPrice,
      Long createTimeStart,
      Long createTimeEnd,
      Boolean autoDraw,
      Pageable pageable) {
    return dao.findPage(
        supplierId,
        paymentNo,
        paymentStatus,
        submitMan,
        orderCount,
        applyPrice,
        paymentPrice,
        createTimeStart,
        createTimeEnd,
        autoDraw,
        pageable);
  }

  @Override
  @Transactional
  public void saveErpPaymentVoucher(SubmitPaymentVoucherParams params) {
    OrderPaymentCollection orderPaymentCollection = new OrderPaymentCollection();
    orderPaymentCollection.setOrderPaymentId(params.getPaymentId());
    orderPaymentCollection.setCreateTime(System.currentTimeMillis());
    orderPaymentCollection.setPaymentTime(params.getPaymentTime());
    orderPaymentCollection.setAmount(params.getReturnPrice());
    orderPaymentCollection.setBusinessCreateTime(params.getCreateTime());
    orderPaymentCollection.setPaymentNo(params.getErpPaymentNo());
    orderPaymentCollection.setCreateVoucherType(Constants.VOUCHER_TYPE_BY_CODE);
    orderPaymentCollectionRepository.save(orderPaymentCollection);
    updateOrderPaymentState(params.getPaymentId());
  }

  @Override
  @Transactional
  public void updateErpPaymentVoucher(UpdatePaymentVoucherParams params) {
    OrderPaymentCollection orderPaymentCollection =
        orderPaymentCollectionRepository.findById(params.getId()).orElseThrow(
            () -> CheckException.noFindException(OrderPaymentCollection.class, params.getId()));
    orderPaymentCollection.setUpdateTime(System.currentTimeMillis());
    orderPaymentCollection.setOrderPaymentId(params.getPaymentId());
    orderPaymentCollection.setPaymentTime(params.getPaymentTime());
    orderPaymentCollection.setAmount(params.getReturnPrice());
    orderPaymentCollection.setBusinessCreateTime(params.getCreateTime());
    orderPaymentCollection.setPaymentNo(params.getErpPaymentNo());
    orderPaymentCollectionRepository.save(orderPaymentCollection);
    updateOrderPaymentState(params.getPaymentId());
  }

  @Override
  @Transactional
  public void delErpPaymentVoucher(String id) {
    OrderPaymentCollection orderPaymentCollection = orderPaymentCollectionRepository.findById(id)
        .orElseThrow(() -> CheckException.noFindException(OrderPaymentCollection.class, id));
    orderPaymentCollection.setState(Constants.STATE_DELETE);
    orderPaymentCollectionRepository.save(orderPaymentCollection);
    updateOrderPaymentState(orderPaymentCollection.getOrderPaymentId());
  }

  @Override
  public Map<String, Long> getOrderPaymentIngNum() {
    HashMap<String, Long> resultMap = new HashMap<>();
    long reviewCount = dao.getOrderPaymentNumByPaymentStatus(Constants_order.UNDER_REVIEW_PAYMENT_TYPE);
    long duringCount = dao.getOrderPaymentNumByPaymentStatus(Constants_order.DURING_PAYMENT_TYPE);
    resultMap.put("reviewCount", reviewCount);
    resultMap.put("duringCount", duringCount);
    return resultMap;
  }

  private void updateOrderPaymentState(String paymentId) {
    Assert.notBlank(paymentId);
    List<OrderPaymentCollection> orderPaymentCollections =
        orderPaymentCollectionRepository.findAllByOrderPaymentIdAndState(paymentId,
            Constants.STATE_OK);
    BigDecimal sumAmount = CollUtil.emptyIfNull(orderPaymentCollections).stream()
        .map(OrderPaymentCollection::getAmount).filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO,
            BigDecimal::add);
    OrderPayment orderPayment = orderPaymentRepository.findById(paymentId)
        .orElseThrow(() -> CheckException.noFindException(OrderPayment.class, paymentId));
    BigDecimal applyPrice = orderPayment.getApplyPrice();
    if (orderPayment.getPaymentStatus().equals(Constants_order.UNDER_REVIEW_PAYMENT_TYPE)) {
      return;
    }
    if (NumberUtil.isGreaterOrEqual(sumAmount, applyPrice)) {
      // 如果原来是付款中，然后变为付款完成则完成付款
      if (Constants_order.CONDUCT_PAYMENT_TYPE.equals(orderPayment.getPaymentStatus())) {
        // 付款完成事件
        srmEventPublisher.publish(new OrderPaymentDoneEvent(orderPayment.getId()));
      }
      orderPayment.setPaymentStatus(Constants_order.COMPLETE_PAYMENT_TYPE);
      orderPayment.setPaymentPrice(sumAmount);
      orderPaymentRepository.save(orderPayment);
      //付款单状态变为付款完成时，保存付款记录
      saveOrderPartialPayment(orderPayment,orderPaymentCollections);
      orderService.updatePaymentState(paymentId);
    }else {
      // 如果原来是付款完成，然后变为付款中则重新锁定
      if (Constants_order.COMPLETE_PAYMENT_TYPE.equals(orderPayment.getPaymentStatus())) {
        // 付款重新锁定
        srmEventPublisher.publish(new OderPaymentReLockEvent(orderPayment.getId()));
      }
      orderPayment.setPaymentStatus(Constants_order.CONDUCT_PAYMENT_TYPE);
      orderPayment.setPaymentPrice(sumAmount);
      orderPaymentRepository.save(orderPayment);
    }
  }

  public void saveOrderPartialPayment(OrderPayment orderPayment, List<OrderPaymentCollection> orderPaymentCollections) {
    if (CollUtil.isEmpty(orderPaymentCollections)) {
      return;
    }
    // 获取最晚的付款时间
    Long paymentTime = orderPaymentCollections.stream().map(OrderPaymentCollection::getPaymentTime)
        .filter(Objects::nonNull).max(Long::compareTo).orElse(null);
    //根据提交人id区分前台提款还是后台提款
    String submitId = orderPayment.getSubmitId();
    String orderPaymentType = StrUtil.EMPTY;
    if (StrUtil.isNotBlank(submitId)) {
      orderPaymentType = supplierUserService.existById(submitId)
          ? OrderPaymentTypeEnum.FRONT_WITHDRAWALS.getKey()
          : userRepository.existsById(submitId) ? OrderPaymentTypeEnum.BACK_WITHDRAWALS.getKey()
              : StrUtil.EMPTY;
    }
    List<OrderPaymentToOrder> paymentToOrderList =
        orderPaymentToOrderRepository.getAllByOrderPaymentIdAndStateAndType(orderPayment.getId(),
            Constants.STATE_OK, Constants_order.ORDER_TO_PAYMENT_TYPE_ORDER_ID);
    for (OrderPaymentToOrder orderPaymentToOrder : paymentToOrderList) {
      if (paymentTime != null && StrUtil.isNotBlank(orderPaymentType)) {
        if (orderPartialPaymentRepository.existsByOrderIdAndPaymentIdAndState(orderPaymentToOrder.getRelationId(), orderPaymentToOrder.getOrderPaymentId(), Constants.STATE_OK))continue;
        OrderPartialPayment orderPartialPayment = new OrderPartialPayment();
        orderPartialPayment.setOrderId(orderPaymentToOrder.getRelationId());
        orderPartialPayment.setCreateTime(System.currentTimeMillis());
        orderPartialPayment.setAmount(orderPaymentToOrder.getApplyPrice());
        orderPartialPayment.setCreateMan(submitId);
        orderPartialPayment.setState(Constants.STATE_OK);
        orderPartialPayment.setPaymentTime(paymentTime);
        orderPartialPayment.setOrderPaymentType(orderPaymentType);
        orderPartialPayment.setPaymentId(orderPaymentToOrder.getOrderPaymentId());
        orderPartialPaymentRepository.save(orderPartialPayment);
      }
    }
  }

  @Override
  public List<OrderPayment> findByOrderId(String orderId) {
    return dao.findAllByOrderId(orderId);
  }

  @Override
  public void abandonOrderPaymentById(String paymentId) {
    // 判断是否已经放弃
    OrderPayment orderPayment = orderPaymentRepository.findById(paymentId)
        .orElseThrow(() -> CheckException.noFindException(OrderPayment.class, paymentId));
    if (Constants_order.OVER_RULE_PAYMENT_TYPE.equals(orderPayment.getPaymentStatus())) {
      srmEventPublisher.publish(new OrderPaymentAbandonEvent(paymentId));
    }
    orderPayment.setPaymentStatus(Constants_order.WAIVED_PAYMENT_TYPE);
    orderPaymentRepository.saveAndFlush(orderPayment);
  }


  @Override
  public BigDecimal getPaymentFinalPrice(String paymentId) {
    return orderPaymentToOrderDao.getPaymentFinalPrice(paymentId).setScale(2, RoundingMode.HALF_UP);
  }

  @Override
  public Optional<OrderPayment> getOrderPaymentNo(String paymentApplyNo) {
    return repository.findFirstByPaymentNoAndState(paymentApplyNo,Constants.STATE_OK);
  }

  @Override
  public void syncOrderPaymentTask() {
    CollUtil.emptyIfNull(orderPaymentCollectionRepository.findAllByAmountIsNullAndState(Constants.STATE_OK)).forEach(
        this::setPayAmountAndTime
    );
  }

  private void setPayAmountAndTime(OrderPaymentCollection orderPaymentCollection) {
    try {
      String paymentNo = orderPaymentCollection.getPaymentNo();
      // 查询相关order
      List<String> orderIds = orderPaymentToOrderService.getOrderIdListByPaymentId(
          orderPaymentCollection.getOrderPaymentId());
      orderIds.add("-1");
      List<Order> orders = orderRepository.findAllById(orderIds);
      // 银行回单是否有值
      boolean hasNoUrl = StrUtil.isBlank(orderPaymentCollection.getUrl());
      boolean needSap = StrUtil.isNotBlank(paymentNo) && orderPaymentCollection.getAmount() == null;
      if (needSap || hasNoUrl) {
        SapPayStateParam param = new SapPayStateParam();
        ITEMDTO itemdto = new ITEMDTO();
        itemdto.setBelnr(paymentNo);
        // 2024年10月8日16:15:30 传参调整：BUKRS字段传递第一个订单的签约抬头的组织编码
        String groupCode =
            orders.stream().findFirst().map(Order::getTitleOfTheContract).orElse(null);
        itemdto.setBukrs(groupCode);
        itemdto.setGjahr( DateUtil.year(DateTime.of(orderPaymentCollection.getCreateTime())));
        if (StrUtil.isNotBlank(orderPaymentCollection.getAccountingYear()) && NumberUtil.isNumber(orderPaymentCollection.getAccountingYear())) {
          itemdto.setGjahr(Convert.toInt(orderPaymentCollection.getAccountingYear()));
        }
        String orderPaymentId = orderPaymentCollection.getOrderPaymentId();
        AtomicReference<BigDecimal> payAmount = new AtomicReference<>(BigDecimal.ZERO);
        if(StrUtil.isNotEmpty(orderPaymentId)){
          orderPaymentRepository.findById(orderPaymentId).ifPresent(
              orderPayment -> {
                payAmount.set(orderPayment.getApplyPrice());
                if (StrUtil.isNotBlank(orderPayment.getSupplierId())) {
                  supplierRepository.findById(orderPayment.getSupplierId())
                      .ifPresent(
                          supplier -> itemdto.setLifnr(supplier.getMdmCode())
                      );
                }
              }
          );
        }
        itemdto.setBuzei("1");
        param.setHead(itemdto);
        SapPayStateResult sapPayStateResult = sapService.sapPayState(param);
        List<RETURNDTO> returnXList = CollUtil.emptyIfNull(sapPayStateResult.getReturnX());
        if (CollUtil.isNotEmpty(returnXList)) {
          RETURNDTO returndto = returnXList.get(0);
          String zaldt = returndto.getZaldt();
          if (StrUtil.isNotBlank(returndto.getZzpaystatus())) {
            orderPaymentCollection.setPaymentState(returndto.getZzpaystatus());
          }
          if (VoucherPaymentStateEnum.PAYMENT_SUCCESSFUL.getKey().equals(returndto.getZzpaystatus())) {
            if (StrUtil.isNotBlank(zaldt)) {
              try {
                orderPaymentCollection.setPaymentTime(DateUtil.parseDate(zaldt).getTime());
              } catch (Exception e) {
                log.error(ExceptionUtil.stacktraceToString(e,-1));
              }
            }
            orderPaymentCollection.setAmount(payAmount.get());
            String zbuchetfileurl = returndto.getZzbuchetfileurl();
            if(StrUtil.isNotEmpty(zbuchetfileurl)){
              try {
                Optional.ofNullable(sapService.uploadBankReceipt(zbuchetfileurl))
                    .ifPresent(orderPaymentCollection::setUrl);
              } catch (Exception e) {
                log.error(ExceptionUtil.stacktraceToString(e,-1));
              }
            }
            orderPaymentCollectionRepository.saveAndFlush(orderPaymentCollection);
            updateOrderPaymentState(orderPaymentId);
          }
          orderPaymentCollectionRepository.saveAndFlush(orderPaymentCollection);
        }
      }
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e,-1));
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void submitPaymentRefund(SubmitPaymentRefundParams params) {
    OrderPayment orderPayment = orderPaymentRepository.findById(params.getOrderPaymentId())
        .orElseThrow(() -> CheckException.noFindException(OrderPayment.class, params.getId()));
    OrderRefundCollection refundCollection;
    if (StrUtil.isBlank(params.getId())) {
      refundCollection = createNewRefundCollection(params);
    } else {
      refundCollection = updateExistingRefundCollection(params);
    }
    orderRefundCollectionRepository.save(refundCollection);
    //落地商退款时调用MM_034
    sapService.sapAdvanceApplyWithLockGroup(buildOrderPaymentRefundSAPParams(params, orderPayment
        ,refundCollection.getRefundNo()));
  }

  @Override
  public OrderPaymentRefundDTO getPaymentRefundInfoById(String orderPaymentCollectionId) {
    OrderRefundCollection refundCollection =
        orderRefundCollectionRepository.findFirstByOrderPaymentCollectionIdAndState(orderPaymentCollectionId,
                Constants.STATE_OK)
            .orElseThrow(() -> new CheckException("未根据回款单id查询到退款信息"));
    return new OrderPaymentRefundDTO(refundCollection);
  }

  private OrderRefundCollection createNewRefundCollection(SubmitPaymentRefundParams params) {
    OrderRefundCollection refundCollection = new OrderRefundCollection();
    refundCollection.setRefundNo(PaymentRefundNumUtil.getInstance().getSeq());
    refundCollection.setOrderPaymentCollectionId(params.getOrderPaymentCollectionId());
    refundCollection.setOrderPaymentId(params.getOrderPaymentId());
    refundCollection.setExpectedRefundTime(params.getExpectedRefundTime());
    refundCollection.setPaymentType(params.getPaymentType());
    refundCollection.setBankName(params.getBankName());
    refundCollection.setBankCode(params.getBankCode());
    refundCollection.setBankAccount(params.getBankAccount());
    refundCollection.setAccountName(params.getAccountName());
    refundCollection.setRemark(params.getRemark());
    refundCollection.setRefundAmount(params.getRefundAmount());
    refundCollection.setRefundState(Constants.PAYMENT_APPLY_EXAMINE);
    refundCollection.setCreateTime(System.currentTimeMillis());
    refundCollection.setCreateMan(params.getUserId());
    refundCollection.setUpdateTime(System.currentTimeMillis());
    refundCollection.setUpdateMan(params.getUserId());
    refundCollection.setState(Constants.STATE_OK);
    return refundCollection;
  }

  private OrderRefundCollection updateExistingRefundCollection(SubmitPaymentRefundParams params) {
    OrderRefundCollection refundCollection = orderRefundCollectionRepository.findById(params.getId())
        .orElseThrow(() -> CheckException.noFindException(OrderRefundCollection.class, params.getId()));
    refundCollection.setRefundState(Constants.PAYMENT_APPLY_EXAMINE);
    refundCollection.setExpectedRefundTime(params.getExpectedRefundTime());
    refundCollection.setPaymentType(params.getPaymentType());
    refundCollection.setBankName(params.getBankName());
    refundCollection.setBankCode(params.getBankCode());
    refundCollection.setBankAccount(params.getBankAccount());
    refundCollection.setAccountName(params.getAccountName());
    refundCollection.setRemark(params.getRemark());
    refundCollection.setUpdateTime(System.currentTimeMillis());
    refundCollection.setUpdateMan(params.getUserId());
    return refundCollection;
  }


  private AdvanceApplyParam buildOrderPaymentRefundSAPParams(SubmitPaymentRefundParams params,
      OrderPayment orderPayment,String refundNo) {
    AdvanceApplyParam param = new AdvanceApplyParam();
    AdvanceApplyParam.DATADTO datadto = new AdvanceApplyParam.DATADTO();
    HEADDTO headdto = new HEADDTO();
    headdto.setSrmid(refundNo);
    String titleOfTheContract =
        orderRepository.findById(params.getOrderIdList().get(0)).map(Order::getTitleOfTheContract)
            .orElse(StrUtil.EMPTY);
    TitleOfTheContractEnum titleOfTheContractEnum =
        TitleOfTheContractEnum.throwExceptionIfNotFind(titleOfTheContract);
    headdto.setBukrs(titleOfTheContractEnum.getCode());
    //3付款退款
    headdto.setZzmfklx("3");
    String mdmCode = supplierRepository.findById(orderPayment.getSupplierId()).map(Supplier::getMdmCode)
        .orElseThrow(() -> new CheckException("付款申请单对应的供应商主数据编码为空"));
    headdto.setLifnr(mdmCode);
    String nowDate =
        DateUtils.formatTimeStampToStr(System.currentTimeMillis(), DatePattern.PURE_DATE_PATTERN);
    headdto.setZzmsqrq(nowDate);
    headdto.setZzmfksqzje(params.getRefundAmount().toPlainString());
    User user = userRepository.findById(params.getUserId())
        .orElseThrow(() -> CheckException.noFindException(User.class, params.getId()));
    if (StrUtil.isBlank(user.getCode()) || StrUtil.isBlank(user.getRealName())) {
      throw new CheckException("申请人工号或姓名不能为空");
    }
    headdto.setZzmsqrgh(user.getCode());
    headdto.setZzmsqr(user.getRealName());
    PayTypeSAPEnums payTypeSAPEnums = PayTypeSAPEnums.fromName(params.getPaymentType());
    if (payTypeSAPEnums == null) {
      payTypeSAPEnums = PayTypeSAPEnums.fromKey(params.getPaymentType());
    }
    List<HEADDTO.ITEMDTO> itemdtoList = new ArrayList<>();
    for (int i = 0; i < params.getOrderIdList().size(); i++) {
      Order order = orderRepository.findById(params.getOrderIdList().get(i))
          .orElseThrow(() -> CheckException.noFindException(OrderPayment.class, params.getId()));
      HEADDTO.ITEMDTO itemdto = new HEADDTO.ITEMDTO();
      itemdto.setZzmfksqhxmh(String.valueOf((i+1)));
      if (StrUtil.isBlank(order.getErpOrderNo())) {
        throw new CheckException("该客户订单号："+order.getOrderNo()+" 对应的采购订单号为空");
      }
      itemdto.setEbeln(order.getErpOrderNo());
      BigDecimal price = NumberUtil.sub(order.getPrice(),order.getRefundPrice());
      itemdto.setZzmsfje(price.toPlainString());
      itemdto.setBrtwr(price.toPlainString());
      itemdto.setZfbdt(nowDate);
      itemdto.setZzmqwfkrq(nowDate);
      itemdto.setZbd1t("1");
      if (payTypeSAPEnums != null) {
        itemdto.setZlsch(payTypeSAPEnums.getCode());
        itemdto.setText2(payTypeSAPEnums.getName());
      }
      itemdto.setZzmfkzt(StrUtil.EMPTY);
      itemdto.setZzmmxbz(params.getRemark());
      itemdto.setBankl(params.getBankCode());
      itemdto.setBankn(params.getBankAccount());
      itemdto.setKoinh(params.getAccountName());
      itemdtoList.add(itemdto);
    }
    headdto.setItem(itemdtoList);
    datadto.setHead(headdto);
    param.setData(datadto);
    return param;
  }
}
