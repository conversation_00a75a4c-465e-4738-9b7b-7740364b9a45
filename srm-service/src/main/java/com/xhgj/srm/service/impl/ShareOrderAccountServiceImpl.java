package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.dto.account.AccountDetailDTO;
import com.xhgj.srm.dto.account.OrderAccountInvoiceInfo;
import com.xhgj.srm.dto.account.OrderInfoDTO;
import com.xhgj.srm.dto.account.ProductInfoDTO;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.dao.OrderDetailDao;
import com.xhgj.srm.jpa.dao.SupplierPerformanceDao;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderAccount;
import com.xhgj.srm.jpa.entity.OrderAccountToOrder;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.repository.OrderAccountRepository;
import com.xhgj.srm.jpa.repository.OrderAccountToOrderRepository;
import com.xhgj.srm.service.OrderAcceptService;
import com.xhgj.srm.service.ShareOrderAccountInvoiceService;
import com.xhgj.srm.service.ShareOrderAccountService;
import com.xhgj.srm.service.SharePlatformService;
import com.xhiot.boot.core.common.exception.CheckException;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ShareOrderAccountServiceImpl implements ShareOrderAccountService {

  private final String url;

  public ShareOrderAccountServiceImpl(SrmConfig config) {
    this.url = config.getUploadUrl();
  }

  @Resource
  private OrderAccountRepository orderAccountRepository;
  @Resource
  private OrderDetailDao orderDetailDao;
  @Resource
  private ShareOrderAccountInvoiceService shareOrderAccountInvoiceService;
  @Resource
  private OrderAccountToOrderRepository orderAccountToOrderRepository;
  @Resource
  private OrderDao orderDao;
  @Resource
  private SupplierPerformanceDao supplierPerformanceDao;
  @Resource
  private HttpUtil httpUtil;
  @Resource
  private OrderAcceptService orderAcceptService;
  @Resource
  private SharePlatformService sharePlatformService;

  @Override
  public AccountDetailDTO getAccountDetail(String accountId) {
    OrderAccount orderAccount = orderAccountRepository.findById(accountId).orElseThrow(() -> CheckException.noFindException(OrderAccount.class, accountId));
    AccountDetailDTO accountDetailDTO = new AccountDetailDTO(orderAccount);
    List<String> orderIdList = this.getOrderIdLIstByOrderAccountId(orderAccount.getId());
    if (CollUtil.isNotEmpty(orderIdList)) {
      accountDetailDTO.setOrderInfoList(CollUtil.emptyIfNull(
          this.getByOrderIdList(orderIdList)).stream().map(
          order -> {
            String name = sharePlatformService.findNameByCode(order.getType());
            OrderInfoDTO orderInfoDTO = new OrderInfoDTO(order, name);
            orderInfoDTO.setSignVoucherState(orderAcceptService.getAcceptState(order.getId()));
            return orderInfoDTO;
          }
      ).collect(Collectors.toList()));
    }else{
      accountDetailDTO.setOrderInfoList(ListUtil.toList());
    }
    List<ProductInfoDTO> productInfoList = new ArrayList<>();
    for (String orderId : orderIdList) {
      // 商品明细
      productInfoList.addAll(CollUtil.emptyIfNull(
          orderDetailDao.getOrderDetailByOrderId(orderId)).stream().map(ProductInfoDTO::new
      ).collect(Collectors.toList()));
    }
    accountDetailDTO.setProductInfoList(productInfoList);
    // 获取发票信息
    List<OrderAccountInvoiceInfo> orderAccountInvoiceInfoList =
        shareOrderAccountInvoiceService.getByAccountId(orderAccount.getId(), url);
    accountDetailDTO.setOrderAccountInvoiceInfoList(orderAccountInvoiceInfoList);
    //是否是新订单 订单中有一个没有销售单号即为旧单
    List<String> saleNoList =
        orderDao.getByOrderIdList(orderIdList).stream().map(Order::getSaleOrderNo)
            .collect(Collectors.toList());
    if(saleNoList.contains(null)){
      accountDetailDTO.setIsNew(false);
    }else {
      accountDetailDTO.setIsNew(true);
    }
    String supplierId = orderAccount.getSupplierId();
    String platformCode = orderAccount.getPlatformCode();
    SupplierPerformance supplierPerformance =
        supplierPerformanceDao.getBySupplierIdAndPlatformCode(supplierId, platformCode);
    if (supplierPerformance != null) {
      String businessLeaderName = getOaUserName(supplierPerformance.getBusinessLeader());
      accountDetailDTO.setBusinessLeader(businessLeaderName);
      String dockingAssistantName = getOaUserName(supplierPerformance.getDockingAssistant());
      accountDetailDTO.setDockingAssistant(dockingAssistantName);
    }
    return accountDetailDTO;
  }

  private String getOaUserName(String oaId) {
    String name = "";
    if (StrUtil.isNotBlank(oaId)) {
      JSONObject userJson = httpUtil.getOAUserInfoById(oaId);
      if (userJson != null) {
        name = userJson.containsKey("name") ? String.valueOf(userJson.get("name")) : "";
      }
    }
    return name;
  }

  public List<Order> getByOrderIdList(List<String> orderIdList) {
    if (CollUtil.isEmpty(orderIdList)) {
      return new ArrayList<>();
    }
    return orderDao.getByOrderIdList(orderIdList);
  }

  public List<String> getOrderIdLIstByOrderAccountId(String orderAccountId) {
    Assert.notBlank(orderAccountId);
    return CollUtil.emptyIfNull(
            orderAccountToOrderRepository.getAllByOrderAccountIdAndState(orderAccountId, Constants.STATE_OK))
        .stream().map(OrderAccountToOrder::getOrderId).collect(Collectors.toList());
  }
}
