package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.common.utils.runner.JedisUtil;
import com.xhgj.srm.jpa.entity.OrderPayment;
import com.xhgj.srm.jpa.entity.OrderPaymentToOrder;
import com.xhgj.srm.jpa.repository.OrderPaymentRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentToOrderRepository;
import com.xhgj.srm.service.OrderPaymentToOrderService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-03-16 15:39
 */
@Service
@Slf4j
public class OrderPaymentToOrderServiceImpl implements OrderPaymentToOrderService {

  @Autowired private OrderPaymentToOrderRepository repository;
  @Resource
  private RedissonClient redissonClient;
  @Resource
  private OrderPaymentRepository orderPaymentRepository;
  @Override
  public BootBaseRepository<OrderPaymentToOrder, String> getRepository() {
    return repository;
  }
  private static final String PAYMENT_NO_PREFIX = "FKSH";
  @Resource
  private JedisUtil jedisUtil;

  public static final String FKD_DATE_SUM_COUNTER = "fkd_date_sum_counter"; // 付款单日期加数量

  @Override
  public OrderPaymentToOrder createOrderPaymentToOrder(
      String orderPaymentId,
      String relationId,
      String type,
      long createTime,
      String content,
      BigDecimal applyPrice) {
    Assert.notBlank(orderPaymentId);
    OrderPaymentToOrder orderPaymentToOrder = new OrderPaymentToOrder();
    orderPaymentToOrder.setOrderPaymentId(orderPaymentId);
    orderPaymentToOrder.setRelationId(relationId);
    orderPaymentToOrder.setType(type);
    orderPaymentToOrder.setCreateTime(createTime);
    orderPaymentToOrder.setState(Constants.STATE_OK);
    orderPaymentToOrder.setContent(content);
    orderPaymentToOrder.setApplyPrice(applyPrice);
    return save(orderPaymentToOrder);
  }

  @Override
  public long getCountByOrderPaymentIdAndTypeAndState(String relationId, String type) {
    return repository.countByOrderPaymentIdAndTypeAndState(relationId, type, Constants.STATE_OK);
  }

  @Override
  public List<String> getOrderIdListByPaymentId(String paymentId) {
    return CollUtil.emptyIfNull(
            repository.getAllByOrderPaymentIdAndStateAndType(
                paymentId, Constants.STATE_OK, Constants_order.ORDER_TO_PAYMENT_TYPE_ORDER_ID))
        .stream()
        .map(OrderPaymentToOrder::getRelationId)
        .collect(Collectors.toList());
  }

  @Override
  public List<OrderPaymentToOrder> getErpPaymentNoListByPaymentId(String paymentId) {
    return repository.getAllByOrderPaymentIdAndStateAndType(
                paymentId,
                Constants.STATE_OK,
                Constants_order.ORDER_TO_PAYMENT_TYPE_ERP_PAYMENT_NO);

  }

  @Override
  public String getAllByRelationIdAndStateAndType(String orderId) {
    return Optional.ofNullable(repository.getFirstByRelationIdAndStateAndType(
        orderId,
        Constants.STATE_OK,
        Constants_order.ORDER_TO_PAYMENT_TYPE_ORDER_ID)).map(OrderPaymentToOrder::getOrderPaymentId)
        .orElseGet(() -> "");
  }

  @Override
  public OrderPayment createOrderPayment(String paymentStatus, BigDecimal applyPrice,
      String submitMan, String submitId, String remark, String supplierId) {
      long createTime = System.currentTimeMillis();
      OrderPayment orderPayment = new OrderPayment();
      orderPayment.setPaymentStatus(paymentStatus);
      orderPayment.setApplyPrice(applyPrice);
      orderPayment.setSubmitMan(submitMan);
      orderPayment.setSubmitId(submitId);
      orderPayment.setCreateTime(createTime);
      orderPayment.setRemark(remark);
      orderPayment.setSupplierId(supplierId);
      orderPayment.setState(Constants.STATE_OK);
      DateTime dateTime = DateTime.of(createTime);
      long count = orderPaymentRepository.countByCreateTimeAfter(DateUtil.beginOfDay(dateTime).getTime());
      String finalCount =
          getOrderPaymentCountByDate(DateUtils.formatTimeStampToNormalDate(createTime),
           String.valueOf(count));
      orderPayment.setPaymentNo(
          PAYMENT_NO_PREFIX + DateUtil.format(dateTime, DatePattern.PURE_DATE_PATTERN)
              + StrUtil.padPre(finalCount, 4, "0"));
      return orderPaymentRepository.save(orderPayment);
  }


  private synchronized String getOrderPaymentCountByDate(String date,String num){
    if (jedisUtil.exists(FKD_DATE_SUM_COUNTER)) {
        String redisDateSum = jedisUtil.get(FKD_DATE_SUM_COUNTER);
        String dateStr = !StringUtils.isNullOrEmpty(redisDateSum)&&redisDateSum.length()>=10?
            redisDateSum.substring(0, 10):"";
        if (!StringUtils.isNullOrEmpty(dateStr)&&Objects.equals(dateStr,date)) {
            num = redisDateSum.substring(10);
           }else{
            num = num;
          }
        }else{
           num = num;
         }
        // 转成数字型
        int  intNum = Integer.parseInt(num)+1;
        String  strNum = String.valueOf(intNum);
        jedisUtil.set(FKD_DATE_SUM_COUNTER,date+strNum);
        return num;
  }


}
