package com.xhgj.srm.service;


import com.xhgj.srm.dto.filedConfig.ProcurementFieldDTO;
import com.xhgj.srm.dto.filedConfig.TitleFieldListDTO;
import com.xhgj.srm.jpa.entity.FieldConfig;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

public interface FieldConfigService extends BootBaseService<FieldConfig, String> {

  /**
   * 获取采购申请模板
   * @return
   */
  ProcurementFieldDTO buildProcurementField();

  /**
   * 获取采购订单 订单列表视图
   */
  List<TitleFieldListDTO> getPurchaseOrderV2DefaultView();
}
