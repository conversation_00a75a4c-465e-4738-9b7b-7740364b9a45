package com.xhgj.srm.service;/**
 * @since 2025/2/25 11:38
 */

import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.dto.transferOrder.TransferOrderFillDTO;
import com.xhgj.srm.dto.transferOrder.TransferOrderSearchForm;
import com.xhgj.srm.common.vo.transferOrder.TransferOrderListVO;
import com.xhgj.srm.dto.transferOrder.TransferOrderSaveForm;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_076Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSTransferOrderResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSTransferOrderReturnResult;
import com.xhgj.srm.vo.transferOrder.TransferOrderVO;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/2/25 11:38:47
 *@description 调拨单业务方法
 */
public interface TransferOrderService {

  /**
   * 调拨单保存
   * @return 调拨单id
   */
  String save(TransferOrderSaveForm saveForm);

  /**
   * 调拨单分页列表
   */
  PageResult<TransferOrderListVO> getPage(TransferOrderSearchForm form);

  /**
   * 调拨单详情
   */
  TransferOrderVO getDetail(String id);

  /**
   * 调拨单删除 批量
   */
  void delete(String id);

  /**
   * 飞搭审核回调
   */
  void auditCallBack(ApprovalResult approvalResult, byte status);

  /**
   * wms仓库执行回调
   */
  WMSTransferOrderReturnResult wmsCallback(WMSTransferOrderResult form);

  /**
   * sap执行
   * @param id
   */
  MM_076Result sapProcess(String id);

  /**
   * 下载调拨单导入模板
   * @param fillDTOList
   * @return
   */
  byte[] downloadDetail(List<TransferOrderFillDTO> fillDTOList);

  /**
   * 导入调拨单
   * @param file
   * @return
   */
  List<TransferOrderFillDTO> fill(MultipartFile file) throws IOException;
}
