package com.xhgj.srm.service.impl;

import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormReviewStatus;
import com.xhgj.srm.service.DingTalkApprovalCallback;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2WarehouseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: fanghuanxu
 * @Date: 2025/2/25 14:29
 * @Description: 调拨单审批结果事件处理
 */
@Service
public class SupplierOrderWarehouseCallbackImpl implements DingTalkApprovalCallback {

  @Resource
  PurchaseOrderV2WarehouseService purchaseOrderV2WarehouseService;


  @Override
  public void doPassHandle(ApprovalResult approvalResult) {
    purchaseOrderV2WarehouseService.auditCallBack(approvalResult, SupplierOrderFormReviewStatus.NORMAL);
  }

  @Override
  public void doRejectHandle(ApprovalResult approvalResult) {
    purchaseOrderV2WarehouseService.auditCallBack(approvalResult, SupplierOrderFormReviewStatus.FEI_DA_REJECT);
  }
}
