package com.xhgj.srm.service;

import com.xhgj.srm.jpa.dto.purchase.order.SupplierOrder2Details;
import com.xhgj.srm.v2.dto.purchaseOrder.SupplierOrder2V2Details;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/5 19:52
 */
public interface ShareSupplierOrderDetailService
    extends BootBaseService<SupplierOrderDetail, String> {

  /**
   * 关联表单 id 查询订单详情
   *
   * @param orderToFormId 关联表单 id 必传
   */
  List<SupplierOrderDetail> getByOrderToFormId(String orderToFormId);

  /** 获取所有的订单 id */
  List<String> getAllIds();

  /**
   * 根据订单 ids 获取订单详情
   * @param orderIds
   * @return
   */
  List<SupplierOrder2Details> getDetailsByOrderIds(List<String> orderIds);

  /**
   * 根据订单 ids 获取订单详情
   * @param orderIds
   * @return
   */
  List<SupplierOrder2V2Details> getDetailsByOrderIdsV2(List<String> orderIds);
}
