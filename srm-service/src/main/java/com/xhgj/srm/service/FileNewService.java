package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.File;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;
import java.util.Optional;

public interface FileNewService extends BootBaseService<File, String> {

  /**
   * 根据关联 id 和关联类型获取附件列表
   *
   * @param relationId 关联 id
   * @param relationType 关联类型
   */
  List<File> getFileListByIdAndType(String relationId, String relationType);


  void delete(String relationId, String relationType);

  void association(String id, String relationId, String relationType);

  /**
   * 查询第一个根据关联id和关联类型
   * @param relationId 关联id
   * @param relationType 关联类型
   * @return Optional<File>
   */
  Optional<File> findFirstByRelationIdAndRelationType(String relationId, String relationType);

  //
  //  /**
  //   * 根据id和类型获取文件集合
  //   * @param id
  //   * @param fileTypeLandingMerchantProductQualification
  //   * @return
  //   */
  //  List<File> getFileList(String id, String fileTypeLandingMerchantProductQualification);


}
