package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.NoticeDao;
import com.xhgj.srm.jpa.dao.SupplierDao;
import com.xhgj.srm.jpa.dao.SupplierPerformanceDao;
import com.xhgj.srm.jpa.entity.Notice;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.repository.NoticeRepository;
import com.xhgj.srm.request.service.third.xhgj.XhgjSMSRequest;
import com.xhgj.srm.service.NoticeService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
  *@ClassName NoticeServiceImpl
  *<AUTHOR>
  *@Date 2023/8/2 10:13
*/
@Service
@Slf4j
public class NoticeServiceImpl implements NoticeService {

  @Autowired
  private NoticeDao noticeDao;
  @Autowired
  private NoticeRepository noticeRepository;
  @Autowired
  private SupplierPerformanceDao performanceDao;
  @Autowired
  private SupplierPerformanceDao supplierPerformanceDao;
  @Autowired
  private SupplierDao supplierDao;
  @Autowired
  private AsyncTaskService asyncTaskService;

  @Override
  public PageResult<Notice> getListByType(String supplierId,String title, Integer rangType,
      int pageNo, int pageSize) {
    if (rangType == null) {
      rangType = 3;
    }
    Page<Notice> page = null;
    //落地上 判断可见范围
    if(2 == rangType || 3 == rangType){
      List<String> codeList = new ArrayList<>();
      List<SupplierPerformance> performanceList = performanceDao.getListBySupplierId(supplierId);
      if(CollUtil.isNotEmpty(performanceList)){
        codeList = performanceList.stream()
            .map(SupplierPerformance::getPlatformCode).collect(Collectors.toList());
      }
      page =
          noticeDao.getPageNew(title, rangType, Integer.parseInt(Constants.STATE_OK),
              String.join(",", codeList), pageNo, pageSize);
    }else {
      page = noticeDao.getPage(title, rangType, Integer.parseInt(Constants.STATE_OK), pageNo,
          pageSize);
    }
    PageResult<Notice> newsPageResult = buildPageResult(page);
    return newsPageResult;
  }

  @Override
  public Notice getNoticeDetail(String id) {
    Notice notice =
        noticeRepository.findById(id).orElseThrow(() -> CheckException.noFindException(Notice.class,
            id));
    return notice;
  }

  @Override
  public PageResult<Notice> getListByTypeManage(String title, Integer rangType, int pageNo,
      int pageSize) {
    Page<Notice> page =
        noticeDao.getPage(title, null, null, pageNo, pageSize);
    PageResult<Notice> newsPageResult = buildPageResult(page);
    return newsPageResult;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void setNotice(String id, String title, String content, String img, Integer rangType,
      String createMan,String fileList,Boolean sendMsg,String codes) {
    Notice notice = new Notice();
    if(StrUtil.isNotEmpty(id)){
      notice = noticeRepository.findById(id)
          .orElseThrow(() -> CheckException.noFindException(Notice.class, id));
    }else {
      //默认上架状态
      notice.setState(Integer.parseInt(Constants.STATE_OK));
      notice.setCreateTime(System.currentTimeMillis());
      notice.setCreateMan(createMan);
    }
    notice.setTitle(title);
    notice.setContent(content);
    notice.setImg(img);
    notice.setRangType(rangType);
    notice.setFileList(fileList);
    notice.setSendMsg(sendMsg);
    if(rangType == 2){
      if(StrUtil.isNotEmpty(codes)){
        notice.setCodes(codes);
      }else {
        notice.setCodes(null);
      }
    }else {
      notice.setCodes(null);
    }
    noticeRepository.save(notice);

    //发送短信
    if(sendMsg){
      //获取需要发短信的供应商，落地商,全部
      List<String> supplierId = new ArrayList<>();
      List<String> supplierIdNew = new ArrayList<>();

        if(StrUtil.isNotEmpty(codes)){
          List<SupplierPerformance> supplierPerformanceList =
              supplierPerformanceDao.getByCodeList(Arrays.asList(codes.split(",")));
          if(CollUtil.isNotEmpty(supplierPerformanceList)){
            supplierIdNew.addAll(supplierPerformanceList.stream()
                .map(SupplierPerformance::getSupplierId).collect(Collectors.toList()));
          }
          for (String s : supplierIdNew) {
            Supplier supplier = supplierDao.getSupplierById(s);
            if(!StrUtil.equals(supplier.getCooperateType(),Constants.COOPERATE_TYPE_SUPPLIER)){
              supplierId.add(supplier.getId());
            }
          }
        }else {
          List<Supplier> supplierList = new ArrayList<>();
          supplierList = supplierDao.getAllByCooperateType(rangType.toString());
          if(CollUtil.isNotEmpty(supplierList)){
            if(StrUtil.equals(Constants.COOPERATE_TYPE_SUPPLIER,rangType.toString())){
              supplierId.addAll(supplierList.stream().filter(s->
                      !StrUtil.equals(Constants.COOPERATE_TYPE_LANDER,s.getCooperateType()))
                  .map(Supplier::getId).collect(Collectors.toList()));
            }else if(StrUtil.equals(Constants.COOPERATE_TYPE_LANDER,rangType.toString())){
              supplierId.addAll(supplierList.stream().filter(s->
                      !StrUtil.equals(Constants.COOPERATE_TYPE_SUPPLIER,s.getCooperateType()))
                  .map(Supplier::getId).collect(Collectors.toList()));
            }else {
              supplierId.addAll(supplierList.stream()
                  .map(Supplier::getId).collect(Collectors.toList()));
            }
          }
        }
      asyncTaskService.noticeSendMsg(title,supplierId);
    }

  }

  @Override
  public void noticeUpOrDown(List<String> ids, int state) {
    List<Notice> list = new ArrayList<>();
    for (String id : ids) {
      Notice notice = noticeRepository.findById(id)
          .orElseThrow(() -> CheckException.noFindException(Notice.class, id));
      notice.setState(state);
      list.add(notice);
    }
    noticeRepository.saveAll(list);
  }

  @Override
  public Notice getLastNotice(String supplierId, Integer rangType) {
    if (rangType == null) {
      rangType = 3;
    }
    Notice notice;
    //落地商 判断可见范围
    if(rangType == 2 || rangType == 3){
      List<String> codeList = new ArrayList<>();
      List<SupplierPerformance> performanceList = performanceDao.getListBySupplierId(supplierId);
      if(CollUtil.isNotEmpty(performanceList)){
        codeList = performanceList.stream()
            .map(SupplierPerformance::getPlatformCode).collect(Collectors.toList());
      }
      notice =
          noticeDao.getLastNoticeNew(rangType, Integer.parseInt(Constants.STATE_OK),
              String.join(",", codeList));
    }else {
      notice = noticeDao.getLastNotice(rangType, Integer.parseInt(Constants.STATE_OK));
    }

    return notice;
  }

  public static <T> PageResult<T> buildPageResult(Page<T> page) {
    return new PageResult(page.getContent(), page.getTotalElements(), page.getTotalPages(), page.getNumber() + 1, page.getSize());
  }
}
