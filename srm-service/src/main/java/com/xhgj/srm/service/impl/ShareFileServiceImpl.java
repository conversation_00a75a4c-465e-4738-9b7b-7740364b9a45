package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.utils.oss.service.OssService;
import com.xhgj.srm.dto.FileDTO;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.service.ShareFileService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2024/07/11 15:55
 */
@Service
@Slf4j
public class ShareFileServiceImpl implements ShareFileService {
  @Autowired private FileDao dao;
  @Resource private FileRepository fileRepository;
  @Resource private OssService ossService;

  // 上传文件的默认保存路径
  protected String configPath = "srm/upload";
  @Override
  public Optional<File> findFirstByRelationIdAndRelationType(String relationId, String relationType) {
    Assert.notBlank(relationId);
    Assert.notBlank(relationType);
    return fileRepository.findFirstByRelationIdAndRelationTypeAndState(relationId, relationType,
        Constants.STATE_OK);
  }
  @Override
  public List<File> getFileListByIdAndType(String relationId, String relationType) {
    List<File> list = dao.getFileListBySId(relationId, relationType);
    return CollUtil.emptyIfNull(list);
  }

  @Override
  public List<File> uploadFile(
      MultipartFile[] files, String uniqueId, String path, String type, String createUserId) {
    List<File> res = new ArrayList<>();
    long currentTimeMillis = System.currentTimeMillis();
    for (MultipartFile f : files) {
      String filename = f.getOriginalFilename();
      if (filename == null) {
        throw new CheckException("获取文件异常，请联系管理员。");
      }
      String newFilePath = ""; // 回传文件存放路径
      String newFileName = ""; // 回传文件名称
      String ossPath = "";
      newFileName = System.currentTimeMillis() + "." + FileUtil.getSuffix(filename); // 文件新名称
      if (!StringUtils.isNullOrEmpty(uniqueId)) {
        newFilePath = configPath + "/" + path + "/" + uniqueId + "/" + newFileName; // 文件存放路径
        ossPath = configPath + "/" + path + "/" + uniqueId;
      } else {
        newFilePath = configPath + "/" + path + "/" + newFileName; // 文件存放路径
        ossPath = configPath + "/" + path;
      }
      // 上传到OSS
      boolean result = ossService.addHeadImage(f, ossPath, newFileName);
      if (!result) {
        log.error("文件上传oss失败:" + filename);
      }
      File newFile = new File();
      newFile.setRelationType(type);
      newFile.setUploadMan(createUserId);
      newFile.setRelationId(uniqueId);
      newFile.setName(newFileName);
      newFile.setUrl(newFilePath);
      newFile.setDescription(filename);
      // 附件类型
      String fileLast = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
      String fileType = Constants.FILE_TYPE_PICTURE;
      if ("doc".equalsIgnoreCase(fileLast)
          || "docx".equalsIgnoreCase(fileLast)
          || "pdf".equalsIgnoreCase(fileLast)
          || "ppt".equalsIgnoreCase(fileLast)
          || "excel".equalsIgnoreCase(fileLast)) {
        fileType = Constants.FILE_TYPE_WORD;
      }
      if ("jpg".equalsIgnoreCase(fileLast)
          || "png".equalsIgnoreCase(fileLast)
          || "bmp".equalsIgnoreCase(fileLast)
          || "gif".equalsIgnoreCase(fileLast)) {
        fileType = Constants.FILE_TYPE_PICTURE;
      }
      if ("rar".equalsIgnoreCase(fileLast) || "zip".equalsIgnoreCase(fileLast)) {
        fileType = Constants.FILE_TYPE_RARZIP;
      }
      newFile.setType(fileType);
      newFile.setState(Constants.STATE_OK);
      newFile.setCreateTime(currentTimeMillis);
      fileRepository.save(newFile);
      res.add(newFile);
    }
    return res;
  }
  @Override
  public List<FileDTO> uploadOrderDescription(MultipartFile[] files, String uniqueId, String path){
    List<FileDTO> res = new ArrayList<>();
    for (MultipartFile f : files) {
      String filename = f.getOriginalFilename();
      if (filename == null) {
        throw new CheckException("获取文件异常，请联系管理员。");
      }
      String newFilePath = ""; // 回传文件存放路径
      String newFileName = ""; // 回传文件名称
      String ossPath = "";
      newFileName = System.currentTimeMillis() + "." + FileUtil.getSuffix(filename); // 文件新名称
      if (!StringUtils.isNullOrEmpty(uniqueId)) {
        newFilePath = configPath + "/" + path + "/" + uniqueId + "/" + newFileName; // 文件存放路径
        ossPath = configPath + "/" + path + "/" + uniqueId;
      } else {
        newFilePath = configPath + "/" + path + "/" + newFileName; // 文件存放路径
        ossPath = configPath + "/" + path;
      }
      // 上传到OSS
      boolean result = ossService.addHeadImage(f, ossPath, newFileName);
      if (!result) {
        log.error("文件上传oss失败:" + filename);
      }
      FileDTO newFile = new FileDTO();
      newFile.setName(filename);
      newFile.setUrl(newFilePath);
      res.add(newFile);
    }
    return res;
  }

}
