package com.xhgj.srm.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.repository.SupplierPerformanceRepository;
import com.xhgj.srm.service.TempSupplierPerformanceService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-04-19 11:02
 */
@Service
public class TempSupplierPerformanceServiceImpl implements TempSupplierPerformanceService {

  @Autowired private SupplierPerformanceRepository repository;

  @Override
  public BootBaseRepository<SupplierPerformance, String> getRepository() {
    return repository;
  }

  @Override
  public String getSupplierPerformanceDockingPurchaseErpCode(String platformCode,
      String supplierId){
    Assert.notEmpty(platformCode);
    Assert.notEmpty(supplierId);
    return Optional.ofNullable(repository.getFirstByPlatformCodeAndSupplierIdAndStateOrderByUpdateTimeDesc(platformCode,supplierId,
        Constants.STATE_OK)).map(SupplierPerformance::getDockingPurchaseErpCode).orElse(
        StrUtil.EMPTY);
  }


}
