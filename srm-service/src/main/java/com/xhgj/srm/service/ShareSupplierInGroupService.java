package com.xhgj.srm.service;

import com.xhgj.cloud.dock.provider.support.dto.DockSupplier;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.SupplierInGroup;

public interface ShareSupplierInGroupService {

  /**
   * @param groupCode 组织编码
   * @param supplierId 供应商id
   */
  SupplierInGroup createIfNotExist(String groupCode, String supplierId,
      EntryRegistrationLandingMerchant landingMerchant,
      EntryRegistrationOrder entryRegistrationOrder);

  /**
   * 构建对接供应商对象
   *
   * @param id 供应商 id
   */
  DockSupplier buildDockSupplier(String id, Group group, String noticePersonId);


  /**
   * 构建对接供应商和银行信息对象
   * @param id 供应商 id
   */
  DockSupplier buildDockSupplierWithBank(String id);
}
