/**
 * @since 2025/4/15 13:21
 */

import cn.hutool.core.collection.ListUtil;
import com.xhgj.srm.api.ApiPortalApplication;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.TestOne;
import com.xhgj.srm.jpa.entity.TestOneItem;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.TestOneItemRepository;
import com.xhgj.srm.jpa.repository.TestOneRepository;
import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.v2.dao.SupplierOrderToFormV2Dao;
import com.xhgj.srm.v2.dao.impl.SupplierOrderDetailV2DaoImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.List;
import java.util.Optional;

/**
 *<AUTHOR>
 *@date 2025/4/15 13:21:39
 *@description
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = ApiPortalApplication.class)
public class ShardingsphereTest {

  @Resource
  TestOneRepository testOneRepository;
  @Resource
  TestOneItemRepository testOneItemRepository;
  @Resource
  private DataSource dataSource;
  @Resource
  private SupplierOrderToFormV2Dao supplierOrderToFormV2Dao;
  @Resource
  private SupplierOrderDetailV2DaoImpl supplierOrderDetailV2Dao;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;

  @Test
  @Transactional
  @Rollback(false)
  public void testDataSource4() {
    SupplierOrder supplierOrder = new SupplierOrder();
    supplierOrder.setId("192776974198481305670eab142635f4");
    supplierOrder.setCode("5555");
    SupplierOrder supplierOrder2 = new SupplierOrder();
    supplierOrder2.setId("1927768530938253312c3a16bef3ee94");
    supplierOrder2.setCode("5555");
    supplierOrderRepository.saveAll(ListUtil.toList(supplierOrder, supplierOrder2));
  }

  @Test
  public void testDataSource3() {
    supplierOrderToFormV2Dao.getAllByTypeAndSupplierOrderIdAndStateOrderByTimeAscSQL(
        SupplierOrderFormType.DELIVER.getType(),
        "19258337884247203846c2661891a634"
    );
  }

  @Test
  public void testDataSource2() {
    List<SupplierOrderDetailV2> test = supplierOrderDetailV2Dao.test();
  }

  @Test
  public void testDataSource() {
    List<SupplierOrderToFormV2> supplierOrderFormByTypeAndState =
        supplierOrderToFormV2Dao.getSupplierOrderFormByTypeAndState(
            SupplierOrderFormType.WAREHOUSING.getType(), "1925004019999182848c21cf0313e714");
    SupplierOrderToFormV2 supplierOrderToFormV2 = supplierOrderFormByTypeAndState.get(0);
    System.out.println(supplierOrderToFormV2);
    System.out.println(supplierOrderFormByTypeAndState);
  }

  @Test
  public void test() {
    Optional<TestOne> byId = testOneRepository.findById("1111");
    System.out.println(byId.isPresent());
  }

  /**
   * 插入，不允许跨表，必须指定路由
   */
  @Test
  public void test2() {
    TestOne testOne = new TestOne();
    testOne.setVersion("2");
    testOneRepository.save(testOne);
  }

  @Test
  public void test3() {
    TestOne firstByVersion = testOneRepository.findFirstByVersion("2");
    System.out.println(firstByVersion);
  }

  @Test
  public void test4() {
    TestOne testOne = new TestOne();
    testOne.setVersion("2");
    testOneRepository.save(testOne);
    TestOneItem item = new TestOneItem();
    item.setTestId(testOne.getId());
    item.setVersion("1");
    testOneItemRepository.save(item);
  }

  @Test
  public void test5() {
    List<TestOne> testOnes = testOneRepository.find();
    System.out.println(testOnes);
  }

  @Test
  public void test6() {
    // 笛卡尔积问题
    ShardingContext.setVersion(VersionEnum.ALL);
    List<TestOne> testOnes = testOneRepository.find();
    System.out.println(testOnes);
  }

  @Test
  public void test7() {
    testOneRepository.findFirstByVersion("2");
  }

  /**
   * 脏数据检查保存
   */
  @Test
  @Transactional
  @Rollback(false)
  public void test8() {
    Optional<TestOne> byId = testOneRepository.findById("1119270832296689664");
    TestOne testOne = byId.get();
    testOne.setVersion("1");
  }
}
