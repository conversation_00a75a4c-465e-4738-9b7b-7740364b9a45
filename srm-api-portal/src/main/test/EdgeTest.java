/**
 * @since 2025/2/24 17:16
 */


import com.xhgj.srm.api.ApiPortalApplication;
import javax.annotation.Resource;
import com.xhgj.srm.request.service.third.api.XhgjEdgeApi;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.junit.Test;

/**
 *<AUTHOR>
 *@date 2025/2/24 17:16:54
 *@description
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = ApiPortalApplication.class)
public class EdgeTest {
  @Resource
  private XhgjEdgeApi xhgjEdgeApi;

  @Test
  public void testGetAllProvinceCity() {
    try {
      xhgjEdgeApi.getAllProvinceCity();
    } catch (Exception e) {
      assert false;
    }
    assert true;
  }

  @Test
  public void testGetAllCountry() {
    try {
      xhgjEdgeApi.getAllCountry();
    } catch (Exception e) {
      assert false;
    }
    assert true;
  }

  @Test
  public void testGetAllIndustry() {
    try {
      xhgjEdgeApi.getAllIndustry();
    } catch (Exception e) {
      assert false;
    }
    assert true;
  }

  @Test
  public void testGetCountryByName() {
    try {
      xhgjEdgeApi.getCountryByName("中国");
    } catch (Exception e) {
      assert false;
    }
    assert true;
  }

  @Test
  public void testPageQueryBankBranchByEdge() {
    try {
      // 使用不同参数组合进行测试
      // 测试1：只提供银行名称
      xhgjEdgeApi.pageQueryBankBranchByEdge("中国银行", null, null);

      // 测试2：只提供银行代码
      xhgjEdgeApi.pageQueryBankBranchByEdge(null, "BOC", null);

      // 测试3：提供银行名称或代码
      xhgjEdgeApi.pageQueryBankBranchByEdge(null, null, "中国银行");

      // 测试4：提供所有参数
      xhgjEdgeApi.pageQueryBankBranchByEdge("中国银行", "BOC", "中国银行");
    } catch (Exception e) {
      assert false;
    }
    assert true;
  }
}
