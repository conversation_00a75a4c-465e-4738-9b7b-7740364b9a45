/**
 * @since 2025/2/24 17:16
 */

import com.xhgj.srm.api.ApiPortalApplication;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.request.service.third.api.DockApi;
import com.xhgj.srm.request.service.third.api.XhgjEdgeApi;
import javax.annotation.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *<AUTHOR>
 *@date 2025/2/24 17:16:54
 *@description
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = ApiPortalApplication.class)
public class DockTest {

  @Resource
  private DockApi dockApi;
  @Resource
  private HttpUtil httpUtil;

  @Test
  public void testGetDockInfo() {
    dockApi.getLogisticsTrajectory(
        "SF3174461896048",
        "shunfeng",
        "18987236458"
    );
  }

  @Test
  public void test2() {
    httpUtil.getLogisticsStatus("SF3174461896048", "shunfeng", "18987236458");
  }
}
