package com.xhgj.srm.api.portal.service;

import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * Created by Geng Shy on 2023/10/13
 * <AUTHOR>
 */
public interface SupplierPerformanceService extends BootBaseService<SupplierPerformance, String> {
  /**
   * 根据供应商 id 和平台编码获取履约信息
   * @param supplierId 供应商 id 必传
   * @param platformCode 平台编码 必传
   * @return
   */
  SupplierPerformance getFirstBySupplierIdAndPlatformCode(String supplierId,String platformCode);

  /**
   *  获取供应商平台数据
   * @param supplierId 供应商id
   * @return
   */
  List<String> getPerformCodeListBySupplierId(String supplierId);
}
