package com.xhgj.srm.api.portal.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.portal.dto.ContractSigningSupplierDTO;
import com.xhgj.srm.api.portal.service.LandingMerchantContractService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.GroupDao;
import com.xhgj.srm.jpa.dao.LandingMerchantContractDao;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.repository.LandingMerchantContractRepository;
import com.xhgj.srm.jpa.repository.SupplierPerformanceRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024-06-20 13:35
 */
@Service
public class LandingMerchantContractServiceImpl implements LandingMerchantContractService {

  @Autowired private LandingMerchantContractDao contractDao;
  @Autowired private LandingMerchantContractRepository repository;
  @Autowired private GroupDao groupDao;
  @Autowired
  private SupplierPerformanceRepository supplierPerformanceRepository;

  @Override
  public BootBaseRepository<LandingMerchantContract, String> getRepository() {
    return repository;
  }


  @Override
  public ContractSigningSupplierDTO getSigningSupplierByPlatformCodeAndSupplierId(String platformCode,
      String supplierId) {
    // Optional.ofNullable(contractDao.getByPlatformCodeAndSupplierId(platformCode, supplierId))
    // 找出groupId
    ContractSigningSupplierDTO result = new ContractSigningSupplierDTO();
    SupplierPerformance supplierPerformance =
        supplierPerformanceRepository.getFirstByPlatformCodeAndSupplierIdAndStateOrderByUpdateTimeDesc(platformCode,
            supplierId, Constants.STATE_OK);
    if (ObjectUtil.isNull(supplierPerformance) || StrUtil.isEmpty(
        supplierPerformance.getLandingContractId())) {
      return null;
    }
    LandingMerchantContract contract = contractDao.get(supplierPerformance.getLandingContractId());
    if (contract == null || StrUtil.isEmpty(contract.getFirstSigningGroupId())) {
      return null;
    }
    result.setBackToBack(ObjectUtil.defaultIfNull(contract.getBackToBack(),false));
    Group group = groupDao.get(contract.getFirstSigningGroupId());
    if (group != null) {
      result.setName(group.getName());
      result.setCode(group.getCode());
      result.setErpCode(group.getErpCode());
    }
    return result;
  }
}
