package com.xhgj.srm.api.portal.service;


import com.xhgj.srm.api.portal.dto.supplier.invoice.OffsetInvoiceParam;
import com.xhgj.srm.api.portal.dto.supplier.invoice.OffsetInvoiceResult;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

public interface InputInvoiceOrderService extends BootBaseService<InputInvoiceOrder, String> {

  /**
   * 发票冲销
   */
  OffsetInvoiceResult offsetInvoice(OffsetInvoiceParam param);
}
