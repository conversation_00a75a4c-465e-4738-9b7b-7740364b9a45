package com.xhgj.srm.api.portal.service.impl;

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.api.portal.service.OrderDetailService;
import com.xhgj.srm.jpa.dao.OrderDetailDao;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.repository.OrderDetailRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.Optional;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * Created by Geng Shy on 2023/10/13
 */
@Service
public class OrderDetailServiceImpl implements OrderDetailService {

  @Resource
  private OrderDetailRepository repository;
  @Resource
  private OrderDetailDao dao;

  @Override
  public BootBaseRepository<OrderDetail, String> getRepository() {
    return repository;
  }

  @Override
  public Optional<OrderDetail> getOrderDetailByOrderIdAndCode(String orderId, String productCode) {
    Assert.notBlank(orderId);
    Assert.notBlank(productCode);
    OrderDetail orderDetail = dao.getOrderDetailByOrderIdAndCode(orderId, productCode);
    if (orderDetail == null) {
      return Optional.empty();
    }
    return Optional.of(orderDetail);
  }
}
