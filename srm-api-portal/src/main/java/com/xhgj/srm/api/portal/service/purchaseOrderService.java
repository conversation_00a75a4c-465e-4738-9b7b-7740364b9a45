package com.xhgj.srm.api.portal.service;

import com.xhgj.srm.api.portal.dto.PurchaseOrderAndCreateTimeDTO;
import com.xhgj.srm.api.portal.dto.PurchaseOrderWithRowParam;
import com.xhgj.srm.api.portal.dto.SAPInterCompanyPurchaseOrderParam;
import com.xhgj.srm.api.portal.dto.purchase.order.ReversalCompletedParam;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

public interface purchaseOrderService extends BootBaseService<SupplierOrder, String> {

  /**
   * 完成仓库冲销
   */
  void doReversalCompleted(ReversalCompletedParam param);

  /**
   * 根据采购申请单号查询采购订单及货期
   *
   * @return key：采购申请单号 value：行信息
   */
  List<PurchaseOrderAndCreateTimeDTO> getPurchaseOrderAndDeliveryTime(
      PurchaseOrderWithRowParam params);

  void pushInterCompanyPurchaseOrderBySAP(SAPInterCompanyPurchaseOrderParam param);
}
