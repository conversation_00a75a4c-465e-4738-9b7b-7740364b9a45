package com.xhgj.srm.api.portal.dto.supplierBrand;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
public class SupplierBrandCheckParam {

  @ApiModelProperty(value = "srm品牌id")
  @NotBlank(message = "srm品牌id不能为空")
  private String srmBrandId;

  @ApiModelProperty(value = "品牌编码")
  private String mpmBrandCode;

  @ApiModelProperty(value = "mpm品牌id")
  private String mpmBrandId;

  @ApiModelProperty(value = "审核时间")
  private Long mpmOperationTime;

  @ApiModelProperty(value = "审核人")
  @NotBlank(message = "审核人不能为空")
  private String mpmOperator;

  @ApiModelProperty(value = "审核意见")
  private String mpmComment;

  @ApiModelProperty(value = "品牌名称(中文)")
  @NotBlank(message = "品牌中文名称不能为空")
  private String brandNameCn;

  @ApiModelProperty(value = "品牌名称(英文)")
  @NotBlank(message = "品牌英文名称不能为空")
  private String brandNameEn;

  @ApiModelProperty(value = "品牌logo地址")
  private String brandLogoUrl;

  @ApiModelProperty(value = "描述信息")
  private String desc;

}
