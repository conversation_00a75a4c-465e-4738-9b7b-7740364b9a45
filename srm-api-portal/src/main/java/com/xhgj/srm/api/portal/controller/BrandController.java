package com.xhgj.srm.api.portal.controller;


import com.xhgj.srm.dto.BrandInfoDTO;
import com.xhgj.srm.service.BrandNewService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Api(tags = {"品牌模块"})
@RestController
@Validated
@RequestMapping("/brand")
@Slf4j
public class BrandController {

  @Autowired
  private BrandNewService brandNewService;

  @ApiOperation(value = "根据品牌编码获取品牌信息")
  @GetMapping(value = "/getBrandInfoByCode")
  public ResultBean<List<BrandInfoDTO>> getBrandInfoDTOListByMpmBrandCode(@NotBlank(message =
      "品牌编码必传")@RequestParam String mpmBrandCode) {
    return new ResultBean<>(brandNewService.getBrandInfoDTOListByMpmBrandCode(mpmBrandCode));
  }
}
