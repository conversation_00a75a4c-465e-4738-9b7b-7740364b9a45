package com.xhgj.srm.api.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * InventoryAddParam
 */
@Data
public class InventoryAddParam {

  @ApiModelProperty("物料编码")
  @JsonProperty("productCode")
  @NotBlank
  private String productCode;

  @ApiModelProperty("组织编码")
  @JsonProperty("groupCode")
  @NotBlank
  private String groupCode;


  @ApiModelProperty("库房名称")
  @JsonProperty("warehouseName")
  private String warehouseName;

  @ApiModelProperty("库房编码")
  @JsonProperty("warehouse")
  @NotBlank
  private String warehouse;


  @ApiModelProperty("批号")
  @JsonProperty("batchNo")
  private String batchNo;

  @ApiModelProperty("名称")
  @JsonProperty("name")
  private String name;

  @ApiModelProperty("库存总数（不含寄售数量）")
  @JsonProperty("inventoryTotalNumber")
  private BigDecimal inventoryTotalNumber;


  @ApiModelProperty("寄售库存数量")
  @JsonProperty("consignmentInventoryNumber")
  private BigDecimal consignmentInventoryNumber;

  @ApiModelProperty("批次锁库数量")
  @JsonProperty("batchLockNumber")
  private BigDecimal batchLockNumber;

  @ApiModelProperty("可用数量")
  @JsonProperty("availableNumber")
  private BigDecimal availableNumber;

  @ApiModelProperty("采购订单号")
  @JsonProperty("orderCode")
  private String orderCode;

  @ApiModelProperty("采购员")
  @JsonProperty("purchaseMan")
  private String purchaseMan;

  @ApiModelProperty("供应商名称")
  @JsonProperty("supplierName")
  private String supplierName;

  @ApiModelProperty("税率")
  @JsonProperty("taxRate")
  private BigDecimal taxRate;

  @ApiModelProperty("未税单价")
  @JsonProperty("unTaxPrice")
  private BigDecimal unTaxPrice;

  @ApiModelProperty("金蝶批次")
  @JsonProperty("kingDeeBatch")
  private String kingDeeBatch;

  @ApiModelProperty("期初备注")
  @JsonProperty("openingNotes")
  private String openingNotes;

  @ApiModelProperty("采购部门")
  @JsonProperty("purchaseDepartment")
  private String purchaseDepartment;

  @ApiModelProperty("采购部门编码")
  @JsonProperty("purchaseDepartmentCode")
  private String purchaseDepartmentCode;

  @ApiModelProperty("在库时间(天)")
  @JsonProperty("inStockTime")
  private String inStockTime;


}
