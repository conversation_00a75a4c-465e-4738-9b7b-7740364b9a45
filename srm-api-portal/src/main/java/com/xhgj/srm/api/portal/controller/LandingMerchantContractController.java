package com.xhgj.srm.api.portal.controller;

import com.xhgj.srm.api.portal.dto.ContractSigningSupplierDTO;
import com.xhgj.srm.api.portal.service.LandingMerchantContractService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024-06-20 13:32
 */
@Api(tags = {"电供合同接口"})
@RestController
@RequestMapping("/LandingMerchantContract")
@Validated
public class LandingMerchantContractController {
  @Resource
  private LandingMerchantContractService service;
  @ApiOperation(value = "根据下单平台和供应商主数据编码获取签约主体", notes = "根据下单平台和供应商主数据编码获取签约主体")
  @GetMapping("getSigningSupplierByPlatformCodeAndSupplierId")
  public ResultBean<ContractSigningSupplierDTO> getSigningSupplierByPlatformCodeAndSupplierId(@NotBlank(message = "下单平台必传")@RequestParam
  String platformCode,
      @NotBlank(message = "供应商主数据编码必传")@RequestParam String supplierId){
    return new ResultBean<>(service.getSigningSupplierByPlatformCodeAndSupplierId(platformCode,supplierId));
  }

}
