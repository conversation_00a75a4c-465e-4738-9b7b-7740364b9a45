package com.xhgj.srm.api.portal.controller;

import com.xhgj.srm.api.portal.dto.filing.FilingCancelParamDTO;
import com.xhgj.srm.api.portal.dto.filing.FilingUpdateParam;
import com.xhgj.srm.api.portal.service.OrderFilingService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.repeat.annotation.RepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("filing")
@Api(tags = {"报备单接口"})
@Slf4j
public class FilingController {

  @Resource
  OrderFilingService orderFilingService;

  @ApiOperation(value = "报备单撤回")
  @PostMapping(value = "filingCancel")
  public ResultBean<Boolean> filingCancel(
      @RequestBody @Validated FilingCancelParamDTO filingCancelParamDTO
  ) {
    orderFilingService.filingCancel(filingCancelParamDTO);
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "更新报备单状态")
  @PostMapping("/updateFilingState")
  @RepeatSubmit
  public ResultBean<Boolean> updateFilingState(@RequestBody @Validated FilingUpdateParam filingUpdateParam) {
    orderFilingService.updateFilingState(filingUpdateParam.getFilingNo(),filingUpdateParam.getFilingState());
    return new ResultBean<>(true, "操作成功!");
  }

}
