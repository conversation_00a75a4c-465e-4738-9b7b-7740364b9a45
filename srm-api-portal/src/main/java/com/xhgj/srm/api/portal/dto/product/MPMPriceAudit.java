package com.xhgj.srm.api.portal.dto.product;/**
 * @since 2025/1/8 8:49
 */

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *<AUTHOR>
 *@date 2025/1/8 08:49:16
 *@description MPM调价审核
 */
@Data
public class MPMPriceAudit {
  /**
   * 产品编码
   */
  @NotBlank(message = "产品编码不能为空")
  private String code;

  /**
   * 商品名称
   */
  @NotBlank(message = "商品名称不能为空")
  private String name;

  /**
   * 审核结果
   */
  @NotNull(message = "审核结果不能为空")
  private Boolean success;

  /**
   * 审核未通过原因
   */
  private String reason;

  /**
   * 供应商编码
   */
  @NotBlank(message = "供应商编码不能为空")
  private String supplierCode;
}
