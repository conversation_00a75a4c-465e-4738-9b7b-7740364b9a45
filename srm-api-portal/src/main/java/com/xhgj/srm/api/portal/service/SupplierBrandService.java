package com.xhgj.srm.api.portal.service;
import com.xhgj.srm.api.portal.dto.supplierBrand.SupplierBrandCheckParam;
import com.xhgj.srm.api.portal.dto.supplierBrand.SupplierBrandUpdateParam;
import com.xhgj.srm.jpa.entity.SupplierBrand;
import com.xhiot.boot.framework.jpa.service.BootBaseService;

public interface SupplierBrandService  extends BootBaseService<SupplierBrand, String> {


  /**
   * * mpm审核通过
   *
   * @param supplierBrandCheckParam
   * <AUTHOR>
   * @date 2023/07/19 14:27
   */
  void setBrandPass(SupplierBrandCheckParam supplierBrandCheckParam);

  /**
   * * mpm审核拒绝
   *
   * @param supplierBrandCheckParam
   * <AUTHOR>
   * @date 2023/07/19 14:27
   */
  void setBrandUnpassReason(SupplierBrandCheckParam supplierBrandCheckParam);


  /**
   * * mpm品牌修改同步
   *
   * @param supplierBrandUpdateParam
   * <AUTHOR>
   * @date 2023/07/19 14:27
   */
  void updateBrand(SupplierBrandUpdateParam supplierBrandUpdateParam);
}
