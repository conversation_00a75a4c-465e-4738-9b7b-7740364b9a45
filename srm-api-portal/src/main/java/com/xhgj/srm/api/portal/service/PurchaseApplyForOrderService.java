package com.xhgj.srm.api.portal.service;


import com.xhgj.srm.api.portal.dto.AddWarehousingEntryParam;
import com.xhgj.srm.api.portal.dto.UpdateExecutionStatusParam;
import com.xhgj.srm.api.portal.dto.purchase.order.PurchaseApplyForOrderAddParam;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhgj.srm.request.service.third.erp.sap.dto.PuchaseOrderAuditParam;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * Created by Geng Shy on 2023/12/10
 */
public interface PurchaseApplyForOrderService extends
    BootBaseService<PurchaseApplyForOrder, String> {

  void addPurchaseApplyForOrder(List<PurchaseApplyForOrderAddParam> params);

  String auditPurchaseOrder(PuchaseOrderAuditParam param);

  void addWarehousingEntry(AddWarehousingEntryParam param);

  void updateExecutionStatus(UpdateExecutionStatusParam param);
}
