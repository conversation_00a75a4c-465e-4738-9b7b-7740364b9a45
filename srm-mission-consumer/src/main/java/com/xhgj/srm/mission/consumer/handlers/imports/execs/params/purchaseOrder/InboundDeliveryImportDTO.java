package com.xhgj.srm.mission.consumer.handlers.imports.execs.params.purchaseOrder;/**
 * @since 2025/3/12 14:42
 */


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *<AUTHOR>
 *@date 2025/3/12 14:42:11
 *@description 入库单导入DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InboundDeliveryImportDTO {
  /**
   * rowIndex
   */
  private Integer rowIndex;

  /**
   * 采购订单号
   */
  private String code;

  /**
   * 关联采购订单明细行id
   */
  private String purchaseOrderDetailRowId;

  /**
   * 物料编码
   */
  private String productCode;


  /**
   * 入库数量
   */
  private String inStockQty;

  /**
   * 退库数量
   */
  private String outStockQty;

  /**
   * 批号
   */
  private String batchNo;

  /**
   * 已开票数量
   */
  private String invoiceQty;

  /**
   * 入库时间
   */
  private String inStockTimeStr;

  /**
   * 入库时间
   */
  private Long inStockTime;

  /**
   * 入库仓库编码
   */
  private String warehouseCode;

  /**
   * 物流公司编码
   */
  private String logisticsCompanyCode;

  /**
   * 物流公司名称
   */
  private String logisticsCompanyName;

  /**
   * 快递单号
   */
  private String trackNum;

  /**
   * SAP物料凭证号
   */
  private String productVoucherNo;

  /**
   * SAP物料凭证行项目
   */
  private String productVoucherLineItem;

  /**
   * 冲销状态
   */
  private String reversalStatus;

  /**
   * 来源 填写SAP
   */
  private String source;

  /**
   * 是否已经失败
   */
  private Boolean isFailed;
  /**
   * 失败原因
   */
  private String failedReason;

  /**
   * inStockTime
   * @return
   */
  public Long getInStockTime() {
    // 创建时间转换为时间戳
    if (StrUtil.isBlank(inStockTimeStr)) {
      return null;
    }
    return DateUtil.parse(inStockTimeStr, "yyyy-MM-dd HH:mm:ss").getTime();
  }
}
