package com.xhgj.srm.mission.consumer.handlers.imports.execs.params.inventory;/**
 * @since 2025/2/20 11:29
 */

import io.swagger.annotations.ApiImplicitParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/2/20 11:29:15
 *@description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InventorySafetyImportDTO {
  /**
   * 库房编码
   */
  private String warehouse;

  /**
   * 库房名称
   */
  private String warehouseName;

  /**
   * 物料编码
   */
  private String productCode;

  /**
   * 安全库存数量
   */
  private BigDecimal minSafetyStock;

  /**
   * 负责人mix
   */
  private String notifiedPersonMix;

  /**
   * 消息通知是否开启
   */
  private String status;

  /**
   * 用户组织code
   */
  private String userGroup;

}
