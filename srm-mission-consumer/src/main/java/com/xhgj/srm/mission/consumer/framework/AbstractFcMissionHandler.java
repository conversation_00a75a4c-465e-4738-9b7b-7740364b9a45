package com.xhgj.srm.mission.consumer.framework;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.interceptor.Interceptor;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.consumer.event.application.MissionCompleteEventPublisher;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.dataProcess.DataProcess;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.NoUniqueBeanDefinitionException;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @since 2024/8/8 9:15
 */
@Slf4j
public abstract class AbstractFcMissionHandler {
  private final FcMissionService fcMissionService;
  private final MissionCompleteEventPublisher missionCompleteEventPublisher;
  private final ApplicationContext applicationContext;

  public AbstractFcMissionHandler(
      FcMissionService fcMissionService,
      MissionCompleteEventPublisher missionCompleteEventPublisher,
      ApplicationContext applicationContext) {
    this.fcMissionService = fcMissionService;
    this.missionCompleteEventPublisher = missionCompleteEventPublisher;
    this.applicationContext = applicationContext;
  }

  public final void handleMission(MissionDispatchParam dispatchParam) {
    if (dispatchParam.getType().equals(MissionTypeEnum.BATCH_TASK_OLD_DATA)) {
      // 旧数据处理专用
      JSONObject jsonObject = JSON.parseObject(dispatchParam.getParams());
      String className = jsonObject.getString("className");
      DataProcess dataProcess = getDataProcess(className);
      if (dataProcess != null) {
        dataProcess.process(dispatchParam);
      }
      return;
    }
    String missionId = dispatchParam.getMissionId();
    Mission mission =
        fcMissionService
            .getOptional(missionId)
            .orElseThrow(() -> CheckException.noFindException(Mission.class, missionId));
    dispatchParam.setUserId(mission.getCreateManId());
    fcMissionService.startTask(missionId);
    String reason = StrUtil.EMPTY;
    String fileName = StrUtil.EMPTY;
    String filePath = StrUtil.EMPTY;
    Boolean forceSuccess = false;
    Boolean prepareDone = false;
    ShardingContext.setVersion(dispatchParam.getParams());
    try {
      log.info("开始数据准备/总数查询阶段...");
      Collection<?> collection = prepareCollection(dispatchParam);
      prepareDone = true;
      long totalCount;
      if (CollUtil.isNotEmpty(collection)) {
        totalCount = collection.size();
        log.info("数据准备阶段完成，共[{}]条数据", totalCount);
      } else {
        totalCount = getTotalCount();
        log.info("数据总数查询阶段完成，共[{}]条数据", totalCount);
      }
      if (totalCount == 0) {
        throw new CheckException("任务内容为空,请核实");
      }
      fcMissionService.putMissionTotalRow(missionId, totalCount);
      log.info("开始任务执行阶段...");
      MissionCompleteResult result = doMission(collection, dispatchParam);
      log.info("任务执行阶段完成，执行结果【{}】", result.toString());
      long successCount = result.getSuccessCount();
      if (StringUtils.isNullOrEmpty(result.getReason())) {
        if (successCount == 0) {
          reason = "任务执行失败";
        } else if (successCount > 0 && successCount < totalCount) {
          reason = "部分明细失败，请检查";
        }
      }
      fileName = result.getFileName();
      filePath = result.getFilePath();
      forceSuccess = result.getForceSuccess();
    } catch (Throwable e) {
      reason = e.getMessage();
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      // 如果准备阶段未完成，需要从参数中获取文件名和文件路径
      if (Boolean.FALSE.equals(prepareDone)) {
        JSONObject jsonObject =
            JSON.parseObject(StrUtil.emptyToDefault(dispatchParam.getParams(), "{}"));
        filePath = StrUtil.emptyIfNull(jsonObject.getString("filePath"));
        fileName = StrUtil.emptyIfNull(jsonObject.getString("fileName"));
      }
    } finally {
      ShardingContext.clear();
    }
    //  设置任务完成
    fcMissionService.markMissionDone(missionId, fileName, filePath, reason, forceSuccess);
    // 发布完成事件
    missionCompleteEventPublisher.publish(this, mission);
  }

  /**
   * 准备导出集合，在模板中用于获取任务总行数
   *
   * @param dispatchParam 调用参数
   * @return 该方法的出参会作为入参传入 doMission 方法，避免在 doMission 时重复执行查询逻辑（分页的情况请重载 getTotalCount 方法，该方法返回 null
   *     即可）
   */
  protected abstract Collection<?> prepareCollection(MissionDispatchParam dispatchParam);

  /** 获取任务总行数，在 prepareCollection 返回空集合时，会调用该方法设置任务总行数 */
  protected long getTotalCount() {
    return 0L;
  }

  /**
   * 执行任务
   *
   * @param collection 上一步准备好的集合对象，可能为空
   * @param dispatchParam 调用参数
   * @return 任务完成结果
   */
  protected abstract MissionCompleteResult doMission(
      Collection<?> collection, MissionDispatchParam dispatchParam);

  /**
   * 获取任务执行器
   * @param
   * @return
   * @param <T>
   */
  protected <T extends DataProcess> DataProcess getDataProcess(String className) {
    try {
      return applicationContext.getBean(className, DataProcess.class);
    } catch (Exception th) {
      try {
        Class<?> clazz = Class.forName(className);
        if (DataProcess.class.isAssignableFrom(clazz)) {
          try {
            // 尝试使用无参构造函数创建实例
            return (DataProcess) clazz.newInstance();
          } catch (InstantiationException | IllegalAccessException ex) {
            // 构造函数可能不是公共的或需要参数
            throw new ForestRuntimeException("无法实例化类: " + className, ex);
          }
        }
      } catch (Exception e) {
        // 处理类加载异常
        log.error("无法加载类: " + className, e);
      }
    }
    return null;
  }
}
