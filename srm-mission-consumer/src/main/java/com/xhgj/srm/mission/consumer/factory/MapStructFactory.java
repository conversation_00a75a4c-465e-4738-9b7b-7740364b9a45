package com.xhgj.srm.mission.consumer.factory;/**
 * @since 2024/12/5 17:13
 */
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.dto.order.OrderPaymentListQuery;
import com.xhgj.srm.jpa.dto.payment.apply.record.PaymentApplyRecordDaoPageParam;
import com.xhgj.srm.map.domain.BaseMapStruct;
import com.xhgj.srm.mission.consumer.handlers.exports.execs.params.ExportProductParams;
import org.mapstruct.AfterMapping;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 *<AUTHOR>
 *@date 2024/12/5 17:13:45
 *@description
 */
@Mapper
public interface MapStructFactory extends BaseMapStruct  {
  MapStructFactory INSTANCE = Mappers.getMapper(MapStructFactory.class);

  /**
   * update ExportProductParams
   * @param source
   * @param target
   */
  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  void updateExportProductParams(ExportProductParams source, @MappingTarget ExportProductParams target);

  /**
   * jsonObject to PaymentApplyRecordDaoPageParam
   * @param paymentApplyRecordDaoPageParam
   * @return
   */
  PaymentApplyRecordDaoPageParam toPaymentApplyRecordDaoPageParam(PaymentApplyRecordDaoPageParam paymentApplyRecordDaoPageParam);

  /**
   * jsonObject to PaymentApplyRecordDaoPageParam
   */
  PaymentApplyRecordDaoPageParam toPaymentApplyRecordDaoPageParam(JSONObject jsonObject);

  /**
   * 在映射完成后进行额外处理
   */
  @AfterMapping
  default PaymentApplyRecordDaoPageParam toPaymentApplyRecordDaoPageParamAfter(@MappingTarget PaymentApplyRecordDaoPageParam param, JSONObject jsonObject) {
    PaymentApplyRecordDaoPageParam paymentApplyRecordDaoPageParam = JSON.parseObject(jsonObject.toJSONString(), new TypeReference<PaymentApplyRecordDaoPageParam>(){});
    return this.toPaymentApplyRecordDaoPageParam(paymentApplyRecordDaoPageParam);
  }

  /**
   * OrderPaymentListQuery to OrderPaymentListQuery
   * @param orderPaymentListQuery
   * @return
   */
  OrderPaymentListQuery toOrderPaymentListQuery(OrderPaymentListQuery orderPaymentListQuery);

  /**
   * JSONObject to OrderPaymentListQuery
   * @param jsonObject
   * @return
   */
  OrderPaymentListQuery toOrderPaymentListQuery(JSONObject jsonObject);

  @AfterMapping
  default OrderPaymentListQuery toOrderPaymentListQueryAfter(@MappingTarget OrderPaymentListQuery param, JSONObject jsonObject) {
    OrderPaymentListQuery orderPaymentListQuery = JSON.parseObject(jsonObject.toJSONString(), new TypeReference<OrderPaymentListQuery>() {});
    return this.toOrderPaymentListQuery(orderPaymentListQuery);
  }
}
