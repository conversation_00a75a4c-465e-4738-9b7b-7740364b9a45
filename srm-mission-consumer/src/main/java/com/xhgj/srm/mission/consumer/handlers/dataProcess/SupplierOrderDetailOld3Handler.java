package com.xhgj.srm.mission.consumer.handlers.dataProcess;/**
 * @since 2025/4/10 18:37
 */

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.jpa.dto.purchase.order.SupplierOrder2Details;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhgj.srm.jpa.repository.SupplierOrderProductRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.util.LazyLoaderContext;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import com.xhgj.srm.service.ShareSupplierOrderDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SupplierOrderDetailOld3Handler implements DataProcess {

  @Resource
  private SupplierOrderProductRepository supplierOrderProductRepository;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;
  @Resource
  private ShareSupplierOrderDetailService shareSupplierOrderDetailService;

  @Override
  public void process(MissionDispatchParam dispatchParam) {
    JSONObject jsonObject = JSONObject.parseObject(dispatchParam.getParams());
    // 分页参数
    int pageSize = 2000;
    int currentPage = 0;
    boolean hasMoreData = true;
    // 批量保存阈值
    int batchSize = 100;

    // 总处理记录计数
    int totalProcessed = 0;
    // 成功处理记录计数
    int totalSuccess = 0;
    // 失败记录计数
    int totalFailed = 0;

    log.info("开始处理供应商订单产品数据更新任务");
    long startTime = System.currentTimeMillis();
    while (hasMoreData) {
      // 分页查询旧数据
      int finalCurrentPage = currentPage;
      Page<SupplierOrder> oldDataPage = supplierOrderRepository.getOldData(PageRequest.of(finalCurrentPage, pageSize));
      List<SupplierOrder> oldData = oldDataPage.getContent();
      // 如果当前页没有数据或者是最后一页，结束循环
      if (oldData.isEmpty() || !oldDataPage.hasNext()) {
        hasMoreData = false;
      }
      List<String> supplierOrderIds =
          oldData.stream().map(SupplierOrder::getId).collect(Collectors.toList());
      List<SupplierOrder2Details> supplierOrder2Details = LazyLoaderContext.lazyLoad(() -> shareSupplierOrderDetailService.getDetailsByOrderIds(supplierOrderIds));
      List<SupplierOrderDetail> supplierOrderDetails = supplierOrder2Details.stream()
          .flatMap(supplierOrder2Detail -> supplierOrder2Detail.getSupplierOrderDetails().stream())
          .collect(Collectors.toList());
      List<String> productIds =
          supplierOrderDetails.stream().map(SupplierOrderDetail::getOrderProductId)
              .collect(Collectors.toList());
      productIds.add("-1");
      Map<String, SupplierOrderProduct> productMap = LazyLoaderContext.lazyLoad(() -> supplierOrderProductRepository.findAllById(productIds)
          .stream().collect(Collectors.toMap(SupplierOrderProduct::getId, product -> product)));
      log.info("正在处理第{}页数据，获取到{}条记录", currentPage + 1, oldData.size());
      int currentPageProcessed = 0;
      int currentPageSuccess = 0;
      List<SupplierOrder> updates = new ArrayList<>();
      for (SupplierOrder oldDatum : oldData) {
        try {
          currentPageProcessed++;
          totalProcessed++;
          List<SupplierOrderDetail> details = supplierOrder2Details
              .stream()
              .filter(supplierOrder2Detail -> supplierOrder2Detail.getSupplierOrder().getId()
                  .equals(oldDatum.getId()))
              .flatMap(supplierOrder2Detail -> supplierOrder2Detail.getSupplierOrderDetails().stream())
              .collect(Collectors.toList());
          // 获取相应的product
          List<SupplierOrderProduct> products = details.stream()
              .map(SupplierOrderDetail::getOrderProductId)
              .map(productMap::get)
              .collect(Collectors.toList());
          Set<String> soldToPartySet = products.stream()
              .map(SupplierOrderProduct::getSoldToParty)
              .collect(Collectors.toSet());
          Set<String> salesmanSet = products.stream()
              .map(SupplierOrderProduct::getSalesman)
              .collect(Collectors.toSet());
          Set<String> saleOrderNoSet = details.stream()
              .map(SupplierOrderDetail::getSalesOrderNo)
              .collect(Collectors.toSet());
          Set<String> projectNoSet = details.stream()
              .map(SupplierOrderDetail::getProjectNo)
              .collect(Collectors.toSet());
          Set<String> projectNameSet = details.stream()
              .map(SupplierOrderDetail::getProjectName)
              .collect(Collectors.toSet());
          oldDatum.setSoldToParty(safeJoin(soldToPartySet));
          oldDatum.setSalesman(safeJoin(salesmanSet));
//          oldDatum.setSaleOrderNo(safeJoin(saleOrderNoSet));
//          oldDatum.setProjectNo(safeJoin(projectNoSet));
          oldDatum.setProjectName(safeJoin(projectNameSet));
          updates.add(oldDatum);
          currentPageSuccess++;
          totalSuccess++;
          // 每积累指定条数据执行一次批量保存
          if (updates.size() >= batchSize) {
            try {
              supplierOrderRepository.saveAll(updates);
              log.info("成功批量更新{}条供应商订单产品数据，当前页进度：{}/{}，总进度：{}",
                  updates.size(), currentPageProcessed, oldData.size(), totalProcessed);
            }finally {
              updates.clear();
            }
          }

        } catch (Exception e) {
          totalFailed++;
          log.error("处理第{}页数据时发生异常，当前处理进度：{}/{}，总进度：{}，异常信息：{}",
              currentPage + 1, currentPageProcessed, oldData.size(), totalProcessed, e.getMessage());
          continue;
        }
      }
      // 保存最后一批未达到阈值的数据
      if (!updates.isEmpty()) {
        try {
          supplierOrderRepository.saveAll(updates);
          log.info("成功批量更新剩余{}条供应商订单产品数据，当前页共处理：{}/{}，当前页成功：{}，总进度：{}",
              updates.size(), currentPageProcessed, oldData.size(), currentPageSuccess, totalProcessed);
        }finally {
          updates.clear();
        }
      }

      log.info("第{}页处理完成，处理：{}条，成功：{}条，总处理：{}条，总成功：{}条，总失败：{}条",
          currentPage + 1, currentPageProcessed, currentPageSuccess,
          totalProcessed, totalSuccess, totalFailed);
      // 进入下一页
      currentPage++;
    }

    long endTime = System.currentTimeMillis();
    log.info("供应商订单产品数据更新任务完成，总处理：{}条，成功：{}条，失败：{}条，耗时：{}秒",
        totalProcessed, totalSuccess, totalFailed, (endTime - startTime) / 1000);
  }

  private String safeJoin(Set<String> values) {
    if (values == null || values.isEmpty()) {
      return null;
    }

    // Filter out null values and empty strings
    Set<String> filteredValues = values.stream()
        .filter(s -> s != null && !s.isEmpty() && !"null".equals(s))
        .collect(Collectors.toSet());

    return filteredValues.isEmpty() ? null : String.join(",", filteredValues);
  }
}

