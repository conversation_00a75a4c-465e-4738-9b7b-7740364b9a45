package com.xhgj.srm.mission.consumer.handlers.exports.execs.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ProductStockDTO
 */
@Data
public class ProductStockDTO {

  /**
   *物料编码
   */
  @ApiModelProperty(name="物料编码")
  private String productCode;
  /**
   物料名称
   */
  @ApiModelProperty(name="物料名称")
  private String productName;
  /**
   *规格型号
   */
  @ApiModelProperty("规格型号")
  private String model;
  /**
   *单位
   */
  @ApiModelProperty("单位")
  private String unit;
  /**
   * 可售区域
   */
  @ApiModelProperty(name="可售区域")
  private String sellArea;

  /**
   * 库存
   */
  @ApiModelProperty(name="库存")
  private String stock;
}
