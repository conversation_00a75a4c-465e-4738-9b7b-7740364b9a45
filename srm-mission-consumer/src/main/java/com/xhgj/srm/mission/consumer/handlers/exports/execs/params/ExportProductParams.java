package com.xhgj.srm.mission.consumer.handlers.exports.execs.params;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
  *@ClassName ExportProductParams
  *<AUTHOR>
  *@Date 2023/9/10 17:26
*/
@Data
public class ExportProductParams {
  @ApiModelProperty("供应商id")
  private String  supplierId;
  @ApiModelProperty("类目id")
  private String  categoryId;
  @ApiModelProperty("品牌")
  private String  brand;
  @ApiModelProperty("商品名称")
  private String  name;
  @ApiModelProperty("物料编号")
  private String  code;
  @ApiModelProperty("型号")
  private String  model;
  @ApiModelProperty("单位")
  private String  unitCode;
  @ApiModelProperty("平台")
  private String  platformCodes;
  @ApiModelProperty("类型")
  private String  srmProductType;
  @ApiModelProperty("查询方案id")
  private String  schemeId;
  @ApiModelProperty("用户id")
  private String  userId;
  @ApiModelProperty("物料编码集合")
  private String codeList;
  @ApiModelProperty("咸亨上架开始日期")
  private String firstPassStart;
  @ApiModelProperty("咸亨上架结束日期")
  private String firstPassEnd;
  @ApiModelProperty("mdm供应商主数据编码")
  private String mdmCode;
  @ApiModelProperty("平台编码")
  private String platformCode;
  @ApiModelProperty("平台名称")
  private String platformName;
}
