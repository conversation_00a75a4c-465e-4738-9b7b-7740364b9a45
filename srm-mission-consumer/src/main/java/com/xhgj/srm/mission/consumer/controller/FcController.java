package com.xhgj.srm.mission.consumer.controller;

import com.xhgj.srm.mission.consumer.framework.AbstractFcMissionHandler;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/8/8 9:02
 */
@RestController
public class FcController {
  private final AbstractFcMissionHandler fcMissionHandler;

  public FcController(AbstractFcMissionHandler fcMissionHandler) {
    this.fcMissionHandler = fcMissionHandler;
  }

  @ApiOperation("触发任务")
  @RequestMapping("/invoke")
  public ResultBean<Boolean> invoke(@RequestBody MissionDispatchParam param) {
    // 目前默认注入的时导出类型任务的处理器，如要接入其他类型的任务，这里要有根据类型选择处理器的逻辑
    fcMissionHandler.handleMission(param);
    return new ResultBean<>(true);
  }
}
