package com.xhgj.srm.mission.consumer.handlers;

import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.framework.MissionCompleteResult;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2024/8/8 17:17
 */
public interface ExecService {

  /**
   * 执行任务
   * @param collection
   * @param dispatchParam
   * @return
   */
  MissionCompleteResult exec(Collection<?> collection, MissionDispatchParam dispatchParam);

  /**
   * 准备数据
   * @param dispatchParam
   * @return
   */
  Collection<?> prepare(MissionDispatchParam dispatchParam);
}
