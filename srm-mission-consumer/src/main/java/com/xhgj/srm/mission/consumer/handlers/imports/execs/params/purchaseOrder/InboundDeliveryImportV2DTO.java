package com.xhgj.srm.mission.consumer.handlers.imports.execs.params.purchaseOrder;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InboundDeliveryImportV2DTO {

  /**
   * rowIndex
   */
  private Integer rowIndex;

  /**
   * 采购订单号
   */
  private String code;

  /**
   * 关联采购订单明细行id
   */
  private String purchaseOrderDetailRowId;
  /**
   * 入库单号
   */
  private String inboundDeliveryNo;
  /**
   * 审批状态
   */
  private String assessStatus;
  /**
   * 创建时间
   */
  private String createTimeStr;
  /**
   * 过账日期
   */
  private String postingDateStr;

  /**
   * 物料编码
   */
  private String productCode;


  /**
   * 入库数量
   */
  private String inStockQty;

  /**
   * 退库数量
   */
  private String outStockQty;

  /**
   * 批号
   */
  private String batchNo;

  /**
   * 已开票数量
   */
  private String invoiceQty;

//  /**
//   * 入库时间
//   */
//  private String inStockTimeStr;
//
//  /**
//   * 入库时间
//   */
//  private Long inStockTime;

  /**
   * 入库仓库编码
   */
  private String warehouseCode;

  /**
   * 物流公司编码
   */
  private String logisticsCompanyCode;

  /**
   * 物流公司名称
   */
  private String logisticsCompanyName;

  /**
   * 快递单号
   */
  private String trackNum;

  /**
   * SAP物料凭证号
   */
  private String productVoucherNo;

  /**
   * SAP物料凭证行项目
   */
  private String productVoucherLineItem;

  /**
   * 冲销状态
   */
  private String reversalStatus;

  /**
   * 来源 填写SAP
   */
  private String source;
  /**
   * 仓库执行状态
   */
  private String warehouseExecuteStatus;

  /**
   * 是否已经失败
   */
  private Boolean isFailed;
  /**
   * 失败原因
   */
  private String failedReason;

  /**
   * postingDate
   * @return
   */
  public Long getPostingDate() {
    // 创建时间转换为时间戳
    if (StrUtil.isBlank(postingDateStr)) {
      return null;
    }
    return DateUtil.parse(postingDateStr, "yyyy-MM-dd HH:mm:ss").getTime();
  }

  /**
   * createTime
   * @return
   */
  public Long getCreateTime() {
    // 创建时间转换为时间戳
    if (StrUtil.isBlank(createTimeStr)) {
      return null;
    }
    return DateUtil.parse(createTimeStr, "yyyy-MM-dd HH:mm:ss").getTime();
  }
}
