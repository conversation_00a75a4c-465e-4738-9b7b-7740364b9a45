package com.xhgj.srm.mission.consumer.handlers.dataProcess;/**
 * @since 2025/6/6 10:07
 */

import com.xhgj.srm.common.event.impl.SrmSpringEventPublisher;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.entity.v2.index.SupplierOrderIndex;
import com.xhgj.srm.jpa.repository.SupplierOrderIndexRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.sharding.listenner.SupplierOrderEntityListener;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.v2.repository.SupplierOrderV2Repository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SupplierOrderIndexHandler implements DataProcess {

  @Resource
  SupplierOrderRepository supplierOrderRepository;
  @Resource
  SupplierOrderV2Repository supplierOrderV2Repository;
  @Resource
  SrmSpringEventPublisher publisher;
  @Resource
  SupplierOrderIndexRepository supplierOrderIndexRepository;

  // 主动修复不一致数据的任务，每天凌晨2点执行
  //  @Scheduled(cron = "0 0 2 * * ?")
  public void init() {
    log.info("Starting data consistency check and repair");
    boolean hasMoreData = true;
    int pageSize = 1000;
    int currentPage = 0;
    // V1
    while (hasMoreData) {
      Page<SupplierOrder> checker =
          supplierOrderRepository.checker(PageRequest.of(currentPage, pageSize));
      List<SupplierOrder> content = checker.getContent();
      log.info("V1修复不一致数据，当前页数：{}, 总数：{}", currentPage, content.size());
      for (SupplierOrder supplierOrder : content) {
        publisher.asyncPublish(SupplierOrderEntityListener.createMessage(supplierOrder, "UPDATE"));
      }
      currentPage++;
      hasMoreData = currentPage < checker.getTotalPages();
    }
    hasMoreData = true;
    currentPage = 0;
    // V2
    while (hasMoreData) {
      Page<SupplierOrderV2> checker =
          supplierOrderV2Repository.checker( PageRequest.of(currentPage, pageSize));
      List<SupplierOrderV2> content = checker.getContent();
      log.info("V2修复不一致数据，当前页数：{}, 总数：{}", currentPage, content.size());
      for (SupplierOrderV2 supplierOrder : content) {
        publisher.asyncPublish(SupplierOrderEntityListener.createMessage(supplierOrder, "UPDATE"));
      }
      currentPage++;
      hasMoreData = currentPage < checker.getTotalPages();
    }
    log.info("Data consistency check and repair completed");
  }

  // 主动修复supplierIndex中v1和v2表中不存在的数据
  //  @Scheduled(cron = "0 0 2 * * ?")
  public void miss() {
    log.info("Starting repair missing data");
    boolean hasMoreData = true;
    int pageSize = 1000;
    int currentPage = 0;
    while (hasMoreData) {
      Page<SupplierOrderIndex> checker =
          supplierOrderIndexRepository.checkerMiss(PageRequest.of(currentPage, pageSize));
      // 进行删除操作
      List<SupplierOrderIndex> content = checker.getContent();
      log.info("修复缺失数据，当前页数：{}, 总数：{}", currentPage, content.size());
      supplierOrderIndexRepository.deleteAll(content);
      currentPage++;
      hasMoreData = currentPage < checker.getTotalPages();
    }
    log.info("Repair missing data completed");
  }

  @Override
  public void process(MissionDispatchParam dispatchParam) {
    String params = dispatchParam.getParams();
    if (params != null && params.contains("init")) {
      init();
    }
    if (params != null && params.contains("miss")) {
      miss();
    }
  }
}

