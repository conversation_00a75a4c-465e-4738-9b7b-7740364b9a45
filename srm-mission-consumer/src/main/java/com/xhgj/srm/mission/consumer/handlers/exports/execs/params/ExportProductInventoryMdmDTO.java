package com.xhgj.srm.mission.consumer.handlers.exports.execs.params;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

/**
 * ExportProductInventoryMdmDTO
 */
@NoArgsConstructor
@Data
public class ExportProductInventoryMdmDTO {

  @ApiModelProperty("返回信息")
  @JsonProperty("msg")
  private String msg;
  @ApiModelProperty("code码")
  @JsonProperty("code")
  private Integer code;
  @ApiModelProperty("数据")
  @JsonProperty("data")
  private DataDTO data;

  @NoArgsConstructor
  @Data
  public static class DataDTO {
    @ApiModelProperty("页数")
    @JsonProperty("pageNo")
    private Integer pageNo;
    @ApiModelProperty("总条数")
    @JsonProperty("totalPages")
    private Integer totalPages;
    @ApiModelProperty("条数")
    @JsonProperty("pageSize")
    private Integer pageSize;
    @ApiModelProperty("总数")
    @JsonProperty("totalCount")
    private Integer totalCount;
    @ApiModelProperty("数据内容")
    @JsonProperty("content")
    private List<ContentDTO> content;

    @NoArgsConstructor
    @Data
    public static class ContentDTO {

      @ApiModelProperty("物料编码")
      @JsonProperty("code")
      private String code;
      @ApiModelProperty("规格型号")
      @JsonProperty("manuCode")
      private String manuCode;
      @ApiModelProperty("物料名称")
      @JsonProperty("name")
      private String name;
      @ApiModelProperty("单位名称")
      @JsonProperty("unitName")
      private String unitName;

      @JsonProperty("areaInventoryList")
      @ApiModelProperty("区域库存信息")
      private List<AreaInventoryDTO> areaInventoryList;


      @NoArgsConstructor
      @Data
      public static class AreaInventoryDTO {

        @ApiModelProperty("区域库存数量")
        @JsonProperty("inventoryAmount")
        private BigDecimal inventoryAmount;

        @ApiModelProperty("平台编码")
        @JsonProperty("platformCode")
        private String platformCode;

        @ApiModelProperty("物料编码")
        @JsonProperty("productCode")
        private String productCode;

        @ApiModelProperty("指定区域名称")
        @JsonProperty("sysRegionCodeName")
        private String sysRegionCodeName;
      }
    }
  }
}
