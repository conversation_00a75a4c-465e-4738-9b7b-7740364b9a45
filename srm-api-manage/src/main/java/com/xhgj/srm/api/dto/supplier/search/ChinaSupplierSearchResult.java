package com.xhgj.srm.api.dto.supplier.search;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 国内供应商查询结果
 *
 * <AUTHOR>
 * @since 2022/7/14 9:27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChinaSupplierSearchResult extends BaseSupplierSearchResult {

  @ApiModelProperty("曾用名")
  private String usedName;

  @ApiModelProperty("统一社会信用代码")
  private String uscc;

  @ApiModelProperty("省份")
  private String province;

  @ApiModelProperty("城市")
  private String city;

  @ApiModelProperty(value = "行业（用于后续绑定表单字段）")
  private String industry;
}
