package com.xhgj.srm.api.dto;

import cn.hutool.core.util.ObjectUtil;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SupplierOrderDTO {

  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("供应商名称")
  private String enterName;

  @ApiModelProperty("erp code")
  private String erpCode;

  @ApiModelProperty("是否开启供应商接单")
  private String isOpenOrder;

  @ApiModelProperty("折扣比例")
  private String rate;

  @ApiModelProperty("是否开启供应商订单接单")
  private Boolean openSupplierOrder;

  @ApiModelProperty("供应商接单时限")
  private Integer orderReceiveTimeLimit;

  @ApiModelProperty("供应商接单平台")
  private String platform;

  public SupplierOrderDTO(Supplier supplier) {
    this.id = supplier.getId();
    this.enterName = supplier.getEnterpriseName();
    this.erpCode = StringUtils.emptyIfNull(supplier.getMdmCode());
    this.isOpenOrder =
        !StringUtils.isNullOrEmpty(supplier.getIsOpenOrder()) ? supplier.getIsOpenOrder() : "";
    this.rate = !StringUtils.isNullOrEmpty(supplier.getRate()) ? supplier.getRate() : "";
    this.openSupplierOrder =
        ObjectUtil.defaultIfNull(supplier.getOpenSupplierOrder(), Boolean.FALSE);
    this.platform = StringUtils.emptyIfNull(supplier.getPlatformRemove());
    this.orderReceiveTimeLimit = openSupplierOrder ? supplier.getOrderReceiveTimeLimit() : null;
  }
}
