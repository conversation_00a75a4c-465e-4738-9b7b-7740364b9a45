package com.xhgj.srm.api.dto.supplierorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * UserScreeningConditionParam
 */
@Data
public class UserScreeningConditionParam {

  @ApiModelProperty(value = "用户id")
  @NotBlank(message = "用户id不能为空")
  private String userId;

  @ApiModelProperty(value = "筛选参数")
  private String screening;
}
