package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.dto.CheckReversalInventoryParam;
import com.xhgj.srm.api.dto.InventoryLocationDTO;
import com.xhgj.srm.api.dto.InventoryLocationQueryForm;
import com.xhgj.srm.api.dto.InventoryLocationUpdateParam;
import com.xhgj.srm.api.service.InventoryLocationService;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 *
 */
@Api(tags = {"库位管理接口"})
@RestController
@Validated
@RequestMapping("/inventoryLocation")
public class InventoryLocationController extends AbstractRestController{

  @Resource
  private InventoryLocationService inventoryLocationService;

  @GetMapping(value = "getInventoryLocationList")
  @ApiOperation("库位列表分页")
  public ResultBean<PageResult<InventoryLocationDTO>> getInventoryLocationList(@Valid InventoryLocationQueryForm param) {
    return new ResultBean<>(inventoryLocationService.getInventoryLocationList(param));
  }

  @ApiOperation("批量修改库位数据")
  @PostMapping(value = "/batchUpdateInventoryLocation")
  public ResultBean<Boolean> batchUpdateInventoryLocation(@RequestBody @Valid InventoryLocationUpdateParam param) {
    inventoryLocationService.batchUpdateInventoryLocation(param);
    return new ResultBean<>(Boolean.TRUE);
  }

  @ApiOperation(value = "同步MDM库位列表")
  @GetMapping(value = "/syncInventoryLocationListByMDM")
  public ResultBean<Boolean> syncInventoryLocationListByMDM() {
    User user = getUser();
    inventoryLocationService.syncInventoryLocationListByMDM(user);
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation("导入批改库位管理")
  @PostMapping(value = "/importInventoryLocation")
  public ResultBean<Boolean> importInventoryLocation(
      @RequestParam MultipartFile file) {
    User user = getUser();
    inventoryLocationService.importInventoryLocation(file, user);
    return new ResultBean<>(true, "操作成功");
  }

  @GetMapping(value = "/checkInventoryLocation")
  @ApiOperation("库位管理校验")
  public ResultBean<Boolean> checkInventoryLocation(@Valid CheckReversalInventoryParam param) {
    inventoryLocationService.checkInventoryLocation(param);
    return new ResultBean<>(true, "操作成功!");
  }


}
