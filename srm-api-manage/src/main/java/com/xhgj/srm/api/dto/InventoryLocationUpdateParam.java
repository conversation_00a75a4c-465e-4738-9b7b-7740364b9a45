package com.xhgj.srm.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * InventoryLocationUpdateParam
 */
@Data
public class InventoryLocationUpdateParam {


  @ApiModelProperty("是否涉及WMS:1-是，0-否")
  @NotBlank(message = "是否涉及WMS必填")
  private String isWms;

  /**
   * 涉及WMS的业务类型：1-采购订单，2-货物移动
   */
  @ApiModelProperty("涉及WMS的业务类型，多个以英文逗号分隔")
  private String businessType;


  @ApiModelProperty("勾选的数据行")
  @NotEmpty(message = "ids集合必填")
  private List<String> ids;

}
