package com.xhgj.srm.api.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.common.constants.Constants_FieldConfig;
import com.xhgj.srm.dto.filedConfig.IdsDTO;
import com.xhgj.srm.dto.filedConfig.ProcurementFieldConfigDTO;
import com.xhgj.srm.dto.filedConfig.ProcurementFieldDTO;
import com.xhgj.srm.dto.filedConfig.TitleFieldListDTO;
import com.xhgj.srm.dto.filedConfig.UpdateAndSaveProcurementDTO;
import com.xhgj.srm.dto.filedConfig.UpdateProcurementShipperDTO;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ButtonConfigParam;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ButtonTypeConfigItemDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.OrderTypeFieldDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.PurchaseOrderConfigParam;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.PurchaseOrderConfigVo;
import com.xhgj.srm.jpa.entity.TemplateFieldConfig;
import com.xhgj.srm.service.TemplateFieldConfigService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("templateField")
@Api(tags = {"模版字段配置管理接口"})
@Slf4j
public class TemplateFieldConfigController {

  @Resource
  private TemplateFieldConfigService service;

  @ApiOperation(value = "修改或保存采购申请模板")
  @PostMapping(value = "updateAndSaveProcurement", consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResultBean<Boolean> updateAndSaveProcurement(
        @Valid @RequestBody UpdateAndSaveProcurementDTO dto) {
    service.saveOrUpdate(dto.getId(),dto.getName(),dto.getRemark(),
        JSONObject.toJSONString(dto.getProcurementField()),Constants_FieldConfig.BIG_TYPE_PURCHASE_APPLY);
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "修改采购申请字段作用组织")
  @PostMapping(value = "updateShipper", consumes =
      MediaType.APPLICATION_JSON_VALUE)
  public ResultBean<Boolean> updateProcurementShipper(
      @Valid @RequestBody UpdateProcurementShipperDTO dto) {
    service.updateProcurementShipper(dto);
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "获取采购段配置模板列表")
  @GetMapping("findProcurementFieldConfigList")
  public ResultBean<List<ProcurementFieldConfigDTO>> findProcurementFieldConfigList() {
    return new ResultBean<>(service.findConfigListByBigType(Constants_FieldConfig.BIG_TYPE_PURCHASE_APPLY));
  }

  @ApiOperation(value = "获取采购申请模板详情,当 id 不传时是获取模板 ")
  @ApiImplicitParams({@ApiImplicitParam(name = "key", value = "1:id;2:组织编码")})
  @GetMapping("getTemplateFieldInfo")
  public ResultBean<ProcurementFieldDTO> getTemplateFieldInfo(String key, String type) {
    return new ResultBean<>(service.getTemplateFieldInfo(key,  type));
  }

  @ApiOperation(value = "删除当前模版")
  @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "模板 id")})
  @PostMapping("deleteTemplate")
  public ResultBean<Boolean> deleteTemplate(@RequestBody IdsDTO ids) {
    if (CollUtil.isEmpty(ids.getIds())) {
      throw new CheckException("id 不能为空");
    }
    service.deleteTemplate(ids.getIds());
    return new ResultBean<>(true);
  }
  /*****  采购订单  *****/
  @ApiOperation(value = "获取2.0采购订单字段配置列表")
  @GetMapping("/purchaseOrderV2/configList")
  // dto模版可复用之前
  public ResultBean<List<ProcurementFieldConfigDTO>> purchaseOrderV2ConfigList() {
    return new ResultBean<>(service.findConfigListByBigType(Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER));
  }
  // 2. 新增或保存
  @ApiOperation(value = "新增或保存采购订单字段配置(详情保存)")
  @PostMapping("/purchaseOrderV2/save")
  public ResultBean<String> purchaseOrderV2SaveConfig(
      @Valid @RequestBody PurchaseOrderConfigParam param) {
    TemplateFieldConfig config =
        service.saveOrUpdate(param.getId(), param.getName(), param.getRemark(),
            param.getConfigItemDto());
    return new ResultBean<>(config.getId());
  }
  // 3. 修改组织 -> 复用updateShipper
  // 4. 删除 -> 复用deleteTemplate
  // 5. 获取采购订单模板详情 无id时获取默认模板
  @ApiOperation(value = "获取采购订单模板详情,无id时获取默认模板")
  @GetMapping("/purchaseOrderV2/detail")
  public ResultBean<PurchaseOrderConfigVo> purchaseOrderV2Detail(@RequestParam(value = "id",
      required = false) String id) {
    return new ResultBean<>(service.purchaseOrderV2Detail(id));
  }
  // 6. 数据初始化
  @ApiOperation(value = "采购订单配置字段数据初始化 -- 全量")
  @PostMapping("/purchaseOrderV2/fieldInit")
  public ResultBean<Boolean> purchaseOrderV2FieldInit(MultipartFile file) {
    if (file == null) {
      throw new CheckException("文件不能为空");
    }
    service.purchaseOrderV2FieldInit(file);
    return new ResultBean<>(true);
  }

  @ApiOperation("根据组织获取采购订单字段配置")
  @GetMapping("/purchaseOrderV2/orderFieldByOrg")
  public ResultBean<OrderTypeFieldDto> purchaseOrderV2DetailByOrg(@RequestParam(value = "orgCode") String orgCode,
      @RequestParam("orderType") String orderType) {
    return new ResultBean<>(service.orderFieldByOrg(orgCode,orderType));
  }

  @ApiOperation("根据组织获取采购订单列表字段配置")
  @GetMapping("/purchaseOrderV2/listFieldByOrg")
  public ResultBean<List<TitleFieldListDTO>> purchaseOrderV2DetailByOrg(@RequestParam(value =
      "orgCode") String orgCode) {
    return new ResultBean<>(service.listFieldByOrg(orgCode));
  }

  @ApiOperation("根据组织获取新增采购订单弹窗")
  @GetMapping("/purchaseOrderV2/orderPopByOrg")
  public ResultBean<List<OrderTypeFieldDto>> purchaseOrderV2OrderPopByOrg(@RequestParam(value =
      "orgCode") String orgCode) {
    return new ResultBean<>(service.orderPopByOrg(orgCode));
  }

  @ApiOperation("根据组织和订单类型校验订单是否可以下推")
  @GetMapping("/purchaseOrderV2/checkPush")
  public ResultBean<Boolean> purchaseOrderV2CheckPush(@RequestParam(value = "orgCode") String orgCode,
      @RequestParam("orderType") String orderType) {
    service.checkPush(orgCode,orderType);
    return new ResultBean<>(true);
  }

  /** 采购订单按钮 **/
  @ApiOperation(value = "获取2.0采购订单按钮配置列表")
  @GetMapping("/purchaseOrderV2/buttonConfigList")
  public ResultBean<List<ProcurementFieldConfigDTO>> purchaseOrderV2ButtonConfigList() {
    return new ResultBean<>(service.findConfigListByBigType(Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER_BUTTON));
  }

  @ApiOperation(value = "新增或保存采购订单按钮配置(详情保存)")
  @PostMapping("/purchaseOrderV2/buttonSave")
  public ResultBean<String> purchaseOrderV2ButtonSaveConfig(
      @Valid @RequestBody ButtonConfigParam param) {
    TemplateFieldConfig config =
        service.saveOrUpdate(param.getId(), param.getName(), param.getRemark(),
            param.getButtonTypeConfigDto());
    return new ResultBean<>(config.getId());
  }

  @ApiOperation(value = "获取采购订单按钮配置详情")
  @GetMapping("/purchaseOrderV2/buttonDetail")
  public ResultBean<ButtonConfigParam> purchaseOrderV2ButtonDetail(@RequestParam(value = "id",
      required = false) String id) {
    return new ResultBean<>(service.purchaseOrderV2ButtonDetail(id));
  }

  @ApiOperation(value = "采购订单按钮配置字段数据初始化 -- 全量")
  @PostMapping("/purchaseOrderV2/buttonFieldInit")
  public ResultBean<Boolean> purchaseOrderV2ButtonFieldInit(MultipartFile file) throws IOException {
    if (file == null) {
      throw new CheckException("文件不能为空");
    }
    service.purchaseOrderV2ButtonFieldInit(file);
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "根据组织和订单类型获取采购订单按钮配置")
  @GetMapping("/purchaseOrderV2/buttonFieldByOrg")
  public ResultBean<ButtonTypeConfigItemDto> purchaseOrderV2ButtonFieldByOrg(
      @RequestParam(value = "orgCode") String orgCode,
      @RequestParam("orderType") String orderType) {
    return new ResultBean<>(service.buttonFieldByOrg(orgCode, orderType));
  }
}
