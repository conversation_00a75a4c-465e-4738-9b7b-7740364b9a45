package com.xhgj.srm.api.task;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import com.xhgj.srm.api.service.PurchaseOrderService;
import com.xhgj.srm.api.service.SupplierOrderService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/12/19 11:03
 */
@Component
@Slf4j
public class SupplierOrderTask {

  @Autowired private SupplierOrderService service;
  @Resource private PurchaseOrderService purchaseOrderService;
  @Resource private SupplierOrderRepository supplierOrderRepository;

  /** 每天早上 8 点发送自动催单短信通知 */
  @Scheduled(cron = "0 0 8 * * ?")
  private void sendReminderSmsTask() {
    service.sendReminderSmsTask();
  }
  //TODO 待产品确认执行时间
  //  @Scheduled(cron = "0 0 8 * * ?")
  //  private void syncSapPayState() {
  //    paymentApplyRecordService.syncSapPayState();
  //  }

  @Scheduled(cron = "0 0 2 * * ?")
  public void handleStockProgress() {
    AtomicInteger atomicInteger = new AtomicInteger();
    List<SupplierOrder> supplierOrders =
        supplierOrderRepository.findAllByTotalStockInputQtyInAndOrderStateInAndState(
            ListUtil.toList(BigDecimal.ZERO, null),
            ListUtil.toList(SupplierOrderState.WAIT.getOrderState(),
                SupplierOrderState.IN_PROGRESS.getOrderState()), Constants.STATE_OK);
    supplierOrders.stream().parallel().forEach(supplierOrder -> {
      BigDecimal totalStockInputQty = supplierOrder.getTotalStockInputQty();
      purchaseOrderService.setStockProgress(supplierOrder);
      BigDecimal totalStockInputQty1 = supplierOrder.getTotalStockInputQty();
      if (!NumberUtil.equals(totalStockInputQty1, totalStockInputQty)) {
        atomicInteger.getAndIncrement();
      }
    });
    log.warn("处理数量:{}", atomicInteger.get());
  }
}
