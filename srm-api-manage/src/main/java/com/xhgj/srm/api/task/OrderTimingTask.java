package com.xhgj.srm.api.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.service.OrderService;
import com.xhgj.srm.api.service.SupplierUserService;
import com.xhgj.srm.api.task.DTO.SupplierMailboxMappingOrderDTO;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.service.OrderAcceptService;
import com.xhgj.srm.service.SharePlatformService;
import com.xhiot.boot.core.common.util.BootMailUtil;
import com.xhiot.boot.core.common.util.StringUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import static java.util.stream.Collectors.toList;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Shangyi @date 2023/7/3
 */
@Component
@Slf4j
@ConfigurationProperties(prefix = "white-list")
public class OrderTimingTask {

  @Resource
  private OrderDao orderDao;
  @Resource
  private SupplierUserService supplierUserService;
  @Resource
  private OrderService orderService;
  /**
   * 测试环境只能给信息化部门内人员发信息，所以才有以下白名单
   */
  private List<String> eMail;
  @Resource
  private OrderAcceptService orderAcceptService;
  @Resource
  private SharePlatformService platformService;

  /**
   * 每天上午九点给供应商下所有邮箱发送可付款订单邮件消息
   */
  @Scheduled(cron = "0 0 9 * * ?")
  private void sendMailMessageForPayable() {
    log.info("每日上午九点发送可付款订单邮件通知，开始执行。");
    List<Order> orders =
        orderDao.getOrderBySupplierAndPaymentStatus(null, Constants_order.WAIT_APPLY_PAYMENT_TYPE);
    orders = orders.stream().filter(item -> !Constants_order.ORDER_STATE_WITHDRAW.equals(item.getOrderState()))
        .collect(toList());
    //禁止付款的订单不需要发送
    orders = CollUtil.emptyIfNull(orders).stream().filter(order ->
      order.getProhibitionPaymentState() == null || !order.getProhibitionPaymentState()
    ).collect(toList());
    //没有可付款订单
    if (CollUtil.isEmpty(orders)) {
      log.info("没有可付款订单");
      return;
    }
    //没有取到供应商下所有账号邮箱、或者订单supplierId字段缺少时，就无法映射到改集合内
    List<SupplierMailboxMappingOrderDTO> supplierMailboxMappingOrderDTOS =
        doOrderToSupplierMailboxMapping(orders);
    if (CollUtil.isEmpty(supplierMailboxMappingOrderDTOS)) {
      return;
    }
    supplierMailboxMappingOrderDTOS.forEach(dto -> {
      StringBuilder htmlElement = new StringBuilder();
      List<Order> currOrder = dto.getOrders();
      currOrder.forEach(order -> {
        htmlElement.append(generateHTMLElementForPayable(order));
      });
      List<String> mailAddress = dto.getMailAddress();
      mailAddress.forEach(address -> {
        if (CollUtil.isNotEmpty(eMail)) {
          boolean contains = eMail.contains(address);
          if (contains) {
            doSendForPayable(address, htmlElement.toString());
          }
        }else {
          doSendForPayable(address, htmlElement.toString());
        }
      });
      log.info("发送邮箱：{}，发送订单号：{}",dto.getMailAddress(),
          CollUtil.emptyIfNull(dto.getOrders()).stream().map(Order::getOrderNo).collect(Collectors.toList()));
    });
  }

  /**
   * 每天上午九点给供应商下所有邮箱发送订单客户回款完成的通知
   */
  @Scheduled(cron = "0 0 9 * * ?")
  private void sendMailMessageForCustomerReturnProgress() {
    log.info("每日上午九点发送完成客户回款订单的邮件通知，开始执行。");
    Map<String, Long> timeMap = getYesterdayTimestamp();
    List<Order> orders = orderDao.getOrderBySupplierAndCustomerReturnProgress(null,
        Constants_order.RETURN_PROGRESS_ALL, timeMap.get("startTime"), timeMap.get("endTime"));
        // 过滤掉已撤回的订单
    orders = orders.stream().filter(item -> !Constants_order.ORDER_STATE_WITHDRAW.equals(item.getOrderState()))
        .collect(toList());
    //昨天没有回款完成的订单
    if (CollUtil.isEmpty(orders)) {
      log.info("昨天没有完成客户回款的订单");
      return;
    }
    //没有取到供应商下所有账号邮箱、或者订单supplierId字段缺少时，就无法映射到改集合内
    List<SupplierMailboxMappingOrderDTO> supplierMailboxMappingOrderDTOS =
        doOrderToSupplierMailboxMapping(orders);
    if (CollUtil.isEmpty(supplierMailboxMappingOrderDTOS)) {
      log.info("订单和供应商邮箱的映射失败，订单信息缺少supplierId或者供应商没有绑定邮箱");
      return;
    }
    supplierMailboxMappingOrderDTOS.forEach(dto -> {
      StringBuilder htmlElement = new StringBuilder();
      List<Order> currOrder = dto.getOrders();
      currOrder.forEach(order -> {
        htmlElement.append(generateHTMLElementForCustomerReturnProgress(order));
      });
      List<String> mailAddress = dto.getMailAddress();
      mailAddress.forEach(address -> {
        //email在测试环境下不为空
        if (CollUtil.isNotEmpty(eMail)) {
          boolean contains = eMail.contains(address);
          if (contains) {
            doSendForCustomerReturnProgress(address, htmlElement.toString());
          }
        }else {
          doSendForCustomerReturnProgress(address, htmlElement.toString());
        }
      });
      log.info("发送邮箱：{}，发送订单客户订单号：{}",dto.getMailAddress(),
          CollUtil.emptyIfNull(dto.getOrders()).stream().map(Order::getOrderNo).collect(Collectors.toList()));
    });
  }

  public void updateOrderCustomerReturnStatus() {
    log.info("每日定时更新订单回款状态任务开始执行");
    List<String> returnStatus =
        ListUtil.of(Constants_order.RETURN_PROGRESS_ALL);
    List<Order> orders = orderService.getAllOrderByReturnProgressExcludeType(returnStatus, null);
    if (CollUtil.isEmpty(orders)) {
      log.info("每日定时更新订单回款状态，未查询到匹配订单");
    }
    for (Order order : orders) {
      try {
        orderService.updateOrderPayment(order);
      } catch (Exception e) {
        log.error("更新订单回款状态出现异常，订单号：{}，异常信息：{}", order.getOrderNo(), e.getMessage());
      }
    }
    log.info("每日定时更新订单回款状态任务执行完成");
  }

  private void setPaymentConditionInfo(Order order) {
    //1.货物验收->签收凭证通过时间
    //2.对方开票->供应商开票时间
    //3.客户回款->客户回款日期
    //付款条件满足时间:付款发起条件对应的时间中最晚的时间，如果有一个付款条件的时间为空，满足日期展示“-”
    if (StrUtil.isEmpty(order.getPaymentCondition())) {
      return;
    }
    Long latestPaymentTriggerTime =
        com.xhgj.srm.common.utils.DateUtil.getLatestPaymentTriggerTime(order.getPaymentCondition(),
            order.getConfirmVoucherTime(), order.getConfirmAccountOpenInvoiceTime(),
            order.getCustomerReturnSignTime());
    order.setPaymentConditionTime(latestPaymentTriggerTime);
    //付款条件满足日期+账期-7天（PS：背靠背视为10天），如果计算后的时间早于今天，就自动取今天。如果以上三个字段有空，这里就是空的
    //预计付款时间
    if (latestPaymentTriggerTime != null) {
      Long predictPaymentTime = com.xhgj.srm.common.utils.DateUtil.getPredictPaymentTimeByCondition(
          latestPaymentTriggerTime, order.getBackToBack(), order.getAccountingPeriod());
      order.setPredictPaymentTime(predictPaymentTime);
    }
  }

  @Scheduled(cron = "0 0 05 * * ?")
  private void updateOrderCustomerReturnStatusAt530AM() {
    updateOrderCustomerReturnStatus();
  }
/*//  @Scheduled(cron = "0 0 12 * * ?")
  private void updateOrderCustomerReturnStatusAt12PM() {
    updateOrderCustomerReturnStatus();
  }
//  @Scheduled(cron = "0 0 20 * * ?")
  private void updateOrderCustomerReturnStatusAt8PM() {
    updateOrderCustomerReturnStatus();
  }*/

  private String orderCustomerReturnStatusMapping(String orderCustomerStatusStr) {
    if (StrUtil.isBlank(orderCustomerStatusStr)) {
      return null;
    }
    switch (orderCustomerStatusStr) {
      case "已回款":
        return "2";
      case "部分回款":
        return "1";
      case "未回款":
        return "0";
    }
    return null;
  }
  /**
   * 获取昨天的开始时间和结束时间的时间戳
   */
  private static Map<String, Long> getYesterdayTimestamp() {
    //一天的毫秒数
    long millisecondsInADay = 1000L * 60L * 60L * 24;
    // 获取昨天日期
    String yesterday = DateUtil.yesterday().toDateStr();
    // 拼接昨天九点钟的时间字符串
    String yesterdayNine = yesterday + " 09:00:00";
    // 将时间字符串转换为时间对象
    DateTime date = DateUtil.parse(yesterdayNine);
    // 获取昨天九点钟的时间戳
    long beginTime = date.getTime();
    long endTime = beginTime + millisecondsInADay;
    return new HashMap<String, Long>(){{
      put("startTime", beginTime);
      put("endTime", endTime);
    }};
  }

  private void doSendForCustomerReturnProgress(String mailAddress, String htmlElement) {
    StringBuilder stringBuilder = new StringBuilder("<!DOCTYPEhtml><htmllang=\"en\">"
        + "<head><metacharset=\"UTF-8\"><title>table</title><style>.table{padding: 10px;}"
        + "table{border-collapse:collapse;}table,td,th{height: 24px; line-height: 24px;"
        + "padding: 0 50px 0 10px;border: 1px solid #9c9fa1; }.title{margin-bottom: 10px; "
        + "}</style>" + "</head><body><div class=\"table\"><div class=\"title\">"
        + "</div> <span>Hi，亲爱的合作伙伴：<br>\n" + "    <br>\n"
        + "    您有以下订单客户已回款！请您前往"
        + "<a href=\"http://srm.xhgjmall.com/login\">咸亨国际供应商协同平台（SCP）(xhgjmall.com)</a>"
        + " 查看。<br>\n"
        + "    若订单没上传签收凭证，或没有对账开进项票，请您及时补充，否则会影响您的订单付款！</span>"
        + "<table><thead><tr><th>客户订单号</th><th>客户名称</th><th>下单平台</th><th>下单时间</th>"
        + "<th>下单金额</th><th>签收凭证</th><th>供应商开票</th></tr></thead><tbody>");
    stringBuilder.append(htmlElement);
    final String subject = "【咸亨国际SCP】您的以下订单客户已经回款了！";
    stringBuilder.append("</tbody></table></div><script></script></body></html>");
    BootMailUtil.sendHtmlMail(mailAddress, subject, stringBuilder.toString());
  }
  private void doSendForPayable(String mailAddress, String htmlElement) {
    StringBuilder stringBuilder = new StringBuilder("<!DOCTYPEhtml><htmllang=\"en\">"
        + "<head><metacharset=\"UTF-8\"><title>table</title><style>.table{padding: 10px;}"
        + "table{border-collapse:collapse;}table,td,th{height: 24px; line-height: 24px;"
        + "padding: 0 50px 0 10px;border: 1px solid #9c9fa1; }.title{margin-bottom: 10px; "
        + "}</style>" + "</head><body><div class=\"table\"><div class=\"title\">"
        + "</div> <span>Hi，亲爱的合作伙伴，<br>\n" + "    <br>\n"
        + "    您和咸亨国际的订单可以发起付款申请了，请前往"
        + "<a href=\"http://srm.xhgjmall.com/login\">咸亨国际供应商协同平台（SCP）(xhgjmall.com)</a>"
        + " 提起付款。<br>\n"
        + "    以下是您可发起付款的订单清单，请查阅：</span>"
        + "<table><thead><tr><th>客户订单号</th><th>客户名称</th><th>下单平台</th><th>下单时间</th>"
        + "<th>最终结算金额</th></tr></thead><tbody>");
    stringBuilder.append(htmlElement);
    final String subject = "【咸亨国际SCP】您有以下订单可以申请付款";
    stringBuilder.append("</tbody></table></div><script></script></body></html>");
    BootMailUtil.sendHtmlMail(mailAddress, subject, stringBuilder.toString());
  }

  /**
   * 订单数据生成html元素（可付款订单）
   */
  private String generateHTMLElementForPayable(Order order) {
    BigDecimal finalPrice = NumberUtil.sub(order.getPrice(), order.getRefundPrice());
    String customer = StringUtils.emptyIfNull(order.getCustomer());
    String orderNo = order.getOrderNo();
    String type = order.getType();
    String typeName = "";
    OrderPlatformDTO orderPlatform = platformService.findByCode(type);
    if (orderPlatform != null && StrUtil.isNotBlank(orderPlatform.getPlatformName())) {
      typeName = orderPlatform.getPlatformName();
    }
    Long orderTime = order.getOrderTime();
    DateTime date = DateUtil.date(orderTime);
    String orderTimeFormat = DateUtil.format(date, "yyyy-MM-dd HH:mm:ss");
    StringBuilder stringBuilder = new StringBuilder("<tr>");
    stringBuilder.append(StrUtil.format("<td>{}</td>", orderNo));
    stringBuilder.append(StrUtil.format("<td>{}</td>", customer));
    stringBuilder.append(StrUtil.format("<td>{}</td>", typeName));
    stringBuilder.append(StrUtil.format("<td>{}</td>", orderTimeFormat));
    stringBuilder.append(StrUtil.format("<td>{}</td>", finalPrice));
    stringBuilder.append("</tr>");
    return stringBuilder.toString();
  }
  /**
   * 订单数据生成html元素（完成回款通知）
   */
  private String generateHTMLElementForCustomerReturnProgress(Order order) {
    String customer = StringUtils.emptyIfNull(order.getCustomer());
    String orderNo = order.getOrderNo();
    String type = order.getType();
    String typeName = "";
    OrderPlatformDTO orderPlatform = platformService.findByCode(type);
    if (orderPlatform != null && StrUtil.isNotBlank(orderPlatform.getPlatformName())) {
      typeName = orderPlatform.getPlatformName();
    }
    Long orderTime = order.getOrderTime();
    DateTime date = DateUtil.date(orderTime);
    String orderTimeFormat = DateUtil.format(date, "yyyy-MM-dd HH:mm:ss");
    BigDecimal orderPrice = order.getPrice();
    String acceptState = orderAcceptService.getAcceptState(order.getId());
    String signEvidenceStr = Constants_order.SIGN_VOUCHER_MAP.get(acceptState);
    String supplierInvoiceStatus =
        StrUtil.isBlank(order.getSupplierOpenInvoiceStatus()) ? "-" :
            Constants.ORDER_SUPPLIER_INVOICE_STATE_TYPE.get(order.getSupplierOpenInvoiceStatus());
    StringBuilder stringBuilder = new StringBuilder("<tr>");
    stringBuilder.append(StrUtil.format("<td>{}</td>", orderNo));
    stringBuilder.append(StrUtil.format("<td>{}</td>", customer));
    stringBuilder.append(StrUtil.format("<td>{}</td>", typeName));
    stringBuilder.append(StrUtil.format("<td>{}</td>", orderTimeFormat));
    //下单金额
    stringBuilder.append(StrUtil.format("<td>{}</td>", orderPrice == null ? "-" : orderPrice));
    //签收凭证
    stringBuilder.append(StrUtil.format("<td>{}</td>", signEvidenceStr));
    //供应商开票
    stringBuilder.append(StrUtil.format("<td>{}</td>", supplierInvoiceStatus));
    stringBuilder.append("</tr>");
    return stringBuilder.toString();
  }

  /**
   * 完成订单和供应商邮箱的映射
   * 没有取到供应商下所有账号邮箱、或者订单supplierId字段缺少时，就无法映射到该集合内
   */
  private List<SupplierMailboxMappingOrderDTO> doOrderToSupplierMailboxMapping(List<Order> orders) {
    if (CollUtil.isEmpty(orders)) {
      return null;
    }
    Map<String, List<Order>> orderToSupplierMapping = orders.stream().filter(order -> {
      return StrUtil.isNotBlank(order.getSupplierId());
    }).collect(Collectors.groupingBy(Order::getSupplierId));
    if (CollUtil.isEmpty(orderToSupplierMapping)) {
      return null;
    }
    return orderToSupplierMapping.keySet().stream().map(supplierId -> {
      Optional<Set<String>> supplierMailbox = supplierUserService.getSupplierMailbox(supplierId);
      boolean present = supplierMailbox.isPresent();
      if (!present) {
        return null;
      }
      return SupplierMailboxMappingOrderDTO.builder()
          .mailAddress(new ArrayList<>(supplierMailbox.get()))
          .orders(orderToSupplierMapping.get(supplierId)).build();
    }).filter(Objects::nonNull).collect(Collectors.toList());
  }

  public void setEMail(List<String> eMail) {
    this.eMail = eMail;
  }
}
