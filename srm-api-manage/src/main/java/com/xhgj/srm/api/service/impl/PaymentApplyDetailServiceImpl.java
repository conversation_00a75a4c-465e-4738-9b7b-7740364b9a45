package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.api.service.PaymentApplyDetailService;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.PaymentApplyDetailDao;
import com.xhgj.srm.jpa.dto.ThisAmountDTO;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail.PaymentApplyDetailBuilder;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.repository.FinancialVoucherRepository;
import com.xhgj.srm.jpa.repository.PaymentApplyDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class PaymentApplyDetailServiceImpl implements PaymentApplyDetailService {

  @Resource
  private PaymentApplyDetailRepository repository;
  @Resource
  private SupplierService supplierService;
  @Resource
  private FinancialVoucherRepository financialVoucherRepository;
  @Resource
  private PaymentApplyDetailDao paymentApplyDetailDao;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;

  @Override
  public BootBaseRepository<PaymentApplyDetail, String> getRepository() {
    return repository;
  }

  @Override
  public void createFreezeApplicationDetail(final FinancialVoucher financialVoucher,
      String paymentApplyRecordId, String remark) {
    Assert.notNull(financialVoucher);
    String supplierName = null;
    Supplier supplier = supplierService.get(financialVoucher.getSupplierId(),
        () -> CheckException.noFindException(Supplier.class, financialVoucher.getSupplierId()));
    if (supplier.isOneTimeSupplier()) {
      SupplierOrder supplierOrder =
          supplierOrderRepository.findFirstByCodeAndStateIn(financialVoucher.getPurchaseOrderNo(),
              CollUtil.toList(Constants.STATE_OK, Constants.STATE_LOCKED));
      if (supplierOrder == null) {
        throw CheckException.noFindException(SupplierOrder.class, financialVoucher.getFinancialVoucherNo());
      }
      supplierName = supplierOrder.getSupplierName();
    } else {
      supplierName = supplier.getEnterpriseName();
    }
    PaymentApplyDetailBuilder builder = PaymentApplyDetail.builder();
    PaymentApplyDetail paymentApplyDetail =
        builder.financialVouchers(financialVoucher.getFinancialVoucherNo())
            .supplierOrderNo(financialVoucher.getPurchaseOrderNo())
            .applyAdvancePrice(financialVoucher.getRelatedAmount())
            .accountingYear(financialVoucher.getAccountingYear())
            .voucherType(financialVoucher.getVoucherType())
            .invoiceNumer(financialVoucher.getInvoiceOrderNo())
            .voucherPrice(financialVoucher.getVoucherPrice())
            .referenceDate(financialVoucher.getBaseDate())
            .period(financialVoucher.getAccountPeriod())
            .advanceDate(financialVoucher.getExpectedPaymentDate())
            .payType(financialVoucher.getPaymentType()).supplierName(supplierName)
            .supplierOrderNo(financialVoucher.getPurchaseOrderNo())
            .financialVouchersId(financialVoucher.getId())
            .remark(remark)
            .paymentApplyRecordId(paymentApplyRecordId).build();
    repository.save(paymentApplyDetail);
  }

  @Override
  public List<PaymentApplyDetail> findByPaymentApplyRecordId(String id) {
    return repository.findByPaymentApplyRecordId(id);
  }

  @Override
  public void createUrgentApplicationDetail(FinancialVoucher financialVoucher,
      String paymentApplyRecordId, Long updateAdvanceDate, String payType, String bank,
      String bankAccount, String accountName, String bankCode, String remark) {
    Assert.notNull(financialVoucher);
    String supplierName = null;
    Supplier supplier = supplierService.get(financialVoucher.getSupplierId(),
        () -> CheckException.noFindException(Supplier.class, financialVoucher.getSupplierId()));
    if (supplier.isOneTimeSupplier()) {
      SupplierOrder supplierOrder =
          supplierOrderRepository.findFirstByCodeAndStateIn(financialVoucher.getPurchaseOrderNo(),
              CollUtil.toList(Constants.STATE_OK, Constants.STATE_LOCKED));
      if (supplierOrder == null) {
        throw CheckException.noFindException(SupplierOrder.class, financialVoucher.getFinancialVoucherNo());
      }
      supplierName = supplierOrder.getSupplierName();
    } else {
      supplierName = supplier.getEnterpriseName();
    }
    PaymentApplyDetailBuilder builder = PaymentApplyDetail.builder();
    PaymentApplyDetail paymentApplyDetail =
        builder.financialVouchers(financialVoucher.getFinancialVoucherNo())
            .supplierOrderNo(financialVoucher.getPurchaseOrderNo())
            .applyAdvancePrice(financialVoucher.getRelatedAmount())
            .accountingYear(financialVoucher.getAccountingYear())
            .voucherType(financialVoucher.getVoucherType())
            .invoiceNumer(financialVoucher.getInvoiceOrderNo())
            .voucherPrice(financialVoucher.getVoucherPrice())
            .referenceDate(financialVoucher.getBaseDate())
            .period(financialVoucher.getAccountPeriod())
            .advanceDate(financialVoucher.getExpectedPaymentDate())
            .payType(payType).supplierName(supplierName)
            .financialVouchersId(financialVoucher.getId())
            .paymentApplyRecordId(paymentApplyRecordId)
            .updateAdvanceDate(updateAdvanceDate)
            .bank(bank)
            .bankAccount(bankAccount)
            .accountName(accountName)
            .bankCode(bankCode)
            .remark(remark)
            .build();
    financialVoucher.setPaymentType(payType);
    financialVoucherRepository.save(financialVoucher);
    repository.save(paymentApplyDetail);
  }

  private List<ThisAmountDTO> thisAmountConvertObject(String thisAmount) {
    return JSON.parseObject(thisAmount, new TypeReference<List<ThisAmountDTO>>() {});
  }

  @Override
  public List<PaymentApplyDetail> findAllLikeFinancialVouchersId(String financialVouchersId,
      boolean isPayable) {
    return paymentApplyDetailDao.findAllLikeFinancialVouchersId(financialVouchersId,isPayable);
  }

}
