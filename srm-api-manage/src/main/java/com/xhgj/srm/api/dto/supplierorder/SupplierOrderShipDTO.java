package com.xhgj.srm.api.dto.supplierorder;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/11/29 16:22
 */
@Data
public class SupplierOrderShipDTO {

  @ApiModelProperty("发货单 id")
  private String id;

  @ApiModelProperty("交货时间")
  private Long deliveryTime;

  @ApiModelProperty("物流公司")
  private String logisticsCompany;

  @ApiModelProperty("物流公司")
  private String logisticsCode;

  @ApiModelProperty("状态")
  private String state;

  @ApiModelProperty("快递单号")
  private String trackNum;

  @ApiModelProperty("是否已入库")
  private Boolean warehousing;

  @ApiModelProperty("发货明细")
  private List<ShipProductDTO> shipProductDTOList;
}
