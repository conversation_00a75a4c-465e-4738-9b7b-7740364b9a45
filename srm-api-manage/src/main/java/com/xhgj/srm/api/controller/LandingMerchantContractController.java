package com.xhgj.srm.api.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xhgj.srm.api.dto.FileDetails;
import com.xhgj.srm.api.dto.LandingContract.ImportFileParams;
import com.xhgj.srm.dto.landingContract.LandingMerchantContractDownloadQuery;
import com.xhgj.srm.api.dto.LandingContract.LandingMerchantContractExportQuery;
import com.xhgj.srm.api.dto.LandingContract.form.LandingMerchantContractQueryForm;
import com.xhgj.srm.api.dto.LandingContractAddParamDTO;
import com.xhgj.srm.api.dto.LandingContractDTO;
import com.xhgj.srm.api.dto.LandingContractDetailsDTO;
import com.xhgj.srm.api.dto.LandingContractOrderDetailsDTO;
import com.xhgj.srm.api.dto.LandingContractPageDTO;
import com.xhgj.srm.api.dto.LandingContractPaymentDetailsDTO;
import com.xhgj.srm.api.dto.SingleBaseParam;
import com.xhgj.srm.api.service.LandingMerchantContractService;
import com.xhgj.srm.common.enums.contract.LandingMerchantContractKeywordEnum;
import com.xhgj.srm.jpa.dto.landingContract.LandingContractStatistics;
import com.xhgj.srm.service.ShareLandingMerchantContractService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 电供商合同Controller
 *
 * <AUTHOR> Shangyi
 */
@Api(tags = {"电供商合同接口"})
@RestController
@RequestMapping("/LandingMerchantContract")
@Validated
public class LandingMerchantContractController extends AbstractRestController {

  @Resource private LandingMerchantContractService service;
  @Resource private ShareLandingMerchantContractService shareLandingMerchantContractService;

  @ApiOperation(value = "分页获取电供合同数据", notes = "分页获取电供合同数据")
  @GetMapping(value = "/page")
  public ResultBean<PageResult<LandingContractPageDTO>> getContractPage(LandingMerchantContractQueryForm form) {
    return new ResultBean<>(
        service.getContractPageRef(form));
  }
  /**
   * 获取电供合同统计数据
   */
  @ApiOperation(value = "获取电供合同统计数据", notes = "获取电供合同统计数据")
  @GetMapping(value = "/statistics")
  public ResultBean<LandingContractStatistics> getContractStatistics(LandingMerchantContractQueryForm form) {
    return new ResultBean<>(service.getContractStatistics(form));
  }

  /** 新增或者更新电供合同 */
  @ApiOperation(value = "新增或者更新合同", notes = "新增或者更新合同")
  @PostMapping(value = "/addOrUpdate")
  public ResultBean<String> addOrUpdate(@RequestBody LandingContractAddParamDTO params) {
    String id = service.addOrUpdate(params,getUser());
    return new ResultBean<>(id, "操作成功!");
  }

  /**
   * 根据名称查询该供应商是否存在于万聚组织
   *
   * @param supplierName 供应商名称
   */
  @ApiOperation(value = "供应商是否是万聚组织内", notes = "供应商是否是万聚组织内")
  @ApiImplicitParam(name = "supplierName", value = "供应商名称", required = true)
  @GetMapping(value = "/is-organization")
  public ResultBean<Boolean> isOrganization(String supplierName,String userGroup) {
    return new ResultBean<>(service.isOrganization(supplierName,userGroup));
  }

  @ApiOperation(value = "查询合同详情", notes = "查询合同详情")
  @ApiImplicitParam(name = "contractId", value = "合同id", required = true)
  @GetMapping("/details")
  public ResultBean<LandingContractDetailsDTO> getContractDetails(String contractId) {
    return new ResultBean<>(service.getContractDetailsById(contractId));
  }

  @ApiOperation(value = "根据合同号查询", notes = "根据合同号查询")
  @ApiImplicitParam(name = "contractNo", value = "合同号", required = true)
  @GetMapping("/byContractNo")
  public ResultBean<LandingContractDTO> getByContractNo(String contractNo) {
    return new ResultBean<>(service.getByContractNo(contractNo));
  }

  @ApiOperation(value = "根据合同id查询订单 - 分页", notes = "根据合同号查询订单 - 分页")
  @ApiImplicitParam(name = "contractId", value = "合同id", required = true)
  @GetMapping("/orderByContractId")
  public ResultBean<PageResult<LandingContractOrderDetailsDTO>> getOrderByContractId(
      String contractId,
      @RequestParam(defaultValue = "1") Integer pageNo,
      @RequestParam(defaultValue = "50") Integer pageSize) {
    return new ResultBean<>(service.getOrderByContractId(contractId, pageNo, pageSize));
  }

  @ApiOperation(value = "根据合同id查询付款单 - 分页", notes = "根据合同号查询付款单 - 分页")
  @ApiImplicitParam(name = "contractId", value = "合同id", required = true)
  @GetMapping("/paymentOrderByContractId")
  public ResultBean<PageResult<LandingContractPaymentDetailsDTO>> getPaymentOrderByContractId(
      String contractId,
      @RequestParam(defaultValue = "1") Integer pageNo,
      @RequestParam(defaultValue = "50") Integer pageSize) {
    return new ResultBean<>(service.getPaymentOrderByContractId(contractId, pageNo, pageSize));
  }

  @ApiOperation(value = "根据文件返回ocr识别字段", notes = "根据文件返回ocr识别字段")
  @ApiImplicitParam(name = "fileUrl", value = "文件的半路径", required = true)
  @GetMapping("/extraction")
  public ResultBean<Map<String, String>> handleFileDiscern(String fileUrl) {
    Map<String, String> resultMap =
        shareLandingMerchantContractService.handleFileDiscern(fileUrl);
    //前端要求要给发票类型带上“发票“二字
    if (resultMap.get(LandingMerchantContractKeywordEnum.INVOICE_TYPE.getCode()) != null) {
      String invoiceType =
          resultMap.get(LandingMerchantContractKeywordEnum.INVOICE_TYPE.getCode()) + "发票";
      resultMap.put(LandingMerchantContractKeywordEnum.INVOICE_TYPE.getCode(), invoiceType);
    }
    return new ResultBean<>(resultMap);
  }

  @ApiOperation("导入合同")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "userId", value = "用户id", required = true),
    @ApiImplicitParam(name = "file", value = "文件", required = true)
  })
  @PostMapping("import")
  public ResultBean<Boolean> importContract(
      MultipartFile file, @NotBlank(message = "参数不合法") String userId) {
    service.importContract(file, userId);
    return new ResultBean<>(true);
  }

  @ApiOperation("批量导入合同附件")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "file", value = "压缩文件", required = true),
    @ApiImplicitParam(name = "platformCode", value = "下单平台编码", required = true),
    @ApiImplicitParam(name = "userId", value = "用户id", required = true),
  })
  @PostMapping("batchImportFile")
  public ResultBean<Boolean> batchImportFile(
      MultipartFile file,
      @NotBlank(message = "参数不合法") String platformCode,
      @NotBlank(message = "参数不合法") String userId) {
    service.batchImportFile(file, platformCode, userId);
    return new ResultBean<>(true, "操作成功");
  }

  /**
   * 导入合同附件
   * @param params
   * @return
   */
  @ApiOperation("导入合同附件")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "params", value = "导入参数", required = true)
  })
  @PostMapping("importFile")
  public ResultBean<Boolean> importFile(@RequestBody ImportFileParams params) {
    service.importFile(params);
    return new ResultBean<>(true, "操作成功");
  }

  @SneakyThrows
  @ApiOperation(value = "导出合同", notes = "导出合同")
  @PostMapping(value = "/exportLandingMerchantContract")
  public ResultBean<Boolean> exportContract(
      @RequestBody @Valid LandingMerchantContractExportQuery param) {
    service.exportLandingMerchantContract(param);
    return new ResultBean<>(true, "操作成功！");
  }

  @ApiOperation(value = "维护保证金", notes = "维护保证金")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "id", value = "合同id", required = true),
    @ApiImplicitParam(name = "state", value = "保证金状态", required = true)
  })
  @GetMapping("/maintenanceDeposit")
  public ResultBean<Boolean> maintenanceDeposit(String id, Boolean state) {
    service.maintenanceDeposit(id, state);
    return new ResultBean<>(true);
  }

  @SneakyThrows
  @ApiOperation(value = "下载合同", notes = "下载合同")
  @PostMapping(value = "/downloadTheContract")
  public Object downloadTheContract(@RequestBody @Valid LandingMerchantContractDownloadQuery params,
      HttpServletResponse response) {
    byte[] downloadTheContract;
    try {
      downloadTheContract = shareLandingMerchantContractService.downloadTheContract(params);
      response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
      response.setHeader("Content-disposition",
          "attachment;filename=" + System.currentTimeMillis() + ".zip");
      response.flushBuffer();
    } catch (CheckException e) {
      ResultBean<Object> objectResultBean = new ResultBean<>();
      objectResultBean.setCode(ResultBean.FAIL);
      objectResultBean.setMsg(ExceptionUtil.getSimpleMessage(e));
      return objectResultBean;
    }
    return ResponseEntity.ok()
        .body(downloadTheContract);
  }

  @ApiOperation(value = "ocr识别合同进行校验", notes = "ocr识别合同进行校验")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "fileUrl", value = "文件的半路径", required = true),
      @ApiImplicitParam(name = "contractId", value = "合同id", required = true)
  })
  @GetMapping("/identification/v2")
  public ResultBean<Boolean> identification(String fileUrl, String contractId) {
    return new ResultBean<>(service.identification(fileUrl, contractId));
  }


  @ApiOperation(value = "合同附件详情", notes = "合同附件详情接口")
  @ApiImplicitParam(name = "id", value = "合同id", required = true)
  @GetMapping("/detailsOfTheAnnexToTheContract")
  public ResultBean<FileDetails> detailsOfTheAnnexToTheContract(
      String id) {
    return new ResultBean<>(service.detailsOfTheAnnexToTheContract(id));
  }

  @ApiOperation(value = "删除合同附件", notes = "删除合同附件")
  @ApiImplicitParam(name = "fileId", value = "文件 id", required = true)
  @PostMapping("/deleteContract")
  public ResultBean<Boolean> deleteContract(@RequestBody @Valid SingleBaseParam singleBaseParam){
    service.deleteContract(singleBaseParam.getId());
    return new ResultBean<>(true);
  }


  @ApiOperation(value = "合同到期通知", notes = "合同到期通知")
  @GetMapping("/contractExpirationNotice")
  public ResultBean<Boolean> contractExpirationNotice(Long time) {
    service.contractExpirationNoticeRef(ObjectUtil.defaultIfNull(time,System.currentTimeMillis()));
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "无效合同是否生效,符合生效则改变成生效", notes = "无效合同是否生效,符合生效则改变成生效")
  @GetMapping("/noValidity")
  public ResultBean<Boolean> noValidity() {
    service.whetherTheInvalidContractIsInForce();
    return new ResultBean<>(true);
  }


  @ApiOperation(value = "处理履约订单对应合同信息", notes = "处理履约订单对应合同信息")
  @GetMapping("/handleOrdersByContractInfo")
  public ResultBean<Boolean> handleOrdersByContractInfo() {
    service.handleOrdersByContractInfo();
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "批处理程序客户订单刷新付款状态", notes = "批处理程序客户订单刷新付款状态")
  @GetMapping("/batchHandleOrderStatus")
  public ResultBean<Boolean> batchHandleOrderStatus() {
    service.batchHandleOrderStatus();
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "批处理程序订单履约根据三要素刷新付款状态", notes = "批处理程序订单履约根据三要素刷新付款状态")
  @GetMapping("/batchHandleOrderStatusByThreeElements")
  public ResultBean<Boolean> batchHandleOrderStatusByThreeElements() {
    service.batchHandleOrderStatusByThreeElements();
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "批处理程序合同列表刷项目大类", notes = "批处理程序合同列表刷项目大类")
  @GetMapping("/batchHandleContractProjectCategory")
  public ResultBean<Boolean> batchHandleContractProjectCategory() {
    service.batchHandleContractProjectCategory();
    return new ResultBean<>(true);
  }

}
