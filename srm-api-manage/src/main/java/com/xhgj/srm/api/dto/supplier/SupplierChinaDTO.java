package com.xhgj.srm.api.dto.supplier;

import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.jpa.entity.BaseSupplierInGroup.PartnershipTypeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 国内供应商分页展示信息
 * <AUTHOR>
 * @since 2022/7/10 15:02
 */
@Data
public class SupplierChinaDTO  {

  @ApiModelProperty("组织内供应商 id")
  private String id;

  @ApiModelProperty("MDM 编码")
  private String mdmCode;

  @ApiModelProperty(value = "供应商名称")
  private String enterpriseName;

  @ApiModelProperty(value = "负责人")
  private String purchaserName;

  @ApiModelProperty(value = "自营合作等级")
  private String enterpriseLevel;

  @ApiModelProperty(value = "经营品牌")
  private String brands;

  @ApiModelProperty(value = "经营类目")
  private String categories;

  @ApiModelProperty(value = "联系人")
  private String contacts;

  @ApiModelProperty(value = "完善度")
  private String integrity;

  @ApiModelProperty(value = "创建时间")
  private Long createTime;

  @ApiModelProperty("是否上传营业执照")
  @Deprecated
  private Boolean uploadLicenseUrl;

  /**
   * 是否开启供应商订单
   */
  @ApiModelProperty("是否开启供应商订单")
  private Boolean openSupplierOrder;

  /**
   * 合作性质
   */
  private List<PartnershipTypeDTO> partnershipTypes;

  /**
   * 推荐自营等级 -- 枚举描述
   */
  private String recommendedLevel;

  /**
   * 上周期自营合作金额
   */
  private BigDecimal lastPeriodSelfAmount;

  /**
   * 自营合作总金额
   */
  private BigDecimal selfTotalAmount;

  /**
   * 电供合作总金额
   */
  private BigDecimal eleTotalAmount;

  /**
   * 定向合作总金额
   */
  private BigDecimal directTotalAmount;

  /**
   * 价格库条数
   */
  private Integer purchaseRecordCount;

  /**
   * 账期 -- 枚举描述
   */
  private String accountPeriod;

  /**
   * 默认付款方式 -- 枚举描述
   */
  private String payType;

  /**
   * 营业执照
   */
  private FileDTO license;

  /**
   * 统计金额相关计算更新最新时间(组织内单个供应商维度)
   */
  private Long amountCountTime;

  /**
   * 推荐自营等级更新时间(组织内单个供应商维度)
   */
  private Long recommendedLevelTime;

  /**
   * 合作性质更新时间(组织内单个供应商维度)
   */
  private Long partnershipTypeTime;

  /**
   * 统计金额相关计算更新最新时间 -- 组织维度
   */
  private Long amountCountOrgTime;

  /**
   * 推荐自营等级更新时间 -- 组织维度
   */
  private Long recommendedLevelOrgTime;

  /**
   * 合作性质更新时间 -- 组织维度
   */
  private Long partnershipTypeOrgTime;

  /**
   * 供应商性质集合 -- 组织维度
   */
  private List<String> enterpriseNatureList;

  @ApiModelProperty("评估表")
  private SupplierFileDTO evaluationTable;

}
