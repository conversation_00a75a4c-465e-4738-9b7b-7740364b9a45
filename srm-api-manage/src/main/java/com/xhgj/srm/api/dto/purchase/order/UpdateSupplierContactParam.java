package com.xhgj.srm.api.dto.purchase.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotBlank;

/**
 * 供方联系人
 */
@Data
@NoArgsConstructor
public class UpdateSupplierContactParam {

  @ApiModelProperty("订单id")
  @NotBlank(message = "订单id不能为空")
  private String id;

  @ApiModelProperty("类型：0：供方 1：收件")
  private String type;

  @ApiModelProperty("供方联系人")
  private String supContacts;

  @ApiModelProperty("供方联系方式")
  private String supMobile;

  @ApiModelProperty("电子邮件")
  private String supEmail;

  @ApiModelProperty("传真")
  private String supFax;

  @ApiModelProperty("收件人")
  @Length(max = 30,message = "收件人最多30字")
  private String receiveMan;

  @ApiModelProperty("收件联系方式")
  @Length(max = 50,message = "收件联系方式最多50字")
  private String receiveMobile;

  @ApiModelProperty("收件地址")
  @Length(max = 200,message = "收件地址最多200字")
  private String receiveAddress;

}
