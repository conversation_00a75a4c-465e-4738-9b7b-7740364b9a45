package com.xhgj.srm.api.service;

import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface PaymentApplyDetailService extends BootBaseService<PaymentApplyDetail, String> {

  /**
   * 创建冻结申请详情
   */
  void createFreezeApplicationDetail(FinancialVoucher financialVoucher,
      String paymentApplyRecordId, String remark);
  /**
   * 创建加急申请详情
   */
  void createUrgentApplicationDetail(FinancialVoucher financialVoucher, String paymentApplyRecordId,
      Long updateAdvanceDate, String payType, String bank, String bankAccount, String accountName,
      String bankCode, String remark);

  /**
   * @param id 申请记录id
   */
  List<PaymentApplyDetail> findByPaymentApplyRecordId(String id);

  List<PaymentApplyDetail> findAllLikeFinancialVouchersId(String financialVouchersId,
      boolean isPayable);}

