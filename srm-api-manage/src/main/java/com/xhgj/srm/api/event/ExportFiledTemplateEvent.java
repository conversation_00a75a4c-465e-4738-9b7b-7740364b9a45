package com.xhgj.srm.api.event;

import com.xhgj.srm.common.dto.exportfiled.ExportTemplateParamProvider;
import lombok.Getter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * 导出字段模板 事件
 */
@ToString
@Getter
public class ExportFiledTemplateEvent extends ApplicationEvent{
  private static final long serialVersionUID = 1L;
  /**
   * 导出模板参数
   */
  private final ExportTemplateParamProvider param;
  /**
   * 模板类型
   */
  private final String type;
  /**
   * 用户id
   */
  private final String userId;

  /**
   * 导出字段模板事件
   * @param source 事件源
   * @param param 导出模板参数
   * @param type 模板类型
   * @param userId 用户id
   */
  public ExportFiledTemplateEvent(Object source, ExportTemplateParamProvider param, String type, String userId) {
    super(source);
    this.param = param;
    this.type = type;
    this.userId = userId;
  }
}
