package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.api.dto.assess.AbstractAssessParam;
import com.xhgj.srm.api.dto.assess.AssessPassParam;
import com.xhgj.srm.api.dto.assess.AssessRejectParam;
import com.xhgj.srm.api.dto.assess.BaseMdmAssessParam;
import com.xhgj.srm.api.dto.assess.MdmAssessPassBackParam;
import com.xhgj.srm.api.dto.assess.MdmAssessRejectBackParam;
import com.xhgj.srm.api.dto.assess.SupplierBlackInfoVO;
import com.xhgj.srm.api.service.AssessService;
import com.xhgj.srm.api.service.EntryRegistrationLandingMerchantService;
import com.xhgj.srm.api.service.EntryRegistrationService;
import com.xhgj.srm.api.service.SupplierInGroupService;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.dto.DingInstanceDetailResult;
import com.xhgj.srm.common.dto.DingInstanceDetailResult.InstanceDetailResult.Task;
import com.xhgj.srm.common.enums.AssessStateEnum;
import com.xhgj.srm.common.enums.AssessTypeEnum;
import com.xhgj.srm.common.enums.DingTalkApproveEventResultEnum;
import com.xhgj.srm.common.enums.FileReviewStateEnum;
import com.xhgj.srm.common.enums.SupplierBlockRangeEnum;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.jpa.dao.AssessDao;
import com.xhgj.srm.jpa.entity.Assess;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.AssessRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationLandingMerchantRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationOrderRepository;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.LandingMerchantContractRepository;
import com.xhgj.srm.service.SupplierCategoryService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.core.common.util.dict.BootDictEnumUtil;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/7/20 19:04
 */
@Service
@Slf4j
public class AssessServiceImpl implements AssessService {
  @Autowired private AssessRepository repository;
  @Autowired private AssessDao dao;
  @Autowired private SupplierInGroupService supplierInGroupService;
  @Autowired private SupplierService supplierService;

  @Autowired
  private EntryRegistrationLandingMerchantService entryRegistrationLandingMerchantService;

  @Autowired
  private EntryRegistrationLandingMerchantRepository entryRegistrationLandingMerchantRepository;

  @Autowired private EntryRegistrationOrderRepository entryRegistrationOrderRepository;
  @Autowired private EntryRegistrationService entryRegistrationService;
  @Resource private LandingMerchantContractRepository landingMerchantContractRepository;
  @Resource private FileRepository fileRepository;
  @Resource private DingUtils dingUtils;
  @Resource private SupplierCategoryService supplierCategoryService;

  @Override
  public BootBaseRepository<Assess, String> getRepository() {
    return repository;
  }

  public Assess createSupplierBizAddAssess(
      String supplierInGroupId,
      String assessMan,
      String createMan,
      Group group,
      String relativeKey,
      String relativeKeyType) {
    Assert.notEmpty(assessMan);
    Assert.notNull(group);
    Map<String, Object> extendJson;
    if (StrUtil.hasEmpty(relativeKey, relativeKeyType)) {
      // 两个参数任意一个为空则没意义，不创建扩展json
      extendJson = null;
    } else {
      extendJson =
          MapUtil.<String, Object>builder()
              .put("relativeKey", relativeKey)
              .put("relativeKeyType", relativeKeyType)
              .build();
    }
    return createAssess(
        null,
        supplierInGroupId,
        null,
        AssessTypeEnum.SUPPLIER_BIZ_ADD,
        assessMan,
        createMan,
        group,
        extendJson);
  }

  public Assess createSupplierBizUpdateAssess(
      String supplierInGroupTempId,
      String supplierInGroupId,
      String oldSupplierInGroupTempId,
      String assessMan,
      String createMan,
      Group group) {
    return createAssess(
        supplierInGroupTempId,
        supplierInGroupId,
        oldSupplierInGroupTempId,
        AssessTypeEnum.SUPPLIER_BIZ_UPDATE,
        assessMan,
        createMan,
        group);
  }

  public Assess createSupplierMainAddAssess(String supplierId, String createMan, Group group) {
    return createAssess(
        null, supplierId, null, AssessTypeEnum.SUPPLIER_MAIN_ADD, null, createMan, group);
  }

  public Assess createSupplierMainUpdateAssess(
      String supplierTempId,
      String supplierId,
      String oldSupplierTempId,
      String createMan,
      Group group) {
    return createAssess(
        supplierTempId,
        supplierId,
        oldSupplierTempId,
        AssessTypeEnum.SUPPLIER_MAIN_UPDATE,
        null,
        createMan,
        group);
  }

  public Assess createSupplierLevelUpdateAssess(
      String supplierTempId,
      String supplierId,
      String oldSupplierTempId,
      String assessMan,
      String createMan,
      Group group,
      String level) {
    return createAssessNew(
        supplierTempId,
        supplierId,
        oldSupplierTempId,
        AssessTypeEnum.SUPPLIER_LEAVE,
        assessMan,
        createMan,
        group,
        level);
  }

  @Override
  public Assess createSupplierBlockAssess(
      String supplierInGroupId,
      String assessMan,
      String createMan,
      Group group,
      SupplierBlockRangeEnum supplierBlockRangeEnum) {
    Assert.notNull(supplierBlockRangeEnum);
    Assess assess =
        createAssess(
            null,
            supplierInGroupId,
            null,
            AssessTypeEnum.SUPPLIER_BLOCK,
            assessMan,
            createMan,
            group);
    assess.setBlockRange(supplierBlockRangeEnum.getKey());
    return assess;
  }

  @Override
  public Assess getFirstAssessingByTargetId(String targetId) {
    Assert.notEmpty(targetId);
    return repository.findFirstByTargetIdAndAssessStateNotIn(
        targetId, AssessStateEnum.getEndStateKeyList());
  }

  @Override
  public Page<Assess> getPage(
      String createMan,
      String assessMan,
      List<AssessStateEnum> assessStateList,
      Pageable pageable) {
    return dao.findPage(
        createMan,
        assessMan,
        CollUtil.emptyIfNull(assessStateList).stream()
            .map(AssessStateEnum::getKey)
            .collect(Collectors.toList()),
        pageable);
  }

  public void handleContractAssess(Assess assess, AbstractAssessParam param) {
    String assessType = assess.getAssessType();
    List<Assess> assesses = repository.findAllByTargetId(assess.getTargetId());
    if (Objects.equals(assessType, AssessTypeEnum.CONTRACT_FILE.getKey())
        && param instanceof AssessRejectParam) {
      for (Assess assesCurrent : assesses) {
        assesCurrent.setAssessState(AssessStateEnum.REJECT.getKey());
        assesCurrent.setAssessResult(((AssessRejectParam) param).getReason());
        assesCurrent.setAssessTime(System.currentTimeMillis());
        save(assesCurrent);
      }
      EntryRegistrationOrder entryRegistrationOrder =
          entryRegistrationOrderRepository
              .findById(assess.getTargetId())
              .orElseThrow(
                  () ->
                      CheckException.noFindException(
                          EntryRegistrationOrder.class, assess.getTargetId()));
      LandingMerchantContract landingMerchantContract =
          landingMerchantContractRepository.findFirstByEntryRegistrationOrderIdAndState(
              entryRegistrationOrder.getId(), Constants.STATE_OK);
      landingMerchantContract.setFileReviewState(FileReviewStateEnum.FAILED_TO_PASS.getKey());
      landingMerchantContractRepository.save(landingMerchantContract);
    }
    if (Objects.equals(assessType, AssessTypeEnum.CONTRACT_FILE.getKey())
        && param instanceof AssessPassParam) {
      for (Assess assesCurrent : assesses) {
        assesCurrent.setAssessState(AssessStateEnum.PASS.getKey());
        assesCurrent.setAssessTime(System.currentTimeMillis());
        save(assesCurrent);
      }
      entryRegistrationService.contractArchivingHandleRef(assess.getTargetId());
    }
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void handleAssess(
      AbstractAssessParam param,
      String assessMan,
      String assessManName,
      String mdmCode,
      boolean isSrm) {
    Assert.isFalse(StrUtil.isAllBlank(assessMan, assessManName));
    String id = param.getId();
    if (StringUtils.isNullOrEmpty(id)) {
      throw new CheckException("审核 id 必传，请联系管理员！");
    }
    Assess assess = get(id, () -> CheckException.noFindException(Assess.class, id));
    long now = System.currentTimeMillis();
    if (!Objects.equals(assess.getAssessState(), AssessStateEnum.UN_ASSESS.getKey())) {
      throw new CheckException("仅待审核状态的审核可以操作，请核实。");
    }
    AssessTypeEnum assessTypeEnum =
        BootDictEnumUtil.getEnumByKey(AssessTypeEnum.class, assess.getAssessType())
            .orElseThrow(() -> new CheckException("审核【" + id + "】的审核状态异常，请联系管理员！"));
    // 合同附件上传审核
    if (assessTypeEnum == AssessTypeEnum.CONTRACT_FILE) {
      handleContractAssess(assess, param);
      return;
    }
    AssessStateEnum assessStateEnum;
    String assessResult;
    if (param instanceof AssessRejectParam) {
      log.warn("驳回审核，id：{}", id);
      // 驳回审核
      String reason = ((AssessRejectParam) param).getReason();
      if (StringUtils.isNullOrEmpty(reason)) {
        throw new CheckException("驳回审核时，原因必传！");
      }
      assessStateEnum = AssessStateEnum.REJECT;
      assessResult = reason;
      if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_BIZ_ADD)) {
        // 业务新增审核驳回，设置删除
        Supplier supplier = supplierInGroupService.makeSupplierInGroupDelete(assess.getTargetId());
        // 如果supplier状态是审核中,删除主数据
        if (Objects.equals(supplier.getState(), Constants.COMMONSTATE_CHECKING)) {
          supplierService.makeSupplierDelete(supplier.getId());
          // 需要删除供应商经营类目
          supplierCategoryService.deleteBySupplierId(supplier.getId(), assess.getCreateMan());
        }
        // 如果主数据编码为空，设置删除
        if (StrUtil.isBlank(supplier.getMdmCode())) {
          supplierService.makeSupplierDelete(supplier.getId());
          // 删除供应商经营类目
          supplierCategoryService.deleteBySupplierId(supplier.getId(), assess.getCreateMan());
        }
        agreeOrRejectDingTask(isSrm, assess, reason, assessStateEnum);
      } else if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_MAIN_ADD)) {
        // 主数据新增审核驳回，设置删除
        String supplierId = assess.getTargetId();
        supplierService.makeSupplierDelete(supplierId);
        supplierInGroupService.makeSupplierInGroupDeleteBySupplierId(supplierId);
        // 需要删除供应商经营类目
        supplierCategoryService.deleteBySupplierId(supplierId, assess.getCreateMan());
      } else if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_BIZ_UPDATE)) {
        agreeOrRejectDingTask(isSrm, assess, reason, assessStateEnum);
        // 需要删除供应商经营类目
        supplierCategoryService.deleteBySupplierInGroupId(
            assess.getSourceId(), assess.getCreateMan());
        supplierCategoryService.deleteBySupplierInGroupId(
            assess.getOldTargetId(), assess.getCreateMan());
      } else if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_LEAVE)) {
        supplierCategoryService.deleteBySupplierInGroupId(
            assess.getOldTargetId(), assess.getCreateMan());
      } else if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_MAIN_UPDATE)) {
        // 需要删除供应商经营类目
        supplierCategoryService.deleteBySupplierId(assess.getSourceId(), assess.getCreateMan());
        supplierCategoryService.deleteBySupplierId(assess.getOldTargetId(), assess.getCreateMan());
      }
    } else {
      // 通过审核
      assessStateEnum = AssessStateEnum.PASS;
      assessResult = "通过";
      if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_BIZ_ADD)) {
        // 业务新增审核通过，修改组织内供应商，并且需要根据 Supplier 表的 state 判断是否需要创建【主数据审核】
        supplierInGroupService.makeSupplierInGroupReal(assess.getTargetId(), assess.getCreateMan());
        agreeOrRejectDingTask(isSrm, assess, assessResult, assessStateEnum);
      } else if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_BIZ_UPDATE)) {
        // 业务修改审核通过，使用副本表信息覆盖真实表
        supplierInGroupService.updateSupplierInGroupByTemp(
            assess.getTargetId(), assess.getSourceId(), assess.getCreateMan());
        agreeOrRejectDingTask(isSrm, assess, assessResult, assessStateEnum);
        // 需要删除供应商经营类目
        supplierCategoryService.deleteBySupplierInGroupId(
            assess.getSourceId(), assess.getCreateMan());
        supplierCategoryService.deleteBySupplierInGroupId(
            assess.getOldTargetId(), assess.getCreateMan());
      } else if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_MAIN_ADD)) {
        // 主数据新增审核通过
        if (StringUtils.isNullOrEmpty(mdmCode)) {
          throw new CheckException("通过主数据新增审核时，mdmCode 必传！");
        }
        supplierService.makeSupplierReal(assess.getTargetId(), mdmCode);
      } else if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_MAIN_UPDATE)) {
        // 主数据修改审核通过
        supplierService.updateSupplierInGroupByTemp(
            assess.getTargetId(), assess.getSourceId(), assess.getCreateMan());
        // 需要删除供应商经营类目
        supplierCategoryService.deleteBySupplierId(assess.getSourceId(), assess.getCreateMan());
        supplierCategoryService.deleteBySupplierId(assess.getOldTargetId(), assess.getCreateMan());
      } else if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_BLOCK)) {
        // 拉黑审核通过
        supplierService.makeSupplierBlock(
            assess.getTargetId(), SupplierBlockRangeEnum.fromKey(assess.getBlockRange()));
      } else if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_LEAVE)) {
        // 供应商等级变更审核通过
        supplierService.updateSupplierLevel(assess.getSourceId(), assess.getExternalId());
        // 需要删除供应商经营类目
        supplierCategoryService.deleteBySupplierInGroupId(
            assess.getOldTargetId(), assess.getCreateMan());
      } else {
        throw new CheckException("暂无法处理类型为【" + assessTypeEnum + "】的审核，请联系管理员！");
      }
    }
    assess.setAssessResult(assessResult);
    assess.setAssessState(assessStateEnum.getKey());
    assess.setAssessTime(now);
    assess.setAssessMan(assessMan);
    assess.setAssessManName(assessManName);
    repository.save(assess);
  }

  @Override
  public long getWaitAssessCount(String assessMan) {
    Assert.notEmpty(assessMan);
    return dao.getWaitAssessCount(assessMan);
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void handleMDMAssessCallback(BaseMdmAssessParam mdmAssessParam) {
    Assess assess = repository.findFirstByExternalId(mdmAssessParam.getId());
    if (assess == null) {
      throw new CheckException("外部审核 id 【" + mdmAssessParam.getId() + "】非法");
    }
    AbstractAssessParam param;
    String mdmCode;
    if (mdmAssessParam instanceof MdmAssessPassBackParam) {
      param = new AssessPassParam(assess.getId());
      mdmCode = ((MdmAssessPassBackParam) mdmAssessParam).getMdmCode();
      handleAssess(param, null, mdmAssessParam.getAssessManName(), mdmCode, false);
      Boolean first = true;
      // 判断是否（第一次通过）
      EntryRegistrationLandingMerchant firstByEntryRegistrationOrderIdAndState =
          entryRegistrationLandingMerchantRepository.findFirstByEntryRegistrationOrderIdAndState(
              assess.getTargetId(), Constants.STATE_OK);
      if (firstByEntryRegistrationOrderIdAndState != null) {
        first = false;
      }
      // 处理准入报备单供应商审核
      entryRegistrationLandingMerchantService.auditPassedHandle(assess.getId(), mdmCode, first);
    } else if (mdmAssessParam instanceof MdmAssessRejectBackParam) {
      param =
          new AssessRejectParam(
              assess.getId(), ((MdmAssessRejectBackParam) mdmAssessParam).getReason());
      mdmCode = null;
      handleAssess(param, null, mdmAssessParam.getAssessManName(), mdmCode, false);
      // 处理准入报备单供应商审核
      String rejectReason = ((AssessRejectParam) param).getReason();
      entryRegistrationLandingMerchantService.auditRejectionHandle(assess.getId(), rejectReason);
    } else {
      throw new IllegalArgumentException();
    }
  }

  /**
   * 创建审核
   *
   * @param sourceId 审核源数据 id
   * @param targetId 审核目标数据 id，必传
   * @param oldTargetId 审核目标的旧数据 id
   * @param assessType 审核类型，必传
   * @param assessMan 审核人（明确审核人时传入）
   * @param createMan 创建人
   */
  private Assess createAssess(
      String sourceId,
      String targetId,
      String oldTargetId,
      AssessTypeEnum assessType,
      String assessMan,
      String createMan,
      Group group) {
    return createAssess(
        sourceId, targetId, oldTargetId, assessType, assessMan, createMan, group, null);
  }

  /**
   * 创建审核
   *
   * @param sourceId 审核源数据 id
   * @param targetId 审核目标数据 id，必传
   * @param oldTargetId 审核目标的旧数据 id
   * @param assessType 审核类型，必传
   * @param assessMan 审核人（明确审核人时传入）
   * @param createMan 创建人
   * @param extendJson 扩展信息
   */
  private Assess createAssess(
      String sourceId,
      String targetId,
      String oldTargetId,
      AssessTypeEnum assessType,
      String assessMan,
      String createMan,
      Group group,
      Map<String, Object> extendJson) {
    Assert.notEmpty(targetId);
    Assert.notNull(assessType);
    Assert.notEmpty(createMan);
    Assess assess = new Assess();
    assess.setSourceId(StrUtil.emptyIfNull(sourceId));
    assess.setTargetId(StrUtil.emptyIfNull(targetId));
    assess.setOldTargetId(StrUtil.emptyIfNull(oldTargetId));
    assess.setAssessType(assessType.getKey());
    // 目前 SRM 中的审核的初始状态都是【待审核】
    assess.setAssessState(AssessStateEnum.UN_ASSESS.getKey());
    assess.setAssessMan(StrUtil.emptyIfNull(assessMan));
    assess.setCreateMan(createMan);
    assess.setCreateTime(System.currentTimeMillis());
    if (group != null) {
      assess.setGroupId(group.getId());
    }
    if (MapUtil.isNotEmpty(extendJson)) {
      assess.setExtendJson(JSONUtil.toJsonStr(extendJson));
    }
    return repository.save(assess);
  }

  @Override
  public SupplierBlackInfoVO getSupplierBlacklistingInformation(String id) {
    Assess assess = get(id, () -> CheckException.noFindException(Assess.class, id));
    String targetId = assess.getTargetId();
    if (StrUtil.isBlank(targetId)) {
      throw new CheckException("数据异常，请联系管理员");
    }
    SupplierBlackInfoVO vo = new SupplierBlackInfoVO();
    SupplierInGroup supplierInGroup = supplierInGroupService.get(targetId);
    if (supplierInGroup == null) {
      throw new CheckException("数据异常，请联系管理员");
    }
    vo.setBlockRange(supplierInGroup.getBlockRange());
    vo.setReason(supplierInGroup.getReason());
    return vo;
  }

  private Assess createAssessNew(
      String sourceId,
      String targetId,
      String oldTargetId,
      AssessTypeEnum assessType,
      String assessMan,
      String createMan,
      Group group,
      String externallId) {
    Assert.notEmpty(targetId);
    Assert.notNull(assessType);
    Assert.notEmpty(createMan);
    Assess assess = new Assess();
    assess.setSourceId(StrUtil.emptyIfNull(sourceId));
    assess.setTargetId(StrUtil.emptyIfNull(targetId));
    assess.setOldTargetId(StrUtil.emptyIfNull(oldTargetId));
    assess.setAssessType(assessType.getKey());
    assess.setExternalId(externallId);
    // 目前 SRM 中的审核的初始状态都是【待审核】
    assess.setAssessState(AssessStateEnum.UN_ASSESS.getKey());
    assess.setAssessMan(StrUtil.emptyIfNull(assessMan));
    assess.setCreateMan(createMan);
    assess.setCreateTime(System.currentTimeMillis());
    if (group != null) {
      assess.setGroupId(group.getId());
    }
    return repository.save(assess);
  }

  @Override
  public Assess findFirstByTargetIdAndAssessTypeAssessStateAndGroupId(
      String targetId, String assessType, String assessState, String groupId) {
    return repository.findFirstByTargetIdAndAssessTypeAndAssessStateAndGroupId(
        targetId, assessType, assessState, groupId);
  }

  @Override
  public Assess createAssess(
      AssessTypeEnum assessTypeEnum, String targetId, User assessMan, User createMan) {
    Assess assess = new Assess();
    assess.setSourceId(targetId);
    assess.setTargetId(targetId);
    assess.setAssessType(assessTypeEnum.getKey());
    assess.setAssessState(AssessStateEnum.UN_ASSESS.getKey());
    assess.setAssessMan(assessMan.getId());
    assess.setAssessManName(assessMan.getRealName());
    assess.setCreateMan(createMan.getId());
    assess.setCreateTime(System.currentTimeMillis());
    return save(assess);
  }

  @Override
  public Map<String, Object> getLandingMerchantContractInfo(String id) {
    Assess assess = get(id, () -> CheckException.noFindException(Assess.class, id));
    if (!Objects.equals(assess.getAssessType(), AssessTypeEnum.CONTRACT_FILE.getKey())) {
      throw new CheckException("审核类型错误");
    }
    EntryRegistrationOrder entryRegistrationOrder =
        entryRegistrationService.get(assess.getTargetId());
    if (entryRegistrationOrder == null) {
      throw new CheckException("数据异常，没有找到关联报备单信息");
    }
    LandingMerchantContract landingMerchantContract =
        landingMerchantContractRepository.findFirstByEntryRegistrationOrderIdAndState(
            entryRegistrationOrder.getId(), Constants.STATE_OK);
    if (landingMerchantContract == null) {
      throw new CheckException("数据异常，没有找到关联合同信息");
    }
    landingMerchantContract.getContractNo();
    File file =
        fileRepository
            .findFirstByRelationIdAndRelationTypeAndState(
                landingMerchantContract.getId(),
                Constants.FILE_TYPE_LANDING_CONTRACT,
                Constants.STATE_OK)
            .orElseThrow(() -> new CheckException("数据异常，没有找到关联合同附件信息"));
    FileDTO fileDTO = new FileDTO(file);
    HashMap<String, Object> result = new HashMap<>();
    result.put("contractNo", StrUtil.emptyIfNull(landingMerchantContract.getContractNo()));
    result.put("file", fileDTO);
    result.put("assessResult", assess.getAssessResult());
    result.put("assessState", assess.getAssessState());
    result.put("reasonsForFailure", StrUtil.emptyIfNull(entryRegistrationOrder.getFileOcrInfo()));
    return result;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void doPassHandle(ApprovalResult approvalResult) {
    log.info("【钉钉审批通过审核】 入参：{}", approvalResult);
    String processInstanceId = approvalResult.getProcessInstanceId();
    Assess assess =
        repository
            .findFirstByProcessInstanceId(processInstanceId)
            .orElseThrow(
                () -> new CheckException("数据异常,未通过钉钉审批实例id【" + processInstanceId + "】查询到审核记录"));
    AssessPassParam param = new AssessPassParam(assess.getId());
    handleAssess(param, assess.getAssessMan(), null, null, false);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void doRejectHandle(ApprovalResult approvalResult) {
    log.info("【钉钉审批驳回审核】 入参：{}", approvalResult);
    String processInstanceId = approvalResult.getProcessInstanceId();
    Assess assess =
        repository
            .findFirstByProcessInstanceId(processInstanceId)
            .orElseThrow(
                () -> new CheckException("数据异常,未通过钉钉审批实例id【" + processInstanceId + "】查询到审核记录"));
    AssessRejectParam param =
        new AssessRejectParam(
            assess.getId(), StrUtil.blankToDefault(approvalResult.getRemark(), "钉钉审批驳回"));
    handleAssess(param, assess.getAssessMan(), null, null, false);
  }

  /**
   * @description: 钉钉同意或拒绝审批任务
   * @param: isSrm 是否来源srm
   * @param: assess 审核记录
   * @param: remark 审批意见
   * @param: stateEnum 审批操作类型
   */
  private void agreeOrRejectDingTask(
      boolean isSrm, Assess assess, String remark, AssessStateEnum stateEnum) {
    // 用户在SRM审批调用钉钉同意或拒绝审批任务接口，审批结果同步钉钉OA
    if (StrUtil.isNotBlank(assess.getProcessInstanceId()) && BooleanUtil.isTrue(isSrm)) {
      // 获取单个审批实例详情
      DingInstanceDetailResult instanceDetail =
          dingUtils.getInstanceDetail(assess.getProcessInstanceId());
      if (instanceDetail != null
          && BooleanUtil.isTrue(instanceDetail.getSuccess())
          && instanceDetail.getResult() != null
          && CollUtil.isNotEmpty(instanceDetail.getResult().getTasks())
          && CollUtil.isNotEmpty(instanceDetail.getResult().getApproverUserIds())) {
        Task task = instanceDetail.getResult().getTasks().get(0);
        if (task.getTaskId() != null && !StrUtil.equals("COMPLETED", task.getStatus())) {
          Map<String, Object> params = new HashMap<>(5);
          params.put("processInstanceId", assess.getProcessInstanceId());
          String result =
              StrUtil.equals(AssessStateEnum.PASS.getKey(), stateEnum.getKey())
                  ? DingTalkApproveEventResultEnum.AGREE.getType()
                  : DingTalkApproveEventResultEnum.REFUSE.getType();
          params.put("result", result);
          params.put("remark", remark);
          params.put("actionerUserId", instanceDetail.getResult().getApproverUserIds().get(0));
          params.put("taskId", task.getTaskId());
          dingUtils.agreeOrRejectDingTask(params);
        }
      }
    }
  }
}
