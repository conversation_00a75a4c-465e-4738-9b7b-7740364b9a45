package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.entity.InventoryLocation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.Column;

/**
 * InventoryLocationDTO
 */
@Data
@NoArgsConstructor
public class InventoryLocationDTO {

  @ApiModelProperty("主键")
  private String id;

  @ApiModelProperty("组织编码")
  private String groupCode;

  @ApiModelProperty("组织名称")
  private String groupName;

  @ApiModelProperty("库房名称")
  private String warehouseName;

  @ApiModelProperty("库房编码")
  private String warehouse;

  @ApiModelProperty("是否涉及WMS:1-是，0-否")
  private String isWms;

  /**
   * 涉及WMS的业务类型：1-采购订单，2-货物移动
   */
  @ApiModelProperty("涉及WMS的业务类型，多个以英文逗号分隔")
  private String businessType;

  /**
   * 允许SRM制单和收货的订单类型（取值为采购订单类型数据字典）
   */
  @ApiModelProperty("允许SRM制单和收货的订单类型（取值为采购订单类型数据字典）")
  private String prepareReceiveOrderType;

  /**
   * 是否允许SRM退货 0 否 1是
   */
  @ApiModelProperty("是否允许SRM退货 0 否 1是")
  private String allowReturnType;

  /**
   * 是否允许SRM入库单退库单冲销 0 否 1是
   */
  @ApiModelProperty("是否允许SRM入库单退库单冲销 0 否 1是")
  private String inboundReturnReversal;

  /**
   * 是否允许SRM调拨 0 否 1是
   */
  @ApiModelProperty("是否允许SRM调拨 0 否 1是")
  private String allowTransfer;

  /**
   * 是否允许SRM组装拆卸 0 否 1是
   */
  @ApiModelProperty("是否允许SRM组装拆卸 0 否 1是")
  private String allowAssembleDisassemble;

  public InventoryLocationDTO(InventoryLocation inventoryLocation) {
    this.id = inventoryLocation.getId();
    this.groupCode = inventoryLocation.getGroupCode();
    this.groupName = inventoryLocation.getGroupName();
    this.warehouseName = inventoryLocation.getWarehouseName();
    this.warehouse = inventoryLocation.getWarehouse();
    this.isWms = inventoryLocation.getIsWms();
    this.businessType = inventoryLocation.getBusinessType();
    this.prepareReceiveOrderType = inventoryLocation.getPrepareReceiveOrderType();
    this.allowReturnType = inventoryLocation.getAllowReturnType();
    this.inboundReturnReversal = inventoryLocation.getInboundReturnReversal();
    this.allowTransfer = inventoryLocation.getAllowTransfer();
    this.allowAssembleDisassemble = inventoryLocation.getAllowAssembleDisassemble();
  }


}
