package com.xhgj.srm.api.dto.financial.voucher;

import com.xhgj.srm.jpa.entity.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
public class UpdateAdvanceReversalParam {
  @ApiModelProperty("申请单id")
  @NotBlank
  private String paymentApplyRecordId;
  @ApiModelProperty("冲销备注")
  private String reversalNotes;
  private User currentUser;
}
