package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.api.service.PermissionUserService;
import com.xhgj.srm.jpa.dao.PermissionUserDao;
import com.xhgj.srm.jpa.entity.PermissionUser;
import com.xhgj.srm.jpa.repository.PermissionUserRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** <AUTHOR> @ClassName UserToGroupServiceImpl */
@Service
public class PermissionUserServiceImpl implements PermissionUserService {
  @Autowired private PermissionUserRepository repository;
  @Autowired private PermissionUserDao dao;

  @Override
  public BootBaseRepository<PermissionUser, String> getRepository() {
    return repository;
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void savePermissionUser(List<String> userIdList, String permissionId) {
    dao.deletePermissionUser(permissionId);
    CollUtil.emptyIfNull(userIdList)
        .forEach(
            userId -> {
              PermissionUser permissionUser = new PermissionUser();
              permissionUser.setPermissionTypeId(permissionId);
              permissionUser.setUserId(userId);
              repository.save(permissionUser);
            });
  }
}
