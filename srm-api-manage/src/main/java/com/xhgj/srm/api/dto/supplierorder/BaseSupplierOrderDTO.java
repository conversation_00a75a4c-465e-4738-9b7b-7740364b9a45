package com.xhgj.srm.api.dto.supplierorder;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderRefuseState;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.dict.BootDictEnumUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/11/29 13:14
 */
@Data
@NoArgsConstructor
public class BaseSupplierOrderDTO {
  @ApiModelProperty("采购订单号")
  private String orderCode;

  @ApiModelProperty("订单状态")
  private SupplierOrderState orderState;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("是否厂家直发")
  private Boolean directShipment;

  @ApiModelProperty("是否有待确认")
  private Boolean confirmState;

  @ApiModelProperty("是否有取消")
  private Boolean cancelState;

  @ApiModelProperty("是否有退货")
  private Boolean returnState;

  @ApiModelProperty("是否有冲销")
  private Boolean writeOffState;

  @ApiModelProperty("是否有拒单")
  private Boolean refuseState;

  @ApiModelProperty("是否有发货单待入库")
  private Boolean shipWaitStock;

  @ApiModelProperty("单子是否赠品订单")
  private Boolean freeState;

  @ApiModelProperty("单子是否自采")
  private Boolean selfState;

  @ApiModelProperty("采购组织")
  private String purchaseGroupName;

  @ApiModelProperty("收件人")
  private String receiveMan;

  @ApiModelProperty("开票状态")
  private String supplierOpenInvoiceState;

  @ApiModelProperty("销售订单号")
  private String salesOrderNo;

  @ApiModelProperty("大票项目号")
  private String largeTicketProjectNumbers;

  @ApiModelProperty("大票项目名称")
  private String largeTicketProjectName;

  /**
   * 售达方--关联单据
   */
  @ApiModelProperty("售达方--关联单据")
  private String soldToParty;

  /**
   * 业务员--关联单据
   */
  @ApiModelProperty("业务员--关联单据")
  private String salesman;

  @ApiModelProperty("客户回款")
  private String customerPaymentCollectionState;

  @ApiModelProperty
  private Boolean deleteStorage;
  @ApiModelProperty("是否走 SAP ")
  private Boolean scp;
  @ApiModelProperty("货币码")
  private String moneyCode;

  @ApiModelProperty("采购申请类型")
  private String applyForType;

  @ApiModelProperty("业务员所在公司名称")
  private String businessCompanyName;

  @ApiModelProperty("制单员名称")
  private String makeManName;

  @ApiModelProperty("是否急单:Y/N")
  private String isWorryOrder;

  @ApiModelProperty("是否查看权限内全部组织订单")
  private Boolean isViewAllOrganization;

  @ApiModelProperty("是否为历史订单")
  private Boolean isHistoricalOrder;

  public BaseSupplierOrderDTO(SupplierOrder supplierOrder) {
    this.orderCode = StrUtil.emptyIfNull(supplierOrder.getCode());
    this.orderState =
        BootDictEnumUtil.getEnumByKey(SupplierOrderState.class, supplierOrder.getOrderState())
            .orElseThrow(() -> new CheckException("【" + supplierOrder.getId() + "】该订单状态非法"));
    this.supplierName = StrUtil.emptyIfNull(supplierOrder.getSupplierName());
    this.directShipment = supplierOrder.getDirectShipment();
    this.confirmState =
        SupplierOrderState.WAIT.getOrderState().equals(supplierOrder.getOrderState())&&supplierOrder.getOrderConfirmState();
    this.cancelState = supplierOrder.getOrderCancelState();
    this.returnState = supplierOrder.getOrderReturnState();
    this.refuseState =
        StrUtil.equals(supplierOrder.getRefuseState(), SupplierOrderRefuseState.REFUSE.getKey());
    this.freeState = supplierOrder.getFreeState();
    this.selfState = supplierOrder.getSelfState();
    this.shipWaitStock = supplierOrder.getOrderShipWaitStockState();
    this.purchaseGroupName = StrUtil.emptyIfNull(supplierOrder.getGroupName());
    this.receiveMan = StrUtil.emptyIfNull(supplierOrder.getReceiveMan());
    this.supplierOpenInvoiceState = StrUtil.emptyIfNull(supplierOrder.getSupplierOpenInvoiceState());
    this.salesOrderNo = supplierOrder.getSaleOrderNo();
    this.largeTicketProjectNumbers = supplierOrder.getProjectNo();
    this.largeTicketProjectName = supplierOrder.getProjectName();
    this.soldToParty = supplierOrder.getSoldToParty();
    this.salesman = supplierOrder.getSalesman();
    this.customerPaymentCollectionState = supplierOrder.getCustomerPaymentCollectionState();
    this.scp = BooleanUtil.isTrue(supplierOrder.getScp());
    this.moneyCode = supplierOrder.getMoneyCode();
  }
}
