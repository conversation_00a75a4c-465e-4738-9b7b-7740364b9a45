package com.xhgj.srm.api.dto.supplier;

import com.xhgj.srm.jpa.entity.BaseSupplier;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/31 15:14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SupplierMainDataAbroadDTO extends BaseSupplierMainDataDTO {

  @NotBlank(message = "国家必传！")
  @ApiModelProperty("国家")
  private String country;

  public SupplierMainDataAbroadDTO(BaseSupplier supplier) {
    super(supplier);
    this.country = supplier.getCountry();
  }
}
