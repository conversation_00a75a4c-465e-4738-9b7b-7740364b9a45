package com.xhgj.srm.api.dto.supplierorder;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/12/1 15:33
 */
@Data
@NoArgsConstructor
public class SupplierOrderReturnDTO {

  @ApiModelProperty("退货单 id")
  private String id;

  @ApiModelProperty("退货时间")
  private Long returnTime;

  @ApiModelProperty("退货单编号")
  private String numbers;

  @ApiModelProperty("退料单编号")
  private String returnNumbers;

  @ApiModelProperty("是否需要退库")
  private Boolean stockOutput;

  @ApiModelProperty("状态")
  private String state;

  @ApiModelProperty("退款金额")
  private BigDecimal returnPrice;

  @ApiModelProperty("是否已入库")
  private Boolean warehousing;

  @ApiModelProperty("物流公司")
  private String logisticsCompany;

  @ApiModelProperty("物流编码")
  private String logisticsCode;
  @ApiModelProperty("物流单号")
  private String trackNum;
  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("退货明细")
  private List<ReturnProductDTO> returnProductDTOList;

  public SupplierOrderReturnDTO(SupplierOrderToForm supplierOrderToForm) {
    this.id = supplierOrderToForm.getId();
    this.returnTime = supplierOrderToForm.getTime();
    this.numbers = supplierOrderToForm.getNumbers();
    this.returnNumbers = supplierOrderToForm.getReturnStock();
    this.stockOutput = Boolean.TRUE.equals(supplierOrderToForm.getStockOutput());
    this.state = supplierOrderToForm.getStatus();
    this.returnPrice = supplierOrderToForm.getReturnPrice();
    this.warehousing = ObjectUtil.equal(Boolean.TRUE, supplierOrderToForm.getWarehousing());
    this.logisticsCompany = StrUtil.emptyIfNull(supplierOrderToForm.getLogisticsCompany());
    this.trackNum = StrUtil.emptyIfNull(supplierOrderToForm.getTrackNum());
    this.remark = StrUtil.emptyIfNull(supplierOrderToForm.getRemark());
    this.logisticsCode = StrUtil.emptyIfNull(supplierOrderToForm.getLogisticsCode());
  }
}
