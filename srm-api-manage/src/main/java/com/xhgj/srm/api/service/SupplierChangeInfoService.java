package com.xhgj.srm.api.service;

import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierChangeInfo;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhiot.boot.framework.jpa.service.BootBaseService;

/** <AUTHOR> @ClassName SupplierChangeInfoService */
public interface SupplierChangeInfoService extends BootBaseService<SupplierChangeInfo, String> {
  /**
   * 保存修改记录
   *  @Author: liuyq @Date: 2022/7/22 16:34
   *
   * @param before
   * @param after
   * @param supplier
   * @param field
   * @return void
   */
  void saveSupplierChange(String before, String after, Supplier supplier, String field);
}
