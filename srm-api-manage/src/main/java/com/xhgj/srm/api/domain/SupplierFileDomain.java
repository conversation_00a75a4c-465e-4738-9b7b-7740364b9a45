package com.xhgj.srm.api.domain;

import cn.hutool.core.collection.CollUtil;
import java.util.List;
import java.util.Objects;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/8/16 20:46
 */
@Data
@Builder
public class SupplierFileDomain {
  private String name;
  private List<String> fileNames;

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SupplierFileDomain that = (SupplierFileDomain) o;
    return Objects.equals(name, that.name)
        && CollUtil.isEmpty(CollUtil.disjunction(fileNames, that.fileNames));
  }

  @Override
  public int hashCode() {
    return Objects.hash(name, fileNames);
  }
}
