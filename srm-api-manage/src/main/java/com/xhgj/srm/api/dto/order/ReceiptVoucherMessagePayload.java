package com.xhgj.srm.api.dto.order;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 同步签收信息给crm的负载
 * Created by Geng Shy on 2023/9/22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReceiptVoucherMessagePayload {

  /**
   * 销售订单号
   */
  private String saleOrderNo;
  /**
   * 附件列表
   */
  private List<String> files;
  /**
   * 销售助理名称
   */
  private String saleAssistantName;
  /**
   * 提交时间
   */
  private Long submissionTime;
  /**
   * 确认时间
   */
  private Long confirmTime;
}
