package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.xhgj.srm.api.dto.supplierorder.LogisticsInformationParam;
import com.xhgj.srm.api.dto.supplierorder.SupplierOrderCountDTO;
import com.xhgj.srm.api.service.ERPService;
import com.xhgj.srm.api.service.SupplierOrderDetailService;
import com.xhgj.srm.api.service.SupplierOrderService;
import com.xhgj.srm.api.service.SupplierOrderToFormService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.jpa.dao.SupplierOrderToFormDao;
import com.xhgj.srm.jpa.dto.RetreatWarehousePageDTO;
import com.xhgj.srm.jpa.dto.WarehousingDTO;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.SupplierOrderToFormRepository;
import com.xhgj.srm.request.dto.erp.RowAndQty;
import com.xhgj.srm.request.service.third.dock.DockService;
import com.xhgj.srm.service.ShareSupplierOrderToFormService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/11/28 17:13
 */
@Service
@Slf4j
public class SupplierOrderToFormServiceImpl implements SupplierOrderToFormService {

  @Autowired private SupplierOrderToFormRepository repository;

  @Autowired private SupplierOrderToFormDao dao;
  @Autowired private SupplierOrderService supplierOrderService;
  @Autowired private SupplierOrderDetailService supplierOrderDetailService;
  @Autowired private ERPService erpService;
  @Resource private HttpUtil httpUtil;
  @Autowired private ShareSupplierOrderToFormService shareSupplierOrderToFormService;
  @Resource
  DockService dockService;
  @Override
  public BootBaseRepository<SupplierOrderToForm, String> getRepository() {
    return repository;
  }

  @Override
  public boolean existStatusBySupplierIdAndStatusAndState(
      SupplierOrderFormType type, List<SupplierOrderFormStatus> status, String supplierOrderId) {
    Assert.notNull(type);
    Assert.notNull(status);
    Assert.notEmpty(supplierOrderId);
    return repository.existsBySupplierOrderIdAndTypeAndAndStatusInAndState(
        supplierOrderId, type.getType(),
        status.stream().map(SupplierOrderFormStatus::getStatus).collect(Collectors.toList()), Constants.STATE_OK);
  }

  @Override
  public SupplierOrderToForm getDetailedBySupplierOrderId(String supplierOrderId) {
    return shareSupplierOrderToFormService.getDetailedBySupplierOrderId(supplierOrderId);
  }

  @Override
  public Long countByTypeAndSupplierOrderIdAndState(
      SupplierOrderFormType type, String supplierOrderId) {
    Assert.notNull(type);
    Assert.notEmpty(supplierOrderId);
    return Optional.ofNullable(
            repository.countByTypeAndSupplierOrderIdAndState(
                type.getType(), supplierOrderId, Constants.STATE_OK))
        .orElse(0L);
  }

  @Override
  public boolean existByTypeAndSupplierOrderIdAndExcludeStatusNotIn(
      List<SupplierOrderFormType> typeList, String supplierOrderId, List<String> excludeStatus) {
    Assert.notEmpty(typeList);
    Assert.notEmpty(supplierOrderId);
    return repository.existsByTypeInAndSupplierOrderIdAndStateAndStatusNotIn(
        typeList.stream().map(SupplierOrderFormType::getType).collect(Collectors.toList()),
        supplierOrderId,Constants.STATE_OK,excludeStatus);
  }


  @Override
  public SupplierOrderToForm getFirstByTypeAndReturnIdAndState(String returnId,
      SupplierOrderFormType supplierOrderDetailType) {
    Assert.notEmpty(returnId);
    Assert.notNull(supplierOrderDetailType);
    return repository.getFirstByTypeAndReturnIdAndState(
        supplierOrderDetailType.getType(), returnId, Constants.STATE_OK);
  }

  @Override
  public List<SupplierOrderToForm> getByTypeAndSupplierOrderId(
      SupplierOrderFormType type, String supplierOrderId) {
    Assert.notEmpty(supplierOrderId);
    Assert.notNull(type);
    return repository.getAllByTypeAndSupplierOrderIdAndStateOrderByTimeAsc(
        type.getType(), supplierOrderId, Constants.STATE_OK);
  }

  @Override
  public List<SupplierOrderToForm> getByTypeAndStatusAndSupplierOrderId(
      SupplierOrderFormType type, String supplierOrderId) {
    return dao.getAllByTypeAndSupplierOrderIdAndStateOrderByTimeAsc(
        type.getType(), supplierOrderId);
  }

  @Override
  public SupplierOrderToForm createSupplierOrderToForm(
      String supplierOrderId, SupplierOrderFormType type, long createTime) {
    Assert.notEmpty(supplierOrderId);
    Assert.notNull(type);
    SupplierOrderToForm supplierOrderToForm = new SupplierOrderToForm();
    supplierOrderToForm.setSupplierOrderId(supplierOrderId);
    supplierOrderToForm.setType(type.getType());
    supplierOrderToForm.setCreateTime(createTime);
    if (SupplierOrderFormType.RETURN.equals(type) || SupplierOrderFormType.CANCEL.equals(type)) {
      supplierOrderToForm.setBatchNumber(String.valueOf(createTime));
    }
    supplierOrderToForm.setState(Constants.STATE_OK);
    return supplierOrderToForm;
  }

  @Override
  public Page<SupplierOrderToForm> getReturnOrCancelPage(
      Long startCreateTime,
      Long endCreateTime,
      List<SupplierOrderFormType> type,
      String number,
      String orderCode,
      String purchaseGroupName,
      String purchaseName,
      Boolean directShipment,
      String batchNumber,
      List<SupplierOrderFormStatus> status,
      Pageable toPageable) {
    return dao.getPageAdmin(
        startCreateTime,
        endCreateTime,
        type,
        number,
        orderCode,
        purchaseGroupName,
        purchaseName,
        directShipment,
        batchNumber,
        status,
        toPageable);
  }

  @Override
  public long countByFormTypeAndFormStatus(
      String supplierOrderId, SupplierOrderFormType type, SupplierOrderFormStatus status) {
    return repository.countBySupplierOrderIdAndTypeAndStatusAndState(
        supplierOrderId,type.getType(),status.getStatus(),Constants.STATE_OK);
  }

  @Override
  public List<SupplierOrderToForm> getReturnOrCancelFormByTypeAndAndStatusSupplierOrderId(
      SupplierOrderFormType type, String supplierOrderId,List<String> statusList) {
    Assert.notEmpty(supplierOrderId);
    Assert.notNull(type);
    return dao.getSupplierOrderFormByTypeAndStatus(type.getType(), statusList, supplierOrderId);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String confirmReceipt(String orderToFormId,User user) {
    SupplierOrderToForm supplierOrderToForm =
        get(
            orderToFormId,
            () -> CheckException.noFindException(SupplierOrderToForm.class, orderToFormId));
    String supplierOrderId = supplierOrderToForm.getSupplierOrderId();
    SupplierOrder supplierOrder =
        supplierOrderService.get(
            supplierOrderId,
            () -> CheckException.noFindException(SupplierOrder.class, supplierOrderId));
    if (!StrUtil.equals(PurchaseOrderTypeEnum.CONSIGNMENT_TO_OWNED.getKey(),
        supplierOrder.getOrderType())&&!Boolean.TRUE.equals(supplierOrder.getDirectShipment())) {
      throw new CheckException("该订单非厂家直发，无法确认入库");
    }
    List<SupplierOrderDetail> byOrderToFormId =
        supplierOrderDetailService.getByOrderToFormId(supplierOrderToForm.getId());
    BigDecimal totalStockInputQty =
        NumberUtil.add(
            supplierOrder.getTotalStockInputQty(),
            CollUtil.emptyIfNull(byOrderToFormId).stream()
                .map(SupplierOrderDetail::getShipQty)
                .reduce(NumberUtil::add)
                .orElse(BigDecimal.ZERO)
                .setScale(0, RoundingMode.HALF_UP));
    save(supplierOrderToForm);
    supplierOrderService.save(supplierOrder);
    List<RowAndQty> rowAndQtyList =
        byOrderToFormId.stream()
            .map(
                supplierOrderDetail -> {
                  RowAndQty rowAndQty = new RowAndQty();
                  rowAndQty.setRowId(supplierOrderDetail.getDetailed().getErpId());
                  rowAndQty.setQty(supplierOrderDetail.getShipQty());
                  return rowAndQty;
                })
            .collect(Collectors.toList());
    String userErpId = StrUtil.emptyIfNull(user.getErpId());
    // 推送 erp
    String erpMessage =
        erpService.addBill(
            supplierOrder.getErpId(),
            supplierOrder.getCode(),
            supplierOrderToForm.getTrackNum(),
            rowAndQtyList,
            supplierOrderToForm.getLogisticsCompany(),userErpId, true);
    supplierOrderToForm.setWarehousing(Boolean.TRUE);
    save(supplierOrderToForm);
    return erpMessage;
  }

  @Override
  public SupplierOrderCountDTO getSupplierOrderFormCountById(String supplierId) {
    return new SupplierOrderCountDTO(
        dao.getSumSupplierOrderFormByTypeAndStatus(SupplierOrderFormType.DELIVER.getKey(),
            supplierId,null),
        dao.getSumSupplierOrderFormByTypeAndStatus(SupplierOrderFormType.RETURN.getKey(),
            supplierId,null),
        dao.getSumSupplierOrderFormByTypeAndStatus(SupplierOrderFormType.CANCEL.getKey(),
            supplierId,null),
        null
    );
  }

  @Override
  public JSONArray getLogisticsInformation(LogisticsInformationParam param) {
    SupplierOrderToForm supplierOrderToForm = get(param.getId(),
        () -> CheckException.noFindException(SupplierOrderToForm.class, param.getId()));
    String logisticsInformation = supplierOrderToForm.getLogisticsInformation();
    if (StrUtil.isNotBlank(logisticsInformation)) {
      return JSON.parseArray(logisticsInformation);
    }
    JSONArray logisticsStatus = new JSONArray();
    try {
      logisticsStatus = dockService.getLogisticsStatus(param.getExpressNo(), param.getExpressCode(),
          param.getPhoneNumber());
      log.info("供应商订单发货单物流查询返回：{}", logisticsStatus);
    } catch (Exception e) {
      log.warn("供应商订单发货单物流查询失败,单号：{}", param.getExpressNo());
      return new JSONArray();
    }
    final String sign_off_mark_1 = "签收";
    final String sign_off_mark_2 = "代收";
    for (Object status : CollUtil.emptyIfNull(logisticsStatus)) {
      if (String.valueOf(status).contains(sign_off_mark_1) || String.valueOf(status).contains(sign_off_mark_2)) {
        //存储物流信息
        supplierOrderToForm.setLogisticsInformation(logisticsStatus.toJSONString());
        save(supplierOrderToForm);
        break;
      }
    }
    return logisticsStatus;
  }

  @Override
  public void deleteById(final List<String> ids) {
    if (CollUtil.isEmpty(ids)) {
      return;
    }
    ids.stream().filter(StrUtil::isNotBlank).map(this::get).filter(Objects::nonNull)
        .forEach(supplierOrderToForm -> {
          supplierOrderToForm.setState(Constants.STATE_DELETE);
          save(supplierOrderToForm);
        });
  }

  @Override
  public List<SupplierOrderToForm> findBySupplierOrderId(final String supplierOrderId) {
    if (StrUtil.isBlank(supplierOrderId)) {
      return new ArrayList<>();
    }
    return repository.findBySupplierOrderIdAndState(supplierOrderId, Constants.STATE_OK);
  }

  @Override
  public List<SupplierOrderToForm> findBySupplierOrderIdAndType(String supplierOrderId,
      String type) {
    Assert.notBlank(supplierOrderId);
    Assert.notBlank(type);
    return repository.getAllByTypeAndSupplierOrderIdAndStateOrderByTimeAsc(type, supplierOrderId,
        Constants.STATE_OK);
  }

  @Override
  public SupplierOrderToForm createSupplierOrderForm(
      String supplierOrderId, SupplierOrderFormType type) {
    Assert.notEmpty(supplierOrderId);
    Assert.notNull(type);
    long now = System.currentTimeMillis();
    SupplierOrderToForm supplierOrderToForm = new SupplierOrderToForm();
    supplierOrderToForm.setSupplierOrderId(supplierOrderId);
    supplierOrderToForm.setType(type.getType());
    supplierOrderToForm.setTime(now);
    supplierOrderToForm.setCreateTime(now);
    supplierOrderToForm.setState(Constants.STATE_OK);
    return supplierOrderToForm;
  }

  @Override
  public boolean confirmThatAllItemsAreStoredInTheWarehouse(SupplierOrder supplierOrder) {
    Objects.requireNonNull(supplierOrder);
    List<SupplierOrderToForm> supplierOrderToForms =
        repository.findBySupplierOrderIdAndTypeAndState(supplierOrder.getId(),
            SupplierOrderFormType.DELIVER.getType(), Constants.STATE_OK);
    if (CollUtil.isEmpty(supplierOrderToForms)) {
      return false;
    }
    for (SupplierOrderToForm supplierOrderToForm : supplierOrderToForms) {
      if (!BooleanUtil.isTrue(supplierOrderToForm.getWarehousing())) {
        return false;
      }
    }
    return true;
  }

  @Override
  public Boolean getExistReversal(String supplierOrderId) {
    return repository.existsBySupplierOrderIdAndTypeInAndStatusInAndState(supplierOrderId,
        ListUtil.toList(SupplierOrderFormType.RETURN.getType(),
            SupplierOrderFormType.WAREHOUSING.getType()),
        ListUtil.toList( SupplierOrderFormStatus.REVERSAL.getStatus(),
            SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus()),Constants.STATE_OK);
  }

  @Override
  public Map<String, Boolean> getExistReversalBatch(List<String> supplierOrderIds) {
    if (CollUtil.isEmpty(supplierOrderIds)) {
      return new HashMap<>();
    }
    List<Object[]> results =
        repository.existsBySupplierOrderIdsAndTypeInAndStatusInAndState(supplierOrderIds,
            ListUtil.toList(SupplierOrderFormType.RETURN.getType(),
                SupplierOrderFormType.WAREHOUSING.getType()),
            ListUtil.toList(SupplierOrderFormStatus.REVERSAL.getStatus(),
                SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus()), Constants.STATE_OK);
    Map<String, Boolean> existReversals = new HashMap<>();
    for (Object[] result : results) {
      existReversals.put((String) result[0], (Boolean) result[1]);
    }
    return existReversals;
  }
}