package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.purchase.order.ProductDetailParam;
import com.xhgj.srm.api.dto.supplierorder.SaveSupplierOrderParams.ProductDetailDTO;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/28 15:20
 */
public interface SupplierOrderDetailService extends BootBaseService<SupplierOrderDetail,String> {

  /**
   * 保存物料明细
   * @param productDetailDTO 物料明细 必传
   * @param supplierOrderId 供应商订单 id 必传
   */
  void saveProductDetail(List<ProductDetailDTO> productDetailDTO, String supplierOrderId);

  /**
   * 通过关联行 erpId 和关联表单 id 查询订单详情
   * @param erpId erp 行 id 必传
   * @param orderToFormId 关联表单 id 必传
   */
  SupplierOrderDetail getByErpIdAndOrderToFormId(String erpId,String orderToFormId);

  /**
   * 关联表单 id 查询订单详情
   * @param orderToFormId 关联表单 id 必传
   */
  List<SupplierOrderDetail> getByOrderToFormId(String orderToFormId);

  /**
   * 创建详细（不会 save ）
   * @param orderProductId 关联物料 id 必传
   * @param detailedId 明细 id 必传
   * @param createTime 创建时间
   * @param sortNum 排序号
   * @param detailedErpId 明细行的 erp id
   */
  SupplierOrderDetail createProductOrderDetail(
      String orderProductId, String detailedId, long createTime, int sortNum,String detailedErpId);

  /**
   * 批量删除
   * @param orderToFormIds 订单详情表单id集合
   */
  void deleteSupplierOrderDetail(List<String> orderToFormIds);

  /**
   * 保存单据明细
   *
   * @param orderFormId 单据id
   * @param productDetailList 保存的单据明细列表
   * @return void
   */
  List<SupplierOrderDetail> saveOrderDetail(
      String orderFormId, List<ProductDetailParam> productDetailList,
      String purchaseOrderId);

  /**
   * @param purchaseApplyForOrderId 采购申请单id
   */
  List<SupplierOrderDetail> findAllByPurchaseApplyForOrderIdAndState(String purchaseApplyForOrderId);

  /**
   * 判断该入库单是否存在退货数量
   * @param warehousingFormId 入库单 id
   * @return
   */
  boolean existByWarehousingFormId(String warehousingFormId);

  /**
   * 获取采购订单发货数量
   *
   * @param purchaseId 采购订单id
   */
  BigDecimal getPurchaseOrderShipQty(String purchaseId);

  SupplierOrderDetail getFormIdAndDetailedId(String formId,String detailedId);

  /**
   * 获取订单退货数量
   * @param supplierOrderId 采购订单
   * @return
   */
  BigDecimal getSupplierOrderReturnQty(String supplierOrderId);

  }
