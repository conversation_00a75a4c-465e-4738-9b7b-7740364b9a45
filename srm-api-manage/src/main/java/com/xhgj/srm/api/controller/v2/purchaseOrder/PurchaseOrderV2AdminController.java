package com.xhgj.srm.api.controller.v2.purchaseOrder;/**
 * @since 2025/4/28 14:20
 */

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.api.controller.AbstractRestController;
import com.xhgj.srm.api.service.PurchaseOrderService;
import com.xhgj.srm.api.service.SupplierOrderService;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2AdminService;
import com.xhgj.srm.v2.vo.purchaseOrder.DeleteAnomalyOrderParam;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2AdminService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 *<AUTHOR>
 *@date 2025/4/28 14:20:17
 *@description 运维管理接口，沿用v1代码
 */
@RestController
@RequestMapping("/v2/purchaseOrder")
@Validated
@Api(tags = {"采购订单运维管理v2 api"})
public class PurchaseOrderV2AdminController extends AbstractRestController  {
  @Resource
  private PurchaseOrderService purchaseOrderService;
  @Resource
  private SupplierOrderService supplierOrderService;
  @Resource
  private PurchaseOrderV2AdminService purchaseOrderV2AdminService;


  @ApiOperation("根据采购订单号修改供应商开票状态")
  @GetMapping(value = "updateOrderCode")
  public ResultBean<Boolean> updateOrderCode(
      @RequestParam @NotBlank(message = "采购订单号不能为空！") String code,
      @RequestParam @NotBlank(message = "供应商开票状态不能为空！") String supplierOpenInvoiceState) {
    purchaseOrderService.updateOrderCode(code, supplierOpenInvoiceState);
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation(value = "导入入库单已开票数量", notes = "导入入库单已开票数量")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userId", value = "用户 id", required = true),
      @ApiImplicitParam(name = "file", value = "文件", required = true)
  })
  @PostMapping("/importWarehouseInvoiceNum")
  public ResultBean<Boolean> importWarehouseInvoiceNum(
      @RequestParam MultipartFile file,
      @RequestParam @NotBlank(message = "用户 id 不能为空") String userId) {
    purchaseOrderService.importWarehouseInvoiceNum(file, userId);
    return new ResultBean<>();
  }

  @ApiOperation(value = "导入退库单已开红票数量", notes = "导入退库单已开红票数量")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userId", value = "用户 id", required = true),
      @ApiImplicitParam(name = "file", value = "文件", required = true)
  })
  @PostMapping("/importReturnRedInvoiceNum")
  public ResultBean<Boolean> importReturnRedInvoiceNum(
      @RequestParam MultipartFile file,
      @RequestParam @NotBlank(message = "用户 id 不能为空") String userId) {
    purchaseOrderService.importReturnRedInvoiceNum(file, userId);
    return new ResultBean<>();
  }

  @ApiOperation(value = "挂起供应商订单接口", notes = "挂起供应商订单")
  @PostMapping(value = "/suspendOrder")
  public ResultBean<Boolean> suspendOrder(
      @NotBlank(message = "参数不合法") String supplierOrderId) {
    supplierOrderService.suspendOrder(supplierOrderId);
    return new ResultBean<>(Boolean.TRUE);
  }


  @ApiOperation(value = "拒绝拒单申请", notes = "拒绝拒单申请")
  @PostMapping(value = "/refusalOfApplyForRefusalOrder")
  public ResultBean<Boolean> refusalOfApplyForRefusalOrder(
      @NotBlank(message = "参数不合法") String supplierOrderId) {
    supplierOrderService.refusalOfApplyForRefusalOrder(supplierOrderId);
    return new ResultBean<>(Boolean.TRUE);
  }


  @ApiOperation(value = "删除异常订单", notes = "删除异常订单")
  @PostMapping(value = "/deleteAnomalyOrder")
  public ResultBean<Boolean> deleteAnomalyOrder(@RequestBody DeleteAnomalyOrderParam param) {
    purchaseOrderV2AdminService.deleteAnomalyOrder(param.getIds());
    return new ResultBean<>(Boolean.TRUE);
  }

  /**
   * 采购订单V2导入
   */
  @ApiOperation(value = "导入采购订单", tags = "0.7.0")
  @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResultBean<Boolean> importPurchaseOrder(MultipartFile file) {
    if (file == null || file.isEmpty()) {
      throw new IllegalArgumentException("文件不能为空");
    }
    User user = getUser();
    purchaseOrderV2AdminService.importPurchaseOrder(file, user.getId());
    return new ResultBean<>(true);
  }

  /**
   * 采购订单V2入库单导入
   */
  @ApiOperation(value = "导入入库单信息", tags = "0.7.0")
  @PostMapping(value = "/import/inboundDelivery", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResultBean<Boolean> importInboundDelivery(MultipartFile file) {
    if (file == null || file.isEmpty()) {
      throw new IllegalArgumentException("文件不能为空");
    }
    User user = getUser();
    purchaseOrderV2AdminService.inboundDelivery(file, user.getId());
    return new ResultBean<>(true);
  }

  /**
   * 采购订单V2退库单导入
   */
  @ApiOperation(value = "导入退库单信息", tags = "0.7.0")
  @PostMapping(value = "/import/outboundDelivery", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResultBean<Boolean> importOutboundDelivery(MultipartFile file) {
    if (file == null || file.isEmpty()) {
      throw new IllegalArgumentException("文件不能为空");
    }
    User user = getUser();
    purchaseOrderV2AdminService.outboundDelivery(file, user.getId());
    return new ResultBean<>(true);
  }

}
