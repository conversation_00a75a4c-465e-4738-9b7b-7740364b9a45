package com.xhgj.srm.api.dto.customerReceivable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.request.dto.erp.ReceivableBillDTO;
import com.xhgj.srm.request.dto.erp.ReceivableReturnDTO;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName OrderDetail
 **/
@Data
public class OrderDetail {

  @ApiModelProperty("订单id")
  private String id;
  @ApiModelProperty("客户订单号")
  private String orderNo;
  @ApiModelProperty("下单平台")
  private String platform;
  @ApiModelProperty("大票价税合计")
  private BigDecimal totalPrice;
  @ApiModelProperty("订单结算金额")
  private BigDecimal settlePrice;
  @ApiModelProperty("客户公司")
  private String customer;
  @ApiModelProperty("收件人")
  private String consignee;
  @ApiModelProperty("回款进度 0-未回款 1-部分回款 2-全部回款")
  private String customerReturnProgress;
  @ApiModelProperty("回款金额")
  private BigDecimal returnPrice;
   @ApiModelProperty("剩余回款")
  private BigDecimal remainPrice;
  @ApiModelProperty("关联大票项目")
  private List<String> largeTicketProjectNameList;
  @ApiModelProperty("应收金额列表")
  private List<CustomerReceivableTableDTO> receivableTableList;
  @ApiModelProperty("回款金额列表")
  private List<CustomerReceivableReturnTableDTO> returnList;
  @ApiModelProperty("回款凭证文件")
  private FileDTO file;
  @ApiModelProperty("手动回款标记人")
  private String signMan;
  @ApiModelProperty("手动回款标记时间")
  private Long signTime;


  public OrderDetail(Order order, List<String> largeTicketProjectNameList,
      Set<ReceivableReturnDTO> receivableTableList, List<ReceivableBillDTO> returnList,
      BigDecimal totalPrice, BigDecimal remainPrice, BigDecimal returnPrice, String signMan,
      FileDTO file, String typeName) {
    this.id = order.getId();
    this.orderNo = order.getOrderNo();
    this.platform = StrUtil.emptyIfNull(typeName);
    if (order.getPrice() != null && order.getRefundPrice() != null) {
      this.settlePrice = order.getPrice().subtract(order.getRefundPrice());
    }
    this.customer = StringUtils.emptyIfNull(order.getCustomer());
    this.customerReturnProgress = StringUtils.emptyIfNull(order.getCustomerReturnProgress());
    this.consignee = StringUtils.emptyIfNull(order.getConsignee());
    this.largeTicketProjectNameList = largeTicketProjectNameList;
    if (CollUtil.isNotEmpty(receivableTableList)) {
      ArrayList<CustomerReceivableTableDTO> customerReceivableTableDTOS = new ArrayList<>();
      for (ReceivableReturnDTO receivableReturnDTO : receivableTableList) {
        CustomerReceivableTableDTO dto =
            new CustomerReceivableTableDTO(receivableReturnDTO.getProjectNo(),
                receivableReturnDTO.getInvoiceNo(), receivableReturnDTO.getInvoiceDate(),
                receivableReturnDTO.getAmount());
        customerReceivableTableDTOS.add(dto);
      }
      this.receivableTableList = customerReceivableTableDTOS;
    }
    if (CollUtil.isNotEmpty(returnList)) {
      ArrayList<CustomerReceivableReturnTableDTO> customerReceivableReturnTableDTOS =
          new ArrayList<>();
      for (ReceivableBillDTO receivableBillDTO : returnList) {
        CustomerReceivableReturnTableDTO dto =
            new CustomerReceivableReturnTableDTO(receivableBillDTO.getProjectNo(),
                receivableBillDTO.getDate(), receivableBillDTO.getAmount());
        customerReceivableReturnTableDTOS.add(dto);
      }
      this.returnList = customerReceivableReturnTableDTOS;
    }
    this.totalPrice = totalPrice;
    this.remainPrice = remainPrice;
    this.returnPrice = returnPrice;
    this.signMan = signMan == null ? "" : signMan;
    this.signTime = order.getCustomerReturnSignTime();
    this.file = file == null ? new FileDTO() : file;
  }
}
