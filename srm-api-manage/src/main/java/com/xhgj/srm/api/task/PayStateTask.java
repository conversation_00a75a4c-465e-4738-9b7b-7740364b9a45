package com.xhgj.srm.api.task;

import com.xhgj.srm.api.service.PaymentApplyRecordService;
import com.xhgj.srm.service.OrderPaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component
@Slf4j
public class PayStateTask {
  @Resource
  private PaymentApplyRecordService paymentApplyRecordService;

  @Resource
  private OrderPaymentService orderPaymentService;

  @Scheduled(cron = "0 0 2 ? * *")
  private void  setPayStateTask() {
    log.info("同步sap付款状态开始========================================================");
    paymentApplyRecordService.syncSapPayState();
    log.info("同步sap付款状态结束========================================================");
  }

  @Scheduled(cron = "0 0 2 ? * *")
  private void  syncOrderPaymentTask() {
    log.info("同步落地商订单付款金额时间开始========================================================");
    orderPaymentService.syncOrderPaymentTask();
    log.info("同步落地商订单付款金额时间结束========================================================");
  }



}
