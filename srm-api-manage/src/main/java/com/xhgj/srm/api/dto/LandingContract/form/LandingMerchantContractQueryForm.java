package com.xhgj.srm.api.dto.LandingContract.form;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.TitleOfTheContractEnum;
import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 电供合同查询表单
 */
@ApiModel(description = "电供合同查询表单")
@Data
public class LandingMerchantContractQueryForm implements BaseDefaultSearchSchemeForm {
  /**
   * 用户Id
   */
  @ApiModelProperty("用户Id")
  private String userId;

  /**
   * 合同号
   */
  @ApiModelProperty("合同号")
  private String contractNum;

  /**
   * 合同类型
   */
  @ApiModelProperty("合同类型")
  private String contractType;

  /**
   * 开始时间
   */
  @ApiModelProperty("开始时间")
  private String createTimeStart;

  /**
   * 结束时间
   */
  @ApiModelProperty("结束时间")
  private String createTimeFinish;

  @ApiModelProperty(value = "时间戳开始", hidden = true)
  private Long startTimeStamp;

  @ApiModelProperty(value = "时间戳结束", hidden = true)
  private Long endTimeStamp;

  /**
   * 供应商名称
   */
  @ApiModelProperty("供应商名称")
  private String enterpriseName;

  /**
   * 供应商编码
   */
  @ApiModelProperty("供应商编码")
  private String enterpriseCode;

  /**
   * 签订方式
   */
  @ApiModelProperty("签订方式")
  private String signingType;

  /**
   * 签章状态
   */
  @ApiModelProperty("签章状态")
  private String signatureStatus;

  /**
   * 合同状态
   */
  @ApiModelProperty("合同状态")
  private String landingContractStatus;

  /**
   * 检索方案id
   */
  @ApiModelProperty("检索方案id")
  private String schemeId;

  /**
   * 关联履约信息的状态  1：已关联 2：未关联"
   */
  @ApiModelProperty("关联履约信息的状态  1：已关联 2：未关联")
  private String associationStatus;

  /**
   * 下单平台
   */
  @ApiModelProperty("下单平台")
  private String platform;

  /**
   * 创建人
   */
  @ApiModelProperty("创建人")
  private String createUser;
  /**
   * 创建人
   */
  @ApiModelProperty(value = "创建人Id", hidden = true)
  private String createUserId;

  /**
   * 付款方式
   */
  @ApiModelProperty("付款方式")
  private String contractPayType;

  /**
   * 票种
   */
  @ApiModelProperty("票种")
  private String invoiceType;

  /**
   * 是否为13%的增值税专票
   */
  @ApiModelProperty("是否为13%的增值税专票")
  private Boolean is13VatSpecial;

  /**
   * 有无保证金
   */
  @ApiModelProperty("有无保证金")
  private Boolean depositState;

  /**
   * 有无合同附件
   */
  @ApiModelProperty("有无合同附件")
  private Boolean contractFile;

  /**
   * 当前页
   */
  @ApiModelProperty("当前页")
  private Integer pageNo;

  /**
   * 每页展示数量
   */
  @ApiModelProperty("每页展示数量")
  private Integer pageSize;

  /**
   * 我方签约主体
   */
  @ApiModelProperty("我方签约主体")
  private String firstSigningGroupCode;

  /**
   * 合作类型
   * @return
   */
  @ApiModelProperty("合作类型")
  private String typeOfCooperation;

  /**
   * 合作有效期开始
   * @return
   */
  @ApiModelProperty("合作有效期开始")
  private Long effectiveStart;

  /**
   * 合作有效期结束
   * @return
   */
  @ApiModelProperty("合作有效期结束")
  private Long effectiveEnd;

  /**
   * 账期 0 背靠背 1 账期
   *
   */
  @ApiModelProperty("账期类型")
  private Byte accountPeriodType;

  /**
   * 是否需要绑品
   */
  @ApiModelProperty("是否需要绑品")
  private Boolean needBundle;

  /** 项目大类，多个以逗号分隔 */
  @ApiModelProperty("项目大类，多个以逗号分隔")
  private String projectCategory;

  public Integer getPageNo() {
    if (pageNo == null || pageNo < 1) {
      pageNo = 1;
    }
    return pageNo;
  }

  public Integer getPageSize() {
    if (pageSize == null || pageSize < 1) {
      pageSize = 50;
    }
    return pageSize;
  }

  public Long getStartTimeStamp() {
    if (StrUtil.isEmpty(createTimeStart)) {
      return null;
    }
    LocalDateTime start = LocalDateTimeUtil.parse(createTimeStart, "yyyy-MM-dd");
    // 设置为一天的开始
    start = start.withHour(0).withMinute(0).withSecond(0).withNano(0);
    return start.toInstant(ZoneOffset.of("+8")).toEpochMilli();
  }

  public Long getEndTimeStamp() {
    if (StrUtil.isEmpty(createTimeFinish)) {
      return null;
    }
    LocalDateTime end = LocalDateTimeUtil.parse(createTimeFinish, "yyyy-MM-dd");
    // 设置为一天的结束
    end = end.withHour(23).withMinute(59).withSecond(59).withNano(999);
    return end.toInstant(ZoneOffset.of("+8")).toEpochMilli();
  }

  public Map<String, Object> toQueryMap(GroupRepository groupRepository) {
    Map<String, Object> map = new HashMap<>();
    map.put("contractNum", contractNum);
    map.put("contractType", contractType);
    map.put("createTimeStart", getStartTimeStamp());
    map.put("createTimeEnd", getEndTimeStamp());
    map.put("landingContractStatus", landingContractStatus);
    map.put("signingType", signingType);
    map.put("signatureStatus", signatureStatus);
    map.put("enterpriseName", enterpriseName);
    map.put("enterpriseCode", enterpriseCode);
    map.put("associationStatus", associationStatus);
    if (StrUtil.isNotBlank(platform)) {
      // 根据,分割
      List<String> platforms = new ArrayList<>(ListUtil.toList(platform.split(",")));
      map.put("platforms", platforms);
    }
    map.put("createUser", createUser);
    map.put("payType", contractPayType);
    map.put("invoiceType", invoiceType);
    map.put("depositState", depositState);
    map.put("contractFile", contractFile);
    map.put("is13VatSpecial", is13VatSpecial);
    map.put("createUserId", createUserId);
    if (StrUtil.isNotBlank(firstSigningGroupCode)) {
      String groupCode = null;
      TitleOfTheContractEnum mode1 =
          TitleOfTheContractEnum.getEnumByCode(firstSigningGroupCode);
      if (mode1 != null) {
        groupCode = mode1.getCode();
      }
      TitleOfTheContractEnum mode2 =
          TitleOfTheContractEnum.getEnumByName2(firstSigningGroupCode);
      if (mode2 != null) {
        groupCode = mode2.getCode();
      }
      if (groupCode != null) {
        Group group = groupRepository.findFirstByCodeAndState(groupCode, Constants.STATE_OK);
        if (group != null) {
          map.put("firstSigningGroupId", group.getId());
        }
      }
    }
    map.put("typeOfCooperation", typeOfCooperation);
    map.put("effectiveStart", effectiveStart);
    map.put("effectiveEnd", effectiveEnd);
    map.put("accountPeriodType", accountPeriodType);
    map.put("pageNo", getPageNo());
    map.put("pageSize", getPageSize());
    map.put("needBundle", needBundle);
    map.put("projectCategory", projectCategory);
    return map;
  }
}
