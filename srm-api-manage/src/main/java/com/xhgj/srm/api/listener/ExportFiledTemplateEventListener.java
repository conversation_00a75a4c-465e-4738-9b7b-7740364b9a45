package com.xhgj.srm.api.listener;

import com.xhgj.srm.api.service.ExportFiledTemplateService;
import com.xhgj.srm.api.event.ExportFiledTemplateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 导出字段模板事件监听
 */
@Slf4j
@Component
public class ExportFiledTemplateEventListener implements ApplicationListener<ExportFiledTemplateEvent> {
  @Autowired
  private ExportFiledTemplateService exportFiledTemplateService;
  @Override
  @Async
  public void onApplicationEvent(ExportFiledTemplateEvent exportFiledTemplateEvent) {
    log.info("ExportFiledTemplateEventListener监听到事件：{}", exportFiledTemplateEvent);
    exportFiledTemplateService.saveExportFieldInfoByType(exportFiledTemplateEvent.getType(),
        exportFiledTemplateEvent.getParam(), exportFiledTemplateEvent.getUserId());
  }
}
