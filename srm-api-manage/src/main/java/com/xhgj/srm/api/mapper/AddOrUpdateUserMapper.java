package com.xhgj.srm.api.mapper;

import com.xhgj.srm.api.dto.UserAddOrUpdateParamDTO;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.BaseMappingConfig;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.core.common.util.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;

/** <AUTHOR> @ClassName AddOrUpdateUserMapper */
@Mapper(componentModel = "spring")
public interface AddOrUpdateUserMapper extends BaseMappingConfig<UserAddOrUpdateParamDTO, User> {
  /**
   * 映射同名属性
   *
   * @param userAddOrUpdateParamDTO 源对象
   * @return 目标对象
   */
  @Mappings({
    @Mapping(target = "realName", source = "userAddOrUpdateParamDTO.name"),
    @Mapping(target = "name", source = "userAddOrUpdateParamDTO.mobile"),
    @Mapping(target = "mobile", source = "userAddOrUpdateParamDTO.mobile"),
    @Mapping(target = "code", source = "userAddOrUpdateParamDTO.erpCode"),
    @Mapping(target = "mail", source = "userAddOrUpdateParamDTO.mail"),
    @Mapping(target = "erpId", source = "userAddOrUpdateParamDTO.erpId"),
    @Mapping(target = "role", source = "userAddOrUpdateParamDTO.role"),
    @Mapping(target = "mdmId", source = "userAddOrUpdateParamDTO.mdmId"),
    @Mapping(target = "id", source = "user.id"),
    @Mapping(target = "duty", source = "user.duty"),
    @Mapping(target = "userGroup", source = "user.userGroup"),
    @Mapping(target = "groupErpCode", source = "user.groupErpCode"),
    @Mapping(target = "depart", source = "user.depart"),
    @Mapping(target = "departCode", source = "user.departCode"),
    @Mapping(target = "departErpId", source = "user.departErpId"),
    @Mapping(target = "password", source = "user.password"),
    @Mapping(target = "createTime", source = "user.createTime"),
    @Mapping(target = "state", source = "user.state"),
    @Mapping(target = "groupDistribute", source = "user.groupDistribute"),
    @Mapping(target = "isManageAbroad", source = "user.isManageAbroad"),
    @Mapping(target = "meetingMails", source = "user.meetingMails"),
    @Mapping(target = "group", source = "user.group"),
    @Mapping(target = "inquiryRank", source = "user.inquiryRank"),
  })
  User sourceToTarget(UserAddOrUpdateParamDTO userAddOrUpdateParamDTO, User user);

  /**
   * 在映射最后一步对属性的自定义映射处理
   *
   * @param user 目标对象
   */
  @AfterMapping
  static void afterUpdateDto(@MappingTarget User user) {
    if (StringUtils.isNullOrEmpty(user.getId())) {
      user.setId(null);
    }
    if (StringUtils.isNullOrEmpty(user.getState())) {
      user.setState(Constants.STATE_OK);
    }
    if (user.getCreateTime() == null) {
      user.setCreateTime(System.currentTimeMillis());
    }
  }
}
