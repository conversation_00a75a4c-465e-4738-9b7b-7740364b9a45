package com.xhgj.srm.api.service;/**
 * @since 2025/2/10 17:12
 */

import com.xhgj.srm.api.dto.returnExchangeOrder.AddNewCancelForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.AddNewReturnForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeOrderCount;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeOrderFillDTO;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeSaveForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeSearchForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.UnCancelForm;
import com.xhgj.srm.api.vo.returnExchange.NewReturnVO;
import com.xhgj.srm.api.vo.returnExchange.ReturnExchangeVO;
import com.xhgj.srm.common.vo.returnExchange.ReturnExchangeListVO;
import com.xhgj.srm.jpa.dto.returnExchange.ReturnExchangeStatistics;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/2/10 17:12:59
 *@description 退换货订单业务接口
 */
public interface ReturnExchangeOrderService {

  /**
   * 保存退换货订单
   */
  String saveRefundExchangeOrder(ReturnExchangeSaveForm saveForm);

  /**
   * 新增退换货订单的 退库单
   */
  void addRefundOrder(AddNewReturnForm form);

  /**
   * 新增退换货订单的 取消单
   */
  void addCancelOrder(AddNewCancelForm cancelForm);

  /**
   * 分页查询 退换货订单列表
   */
  PageResult<ReturnExchangeListVO> getPage(ReturnExchangeSearchForm form);

  /**
   * 查询 退换货订单统计
   * @param form
   * @return
   */
  ReturnExchangeStatistics getStatistics(ReturnExchangeSearchForm form);

  /**
   * 查询退换货订单数量统计
   * @param form
   * @return
   */
  ReturnExchangeOrderCount getCount(ReturnExchangeSearchForm form);

  /**
   * 查询 退换货订单详情
   */
  ReturnExchangeVO getDetail(String id);

  /**
   * 导出退换货订单列表
   */
  void export(ReturnExchangeSearchForm form);

  /**
   * 退换货订单删除(暂存或驳回状态)
   */
  void delete(String id, boolean isCheck);

  /**
   * 查询退换货订单 - 退库单列表
   * @param supplierOrderId
   * @return
   */
  List<NewReturnVO> getReturnList(String supplierOrderId);

  /**
   * 查询退换货订单 - 取消单列表 todo 计划共用采购订单
   */
  void getCancelOrderList(String test);

  /**
   * 下载退换货订单填充物料信息
   * @param fillDTOList
   * @return
   */
  byte[] downloadReturnExchangeOrderFill(List<ReturnExchangeOrderFillDTO> fillDTOList)
      throws IOException;

  /**
   * 退换货订单批量填充物料信息
   * @param file
   * @return
   */
  List<ReturnExchangeOrderFillDTO> fill(MultipartFile file) throws IOException;

  /**
   * 查询退换货订单预计导出数量
   * @param form
   * @return
   */
  Long exportCount(ReturnExchangeSearchForm form);

  /**
   * 反取消订单物料行
   * @param unCancelForm
   */
  void unCancelOrder(UnCancelForm unCancelForm);

  void importExchangeOrder(MultipartFile file);

  void deleteReturnExchangeOrder(List<String> ids);

  /**
   * 批量删除退换货订单
   * @param ids
   */
  void batchDelete(List<String> ids);
}
