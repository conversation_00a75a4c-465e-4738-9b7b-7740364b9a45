package com.xhgj.srm.api.dto.permissions;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.GrpPermissions;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/10/23
 */
@Data
public class FindPermissionsDTO {

  private String id;

  /**
   * 父id
   */
  private String parentId;

  /**
   * 名称
   */
  private String name;

  /**
   * url路径
   */
  private String url;

  /**
   * 组件
   */
  private String component;

  /**
   * 组件名称
   */
  private String componentName;

  private String redirect;

  /**
   * 菜单类型
   */
  private String menuType;

  private String perms;

  private String permsType;

  /**
   * 排序
   */
  private Integer sort;

  private String alwaysShow;

  private String icon;

  private String route;

  private String leaf;

  private String keepAlive;

  /**
   * 是否隐藏
   */
  private String hidden;

  /**
   * 描述
   */
  private String description;

  /**
   * 创建人
   */
  private String createMan;

  /**
   * 创建时间
   */
  private Long createTime;

  /**
   * 更新人
   */
  private String updateMan;

  /**
   * 更新时间
   */
  private Long updateTime;

  /**
   * 数据状态
   */
  private String state;

  /**
   * 规则标志
   */
  private String ruleFlag;

  private String status;

  private String internalOrExternal;

  private List<FindPermissionsDTO> child;

  private static FindPermissionsDTO getInstance(GrpPermissions grpPermissions) {
    FindPermissionsDTO permissions = new FindPermissionsDTO();
    permissions.id = grpPermissions.getId();
    permissions.parentId = grpPermissions.getParentId();
    permissions.name = grpPermissions.getName();
    permissions.url = grpPermissions.getUrl();
    permissions.component = grpPermissions.getComponent();
    permissions.componentName = grpPermissions.getComponentName();
    permissions.redirect = grpPermissions.getRedirect();
    permissions.menuType = grpPermissions.getMenuType();
    permissions.perms = grpPermissions.getPerms();
    permissions.permsType = grpPermissions.getPermsType();
    permissions.sort = grpPermissions.getSort();
    permissions.alwaysShow = grpPermissions.getAlwaysShow();
    permissions.icon = grpPermissions.getIcon();
    permissions.route = grpPermissions.getRoute();
    permissions.leaf = grpPermissions.getLeaf();
    permissions.keepAlive = grpPermissions.getKeepAlive();
    permissions.hidden = grpPermissions.getHidden();
    permissions.description = grpPermissions.getDescription();
    permissions.createMan = grpPermissions.getCreateMan();
    permissions.createTime = grpPermissions.getCreateTime();
    permissions.updateMan = grpPermissions.getUpdateMan();
    permissions.updateTime = grpPermissions.getUpdateTime();
    permissions.state = grpPermissions.getState();
    permissions.ruleFlag = grpPermissions.getRuleFlag();
    permissions.status = grpPermissions.getStatus();
    permissions.internalOrExternal = grpPermissions.getInternalOrExternal();
    return permissions;
  }

  private static List<FindPermissionsDTO> batchGetInstance(List<GrpPermissions> grpPermissionsList) {
    if (CollUtil.isEmpty(grpPermissionsList)) {
      return null;
    }
    ArrayList<FindPermissionsDTO> result = new ArrayList<>();
    grpPermissionsList.forEach(grpPermissions -> {
      result.add(getInstance(grpPermissions));
    });
    return result;
  }

  /**
   * 根据菜单实体集合获取实例
   *
   * @param grpPermissionsList 菜单实体集合
   * @return FindPermissionsDTO
   */
  public static List<FindPermissionsDTO> getInstance(List<GrpPermissions> grpPermissionsList) {
    Assert.notEmpty(grpPermissionsList);
    List<GrpPermissions> parents = getRootNodeMenu(grpPermissionsList);
    List<FindPermissionsDTO> result = FindPermissionsDTO.batchGetInstance(parents);
    grpPermissionsList.removeAll(parents);
    Map<String, List<GrpPermissions>> menuMap =
        grpPermissionsList.stream().collect(Collectors.groupingBy(GrpPermissions::getParentId));
    fillingChildNodeMenu(result, menuMap);
    return result;
  }

  /**
   * 查找出所有根节点菜单
   *
   * @param grpPermissions 菜单实体集合
   * @return List<GrpPermissions>
   */
  private static List<GrpPermissions> getRootNodeMenu(List<GrpPermissions> grpPermissions) {
    if (CollUtil.isEmpty(grpPermissions)) {
      return new ArrayList<>();
    }
    return grpPermissions.stream().filter(grpPermission -> {
      return StrUtil.isBlank(grpPermission.getParentId());
    }).collect(Collectors.toList());
  }

  /**
   * 填充子节点
   *
   * @param findPermissionsDTOS 菜单视图对象集合
   * @param menuMap 根据父节点分组的菜单集合
   */
  private static void fillingChildNodeMenu(List<FindPermissionsDTO> findPermissionsDTOS,
      Map<String, List<GrpPermissions>> menuMap) {
    if (CollUtil.isEmpty(findPermissionsDTOS) || CollUtil.isEmpty(menuMap)) {
      return;
    }
    findPermissionsDTOS.forEach(findPermissionsDTO -> {
      List<GrpPermissions> grpPermissionsList = menuMap.get(findPermissionsDTO.getId());
      findPermissionsDTO.setChild(batchGetInstance(grpPermissionsList));
      menuMap.remove(findPermissionsDTO.getId());
      fillingChildNodeMenu(findPermissionsDTO.getChild(), menuMap);
    });
  }
}

