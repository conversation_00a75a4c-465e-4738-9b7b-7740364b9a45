package com.xhgj.srm.api.controller.partner;

import com.xhgj.srm.api.controller.AbstractRestController;
import com.xhgj.srm.request.dto.partner.PartnerDTO;
import com.xhgj.srm.request.service.third.partner.PartnerService;
import com.xhiot.boot.mvc.base.PageResult;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;


/**
 * <AUTHOR>
 * 进项票扩充接口
 */
@RestController
@RequestMapping("/partner")
public class PartnerController extends AbstractRestController {

  public static final String PARTNER_TYPE_CUSTOMER = "1";
  @Resource
  private PartnerService partnerService;

  /**
   * 查询供应商合作类型是客户的供应商
   */
  @ApiOperation("查询供应商合作类型是客户的供应商")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "pageNo", value = "页码", required = true, dataType = "int", paramType = "query"),
      @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "int", paramType = "query"),
      @ApiImplicitParam(name = "mdmCode", value = "mdm编码", required = false, dataType = "String", paramType = "query"),
      @ApiImplicitParam(name = "partnerName", value = "合作商名称", required = false, dataType = "String", paramType = "query")
  })
  @GetMapping("/customer")
  public PageResult<PartnerDTO> getCustomerPartnerPage(
      @RequestParam(value = "pageNo") Integer pageNo,
      @RequestParam(value = "pageSize") Integer pageSize,
      @RequestParam(value = "mdmCode", required = false) String mdmCode,
      @RequestParam(value = "partnerName", required = false) String partnerName
  ) {
    return partnerService.getChinaPartnerPage(pageNo, pageSize, PARTNER_TYPE_CUSTOMER, mdmCode,
        partnerName);
  }

}
