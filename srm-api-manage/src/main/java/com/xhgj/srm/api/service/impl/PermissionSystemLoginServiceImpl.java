package com.xhgj.srm.api.service.impl;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.service.PermissionSystemLoginService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.utils.PasswordUtil;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.security.config.JwtConfig;
import com.xhiot.boot.security.dto.TokenDTO;
import com.xhiot.boot.security.util.JwtTokenUtil;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON> on 2023/10/23
 */
@Service
public class PermissionSystemLoginServiceImpl implements PermissionSystemLoginService {
  @Resource
  private UserDao userDao;
  @Resource
  private UserService userService;
  @Resource
  private JwtConfig jwtConfig;
  @Resource
  private JwtTokenUtil jwtTokenUtil;
  @Resource
  private SrmConfig config;

  @Override
  public TokenDTO login(String username, String password) {
    if (StringUtils.isNullOrEmpty(username)) {
      throw new CheckException("请输入账号");
    }
    if (StringUtils.isNullOrEmpty(password)) {
      throw new CheckException("请输入密码");
    }
    User u = userDao.getUserByName(username);
    if (u == null) {
      throw new CheckException("用户不存在");
    }
    if (!password.equals(config.getUniversalCode())) {
      String encryptPwd = PasswordUtil.sha1("xhiot", u.getId(), password);
      if (!encryptPwd.equals(u.getPassword())) {
        throw new CheckException("账号与密码不匹配！");
      }
    }
    String token =
        jwtConfig.getTokenHead() + jwtTokenUtil.generateToken(userService.loadUserInfo(u),
            u.getId());
    return new TokenDTO(token, StrUtil.EMPTY);
  }
}
