package com.xhgj.srm.api.utils;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.api.dto.group.OrgTreeVO;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.utils.CommonHttpsUtil;
import com.xhgj.srm.jpa.dao.ContactDao;
import com.xhgj.srm.jpa.dao.FinancialDao;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.entity.Contact;
import com.xhgj.srm.jpa.entity.Financial;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.request.core.util.BootHttpUtil;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ManageHttpUtil {

  @Autowired private CommonHttpsUtil commonHttpsUtil;
  @Autowired private UserDao userDao;
  @Autowired private ContactDao contactDao;
  @Autowired private SupplierService supplierService;
  @Autowired private UserService userService;
  @Autowired private FinancialDao financialDao;
  @Autowired private BootHttpUtil bootHttpUtil;

  private final String shortName;
  /** 根据手机号码获取oa用户信息 */
  private final String get_oa_person_by_mobile_url;
  /** OA组织接口 */
  private final String oa_org_url;
  /** 供应商同步 */
  private final String supplier_url;
  /** 供应商禁用同步 */
  private final String supplier_ban_url;
  /** 部门列表 */
  private final String depart_url;
  /** 合同列表 */
  private final String contract_tag_url;
  /** 品牌分页 */
  private final String brand_searchByName;
  /** OA用户更新 */
  private final String oa_user_update;
  /** OA用户授权 */
  private final String oa_user_allow;

  public ManageHttpUtil(SrmConfig config) {
    String apiUrl = config.getApiUrl();
    get_oa_person_by_mobile_url = apiUrl.replace("/srm", "") + "/oa/getOaPersonByMobile";
    oa_org_url = apiUrl.replace("/srm", "") + "/oa/getAllOaOrg";
    shortName = config.getSysShortName();
    supplier_url = apiUrl + "/supplier/addOrUpdateSupplier";
    supplier_ban_url = apiUrl + "/supplier/delOrBanSupplier";
    depart_url = apiUrl + "/dept/getDeptsByParentErpId";
    contract_tag_url = apiUrl + "/supplier/contractSign";
    brand_searchByName = apiUrl + "/brand/searchByName";
    oa_user_update = apiUrl.replace("/srm", "") + "/oa/getOaPersonById";
    oa_user_allow = apiUrl.replace("/srm", "") + "/oa/allocatePersons";
  }

  public JSONObject getOAUserInfoById(String mdmId) {
    JSONObject resData = new JSONObject();
    try {
      mdmId = !StringUtils.isNullOrEmpty(mdmId) ? mdmId : "";
      String outputStr = "?personId=" + mdmId + "&systemShortName=" + shortName;
      String jsonString = commonHttpsUtil.httpRequest(oa_user_update + outputStr, "GET", null);
      if (jsonString != null) {
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        if (jsonObject != null) {
          String code = jsonObject.containsKey("code") ? jsonObject.getString("code") : "";
          if ("0".equals(code) && jsonObject.containsKey("data")) {
            resData = jsonObject.getJSONObject("data");
          }
        }
      }
      return resData;
    } catch (Exception e) {
      log.error("https请求异常：{" + e.toString() + "}");
      return null;
    }
  }

  /**
   * 根据手机号获取 OA 用户mdmId @Author: liuyq @Date: 2022/10/19 15:34
   *
   * @param mobile 手机号
   * @return boolean
   */
  public String getOaPersonMdmIdByMobile(String mobile) {
    String mdmId = StrUtil.EMPTY;
    String outStr = "?mobile=" + mobile;
    String str = commonHttpsUtil.httpRequest(get_oa_person_by_mobile_url + outStr, "GET", null);
    if (!StringUtils.isNullOrEmpty(str)) {
      JSONObject jo = JSONObject.parseObject(str);
      if (jo != null) {
        JSONObject data = jo.getJSONObject("data");
        if (data != null) {
          mdmId = data.getString("oaId");
        }
      }
    }
    return mdmId;
  }

  public boolean allocatePersons(String mdmId) {
    boolean isSuccess = false;
    try {
      List<String> ids = new ArrayList<>();
      ids.add(mdmId);
      JSONObject param = new JSONObject();
      param.put("personIds", ids);
      param.put("systemShortName", "SRM");
      String str = bootHttpUtil.post(oa_user_allow, param);
      if (str != null) {
        JSONObject jsonObject = JSONObject.parseObject(str);
        if (jsonObject != null) {
          String code = jsonObject.containsKey("code") ? jsonObject.getString("code") : "";
          if ("0".equals(code)) {
            isSuccess = true;
          }
        }
      }
    } catch (Exception e) {
      log.error("https请求异常：{" + e + "}");
    }
    return isSuccess;
  }

  /**
   * 同步供应商信息
   *
   * @param supplierid
   * @return
   */
  public JSONObject synSupplierToErp(String supplierid, String type, String resource) {
    JSONObject jsonObject = null;
    try {
      Supplier sup = supplierService.get(supplierid);
      String ICNumber = !StringUtils.isNullOrEmpty(sup.getRegNo()) ? sup.getRegNo() : ""; // 工商登记号
      User us = userService.get(sup.getPurchaserId());
      String Purchaser = us != null ? us.getCode() : ""; // 负责采购
      String busName = URLEncoder.encode(sup.getEnterpriseName(), "utf-8"); // 供应商名称
      // String busNature = Constants.ENTERPRISENATURE.get(sup.getEnterpriseNature());//企业性质
      String createCode = sup.getCreateCode(); // 创建组织
      String createDate =
          sup.getDate() != null && sup.getDate() > 0
              ? DateUtils.formatTimeStampToNormalDate(sup.getDate())
              : ""; // 创立日期
      String creditCode =
          !StringUtils.isNullOrEmpty(sup.getUscc()) ? sup.getUscc() : ""; // 统一社会信用代码
      String deptid = us != null ? us.getDepartCode() : ""; // 部门
      List<Contact> colist = contactDao.getContactListBySid(supplierid);
      String cname = "";
      String duty = "";
      String email = "";
      String isfault = "";
      String mobile = "";
      if (colist != null && colist.size() > 0) {
        for (Contact co : colist) {
          if (co != null) {
            cname += !StringUtils.isNullOrEmpty(co.getName()) ? co.getName() + "," : "*,";
            duty += !StringUtils.isNullOrEmpty(co.getDuty()) ? co.getDuty() + "," : "*,";
            email += !StringUtils.isNullOrEmpty(co.getMail()) ? co.getMail() + "," : "*,";
            mobile += !StringUtils.isNullOrEmpty(co.getPhone()) ? co.getPhone() + "," : "*,";
            isfault +=
                !StringUtils.isNullOrEmpty(co.getPhone()) && sup.getMobile().equals(co.getPhone())
                    ? "true,"
                    : "*,";
          }
        }
      }
      if (cname.length() > 0) {
        cname = cname.substring(0, cname.length() - 1);
      }
      if (duty.length() > 0) {
        duty = duty.substring(0, duty.length() - 1);
      }
      if (email.length() > 0) {
        email = email.substring(0, email.length() - 1);
      }
      if (mobile.length() > 0) {
        mobile = mobile.substring(0, mobile.length() - 1);
      }
      if (isfault.length() > 0) {
        isfault = isfault.substring(0, isfault.length() - 1);
      }

      String licence =
          !StringUtils.isNullOrEmpty(sup.getLicense()) ? sup.getLicense() : ""; // 生产经营许可证
      String operationType = type; // 操作类型
      String legalperson =
          !StringUtils.isNullOrEmpty(sup.getCorporate()) ? sup.getCorporate() : ""; // 法人代表
      String registerAddress = sup.getRegAddress(); // 注册地址
      String registeredCapital = ""; // 注册资本
      String supplierGrade = sup.getEnterpriseLevel(); // 供应商等级
      String supplierId = sup.getErpid(); // 供应商id
      String useCode = sup.getUseCode(); // 使用组织
      // 财务信息
      String accountName = ""; // 账户名称
      String bankNumber = ""; // 银行账号
      String openBank = ""; // 开户行
      String FPayCurrencyId = ""; // 结算币种
      String FInvoiceType = ""; // 发票类型
      String FTaxRegisterCode = ""; // 税务登记号
      String FTaxRateId = ""; // 默认税率
      String FSwiftCode = ""; // 银行代码
      List<Financial> financialList = financialDao.getFinancialListBySid(sup.getId());
      if (financialList != null && financialList.size() > 0) {
        for (Financial fin : financialList) {
          if (fin != null) {
            accountName +=
                !StringUtils.isNullOrEmpty(fin.getBankAccount())
                    ? fin.getBankAccount() + "@"
                    : "*@";
            bankNumber +=
                !StringUtils.isNullOrEmpty(fin.getBankNum()) ? fin.getBankNum() + "@" : "*@";
            openBank +=
                !StringUtils.isNullOrEmpty(fin.getBankName()) ? fin.getBankName() + "@" : "*@";
            FPayCurrencyId +=
                !StringUtils.isNullOrEmpty(fin.getSettleCurrency())
                    ? fin.getSettleCurrency() + ","
                    : "*,";
            FInvoiceType +=
                !StringUtils.isNullOrEmpty(fin.getInvoiceType())
                    ? fin.getInvoiceType() + ","
                    : "*,";
            FTaxRegisterCode +=
                !StringUtils.isNullOrEmpty(fin.getTaxNo()) ? fin.getTaxNo() + "," : "*,";
            FTaxRateId +=
                !StringUtils.isNullOrEmpty(fin.getTaxRate()) ? fin.getTaxRate() + "," : "*,";
            FSwiftCode +=
                !StringUtils.isNullOrEmpty(fin.getSwiftCode()) ? fin.getSwiftCode() + "," : "*,";
          }
        }
      }
      if (accountName.length() > 0) {
        accountName = accountName.substring(0, accountName.length() - 1);
      }
      if (bankNumber.length() > 0) {
        bankNumber = bankNumber.substring(0, bankNumber.length() - 1);
      }
      if (openBank.length() > 0) {
        openBank = openBank.substring(0, openBank.length() - 1);
      }
      if (FPayCurrencyId.length() > 0) {
        FPayCurrencyId = FPayCurrencyId.substring(0, FPayCurrencyId.length() - 1);
      }
      if (FInvoiceType.length() > 0) {
        FInvoiceType = FInvoiceType.substring(0, FInvoiceType.length() - 1);
      }
      if (FTaxRegisterCode.length() > 0) {
        FTaxRegisterCode = FTaxRegisterCode.substring(0, FTaxRegisterCode.length() - 1);
      }
      if (FTaxRateId.length() > 0) {
        FTaxRateId = FTaxRateId.substring(0, FTaxRateId.length() - 1);
      }
      if (FSwiftCode.length() > 0) {
        FSwiftCode = FSwiftCode.substring(0, FSwiftCode.length() - 1);
      }

      String country = "";
      String FAddress =
          !StringUtils.isNullOrEmpty(sup.getDetails()) ? sup.getDetails() : ""; // 邮寄地址
      if (Constants.SUPPLIERTYPE_CHINA.equals(resource)) {
        country = "&country=China";
      }
      String outstr =
          "ICNumber="
              + ICNumber
              + "&Purchaser="
              + Purchaser
              + "&accountName="
              + accountName
              + "&bankNumber="
              + bankNumber
              + "&busName="
              + busName
              + "&createCode="
              + createCode
              + "&createDate="
              + createDate
              + "&creditCode="
              + creditCode
              + "&deptid="
              + deptid
              + "&gender="
              + cname
              + "&duty="
              + duty
              + "&email="
              + email
              + "&licence="
              + licence
              + "&operationType="
              + operationType
              + "&phone="
              + mobile
              + "&receivablesBank="
              + "&legalperson="
              + legalperson
              + "&openBank="
              + openBank
              + "&registerAddress="
              + registerAddress
              + "&registeredCapital="
              + registeredCapital
              + "&supplierGrade="
              + supplierGrade
              + "&supplierId="
              + supplierId
              + "&useCode="
              + useCode
              + country
              + "&FPayCurrencyId="
              + FPayCurrencyId
              + "&FTaxRegisterCode="
              + FTaxRegisterCode
              + "&FInvoiceType="
              + FInvoiceType
              + "&FTaxRateId="
              + FTaxRateId
              + "&FContactIsDefault="
              + isfault
              + "&FAddress="
              + FAddress;
      if (Constants.SUPPLIERTYPE_ABROAD.equals(resource)) {
        country = !StringUtils.isNullOrEmpty(sup.getCountry()) ? sup.getCountry() : "";
        outstr += "&FSwiftCode=" + FSwiftCode + "&country=" + country; // 国家
      }
      log.info("[=====供应商同步入参=====]参数: " + outstr);
      // 发起POST请求获取凭证
      String str = commonHttpsUtil.httpRequest(supplier_url, "POST", outstr);
      if (!StringUtils.isNullOrEmpty(str)) {
        jsonObject = JSONObject.parseObject(str);
        return jsonObject;
      }
      return jsonObject;
    } catch (Exception e) {
      log.error("https请求异常：{" + ExceptionUtil.stacktraceToString(e) + "}");
      return jsonObject;
    }
  }

  /**
   * 禁用或启用供应商
   *
   * @param erpId 供应商erpId
   * @param operationType 操作类型(del--删除,banYes--禁用,banNo--启用)
   * @return
   */
  public JSONObject delOrBanSupplier(String erpId, String operationType) {
    JSONObject jsonObject = null;
    try {
      String outputStr = "erp_supplierid=" + erpId + "&operationType=" + operationType;
      // 发起GET请求获取凭证
      String str = commonHttpsUtil.httpRequest(supplier_ban_url, "POST", outputStr);
      if (!StringUtils.isNullOrEmpty(str)) {
        jsonObject = JSONObject.parseObject(str);
        return jsonObject;
      }
      return null;
    } catch (Exception e) {
      log.error("https请求异常：{" + ExceptionUtil.stacktraceToString(e) + "}");
      return null;
    }
  }

  /**
   * @Title: @Description:获取部门列表
   *
   * @param erpid
   * <AUTHOR>
   * @date 2019/12/11 15:42
   */
  public List<String> departList(String erpid) {
    List<String> ulist = new ArrayList<String>();
    try {
      String outputStr = "?erpId=" + erpid;
      String jsonString = commonHttpsUtil.httpRequest(depart_url + outputStr, "GET", null);
      if (jsonString != null) {
        JSONObject jsonData = JSONObject.parseObject(jsonString);
        if (jsonData != null) {
          JSONArray jsonArray = jsonData.getJSONArray("data");
          if (jsonArray != null && jsonArray.size() > 0) {
            for (int i = 0; i < jsonArray.size(); i++) {
              if (!StringUtils.isNullOrEmpty(jsonArray.getJSONObject(i).get("erpId").toString())) {
                List<User> curlist =
                    userDao.getUserByDepart(jsonArray.getJSONObject(i).get("erpId").toString());
                if (curlist != null && curlist.size() > 0) {
                  for (int j = 0; j < curlist.size(); j++) {
                    String uid =
                        curlist.get(j) != null
                                && !StringUtils.isNullOrEmpty(curlist.get(j).getErpId())
                            ? "'" + curlist.get(j).getErpId() + "'"
                            : "";
                    if (!StringUtils.isNullOrEmpty(uid) && !ulist.contains(uid)) {
                      ulist.add(uid);
                    }
                  }
                }
              }
            }
          }
        }
      }
      return ulist;
    } catch (Exception e) {
      log.error("https请求异常：{" + ExceptionUtil.stacktraceToString(e) + "}");
      return ulist;
    }
  }

  /**
   * @return @Title: uploadContract @Description:改变合同附件状态
   * <AUTHOR>
   * @date 2019年8月14日下午5:45:23
   */
  public JSONObject uploadContract(String numbers, String type) {
    JSONObject jsonObject = null;
    try {
      String outputStr = "numbers=" + numbers + "&type=" + type;
      // 发起GET请求获取凭证
      String str = commonHttpsUtil.httpRequest(contract_tag_url, "POST", outputStr);
      if (!StringUtils.isNullOrEmpty(str)) {
        jsonObject = JSONObject.parseObject(str);
      }
    } catch (Exception e) {
      log.error("https请求异常：{" + ExceptionUtil.stacktraceToString(e) + "}");
    }
    return jsonObject;
  }

  public JSONObject getBrandPageSearchByName(String brandName, int pageNo, int pageSize) {
    JSONObject jo = null;
    try {
      if (!StringUtils.isNullOrEmpty(brandName)) {
        brandName = URLEncoder.encode(brandName, "UTF-8");
      }
      String outStr = "brandName=" + brandName + "&pageNo=" + pageNo + "&pageSize=" + pageSize;
      String str = commonHttpsUtil.httpRequest(brand_searchByName, "POST", outStr);
      JSONObject brandJson = new JSONObject();
      if (!StringUtils.isNullOrEmpty(str)) {
        jo = JSONObject.parseObject(str);
        if (jo != null && "success".equals(jo.getString("msg"))) {
          JSONObject dataJo = jo.getJSONObject("data");
          if (dataJo != null) {
            JSONArray brandJa = dataJo.getJSONArray("content");
            brandJson.put("data", brandJa);
            brandJson.put("total", dataJo.getString("totalCount"));
          }
        }
      }
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e));
    }
    return jo;
  }

  /**
   * OA组织接口
   *
   * @return
   */
  public List<OrgTreeVO> getAllOaOrg() {
    JSONObject jsonObject;
    List<OrgTreeVO> result = new ArrayList();
    try {
      // 发起GET请求获取凭证
      String str = commonHttpsUtil.httpRequest(oa_org_url, "GET", null);
      JSONArray resData = new JSONArray();
      if (!StringUtils.isNullOrEmpty(str)) {
        jsonObject = JSONObject.parseObject(str);
        if (jsonObject != null) {
          String code = jsonObject.containsKey("code") ? jsonObject.getString("code") : "";
          if ("0".equals(code) && jsonObject.containsKey("data")) {
            resData = jsonObject.getJSONArray("data");
            if (resData != null && resData.size() > 0) {
              for (int i = 0; i < resData.size(); i++) {
                JSONObject jo = resData.getJSONObject(i);
                OrgTreeVO orgTreeVO = new OrgTreeVO();
                orgTreeVO.setId(jo.containsValue("id") ? jo.getString("id") : "");
                orgTreeVO.setName(jo.containsValue("name") ? jo.getString("name") : "");
                result.add(orgTreeVO);
              }
            }
          }
        }
      }
    } catch (Exception e) {
      log.error("https请求异常：{" + ExceptionUtil.stacktraceToString(e) + "}");
    }
    return result;
  }
}
