package com.xhgj.srm.api.dto.supplier;

import cn.hutool.core.util.StrUtil;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/11 20:37
 */
@Data
@NoArgsConstructor
public class SupplierFileDTO {
  @ApiModelProperty("附件名称")
  private String name;

  @ApiModelProperty("附件路径")
  private String url;

  public SupplierFileDTO(String name, String url) {
    this.name = name;
    if (!StringUtils.isNullOrEmpty(url) && !url.contains("srm/")) {
      url = "srm" + StrUtil.addPrefixIfNot(url, "/");
    }
    this.url = url;
  }

  public boolean isNotEmpty() {
    return !StringUtils.isNullOrEmpty(name) && !StringUtils.isNullOrEmpty(url);
  }
}
