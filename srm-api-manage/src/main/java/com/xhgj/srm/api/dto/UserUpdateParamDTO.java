package com.xhgj.srm.api.dto;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class UserUpdateParamDTO {

    @ApiModelProperty(value = "用户id",required = true)
    @NotEmpty(message = "用户id不能为空")
    private String id;
    @ApiModelProperty(value = "联系方式",required = true)
    @NotEmpty(message = "联系方式不能为空")
    private String mobile;
    @ApiModelProperty(value = "邮箱",required = true)
    @NotEmpty(message = "邮箱不能为空")
    private String mail;
    @ApiModelProperty(value = "部门",required = true)
    @NotEmpty(message = "srm部门不能为空")
    private String departId;
    @ApiModelProperty(value = "角色名称",required = true)
    @NotEmpty(message = "角色不能为空")
    private String role;
    @ApiModelProperty(value = "erp编码",required = true)
    @NotEmpty(message = "erp编码不能为空")
    private String erpCode;
    @ApiModelProperty(value = "erpId",required = true)
    @NotEmpty(message = "erpId不能为空")
    private String erpId;
    @ApiModelProperty(value = "是否组织分配")
    private String groupDistribute;
    @ApiModelProperty(value = "是否管理国际供应商")
    private String isManageAbroad;
    @ApiModelProperty(value = "审核关系")
    private List<CheckRelationDTO> checkRelationDTOList;


    public User updateUser(User user){
        user.setCode(erpCode);
        user.setErpId(erpId);
        user.setMail(mail);
        user.setGroupDistribute(!StringUtils.isNullOrEmpty(groupDistribute)?groupDistribute:"0");
        user.setIsManageAbroad(!StringUtils.isNullOrEmpty(isManageAbroad)?isManageAbroad:"0");
        user.setRole(role);
        user.setMobile(mobile);
        user.setName(mobile);
        user.setState(Constants.STATE_OK);
        return user;
    }


}
