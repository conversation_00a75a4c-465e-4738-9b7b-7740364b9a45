package com.xhgj.srm.api.dto.order;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;


@Data
public class OrderProductDetailDTO {

    @ApiModelProperty("明细id")
    private String id;
    @ApiModelProperty("物料编码")
    private String code;
    @ApiModelProperty("品牌")
    private String brand;
    @ApiModelProperty("商品名称")
    private String name;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("型号")
    private String description;
    @ApiModelProperty("数量")
    private BigDecimal num;
    @ApiModelProperty("单位")
    private String unit;
    @ApiModelProperty("单价")
    private BigDecimal price;
    @ApiModelProperty("发货数量")
    private BigDecimal delCount;
    @ApiModelProperty("未发数量")
    private BigDecimal unCount;
    @ApiModelProperty("退货数量")
    private BigDecimal reCount;
    @ApiModelProperty("取消数量")
    private BigDecimal cancelCount;
    @ApiModelProperty("是否可以发货")
    private Boolean isDisabled;

    @ApiModelProperty("销售单价")
    private BigDecimal salePrice;

    @ApiModelProperty("税率")
    private String rate;

    @ApiModelProperty("内部物料编码")
    private String innerCode;

    @ApiModelProperty("ERP 物料名称")
    private String erpProductName;
    /**
     * 去税单价
     */
    @ApiModelProperty("去税单价")
    private BigDecimal  taxFreeCbPrice;
    /**
     * 价税合计
     */
    @ApiModelProperty("价税合计")
    private BigDecimal totalAmountIncludingTax;
    /**
     * 合计金额
     */
    @ApiModelProperty("合计金额")
    private BigDecimal totalAmount;
    /**
     * 合计税额
     */
    @ApiModelProperty("合计税额")
    private BigDecimal totalTaxAmount;

    /**
     * 销售价税率
     */
    @ApiModelProperty("销售价税率")
    private String salesPriceTaxRate;
    /**
     * 成本价税率
     */
    @ApiModelProperty("成本价税率")
    private String costPriceTaxRate;
    @ApiModelProperty("行id")
    private String rowId;
    @ApiModelProperty("销售订单物料行id")
    private String rowNo;
  /**
   *
   * @param orderDetail
   */

    public OrderProductDetailDTO(OrderDetail orderDetail){
        this.id = orderDetail.getId();
        this.code = StringUtils.emptyIfNull(orderDetail.getCode());
        this.brand = StringUtils.emptyIfNull(orderDetail.getBrand());
        this.name = StringUtils.emptyIfNull(orderDetail.getName());
        this.model = StringUtils.emptyIfNull(orderDetail.getModel());
        this.num = BigDecimalUtil.formatForStandard(orderDetail.getNum());
        this.unit = StringUtils.emptyIfNull(orderDetail.getUnit());
        this.price = orderDetail.getPrice();
        this.delCount = BigDecimalUtil.formatForStandard(orderDetail.getShipNum());
        this.unCount = BigDecimalUtil.formatForStandard(orderDetail.getUnshipNum());
        this.reCount = BigDecimalUtil.formatForStandard(orderDetail.getReturnNum());
        BigDecimal remain =
            NumberUtil.sub(orderDetail.getNum(), orderDetail.getShipNum(), orderDetail.getCancelNum());
        // 如果remain数量小于等于0则isDisabled为true
        this.isDisabled = remain.compareTo(BigDecimal.ZERO) <= 0;
        this.cancelCount = BigDecimalUtil.formatForStandard(orderDetail.getCancelNum());
        this.salePrice = orderDetail.getSalePrice();
        this.rate = orderDetail.getRate();
        this.innerCode = StrUtil.emptyIfNull(orderDetail.getInnerCode());
        this.erpProductName = StrUtil.emptyIfNull(orderDetail.getErpProductName());
        this.taxFreeCbPrice = orderDetail.getTaxFreeCbPrice();
        this.totalAmountIncludingTax = orderDetail.getTotalAmountIncludingTax();
        this.totalAmount = orderDetail.getTotalAmount();
        this.totalTaxAmount = orderDetail.getTotalTaxAmount();
        this.salesPriceTaxRate = orderDetail.getSalesPriceTaxRate();
        this.costPriceTaxRate = orderDetail.getCostPriceTaxRate();
        this.rowId = orderDetail.getErpRowId();
        this.rowNo = orderDetail.getRowNo();
    }

}
