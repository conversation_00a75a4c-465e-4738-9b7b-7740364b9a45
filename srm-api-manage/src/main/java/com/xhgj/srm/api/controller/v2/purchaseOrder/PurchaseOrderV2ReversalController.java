package com.xhgj.srm.api.controller.v2.purchaseOrder;/**
 * @since 2025/4/28 14:20
 */

import com.xhgj.srm.api.controller.AbstractRestController;
import com.xhgj.srm.api.service.PurchaseOrderService;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2BaseService;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2ReturnService;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.repeat.annotation.RepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 *<AUTHOR>
 *@date 2025/4/28 14:20:17
 *@description
 */
@RestController
@RequestMapping("/v2/purchaseOrder")
@Validated
@Api(tags = {"采购订单冲销管理v2 api"})
public class PurchaseOrderV2ReversalController extends AbstractRestController  {
  @Resource
  private PurchaseOrderService purchaseOrderService;
  @Resource
  private PurchaseOrderV2BaseService purchaseOrderV2BaseService;
  @Resource
  private PurchaseOrderV2ReturnService purchaseOrderV2ReturnService;
  @ApiOperation("入库/退货单 冲销")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "orderToFormId", value = "入库/退货单 id", required = true)
  })
  @PostMapping(value = "receiptOrReturnReversal")
  @RepeatSubmit
  public ResultBean<Boolean> receiptOrReturnReversal(
      @RequestParam @NotBlank(message = "入库/退货单 " + "id 必传") String orderToFormId) {
    purchaseOrderService.receiptOrReturnReversal(orderToFormId);
    return new ResultBean<>(true);
  }

  @ApiOperation("退货单冲销")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "orderToFormId", value = "退货单 id", required = true)
  })
  @PostMapping(value = "returnReversal")
  @RepeatSubmit
  public ResultBean<Boolean> returnReversal(
      @RequestParam @NotBlank(message = "退货单 id 必传") String orderToFormId) {
    purchaseOrderV2ReturnService.returnReversal(orderToFormId);
    return new ResultBean<>(true);
  }

}
