package com.xhgj.srm.api.service.impl;

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.api.service.SupplierBizInfoService;
import com.xhgj.srm.jpa.dao.SupplierBizInfoDao;
import com.xhgj.srm.jpa.entity.SupplierBizInfo;
import com.xhgj.srm.jpa.repository.SupplierBizInfoRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/7/10 16:41
 */
@Service
public class SupplierBizInfoServiceImpl implements SupplierBizInfoService {
  @Autowired private SupplierBizInfoRepository repository;
  @Autowired private SupplierBizInfoDao dao;

  @Override
  public BootBaseRepository<SupplierBizInfo, String> getRepository() {
    return repository;
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void saveOrUpdate(String supplierId, String uscc, String industry) {
    Assert.notEmpty(supplierId);
    SupplierBizInfo info = repository.findById(supplierId).orElse(new SupplierBizInfo());
    info.setSupplierId(supplierId);
    repository.save(info);
  }
}
