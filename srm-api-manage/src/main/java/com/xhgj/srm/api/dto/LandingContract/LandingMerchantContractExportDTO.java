package com.xhgj.srm.api.dto.LandingContract;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LandingMerchantContractExportDTO {

  /** 合同号 */
  @ApiModelProperty("合同号")
  private String contractNo;

  /** 对方签约主体 */
  @ApiModelProperty("对方签约主体")
  private String secondSigningSupplierName;

  /** 项目/平台。 */
  @ApiModelProperty("项目/平台")
  private String platform;

  /** 合作形式 */
  @ApiModelProperty("合作形式")
  private String cooperationType;

  /** 我方签约主体 */
  @ApiModelProperty("我方签约主体")
  private String firstSigningGroupName;

  /** 业务员姓名。 */
  @ApiModelProperty("业务员姓名")
  private String salesmanName;

  /** 合作区域。 */
  @ApiModelProperty("合作区域")
  private String cooperationRegion;

  /** 合作类型 */
  @ApiModelProperty("合作类型")
  private String typeOfCooperation;

  /** 合作品牌。 */
  @ApiModelProperty("合作品牌")
  private String cooperationBrand;

  /** 供货折扣。 */
  @ApiModelProperty("供货折扣")
  private String initialDiscountRatio;

  /** 合同有效期。 */
  @ApiModelProperty("合同有效期")
  private String validityOfContract;

  /** 签章状态 */
  @ApiModelProperty("签章状态")
  private String signatureStatus;

  /** 合同状态 */
  @ApiModelProperty("合同状态")
  private String contractStatus;

  /** 账期 */
  @ApiModelProperty("账期")
  private String accountingPeriod;

  /** 账期（背靠背） */
  @ApiModelProperty("账期（背靠背)")
  private Boolean backToBack;

  /**
   * 付款条件，逗号隔开。（前后需要加一个逗号，如：,对方开票,客户回款,） {@link
   * com.xhgj.srm.common.enums.entryregistration.EntryRegistrationPaymentConditionEnum}}
   */
  @ApiModelProperty("付款条件")
  private String paymentTerms;

  /** 合同金额 */
  @ApiModelProperty("合同金额")
  private String contractAmount;

  /** 合同原件 */
  @ApiModelProperty("合同原件")
  private String contractOriginal;

  /** 补充协议 */
  @ApiModelProperty("补充协议")
  private String supplementaryAgreement;

  /** 创建人 */
  @ApiModelProperty("创建人")
  private String createMan;

  /** 创建时间 */
  @ApiModelProperty("创建时间")
  private String createTime;
}
