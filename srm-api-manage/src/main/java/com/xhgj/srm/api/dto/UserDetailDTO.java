package com.xhgj.srm.api.dto;


import com.xhgj.srm.api.dto.group.UserToGroupTableDTO;
import com.xhgj.srm.api.dto.user.BaseUserDTO;
import com.xhgj.srm.jpa.dto.user.UserRoleAware;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("用户详情对象")
public class UserDetailDTO extends BaseUserDTO implements UserRoleAware {
  @ApiModelProperty("员工名称")
  private String realName;
  @ApiModelProperty("分配组织部门")
  private List<UserToGroupTableDTO> userToGroupList;
  @ApiModelProperty("角色名称")
  private String roleName;
   @ApiModelProperty("组织id")
  private String orgId;
  @ApiModelProperty("邀请码")
  private String invitationCode;
  @ApiModelProperty("登陆账号")
  private String name;
  private String state;
  @ApiModelProperty("是否组织分配")
  private String groupDistribute;
  @ApiModelProperty("是否管理国际供应商")
  private String isManageAbroad;
 @ApiModelProperty("组织名称，多个用'，'分隔")
  private String userGroup;
 @ApiModelProperty("部门名称，多个用'，'分隔")
  private String depart;

}
