package com.xhgj.srm.api.vo.returnExchange;/**
 * @since 2025/2/13 11:42
 */
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.WarehouseEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormExecutionStatusEnum;
import lombok.Data;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/2/13 11:42:40
 *@description 新增退货单详情
 */
@Data
public class NewReturnVO {
  /**
   * 退库单id
   */
  private String id;

  /**
   * 退库时间
   */
  private Long time;

  /**
   * 来源 根据仓库判断
   */
  private String source;
  /**
   * 物流公司
   */
  private String logisticsCompany;
  /**
   * 物料编码
   */
  private String logisticsCode;
  /**
   * 快递单号
   */
  private String trackNum;
  /**
   * 物料凭证
   */
  private String productVoucher;
  /**
   * sap会计年度
   */
  private String productVoucherYear;
  /**
   * 备注
   */
  private String remark;
  /**
   * wms仓库执行状态 0 仓库未审批未执行 1仓库未审批未执行 2无需仓库执行
   * {@link com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormExecutionStatusEnum }
   */
  private String executionStatus;
  /**
   * wms仓库执行状态-值
   */
  private String executionStatusValue;
  /**
   * 退货仓库
   */
  private String returnWarehouse;
  /**
   * 收件人
   */
  private String consignee;
  /**
   * 收件人地址
   */
  private String receiveAddress;
  /**
   * 退库单物料明细
   */
  private List<NewReturnProductDetailVO>  returnProductDetails;

  /*
  public String getSource() {
    if (StrUtil.equals(WarehouseEnum.HAI_NING_DIRECT_SALES.getCode(), returnWarehouse)) {
      return "SRM";
    }
    return "WMS";
  }*/

  /**
   * wms仓库执行状态-值
   * @return
   */
  public String getExecutionStatusValue() {
    if (StrUtil.isBlank(executionStatus)) {
      return "";
    }
    SupplierOrderFormExecutionStatusEnum supplierOrderFormExecutionStatusEnum =
        SupplierOrderFormExecutionStatusEnum.fromKey(executionStatus);
    if (supplierOrderFormExecutionStatusEnum == null) {
      return "";
    }
    return supplierOrderFormExecutionStatusEnum.getValue();
  }

}
