package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.PurchaseOrderListDTO;
import com.xhgj.srm.api.dto.PurchaseOrderPageQuery;
import com.xhgj.srm.api.dto.PurchaseOrderProductListDTO;
import com.xhgj.srm.api.dto.PurchaseOrderProductTableHeaderQuery;
import com.xhgj.srm.api.dto.PurchaseOrderTableHeaderQuery;
import com.xhgj.srm.api.dto.SupplierOrderInternalRemarkParam;
import com.xhgj.srm.api.dto.UpdateSupplierOrderBaseInfoDTO;
import com.xhgj.srm.api.dto.purchase.order.AddPurchaseOrderDeliveryParam;
import com.xhgj.srm.api.dto.purchase.order.AddPurchaseOrderReturnParam;
import com.xhgj.srm.api.dto.purchase.order.ConsignmentReturnOrderParam;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderContractVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderDetailedVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderInvoiceVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderLargeTicketInfoDTO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderProductDetailedVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderReturnVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderWarehousingEntryInfoVO;
import com.xhgj.srm.api.dto.purchase.order.ShippingAndWarehousingInformationVO;
import com.xhgj.srm.api.dto.purchase.order.UpdateNotesParam;
import com.xhgj.srm.api.dto.purchase.order.UpdateProductDetailParam;
import com.xhgj.srm.api.dto.purchase.order.UpdateSupplierContactParam;
import com.xhgj.srm.api.dto.purchase.order.UpdateWarehouseLogisticsParam;
import com.xhgj.srm.api.dto.supplierorder.CancelPurchaseOrderListDTO;
import com.xhgj.srm.api.dto.supplierorder.CancelPurchaseOrderParam;
import com.xhgj.srm.api.dto.supplierorder.ExportPurchaseOrderProductParams;
import com.xhgj.srm.api.dto.supplierorder.OutBoundDeliveryPrams;
import com.xhgj.srm.api.dto.supplierorder.PuchaseOrderAddParams;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPaymentTermsPageVO;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPaymentTermsParam;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPrepaidApplicationPreInfoDTO;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderProductSearchForm;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderWarehousingDTO;
import com.xhgj.srm.api.dto.supplierorder.RetreatWarehouseDTO;
import com.xhgj.srm.api.dto.supplierorder.SupplierOrderCountDTO;
import com.xhgj.srm.api.dto.supplierorder.UnCancelPurchaseOrderDTO;
import com.xhgj.srm.api.dto.supplierorder.UserScreeningConditionParam;
import com.xhgj.srm.api.dto.supplierorder.WarehouseEntryListParams;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderOutBoundDeliveryStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderPaymentTermsStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderProductStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderWarehousingStatistics;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.request.dto.mdm.NameAndCodeDTO;
import com.xhgj.srm.request.dto.oms.SalesOrderListDTO;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * @ClassName PuchaseOrderService <AUTHOR> @Date 2023/12/13 11:22
 */
public interface PurchaseOrderService extends BootBaseService<SupplierOrder, String> {

  /**
   * 新增采购订单
   *
   * @param params
   * @return
   */
  String addPurchaseOrder(PuchaseOrderAddParams params);
  /**
   * 根据采购订单 id 获得订单明细
   *
   * @param id 采购订单 id
   */
  PurchaseOrderDetailedVO getPurchaseOrderDetailed(String id);

  /**
   * 根据采购订单 id 查询发货单
   *
   * @param id 采购订单 id 必传
   */
  List<PurchaseOrderInvoiceVO> getPurchaseOrderInvoiceInfo(String id);

  /**
   * 取消发货单
   *
   * @param orderToFormId 发货单 id 必传
   */
  void cancelInvoiceForm(String orderToFormId);

  /**
   * 新建发货单
   *
   * @param deliveryParam 发货单对象
   * @return 发货单id
   */
  void purchaseOrderDelivery(AddPurchaseOrderDeliveryParam deliveryParam, boolean isConfirmReceipt);
  /**
   * 确认入库
   *
   * @param orderToFormId 发货单 id 必传
   */
  void confirmReceipt(String orderToFormId);

  /**
   * 根据供应商 id 统计数量
   *
   * @param purchaseOrderId 采购订单 id
   * @return com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn.SupplierOrderCountDTO
   */
  SupplierOrderCountDTO getSupplierOrderFormCountById(String purchaseOrderId);

  /**
   * 分页查询采购订单
   *
   * @param user 用户
   * @param query 查询参数 必传
   * @param toPageable 分页参数 必传
   */
  PageResult<PurchaseOrderListDTO> getPagePurchaseOrderPage(
      User user, PurchaseOrderPageQuery query, Pageable toPageable);

  /**
   * 分页查询采购订单 Ref 重构
   * @param query
   * @return
   */
  PageResult<PurchaseOrderListDTO> getPagePurchaseOrderPageRef(PurchaseOrderPageQuery query);

  /**
   * 分页查询采购单物料信息 Ref 重构
   * @param form
   * @return
   */
  PageResult<PurchaseOrderProductListDTO> getPagePurchaseOrderProductPageRef(
      PurchaseOrderProductSearchForm form);

  /**
   * 取消订货
   *
   * @param params 参数
   */
  void cancelPurchaseOrder(CancelPurchaseOrderParam params);

  /**
   * 获取采购单集合
   *
   * @param purchaseOrderId 采购单id
   */
  List<CancelPurchaseOrderListDTO> getCancelPurchaseOrderList(String purchaseOrderId);

  /**
   * 反取消
   *
   * @param params 参数
   */
  void unCancelPurchaseOrder(UnCancelPurchaseOrderDTO params);

  /**
   * 入库/退货单 冲销
   * @param orderToFormId
   */
  void receiptOrReturnReversal(String orderToFormId);
  /**
   * @param id 采购订单物料行id
   * @return List<PurchaseOrderProductDetailedVO> 委外组件下的物料信息
   */
  List<PurchaseOrderProductDetailedVO> getPurchaseOrderProductDetailed(String id);

  /**
   * 根据采购订单 id 查询发货单
   *
   * @param id 采购订单 id 必传
   */
  List<PurchaseOrderWarehousingEntryInfoVO> getPurchaseOrderWarehousingEntryInfo(String id);

  /**
   * 更新备注
   */
  void updateNotes(UpdateNotesParam param);
  /**
   * 新建退货单
   * @param param
   */
  void addReturnOrder(AddPurchaseOrderReturnParam param);

  /**
   * 根据采购单查询退库单
   * @param id
   * @return
   */
  List<PurchaseOrderReturnVO> purchaseOrderReturn(String id);

  /**
   * 修改发货单
   */
  void updateShipForm(AddPurchaseOrderDeliveryParam param);

  /**
   * 查询采购单发货入库信息
   * @param id 采购单id
   */
  ShippingAndWarehousingInformationVO getShippingAndWarehousingInformationVO(String id);

  /**
   * 采购订单付款条件清单
   */
  PageResult<PurchaseOrderPaymentTermsPageVO> getPaymentTermsPage(User user,
      PurchaseOrderPaymentTermsParam param);

  /**
   * 采购订单付款条件统计
   * @param user
   * @param param
   * @return
   */
  PurchaseOrderPaymentTermsStatistics getPaymentTermsStatistics(User user, PurchaseOrderPaymentTermsParam param);

  /**
   * oms销售订单详情分页查询接口
   * @param salesOrgCode 销售组织
   * @param orderNo 订单号
   * @param projectNo 大票项目号
   */
  PageResult<SalesOrderListDTO> getSalesOrderDetail(String salesOrgCode, String orderNo,
      String projectNo, int pageNo, int pageSize);

  /**
   * @param sapComponentCode 公司代码
   * @param sapFactoryCode 工厂代码
   */
  List<NameAndCodeDTO> getSAPStockAddr(String name, String code, String sapComponentCode,
      String sapFactoryCode);

  /**
   * 获取订单合同
   * @param id 订单id
   * @return
   */
  PurchaseOrderContractVO getContractFiles(String id);

  /**
   * 导入采购合同
   * @param file
   * @param userId
   */
  void saveSupplierOrderExcel(MultipartFile file,String userId);

  SupplierOrderCountDTO getSupplierOrderCount(User user, PurchaseOrderPageQuery query);

  SupplierOrderCountDTO getOrderProductCount(User user,PurchaseOrderProductSearchForm query);


  byte[] printOutPurchaseOrderContract(String id);

  /**
   * 设置入库进度
   * @param supplierOrder 采购订单
   */
  String setStockProgress(SupplierOrder supplierOrder);

  /**
   * 获取采购订单的入库总数量
   */
  BigDecimal getInventoryQuantity(String supplierOrderId);

  /**
   * @description: 导出采购单物料信息
   * @param: user
   * @param: exportPurchaseOrderParams
   **/
  void exportPurchaseOrderProduct(ExportPurchaseOrderProductParams exportPurchaseOrderParams);
  /**
   * @description: 修改供方和收方联系人信息
   * @param:  UpdateSupplierContactParam
   **/
  void updateSupplierContact(UpdateSupplierContactParam param);
  /**
   * @description: 修改物料明细
   * @param: UpdateProductDetailParam
   **/
  void updateProductDetail(UpdateProductDetailParam param);

  /**
   * @param ids id集合
   * @return 订单预付申请前置信息
   */
  List<PurchaseOrderPrepaidApplicationPreInfoDTO> getPrepaidApplicationPreInfoByIds(List<String> ids, String applyId);

  /**
   * @param ids id集合
   * @return 订单预付申请前置信息
   */
  List<PurchaseOrderPrepaidApplicationPreInfoDTO> getPrepaidApplicationPreInfo(List<String> purchaseOrderNos, String applyId);


  /**
   * @description: 根据表头筛选采购物料数据
   * @param: param PurchaseOrderProductTableHeaderQuery
   **/
  List<Object> getProductListByTableHeaderRef(PurchaseOrderProductTableHeaderQuery param);

  /**
   * @description: 根据表头筛选采购订单数据
   * @param: param PurchaseOrderTableHeaderQuery
   **/
  List<Object> getOrderListByTableHeaderRef(PurchaseOrderTableHeaderQuery param);


  /**
   * @description: 入库单列表查询
   * @param param WarehouseEntryListParams
   */
  PageResult<PurchaseOrderWarehousingDTO> warehouseWarrantPageRef(WarehouseEntryListParams param,
      User user);

  /**
   * 采购订单入库单统计
   * @param param
   * @param user
   * @return
   */
  PurchaseOrderWarehousingStatistics warehouseStatistics(WarehouseEntryListParams param, User user);

  /**
   * @description: 退货单列表查询
   * @param param OutBoundDeliveryPrams
   */
  PageResult<RetreatWarehouseDTO> outBoundDeliveryPageRef(OutBoundDeliveryPrams param, User user);

  /**
   * 退库单统计
   * @param param
   * @param user
   * @return
   */
  PurchaseOrderOutBoundDeliveryStatistics outBoundDeliveryStatistics(OutBoundDeliveryPrams param, User user);

  /**
   * 退库单导出
   */
  void exportOutBoundDelivery(User user, OutBoundDeliveryPrams exportOutBoundDeliveryParams);

  /**
   * 入库单导出
   */
  void exportWarehouseWarrant(User user, WarehouseEntryListParams warehouseEntryListParams);

  /**
   * @description: 寄售订单退货
   * @param: ConsignmentReturnOrderParam
   */
  String consignmentReturnOrder(ConsignmentReturnOrderParam params);

  /**
   * @param purchaseOrder 采购订单
   * @return 获取该订单的最大可预付金额
   */
  BigDecimal getMaxAdvancePrice(String purchaseOrderCode, FinancialVoucher originFinancialVoucher);

  void updateSupplierOrderBaseInfo(UpdateSupplierOrderBaseInfoDTO params);

  /**
   * 根据采购订单号修改供应商开票状态
   *
   * @param code 采购订单号
   * @param supplierOpenInvoiceState 供应商开票状态
   */
  void updateOrderCode(String code, String supplierOpenInvoiceState);

  /**
   * 导入入库单已开票数量
   * @param file 文件
   * @param userId 用户 id
   */
  void importWarehouseInvoiceNum(MultipartFile file, String userId);
  /**
   * 提交内部注释
   * @param param 内部注释参数
   * @param user
   */
  void submitInternalRemark(SupplierOrderInternalRemarkParam param, User user);

  /**
   * 删除退库单
   */
  void deleteOutBound(String id,String sapRwoId);

  /**
   * @description: 处理入库错误数据
   * @param code 采购订单
   * @param orderState 订单状态
   * @param stockProgress 入库进度
   * @param finalPrice 实际订货金额
   **/
  void handleInboundErrorData(String code, String orderState, String stockProgress, BigDecimal finalPrice);

  /**
   * 查询预计导出入库单信息条数
   * @param param
   * @param user
   */
  Long getExportWarehouseWarrantCount(WarehouseEntryListParams param, User user);

  /**
   * 查询预计导出退库单信息条数
   * @param param
   * @param user
   */
  Long getExportOutBoundCount(OutBoundDeliveryPrams param, User user);

  /**
   * @description: 采购订单根据用户保存表头筛选条件
   * @param: UserAddScreeningConditionParam
   **/
  void addScreeningCondition(UserScreeningConditionParam param);

  /**
   * @description: 获取采购订单用户表头筛选条件
   * @param: userId
   **/
  @Deprecated
  UserScreeningConditionParam getScreeningConditionByUser(String userId);
  /**
   * @description: 修改入库单物流信息
   * @param: UpdateWarehouseLogisticsParam
   **/
  void updateWarehouseLogistics(UpdateWarehouseLogisticsParam param);

  /**
   * @param id 采购订单id
   * @return 采购订单合同附件oss下载地址
   */
  String getContractAttachment(String id);

  /**
   * 导入入库单已开票数量
   * @param file 文件
   * @param userId 用户 id
   */
  void importReturnRedInvoiceNum(MultipartFile file, String userId);

  /**
   * 采购订单物料统计
   * @param form
   * @return
   */
  PurchaseOrderProductStatistics getPagePurchaseOrderStatisticsForProduct(PurchaseOrderProductSearchForm form);

  /**
   * 采购订单统计
   * @param query
   * @return
   */
  PurchaseOrderStatistics getPagePurchaseOrderStatisticsForOrder(PurchaseOrderPageQuery query);

  void importOutBoundDelivery(MultipartFile file, User user);

  /**
   * 获取大票信息
   * @param projectNos 项目编码
   * @return
   */
  PurchaseOrderLargeTicketInfoDTO getLargeTicketInfoByProjectNoList( String projectNos);
}
