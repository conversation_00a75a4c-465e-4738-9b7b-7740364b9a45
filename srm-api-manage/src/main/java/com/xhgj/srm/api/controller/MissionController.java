package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.dto.Mission.MissionPageData;
import com.xhgj.srm.api.dto.Mission.MissionPageDetailParam;
import com.xhgj.srm.api.service.MissionService;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;

@Api(tags = {"任务中心"})
@RestController
@RequestMapping("/mission")
@Slf4j
public class MissionController {

    @Autowired
    private MissionService missionService;

  @ApiOperation(value = "分页获取任务中心列表")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "type", value = "操作类型"),
      @ApiImplicitParam(name = "state", value = "状态"),
      @ApiImplicitParam(name = "startDate", value = "开始时间"),
      @ApiImplicitParam(name = "endDate", value = "结束时间"),
      @ApiImplicitParam(name = "code", value = "任务编码"),
      @ApiImplicitParam(name = "userId", value = "用户Id")
  })
  @RequestMapping(value = "/getMissionPage", method = RequestMethod.GET)
  @ResponseBody
  public ResultBean<PageResult<MissionPageData>> getMissionPage(
      String type,
      String state,
      String startDate,
      String endDate,
      String code,
      String userId,
      @RequestParam(defaultValue = "1") Integer pageNo,
      @RequestParam(defaultValue = "10") Integer pageSize
  ) {
    return new ResultBean<>(missionService.getMissionPage(type,state,startDate,endDate,code,userId,pageNo,pageSize));
  }

    @ApiOperation(value = "分页获取任务中心详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "任务id"),
    })
    @RequestMapping(value = "/getMissionDetailPage", method = RequestMethod.GET)
    @ResponseBody
    public ResultBean<MissionPageDetailParam> getMissionDetailPage(
            String id,
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        return new ResultBean<>(missionService.getMissionDetailPage(id,pageNo,pageSize));
    }

    @SneakyThrows
    @ApiOperation(value = "导出失败任务详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "任务id"),
    })
    @RequestMapping(value = "/exportMissionDetail", method = RequestMethod.GET)
    public ResponseEntity<byte[]> exportMissionDetail(
            String id
    )  {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", URLEncoder.encode("missionDetail.xlsx", "UTF-8"));
        byte[] bytes = missionService.exportMission(id);
        return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
    }


}
