package com.xhgj.srm.api.dto.purchase.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel
public class PurchaseOrderWarehousingEntryInfoVO {
  @ApiModelProperty("发货单 id")
  private String id;
  @ApiModelProperty("入库时间")
  private Long warehousingTime;
  @ApiModelProperty("物流公司")
  private String logisticsCompany;
  @ApiModelProperty("物流编码")
  private String logisticsCode;
  @ApiModelProperty("快递单号")
  private String trackNum;
  @ApiModelProperty("单据状态")
  private String state;
  @ApiModelProperty("是否已入库")
  private Boolean warehousing;
  @ApiModelProperty("物料明细")
  private List<PurchaseOrderInvoiceProductVO> shipProductDTOList;
  @ApiModelProperty("来源")
  private String source;
  @ApiModelProperty("SAP物料凭证号")
  private String productVoucherNo;
  @ApiModelProperty("SAP物料凭证年度")
  private String productVoucherAnnual;
  @ApiModelProperty("SAP冲销单物料凭证号")
  private String sapReversalNo;
  @ApiModelProperty("发货单id")
  private String shippingOrderId;
  @ApiModelProperty("是否冲销")
  private Boolean reversalStatus;
  @ApiModelProperty("采购单号")
  private String orderNo;
  @ApiModelProperty("采购单id")
  private String orderId;
}
