package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xhgj.srm.api.dto.InventoryDTO;
import com.xhgj.srm.common.dto.SearchSupplierOrderDetailDTO;
import com.xhgj.srm.common.dto.SearchSupplierOrderDetailForm;
import com.xhgj.srm.dto.InventoryQueryForm;
import com.xhgj.srm.api.service.InventoryService;
import com.xhgj.srm.api.service.MissionService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Redis;
import com.xhgj.srm.common.utils.CommonlyUseUtil;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.dto.InventoryProductInfoDTO;
import com.xhgj.srm.dto.InventoryProductInfoDTO.DataDTO;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dao.InventoryDao;
import com.xhgj.srm.jpa.dao.SupplierOrderDetailDao;
import com.xhgj.srm.jpa.entity.Inventory;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.jpa.repository.InventoryRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.jpa.util.BatchQueryUtil;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.dispatcher.MissionDispatcher;
import com.xhgj.srm.request.dto.sap.SapInventoryQueryForm;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.request.vo.sap.SapInventoryVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
 * InventoryServiceImpl
 */
@Service
@Slf4j
public class InventoryServiceImpl implements InventoryService {

  @Resource
  private InventoryRepository repository;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;

  @Resource
  private HttpUtil httpUtil;

  @Resource
  private SAPService sapService;

  @Resource
  private RedisUtil redisUtil;

  @Resource
  private InventoryDao inventoryDao;
  @Resource
  private UserService userService;
  @Resource
  private MissionService missionService;
  @Autowired
  private MissionDispatcher missionDispatcher;
  @Resource
  private SupplierOrderDetailDao supplierOrderDetailDao;
  @Resource
  private InventoryLocationRepository inventoryLocationRepository;
  @Override
  public BootBaseRepository<Inventory, String> getRepository() {
    return repository;
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_INVENTORY)
  public PageResult<InventoryDTO> getAllInventoryList(InventoryQueryForm param) {
    Page<Inventory> inventoryPage = inventoryDao.getAllInventoryList(param.toQueryMap());
    List<Inventory> content = inventoryPage.getContent();
    if (CollUtil.isEmpty(content)) {
      return PageResult.empty(param.getPageNo(), param.getPageSize());
    }
    List<String> orderCodes =
        content.stream().map(Inventory::getOrderCode).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
    List<SupplierOrder> supplierOrders = BatchQueryUtil.batchInQuery(orderCodes,
        batchIds -> supplierOrderRepository.findAllByCodeInAndStateIn(batchIds,
            CollUtil.toList(Constants.STATE_OK, Constants.STATE_LOCKED)));
    // 将SupplierOrder转换为Map，方便后续查找
    Map<String, SupplierOrder> supplierOrderMap = supplierOrders.stream()
        .collect(Collectors.toMap(SupplierOrder::getCode, Function.identity(), (v1, v2) -> v1));
    List<InventoryDTO> result = content.stream().map(item -> {
      SupplierOrder supplierOrder = supplierOrderMap.get(item.getOrderCode());
      InventoryDTO inventoryDTO = new InventoryDTO(item,supplierOrder);
      String inventoryLocationId =
          inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(item.getGroupCode(),
                  item.getWarehouse(), Constants.STATE_OK).map(InventoryLocation::getId)
              .orElse(StrUtil.EMPTY);
      inventoryDTO.setInventoryLocationId(inventoryLocationId);
      return inventoryDTO;
    }).collect(Collectors.toList());
    if (Boolean.TRUE.equals(param.getForceRefresh())) {
      this.forceRefresh(result);
    }
    return new PageResult<>(result, inventoryPage.getTotalElements(), inventoryPage.getTotalPages(),
        param.getPageNo(), param.getPageSize());
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_INVENTORY)
  public List<InventoryDTO> getCurrentInventoryList(InventoryQueryForm param) {
    if (StrUtil.isNotBlank(param.getGroupCode()) && StrUtil.isAllBlank(param.getProductCode(),
        param.getWarehouse(),param.getBatchNo(),param.getBrand(),param.getModel(),
        param.getPurchaseMan(),param.getPurchaseDepartment(),param.getName())) {
      return Collections.emptyList();
    }
    // 1. 构建SAP库存查询表单
    SapInventoryQueryForm queryForm = buildSapInventoryQueryForm(param);
    // 2. 调用SAP服务查询库存信息
    List<SapInventoryVO> sapInventoryVOList = sapService.sapInventoryQuery(queryForm);
    if (CollUtil.isEmpty(sapInventoryVOList)) {
      return Collections.emptyList();
    }
    // 3. 将SapInventoryVO转换为InventoryDTO，并关联供应商订单信息
    List<InventoryDTO> inventoryDTOList = convertToInventoryDTOList(sapInventoryVOList);
    // 4. 获取所有产品的编码集合
    Set<String> codes =
        inventoryDTOList.stream().map(InventoryDTO::getProductCode).collect(Collectors.toSet());
    // 5. 根据产品编码获取产品详细信息，并更新InventoryDTO
    if (CollUtil.isNotEmpty(codes)) {
      updateInventoryDTOsWithProductInfo(inventoryDTOList, codes);
    }
    if (Boolean.TRUE.equals(param.getForceRefresh())) {
      this.forceRefresh(inventoryDTOList);
    }
    if (Boolean.TRUE.equals(param.getIsFilterTotalZero())) {
      List<InventoryDTO> filterTotalInventoryList = inventoryDTOList.stream().filter(
          item -> item.getInventoryTotalNumber() != null && !NumberUtil.equals(
              item.getInventoryTotalNumber(), BigDecimal.ZERO)).collect(Collectors.toList());
      return filterTotalInventoryList;
    }
    return inventoryDTOList;
  }

  @Override
  public void forceRefresh(List<InventoryDTO> list) {
    // 过滤出未税单价为空的数据
    List<InventoryDTO> filter = list.stream().filter(item -> item.getUnTaxPrice() == null
        || item.getUnTaxPrice().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
    if (CollUtil.isEmpty(filter)) {
      return;
    }
    log.info("forceRefresh inventory list size:{}", filter.size());
    // 如果有未税单价为空的数据，再次查询未税单价
    // 根据批次在srm入库单列表查一下对应批次&物料的含税单价
    List<SearchSupplierOrderDetailForm> productCode2BatchNo = filter.stream()
        .map(item -> new SearchSupplierOrderDetailForm(item.getProductCode(), item.getBatchNo()))
        .collect(Collectors.toList());
    // 分批查询
    List<List<SearchSupplierOrderDetailForm>> partition = Lists.partition(productCode2BatchNo, 500);
    List<SearchSupplierOrderDetailDTO> supplierOrderDetails = Lists.newArrayList();
    for (List<SearchSupplierOrderDetailForm> searchSupplierOrderDetailForms : partition) {
      List<SearchSupplierOrderDetailDTO> part = supplierOrderDetailDao.findByProductCodeAndBatchNo(searchSupplierOrderDetailForms);
      supplierOrderDetails.addAll(part);
    }
    Map<String, SearchSupplierOrderDetailDTO> detailDTOMap =
        supplierOrderDetails.stream().collect(Collectors.toMap(
            item -> item.getProductCode() + item.getBatchNo(),
            Function.identity(), (v1, v2) -> v1));
    // 更新未税单价
    filter.forEach(item -> {
      SearchSupplierOrderDetailDTO detailDTO =
          detailDTOMap.get(item.getProductCode() + item.getBatchNo());
      if (detailDTO != null) {
        item.setUnTaxPrice(detailDTO.getNetPrice());
      }
    });
  }

  /**
   * 根据产品编码获取产品详细信息，并更新InventoryDTO
   */
  private void updateInventoryDTOsWithProductInfo(List<InventoryDTO> inventoryDTOList, Set<String> productCodes) {
    JSONObject jsonObject = httpUtil.getInventoryProductInfoByMPM(productCodes);
    InventoryProductInfoDTO result = JSONObject.toJavaObject(jsonObject, InventoryProductInfoDTO.class);

    if (result != null && ResultBean.SUCCESS == result.getCode() && CollUtil.isNotEmpty(result.getData())) {
      Map<String, DataDTO> code2DataMap = result.getData().stream()
          .collect(Collectors.toMap(DataDTO::getCode, Function.identity()));

      inventoryDTOList.forEach(inventoryDTO -> {
        DataDTO dataDTO = code2DataMap.get(inventoryDTO.getProductCode());
        if (dataDTO != null) {
          inventoryDTO.setBrand(CommonlyUseUtil.buildBrandName(dataDTO.getBrandNameCn(), dataDTO.getBrandNameEn()));
          inventoryDTO.setModel(dataDTO.getManuCode());
          inventoryDTO.setDescription(dataDTO.getDesc());
          inventoryDTO.setUnit(dataDTO.getUnitName());
          inventoryDTO.setUnitCode(dataDTO.getUnitCode());
          inventoryDTO.setProductManager(dataDTO.getProductManagerName());
          inventoryDTO.setProjectQuote(dataDTO.getProjectQuotationName());
          inventoryDTO.setOneCategory(dataDTO.getCategoryOneName());
          inventoryDTO.setTwoCategory(dataDTO.getCategoryTwoName());
          inventoryDTO.setThreeCategory(dataDTO.getCategoryThreeName());
          inventoryDTO.setFourCategory(dataDTO.getCategoryFourName());
        }
      });
    }
  }



  /**
   * 将SapInventoryVO列表转换为InventoryDTO列表，并关联供应商订单信息
   */
  private List<InventoryDTO> convertToInventoryDTOList(List<SapInventoryVO> sapInventoryVOList) {
    List<String> orderCodes =
        sapInventoryVOList.stream().map(SapInventoryVO::getOrderNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
    List<SupplierOrder> supplierOrders = BatchQueryUtil.batchInQuery(orderCodes,
        batchIds -> supplierOrderRepository.findAllByCodeInAndStateIn(batchIds,
            CollUtil.toList(Constants.STATE_OK, Constants.STATE_LOCKED)));
    // 将SupplierOrder转换为Map，方便后续查找
    Map<String, SupplierOrder> supplierOrderMap = supplierOrders.stream()
        .collect(Collectors.toMap(SupplierOrder::getCode, Function.identity(), (v1, v2) -> v1));
    return sapInventoryVOList.stream().map(item -> {
      SupplierOrder supplierOrder = supplierOrderMap.get(item.getOrderNo());
      return new InventoryDTO(item, supplierOrder);
    }).collect(Collectors.toList());
  }


  /**
   * 构建SAP库存查询表单
   */
  private SapInventoryQueryForm buildSapInventoryQueryForm(InventoryQueryForm param) {
    SapInventoryQueryForm queryForm = new SapInventoryQueryForm();
    queryForm.setUserGroup(param.getGroupCode());
    queryForm.setProductCode(param.getProductCode());
    queryForm.setWarehouse(param.getWarehouse());
    queryForm.setBatchNo(param.getBatchNo());
    queryForm.setBrand(param.getBrand());
    queryForm.setManuCode(param.getModel());
    queryForm.setPurchaseMan(param.getPurchaseMan());
    queryForm.setPurchaseDept(param.getPurchaseDepartment());
    queryForm.setProductName(param.getName());
    return queryForm;
  }

  @Override
  public Map<String, Long> getLastSyncTime() {
    HashMap<String, Long> resultMap = new HashMap<>();
    Object lastUpdateTimeBySap = redisUtil.get(
        StrUtil.format(Constants_Redis.CACHE_LAST_UPDATE_INVENTORY_BY_SAP));
    resultMap.put("lastUpdateTimeBySap", Convert.toLong(lastUpdateTimeBySap));
    return resultMap;
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_INVENTORY)
  public void export(InventoryQueryForm param, User user) {
    checkExportPermission(user.getId());
    param.setPageNo(1);
    param.setPageSize(Integer.MAX_VALUE);
    Map<String, Object> queryMap = param.toQueryMap();
    queryMap.put("version", ShardingContext.getVersion());
    Mission mission =
        missionService.createMission(user, "导出-库存列表", Constants.PLATFORM_TYPE_AFTER, null,
            null);
    missionDispatcher.doDispatch(mission.getId(), JSON.toJSONString(queryMap),
        MissionTypeEnum.BATCH_TASK_EXPORT_INVENTORY_LIST);
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_INVENTORY)
  public Long getExportCount(InventoryQueryForm param) {
    return CollUtil.isNotEmpty(param.getIds()) ? Long.valueOf(param.getIds().size())
        : this.getAllInventoryList(param).getTotalCount();
  }

  private void checkExportPermission(String userId) {
    String permissionCode =
        userService.getPermissionByType(userId, Constants.USER_PERMISSION_EXPORT_INVENTORY);
    permissionCode = StrUtil.blankToDefault(permissionCode, Constants.NOT_EXPORT_IMPORT_KEY);
    if (StrUtil.equals(permissionCode, Constants.NOT_EXPORT_IMPORT_KEY)) {
      throw new CheckException("您没有导出库存列表的权限！");
    }
  }
}
