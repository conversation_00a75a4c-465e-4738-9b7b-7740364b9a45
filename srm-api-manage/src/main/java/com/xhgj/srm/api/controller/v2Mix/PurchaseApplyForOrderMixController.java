
package com.xhgj.srm.api.controller.v2Mix;

import com.xhgj.srm.api.controller.AbstractRestController;
import com.xhgj.srm.api.dto.purchase.order.*;
import com.xhgj.srm.api.service.PurchaseApplyForOrderService;
import com.xhgj.srm.jpa.annotations.VersionQuery;
import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * Created by <PERSON><PERSON> on 2023/12/10
 */
@RestController
@RequestMapping("/purchaseApplyForOrder/mix")
@Validated
@Api(tags = {"采购单申请相关api"})
public class PurchaseApplyForOrderMixController extends AbstractRestController {

  @Resource
  private PurchaseApplyForOrderService purchaseOrderService;

  @ApiOperation(value = "采购申请订单列表", notes = "采购申请订单列表")
  @RequestMapping(value = "/page", method = {RequestMethod.GET, RequestMethod.POST})
  @VersionQuery(VersionEnum.V2)
  public ResultBean<PageResult<PurchaseApplyForOrderPageVO>> findPage(
      PurchaseApplyForOrderPageParam param) {
    return new ResultBean<>(purchaseOrderService.findPage(param));
  }

  @ApiOperation(value = "采购申请订单详情", notes = "采购申请订单详情")
  @GetMapping(value = "/details")
  @VersionQuery(VersionEnum.V2)
  public ResultBean<PurchaseApplyForOrderDetailsVO> findPurchaseApplyForOrderDetails(
      @NotBlank String id) {
    return new ResultBean<>(purchaseOrderService.findPurchaseApplyForOrderDetails(id));
  }

  @ApiOperation(value = "采购申请订单物料详情", notes = "采购申请订单物料详情")
  @GetMapping(value = "/productDetails")
  @VersionQuery(VersionEnum.V2)
  public ResultBean<PurchaseApplyForOrderProductDetailsVO> findPurchaseApplyForOrderProductDetails(
      @NotBlank String id) {
    return new ResultBean<>(purchaseOrderService.findPurchaseApplyForOrderProductDetails(id));
  }

  @ApiOperation(value = "关联采购申请单详情", notes = "关联采购申请单详情")
  @GetMapping(value = "/purchaseApplyOrderDetail")
  @VersionQuery(VersionEnum.V2)
  public ResultBean<PuchaseApplyOrderDetailVO> purchaseApplyOrderDetail(
      @ApiParam("采购申请单id") @NotBlank String id) {
    return new ResultBean<>(purchaseOrderService.purchaseApplyOrderDetail(id));
  }
}
