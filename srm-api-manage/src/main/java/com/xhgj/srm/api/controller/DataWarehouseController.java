package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.service.DataWarehouseService;
import com.xhgj.srm.common.dto.MDMBankInfoDTO;
import com.xhgj.srm.request.service.third.xhgj.impl.XhgjEdgeServiceImpl;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by Geng Shy on 2023/8/3
 */
@RestController
@RequestMapping("/data-warehouse")
public class DataWarehouseController {

  @Resource
  private DataWarehouseService service;
  @Resource
  private XhgjEdgeServiceImpl xhgjEdgeService;

  /**
   * 分页查询网点信息，只查询第一页,默认二十行
   * @param branchName 网点名称
   */
  @ApiOperation(value = "查询全部银行网点")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "branchName", value = "网点名称")
  })
  @GetMapping("/bank/branch/page")
  public String pageQueryBankBranch(String branchName,String interBankNum, String branchNameOrInterBankNum) {
    int pageNo = 1;
    int pageSize = 30;
    return service.pageQueryBankBranchByBranchNameLike(branchName, interBankNum, branchNameOrInterBankNum, pageNo, pageSize);
  }

  @ApiOperation(value = "查询银行联行号")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "branchName", value = "网点名称")
  })
  @GetMapping("/bank/branch/interbank-num")
  public String queryInterBankNum(@NotBlank String branchName) {
    return service.queryInterBankNum(branchName);
  }


  @ApiOperation(value = "查询全部银行网点(新)")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "branchName", value = "开户行名称"),
      @ApiImplicitParam(name = "bankCode", value = "联行号"),
      @ApiImplicitParam(name = "bankNameOrBankCode", value = "开户行名称或联行号")
  })
  @GetMapping("/bank/branch/pageNew")
  public MDMBankInfoDTO pageQueryBankBranchByEdge(String branchName, String bankCode,String bankNameOrBankCode) {
    return xhgjEdgeService.pageQueryBankBranchByEdge(branchName, bankCode,bankNameOrBankCode);
  }

}
