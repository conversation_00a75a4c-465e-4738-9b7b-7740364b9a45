package com.xhgj.srm.api.task;

import cn.hutool.core.date.DateUtil;
import com.xhgj.srm.api.service.CustomerReceivableService;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/** <AUTHOR> @ClassName CustomerReceivalbeTask */
@Component
public class CustomerReceivableTask {
  private static final String START_TIME = "22-04-01";
  @Autowired private CustomerReceivableService customerReceivableService;

  /** 定时同步应收款信息 */
//  @Scheduled(cron = "0 0 0 * * ?")

  /**
   * （10/30）排查客户回款状态二次更新问题时，怀疑是此任务更新时导致。由于5.8.0版本已经做了从OMS同步回款信息，故此流程注释掉
   */
  private void syncCustomerReceivable() {
    // 当前时间
    String formatDate = DateUtil.formatDate(new Date());
    customerReceivableService.initProjectReceivable(START_TIME, formatDate);
  }
}
