package com.xhgj.srm.api.service.impl;

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.api.dto.supplier.BaseSupplierDTO;
import com.xhgj.srm.api.factory.MapStructFactory;
import com.xhgj.srm.api.service.SupplierInGroupTempService;
import com.xhgj.srm.api.utils.SupplierInGroupUtil;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.SupplierInGroupTemp;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.SupplierInGroupTempRepository;
import com.xhgj.srm.map.domain.IgnoreFieldContext;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/7/20 21:38
 */
@Service
public class SupplierInGroupTempServiceImpl implements SupplierInGroupTempService {
  @Autowired private SupplierInGroupTempRepository repository;
  @Autowired private SupplierInGroupUtil supplierInGroupUtil;

  @Override
  public BootBaseRepository<SupplierInGroupTemp, String> getRepository() {
    return repository;
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public SupplierInGroupTemp createBySupplierInGroup(SupplierInGroup supplierInGroup,
      BaseSupplierDTO dto,User user) {
    Assert.notNull(supplierInGroup);
    String supplierInGroupId = supplierInGroup.getId();
    Assert.notEmpty(supplierInGroupId);
    SupplierInGroupTemp temp = new SupplierInGroupTemp();
    MapStructFactory.INSTANCE.updateSupplierInGroupTemp(supplierInGroup, temp, IgnoreFieldContext.create("id"));
    temp.setCreateMan(user.getId());
    temp.setPayType(supplierInGroup.getSupplier().getPayType());
    temp.setPayTypeOther(supplierInGroup.getSupplier().getPayType());
    repository.save(temp);
    String enterpriseLevel = supplierInGroup.getEnterpriseLevel();
    //  创建关联关系
    supplierInGroupUtil.saveSupplierRelations(
        temp,
        null,
        enterpriseLevel,
        supplierInGroupUtil.getFinancialDTOListBySupplierInGroupId(supplierInGroupId),
//        supplierInGroupUtil.getContactDTOListBySupplierInGroupId(supplierInGroupId),
        dto != null ? dto.getContacts() : supplierInGroupUtil.getContactDTOListBySupplierInGroupId(supplierInGroupId),
        supplierInGroupUtil.getBrandDTOListBySupplierInGroupId(supplierInGroupId, false),
        supplierInGroupUtil.getSupplierCategorySaveFormBySupplierInGroupId(supplierInGroupId),
        supplierInGroupUtil.getSupplierInGroupAgreement(supplierInGroup),
        supplierInGroupUtil.getSupplierInGroupEvaluationTable(supplierInGroup),
        supplierInGroupUtil.getSupplierFiles(supplierInGroupId),
        user,
        supplierInGroup.getId()
        );
    return repository.save(temp);
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public SupplierInGroupTemp createByDTO(
      BaseSupplierDTO dto, SupplierInGroup supplierInGroup, User user, Group group) {
    SupplierInGroupTemp temp = new SupplierInGroupTemp();
    temp.setGroup(group);
    temp.setPurchaser(supplierInGroup.getPurchaser());
    temp.setCreateMan(user.getId());
    temp.setPayType(dto.getPayType());
    temp.setPayTypeOther(dto.getPayTypeOther());
    supplierInGroupUtil.saveSupplierInGroupFieldByDTO(temp, dto, supplierInGroup.getSupplier());
    repository.save(temp);
    supplierInGroupUtil.saveSupplierRelations(temp, supplierInGroup, dto, user);
    return repository.save(temp);
  }
}
