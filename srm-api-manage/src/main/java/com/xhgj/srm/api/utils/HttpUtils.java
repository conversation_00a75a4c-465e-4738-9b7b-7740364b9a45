package com.xhgj.srm.api.utils;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.http.HttpUtil;
import com.xhiot.boot.core.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * Created on 2020/6/9 18:04
 */
@Slf4j
@Service
@Component
public class HttpUtils {
    /**
     * hutool插件处理请求项目(仅限于Get请求)
     * @param url
     * @return
     */
    public static String getHttpUtils(String url){
        String getResult = "";
        try{
            if(!StringUtils.isNullOrEmpty(url)){
                getResult = HttpUtil
                        .createGet(url)
                        .timeout(5000)
                        .header("Content-Type","application/json")
                        .execute()
                        .charset("UTF-8")
                        .body();
            }
        }catch (Exception e){
            log.error(e.toString());
            return null;
        }
        return getResult;
    }

    public static String commonRequestPost(String url) {
        try {
            HttpClient httpClient = new HttpClient();
            HttpMethod method = new PostMethod(url);
            httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(5000);
            method.addRequestHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
            method.getParams().setContentCharset("UTF-8");
            httpClient.executeMethod(method);
            return method.getResponseBodyAsString();
        } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e));
            return null;
        }

    }

}
