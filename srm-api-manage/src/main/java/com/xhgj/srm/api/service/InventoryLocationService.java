package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.CheckReversalInventoryParam;
import com.xhgj.srm.api.dto.InventoryLocationDTO;
import com.xhgj.srm.api.dto.InventoryLocationQueryForm;
import com.xhgj.srm.api.dto.InventoryLocationUpdateParam;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.web.multipart.MultipartFile;

/**
 * InventoryLocationService
 */
public interface InventoryLocationService extends BootBaseService<InventoryLocation, String> {

  PageResult<InventoryLocationDTO> getInventoryLocationList(InventoryLocationQueryForm param);

  void batchUpdateInventoryLocation(InventoryLocationUpdateParam param);

  void syncInventoryLocationListByMDM(User user);

  void syncInventoryLocationListTask();

  void importInventoryLocation(MultipartFile file, User user);

  void checkInventoryLocation(CheckReversalInventoryParam param);
}
