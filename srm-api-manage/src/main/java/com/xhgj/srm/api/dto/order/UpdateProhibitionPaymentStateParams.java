package com.xhgj.srm.api.dto.order;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR> @date 2023/7/20
 */
@Data
public class UpdateProhibitionPaymentStateParams {
  @ApiModelProperty("用户id")
  @NotBlank(message = "缺少必要参数")
  private String userId;
  @ApiModelProperty("订单id集合")
  @NotEmpty(message = "缺少必要参数")
  private List<String> orderIds;
  @ApiModelProperty("禁止付款状态； true表示禁止，false表示不禁止")
  private Boolean prohibitionPaymentState;
}
