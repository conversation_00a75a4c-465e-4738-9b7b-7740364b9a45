package com.xhgj.srm.api.dto.account;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.OrderAccountDetail;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class AccountOrderDetailDTO {

    @ApiModelProperty("订单明细id")
    private String id;
    @ApiModelProperty("客户订单号")
    private String orderNo;
    @ApiModelProperty("下单时间")
    private String createTime;
    @ApiModelProperty("下单平台")
    private String platform;
    @ApiModelProperty("结算金额")
    private BigDecimal price;
    @ApiModelProperty("客户名称")
    private String customer;
    @ApiModelProperty("收件人")
    private String consignee;
    @ApiModelProperty("联系方式")
    private String mobile;

    public AccountOrderDetailDTO(OrderAccountDetail orderAccountDetail) {
        this.id = orderAccountDetail.getId();
        this.orderNo = orderAccountDetail.getOrderNo();
        this.platform =
            StrUtil.emptyIfNull(orderAccountDetail.getTypeName());
        this.price = orderAccountDetail.getPrice();
        this.customer = StringUtils.emptyIfNull(orderAccountDetail.getCustomer());
        this.consignee = StringUtils.emptyIfNull(orderAccountDetail.getConsignee());
        this.mobile = StringUtils.emptyIfNull(orderAccountDetail.getMobile());
        this.createTime = orderAccountDetail.getOrderTime() > 0 ? DateUtils.formatTimeStampToNormalDateTime(orderAccountDetail.getOrderTime()) : "";
    }

}
