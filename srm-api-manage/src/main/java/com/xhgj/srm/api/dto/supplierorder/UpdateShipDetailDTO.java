package com.xhgj.srm.api.dto.supplierorder;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/15 13:03
 */
@Data
public class UpdateShipDetailDTO {

  @ApiModelProperty("退货明细行 id")
  @NotBlank(message = "退货行明细 id 必传")
  private String shipDetailId;

  @ApiModelProperty("修改后的数量")
  @NotNull(message = "修改后的数量必传")
  private BigDecimal updateNum;

}
