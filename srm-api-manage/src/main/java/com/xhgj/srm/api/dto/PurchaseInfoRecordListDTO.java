package com.xhgj.srm.api.dto;

import cn.hutool.core.date.DateUtil;
import com.xhgj.srm.jpa.entity.PurchaseInfoRecord;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/12/29 15:04
 */
@Data
@NoArgsConstructor
public class PurchaseInfoRecordListDTO {

  @ApiModelProperty(value = "供应商交货时间")
  private Long deliveryDays;

  @ApiModelProperty("物料编码")
  private String productCode;

  @ApiModelProperty(value = "物料税率")
  private String productTaxRate;

  @ApiModelProperty("含税单价")
  private BigDecimal productTaxPrice;


  /** 计划交货日拼接模板 */
  private static final String DELIVERY_DAYS_TEMPLATE = "{}天";

  public PurchaseInfoRecordListDTO(
      PurchaseInfoRecord purchaseInfoRecord) {
    this.deliveryDays = DateUtil.offsetDay(new Date(),purchaseInfoRecord.getDeliveryDays()).getTime() ;
    this.productCode = purchaseInfoRecord.getProductCode();
    this.productTaxRate = purchaseInfoRecord.getProductTaxRate();
    this.productTaxPrice = purchaseInfoRecord.getProductTaxPrice();
  }
}
