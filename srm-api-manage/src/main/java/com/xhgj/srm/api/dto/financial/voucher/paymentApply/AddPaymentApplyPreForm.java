package com.xhgj.srm.api.dto.financial.voucher.paymentApply;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * <AUTHOR>
 * 提交付款申请前数据预处理
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddPaymentApplyPreForm {
  /**
   * 勾选的财务凭证ids
   */
  private List<String> financialVoucherIds;

  /**
   * 财务凭证申请类型
   */
  private String applyType;
}
