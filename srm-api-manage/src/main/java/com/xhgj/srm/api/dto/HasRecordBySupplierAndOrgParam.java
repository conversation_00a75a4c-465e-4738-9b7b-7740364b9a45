package com.xhgj.srm.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
public class HasRecordBySupplierAndOrgParam {

  @ApiModelProperty(value = "组织编码", required = true)
  @NotBlank(message = "组织编码 必传")
  private String userGroup;
  @ApiModelProperty(value = "供应商Id", required = true)
  @NotBlank(message = "供应商ID 必传")
  private String supplierId;
}
