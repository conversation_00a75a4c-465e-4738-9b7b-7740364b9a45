package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.api.dto.supplier.SupplierFileDTO;
import com.xhgj.srm.api.service.FileService;
import com.xhgj.srm.api.service.SupplierFbService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.utils.ZIPUtils;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import io.netty.channel.ChannelException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2021/3/1 18:46
 */
@Service
@Slf4j
public class FileServiceImpl implements FileService {

  @Autowired private FileRepository repository;
  @Autowired private FileDao dao;
  @Autowired private SupplierFbService supplierFbService;
  @Autowired private DownloadThenUpUtil downloadThenUpUtil;

  @Override
  public BootBaseRepository<File, String> getRepository() {
    return repository;
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  @Override
  public void addFile(String cid, String filesall, String filetype) {
    dao.deleteByRelationIdAndRelationType(cid, filetype);
    if (!StringUtils.isNullOrEmpty(filesall)) {
      String[] oneFile = filesall.split(",");
      if (ArrayUtil.isNotEmpty(oneFile)) {
        for (String s : oneFile) {
          String[] fileContent = s.split(";");
          saveFile(
              cid,
              ArrayUtil.get(fileContent, 0),
              ArrayUtil.get(fileContent, 1),
              ArrayUtil.get(fileContent, 2),
              filetype);
        }
      }
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void addSupplierFile(String relationId, List<SupplierFileDTO> files, String fileType) {
    if (!Constants.FILE_TYPE_TO_NAME.containsKey(fileType)
        && !Constants.SUPPLIER_AGREEMENT_FILE_TYPE_LIST.contains(fileType)) {
      throw new CheckException("未知的附件类型【" + fileType + "】，请联系管理员！");
    }
    dao.deleteByRelationIdAndRelationType(relationId, fileType);
    for (SupplierFileDTO file : CollUtil.emptyIfNull(files)) {
      saveFile(relationId, file.getName(), file.getUrl(), null, fileType);
    }
  }
  @Transactional(rollbackFor = Exception.class)
  public void addSupplierEvaluationTableFile(String relationId, List<SupplierFileDTO> files,
      String fileType) {
    dao.deleteByRelationIdAndRelationType(relationId, fileType);
    for (SupplierFileDTO file : CollUtil.emptyIfNull(files)) {
      saveFile(relationId, file.getName(), file.getUrl(), null, fileType);
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void addSupplierBrandFile(String relationId, List<SupplierFileDTO> files,
      String fileType) {

    dao.deleteByRelationIdAndRelationType(relationId, fileType);
    for (SupplierFileDTO file : CollUtil.emptyIfNull(files)) {
      if (Objects.nonNull(file)) {
        saveFile(relationId, file.getName(), file.getUrl(), null, fileType);
      }
    }
  }

  private void saveFile(String cid, String name, String url, String description, String filetype) {
    if (StringUtils.isNullOrEmpty(url)) {
      return;
    }
    File file = new File();
    file.setRelationType(filetype);
    file.setRelationId(cid);
    file.setUrl(url);
    file.setDescription(description);
    file.setName(StrUtil.emptyIfNull(name));
    if (!StringUtils.isNullOrEmpty(name)) {
      // 附件类型
      String fileType = name.substring(name.lastIndexOf(".") + 1).toLowerCase();
      String type = Constants.FILE_TYPE_PICTURE;
      if ("doc".equals(fileType)
          || "docx".equals(fileType)
          || "pdf".equals(fileType)
          || "ppt".equals(fileType)
          || "excel".equals(fileType)) {
        type = Constants.FILE_TYPE_WORD;
      }
      if ("jpg".equals(fileType)
          || "png".equals(fileType)
          || "bmp".equals(fileType)
          || "gif".equals(fileType)) {
        type = Constants.FILE_TYPE_PICTURE;
      }
      if ("rar".equals(fileType) || "zip".equals(fileType)) {
        type = Constants.FILE_TYPE_RARZIP;
      }
      file.setType(type);
    }
    file.setState(Constants.STATE_OK);
    file.setCreateTime(System.currentTimeMillis());
    repository.save(file);
  }

  @Override
  public void copySupplierAgreeFileToSupplierFb(String supplierId, String supplierFbId) {
    addFileToSupplierFb(dao.getFileListByRIdAndAgree(supplierId), supplierFbId);
  }

  @Override
  public void copySupplierFileToSupplierFb(String supplierId, String supplierFbId) {
    addFileToSupplierFb(dao.getFileListByRIdAndRtype(supplierId), supplierFbId);
  }

  @Transactional
  @Override
  public void deleteInformationAllFile(String relationId) {
    String hql =
        "delete from File f where f.state = ? and f.relationId = ? and "
            + " ( f.relationType = ? or f.relationType = ? or f.relationType = ? "
            + "or f.relationType = ? or f.relationType = ? or f.relationType = ? "
            + "or f.relationType = ? or f.relationType = ? or f.relationType = ? ) ";
    Object[] params =
        new Object[] {
          Constants.STATE_OK,
          relationId,
          Constants.FILE_TYPE_SCXK,
          Constants.FILE_TYPE_ZLZS,
          Constants.FILE_TYPE_SPZCZS,
          Constants.FILE_TYPE_CPSYBG,
          Constants.FILE_TYPE_DSFJCBG,
          Constants.FILE_TYPE_ISOZLRZTX,
          Constants.FILE_TYPE_ISO14001HJGLTX,
          Constants.FILE_TYPE_OHSAS18001ZYJKAQTX,
          Constants.FILE_TYPE_CMSZLGLTXRZZS,
        };
    dao.executeUpdate(hql, params);
  }

  @Transactional
  @Override
  public void deleteAllFileExxy(String supplierId) {
    String hql =
        "delete from File f where f.state = ? and f.relationId = ? and "
            + " ( f.relationType = ? or f.relationType = ? or f.relationType = ? "
            + "or f.relationType = ? or f.relationType = ? or f.relationType = ? "
            + "or f.relationType = ? or f.relationType = ? or f.relationType = ? or f.relationType = ? ) ";
    Object[] params =
        new Object[] {
            Constants.STATE_OK,
            supplierId,
            Constants.FILE_TYPE_SCXK,
            Constants.FILE_TYPE_ZLZS,
            Constants.FILE_TYPE_SPZCZS,
            Constants.FILE_TYPE_CPSYBG,
            Constants.FILE_TYPE_DSFJCBG,
            Constants.FILE_TYPE_ISOZLRZTX,
            Constants.FILE_TYPE_ISO14001HJGLTX,
            Constants.FILE_TYPE_OHSAS18001ZYJKAQTX,
            Constants.FILE_TYPE_CMSZLGLTXRZZS,
            Constants.FILE_TYPE_DLZS
        };
    dao.executeUpdate(hql, params);
  }
  @Transactional
  @Override
  public void updateSupplierFilesByFb(String supplierId, String fbid) {
    List<File> flist = dao.getFileListByRIdAndRtype(fbid);
    if (flist != null && flist.size() > 0) {
      for (File f : flist) {
        File fi = new File();
        fi.setName(f.getName());
        fi.setRelationId(supplierId);
        fi.setRelationType(f.getRelationType());
        fi.setState(Constants.STATE_OK);
        fi.setType(f.getType());
        fi.setUrl(f.getUrl());
        fi.setCreateTime(System.currentTimeMillis());
        repository.save(fi);
      }
      repository.flush();
    }
  }

  @Transactional
  @Override
  public void updateSupplierFbFilesByFb(String oldFbId, String fbid) {
    List<File> flist = dao.getFileListByRIdAndRtype(oldFbId);
    SupplierFb supplierFb = supplierFbService.get(fbid);
    if (flist != null && flist.size() > 0) {
      for (File f : flist) {
        File fi = new File();
        fi.setName(f.getName());
        fi.setRelationId(supplierFb.getId());
        fi.setRelationType(f.getRelationType());
        fi.setState(Constants.STATE_OK);
        fi.setType(f.getType());
        fi.setUrl(f.getUrl());
        fi.setCreateTime(System.currentTimeMillis());
        repository.save(fi);
      }
    }
    repository.flush();
  }

  @Transactional
  @Override
  public void updateSupplierFbXyByFb(String oldFbId, String fbid) {
    List<File> flist1 = dao.getFileListByRIdAndAgree(oldFbId);
    SupplierFb supplierFb = supplierFbService.get(fbid);
    if (flist1 != null && flist1.size() > 0) {
      for (File f : flist1) {
        File fi = new File();
        fi.setName(f.getName());
        fi.setRelationId(supplierFb.getId());
        fi.setRelationType(f.getRelationType());
        fi.setState(Constants.STATE_OK);
        fi.setType(f.getType());
        fi.setUrl(f.getUrl());
        fi.setCreateTime(System.currentTimeMillis());
        repository.save(fi);
      }
      repository.flush();
    }
  }

  @Override
  public long getContractFileCountByContractId(List<String> contractSignedSupplierIdList) {
    if (CollUtil.isNotEmpty(contractSignedSupplierIdList)) {
      return dao.getContractFileCountByContractId(contractSignedSupplierIdList);
    } else {
      return 0;
    }
  }

  @Override
  public long getNumberOfSuppliersWithAgreementBySupplierInGroupIds(
      List<String> myResponsibleSupplierIdList) {
    if (CollUtil.isNotEmpty(myResponsibleSupplierIdList)) {
      return dao.getNumberOfSuppliersWithAgreementBySupplierInGroupIds(myResponsibleSupplierIdList);
    } else {
      return 0;
    }
  }

  @Override
  public List<File> getFileListByIdAndType(String relationId, String relationType) {
    List<File> list = dao.getFileListBySId(relationId, relationType);
    return CollUtil.emptyIfNull(list);
  }

  @Override
  @SneakyThrows
  public byte[] downloadZipMoreFile(List<String> fileIdList) {
    if (CollUtil.isEmpty(fileIdList)) {
      return new byte[]{};
    }
    Map<String, byte[]> fileNameMapByte = new HashMap<>(16);
    for (String fileId : fileIdList) {
      File file = get(fileId);
      if (file == null) {
        log.error("【"+ fileId +"】文件不存在");
        continue;
      }
      String url = file.getUrl();
      if (StrUtil.isNotBlank(url)) {
        try ( InputStream inputStream = downloadThenUpUtil.getInputStreamFromOSS(url)) {
          if (inputStream!=null) {
            String fileName = StrUtil.blankToDefault(file.getName(),
                String.valueOf(System.currentTimeMillis()));
            byte[] bytes = IOUtils.toByteArray(inputStream);
            if (fileNameMapByte.containsKey(fileName)) {
              String subBefore = StrUtil.subBefore(fileName, CharUtil.DOT, true)
                  + System.currentTimeMillis();
              String suffix = StrUtil.subAfter(fileName, CharUtil.DOT, true);
              fileName = subBefore+CharUtil.DOT+suffix;
            }
            fileNameMapByte.put(fileName,bytes);
          }
        }
      }
    }
    return ZIPUtils.batchFileToZIP(fileNameMapByte);
  }



  private void addFileToSupplierFb(List<File> fileList, String supplierFbId) {
    if (CollUtil.isNotEmpty(fileList)) {
      for (File file : fileList) {
        File newFile = new File();
        newFile.setName(file.getName());
        newFile.setRelationId(supplierFbId);
        newFile.setRelationType(file.getRelationType());
        newFile.setState(Constants.STATE_OK);
        newFile.setType(file.getType());
        newFile.setUrl(file.getUrl());
        newFile.setCreateTime(System.currentTimeMillis());
        repository.save(newFile);
      }
    }
  }
  private boolean checkFileDTO(FileDTO fileDTO) {
    if (fileDTO == null) {
      return false;
    }
    return StrUtil.isNotBlank(fileDTO.getName()) && StrUtil.isNotBlank(fileDTO.getUrl());
  }
  @Override
  @Transactional
  public void saveFile(FileDTO contractFile, String userId, String relationId,
      String relationType){
    boolean answer = checkFileDTO(contractFile);
    if (!answer) {
      throw new ChannelException("存储文件失败，参数异常。");
    }
    File file = new File();
    file.setUrl(contractFile.getUrl());
    file.setName(contractFile.getName());
    file.setDescription(contractFile.getName());
    file.setState(Constants.STATE_OK);
    file.setRelationType(relationType);
    file.setRelationId(relationId);
    file.setCreateTime(System.currentTimeMillis());
    file.setUploadMan(userId);
    dao.save(file);
  }

  @Override
  public int countNumByRelationIdAndRelationType(String relationId, String relationType) {
    return repository.countAllByRelationIdAndRelationType(relationId, relationType);
  }

  @Override
  public List<String> getUrls(List<String> ids) {
    Assert.notEmpty(ids);
    ArrayList<String> urls = new ArrayList<>();
    for (String id : ids) {
      File file = get(id, () -> CheckException.noFindException(File.class, id));
      urls.add(file.getUrl());
    }
    return urls;
  }

  @Override
  public Optional<File> findFirstByRelationIdAndRelationType(String relationId, String relationType) {
    Assert.notBlank(relationId);
    Assert.notBlank(relationType);
    return repository.findFirstByRelationIdAndRelationTypeAndState(relationId, relationType,
        Constants.STATE_OK);
  }

  @Override
  public void delete(String relationId, String relationType) {
    repository.findAllByRelationIdAndRelationTypeAndState(relationId, relationType,
        Constants.STATE_OK).ifPresent(files -> {
      files.forEach(file -> {
        file.setState(Constants.STATE_DELETE);
        repository.save(file);
      });
    });
  }

  @Override
  public void association(String id, String relationId, String relationType) {
    repository.findById(id).ifPresent(file -> {
      file.setRelationId(relationId);
      file.setRelationType(relationType);
      file.setState(Constants.STATE_OK);
      repository.save(file);
    });
  }

  @Override
  public List<File> getFileList(String id, String type) {
    List<File> list = dao.getFileList(id, type);
    return CollUtil.emptyIfNull(list);
  }

  @Override
  public List<File> findFirstByRelationIdInAndRelationType(List<String> contractIds,
      String fileType) {
    if (CollUtil.isEmpty(contractIds)) {
      return Collections.emptyList();
    }
    if (StrUtil.isEmpty(fileType)) {
      return Collections.emptyList();
    }
    List<File> fileList =
        repository.findByRelationIdInAndRelationTypeAndState(contractIds, fileType,
            Constants.STATE_OK);
    Map<String, File> firstFileMap = fileList.stream().collect(
        Collectors.toMap(File::getRelationId, file -> file,
            (existing, replacement) -> existing));
    return new ArrayList<>(firstFileMap.values());
  }
}
