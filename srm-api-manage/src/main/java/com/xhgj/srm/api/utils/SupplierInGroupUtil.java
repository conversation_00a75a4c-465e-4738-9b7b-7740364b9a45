package com.xhgj.srm.api.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.supplier.AbroadSupplierDTO;
import com.xhgj.srm.api.dto.supplier.BaseSupplierDTO;
import com.xhgj.srm.api.dto.supplier.ChinaSupplierDTO;
import com.xhgj.srm.api.dto.supplier.SupplierBrandDTO;
import com.xhgj.srm.api.dto.supplier.SupplierContactDTO;
import com.xhgj.srm.api.dto.supplier.SupplierFileDTO;
import com.xhgj.srm.api.dto.supplier.SupplierFileWrapper;
import com.xhgj.srm.api.dto.supplier.SupplierFinancialDTO;
import com.xhgj.srm.api.service.BrandService;
import com.xhgj.srm.api.service.ContactService;
import com.xhgj.srm.api.service.ExtraFileService;
import com.xhgj.srm.api.service.FileService;
import com.xhgj.srm.api.service.FinancialService;
import com.xhgj.srm.api.service.SupplierTemplateService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.dto.MdmBrand;
import com.xhgj.srm.common.enums.BrandRelationTypeEnum;
import com.xhgj.srm.common.enums.SupplierTemplateTypeEnum;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import com.xhgj.srm.common.utils.DictMapUtil;
import com.xhgj.srm.dto.supplierCategory.SupplierCategoryDto;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.dto.supplierCategory.SupplierCategorySaveForm;
import com.xhgj.srm.jpa.entity.BaseSupplierInGroup;
import com.xhgj.srm.jpa.entity.BaseSupplierInGroup.PartnershipTypeDTO;
import com.xhgj.srm.jpa.entity.Brand;
import com.xhgj.srm.jpa.entity.ExtraFile;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierCategory;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhgj.srm.service.SupplierCategoryService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2022/7/20 21:40
 */
@Slf4j
@Component
public class SupplierInGroupUtil {
  @Autowired private ExtraFileService extraFileService;
  @Autowired private FileService fileService;
  @Autowired private FinancialService financialService;
  @Autowired private ContactService contactService;
  @Autowired private UserService userService;
  @Autowired private SupplierTemplateService supplierTemplateService;
  @Autowired private MPMService mpmService;
  @Autowired private BrandService brandService;
  @Resource private SupplierCategoryService supplierCategoryService;
  @Resource private UserRepository userRepository;

  /**
   * 保存供应商字段及关联信息
   *
   * @param supplierInGroup 组织内供应商
   * @param dto 参数对象
   * @param supplier 关联供应商
   * @param user 操作用户
   */
  public void saveSupplierInGroupFieldAndRelationsByDTO(
      SupplierInGroup supplierInGroup, BaseSupplierDTO dto, Supplier supplier, User user) {
    saveSupplierInGroupFieldByDTO(supplierInGroup, dto, supplier);
    saveSupplierRelations(supplierInGroup, supplierInGroup, dto, user);
  }
  /**
   * 保存供应商字段
   *
   * @param supplierInGroup 组织内供应商，必传
   * @param dto 参数对象，必传
   * @param supplier 关联供应商，必传
   */
  public void saveSupplierInGroupFieldByDTO(
      BaseSupplierInGroup supplierInGroup, BaseSupplierDTO dto, Supplier supplier) {
    Assert.notNull(supplierInGroup);
    Assert.notNull(dto);
    Assert.notNull(supplier);
    supplierInGroup.setSupplier(supplier);
    supplierInGroup.setSupplierId(supplier.getId());
    String enterpriseLevel = dto.getEnterpriseLevel();
    String invoiceType = dto.getInvoiceType();
    String descByCode = SupplierLevelEnum.getAbbrByCode(enterpriseLevel);
    if (StrUtil.isBlank(descByCode)) {
      throw new CheckException("供应商等级选择非法，请联系管理员！");
    }
    if (DictMapUtil.isInvalidDictMapKeyAcceptEmpty(Constants.INVOICETYPE, invoiceType)) {
      throw new CheckException("发票类型选择非法，请联系管理员！");
    }
    supplierInGroup.setEnterpriseLevel(enterpriseLevel);
    supplierInGroup.setInvoiceType(invoiceType);
    supplierInGroup.setTaxRate(dto.getTaxRate());
    supplierInGroup.setSettleCurrency(dto.getSettleCurrency());
    SupplierTemplateTypeEnum supplierTemplateTypeEnum;
    if (dto instanceof ChinaSupplierDTO) {
      List<PartnershipTypeDTO> partnershipTypes = ((ChinaSupplierDTO) dto).getPartnershipTypes();
      ChinaSupplierDTO chinaSupplierDTO = (ChinaSupplierDTO) dto;
      // 法人
      supplierInGroup.setCorporate(chinaSupplierDTO.getCorporate());
      // 注册地址
      supplierInGroup.setRegAddress(chinaSupplierDTO.getRegAddress());
      supplierInGroup.makePartnershipTypes(partnershipTypes);
      SupplierFileDTO license = chinaSupplierDTO.getLicense();
      String licenseUrl;
      if (license != null && license.isNotEmpty()) {
        licenseUrl = (license.getUrl());
      } else {
        licenseUrl = StrUtil.EMPTY;
      }
      supplierInGroup.setLicenseUrl(licenseUrl);
      // 供应商性质
      List<String> enterpriseNatures = chinaSupplierDTO.getEnterpriseNatures();
      supplierInGroup.setEnterpriseNature(
          CollUtil.emptyIfNull(enterpriseNatures).stream()
              .peek(
                  nature -> {
                    if (DictMapUtil.isInvalidDictMapKeyAcceptEmpty(
                        Constants.ENTERPRISENATURE, nature)) {
                      throw new CheckException("供应商性质选择非法，请联系管理员！");
                    }
                  })
              .collect(Collectors.joining(",")));
      supplierInGroup.setDetails(chinaSupplierDTO.getDetails());
      // 账期
      String accountPeriod = chinaSupplierDTO.getAccountPeriod();
      if (DictMapUtil.isInvalidDictMapKeyAcceptEmpty(Constants.ACCOUNT_PERIOD, accountPeriod)) {
        throw new CheckException("账期选择非法，请联系管理员！");
      }
      supplierInGroup.setAbbreviation(chinaSupplierDTO.getAbbreviation());
      supplierInGroup.setAccountPeriod(accountPeriod);
      supplierInGroup.setIntegrity(chinaSupplierDTO.getIntegrity());
      supplierTemplateTypeEnum = SupplierTemplateTypeEnum.CHINA;
    } else {
      AbroadSupplierDTO abroadSupplierDTO = (AbroadSupplierDTO) dto;
      String accountPeriod = abroadSupplierDTO.getAccountPeriod();
      supplierInGroup.setAccountPeriod(accountPeriod);
      supplierInGroup.setAbbreviation(abroadSupplierDTO.getAbbreviation());
      supplierTemplateTypeEnum = SupplierTemplateTypeEnum.ABROAD;
    }
    // 根据模板校验
    TemplateUtil.validateObjByTemplateMap(
        dto,
        supplierTemplateService.getSupplierRequiredMap(
            supplierInGroup.getGroup(), supplierTemplateTypeEnum));
  }

  /**
   * 根据 DTO 保存供应商关联信息
   *
   * @param baseSupplierInGroup 组织内供应商
   * @param dto 供应商参数对象
   * @param user 操作人
   */
  public void saveSupplierRelations(
      BaseSupplierInGroup baseSupplierInGroup,
      SupplierInGroup supplierInGroup,
      BaseSupplierDTO dto,
      User user) {
    SupplierFileDTO agreement;
    SupplierFileDTO evaluationTable;
    List<SupplierFileWrapper> fileWrappers;
    List<SupplierBrandDTO> brandList;
    List<SupplierCategorySaveForm> categoryList = Collections.emptyList();
    if (dto instanceof ChinaSupplierDTO) {
      categoryList = ((ChinaSupplierDTO) dto).getCategoryList();
      agreement = ((ChinaSupplierDTO) dto).getAgreement();
      fileWrappers = ((ChinaSupplierDTO) dto).getFileWrappers();
      brandList = ((ChinaSupplierDTO) dto).getBrandList();
      evaluationTable = ((ChinaSupplierDTO) dto).getEvaluationTable();
    } else {
      agreement = null;
      evaluationTable = null;
      fileWrappers = Collections.emptyList();
      brandList = Collections.emptyList();
    }
    saveSupplierRelations(
        baseSupplierInGroup,
        supplierInGroup,
        dto.getEnterpriseLevel(),
        dto.getFinancials(),
        dto.getContacts(),
        brandList,
        categoryList,
        agreement,
        evaluationTable,
        fileWrappers,
        user,
        supplierInGroup.getId()
    );
  }

  /**
   * 保存供应商关联信息
   *
   * @param baseSupplierInGroup 组织内供应商
   * @param supplierInGroup 目标供应商
   * @param enterpriseLevel 供应商等级
   * @param financials 财务信息
   * @param contacts 联系人信息
   * @param brands 品牌
   * @param agreement 协议
   * @param fileWrappers 附件
   * @param user 操作人
   */
  public void saveSupplierRelations(
      BaseSupplierInGroup baseSupplierInGroup,
      SupplierInGroup supplierInGroup,
      String enterpriseLevel,
      List<SupplierFinancialDTO> financials,
      List<SupplierContactDTO> contacts,
      List<SupplierBrandDTO> brands,
      List<SupplierCategorySaveForm> categoryList,
      SupplierFileDTO agreement,
      SupplierFileDTO evaluationTable,
      List<SupplierFileWrapper> fileWrappers,
      User user,
      String originSupplierInGroupId) {
    SupplierTemplateTypeEnum supplierTemplateTypeEnum =
        SupplierTemplateTypeEnum.getSupplierTemplateTypeEnumByType(
            baseSupplierInGroup.getSupplier().getSupType());
    Map<String, Boolean> supplierRequiredMap =
        supplierTemplateService.getSupplierRequiredMap(
            baseSupplierInGroup.getGroup(), supplierTemplateTypeEnum);
    String supplierInGroupId = baseSupplierInGroup.getId();
    // 财务信息
//    for (SupplierFinancialDTO financial : financials) {
//      TemplateUtil.validateObjByTemplateMap(financial, supplierRequiredMap);
//    }
    financialService.saveSupplierInGroupFinancial(baseSupplierInGroup, supplierInGroup, financials);
    // 联系人
    for (SupplierContactDTO contact : contacts) {
      TemplateUtil.validateObjByTemplateMap(contact, supplierRequiredMap);
    }
    contactService.saveSupplierInGroupContact(user, baseSupplierInGroup, supplierInGroup, contacts);
    // 品牌
//    brandService.saveBrandsByRelation(
//        brands, supplierInGroupId, BrandRelationTypeEnum.SUPPLIER_IN_GROUP);
    // 保存品牌相关信息
    brandService.patchUpdate(brands, supplierInGroupId, BrandRelationTypeEnum.SUPPLIER_IN_GROUP, user.getId());

    // 经营类目
    supplierCategoryService.patchUpdate(categoryList, baseSupplierInGroup.getSupplierId(),
        baseSupplierInGroup.getId(), user.getId(), originSupplierInGroupId);
    // 协议
    if (agreement != null) {
      // 经产品确认本版本需求：供应商等级符合条件的才保存协议
      String agreementType = getAgreementTypeByLevel(enterpriseLevel);
      String synState;
      if (!StringUtils.isNullOrEmpty(agreementType)) {
        if (agreement.isNotEmpty()) {
          fileService.addSupplierFile(
              supplierInGroupId, Collections.singletonList(agreement), agreementType);
          // 未同步ERP
          synState = Constants.SUPPLIER_SYN_STATE_NOERPCODE;
        } else {
          // 协议未上传
          synState = Constants.SUPPLIER_SYN_STATE_NOXE;
        }
      } else {
        // 未同步ERP
        synState = Constants.SUPPLIER_SYN_STATE_NOERPCODE;
      }
      if (baseSupplierInGroup instanceof SupplierInGroup) {
        ((SupplierInGroup) baseSupplierInGroup).setSynState(synState);
      }
    } else {
      // 清除协议类型的附件
      for (String agreementType : Constants.SUPPLIER_AGREEMENT_FILE_TYPE_LIST) {
        fileService.addSupplierFile(supplierInGroupId, Collections.emptyList(), agreementType);
      }
    }
    // 供应商评估表
    if (evaluationTable != null) {
      fileService.addSupplierEvaluationTableFile(
          supplierInGroupId, Collections.singletonList(evaluationTable), Constants.FILE_TYPE_EVALUATE);
    } else {
      // 清除供应商评估表类型的附件
      fileService.addSupplierEvaluationTableFile(supplierInGroupId, Collections.emptyList(), Constants.FILE_TYPE_EVALUATE);
    }
    // 其他附件（含自定义附件）
    handleFiles(supplierInGroupId, fileWrappers);
  }

  /**
   * 获取协议
   *
   * @param supplierInGroup 组织内供应商（副本）
   */
  public SupplierFileDTO getSupplierInGroupAgreement(BaseSupplierInGroup supplierInGroup) {
    String agreementType = getAgreementTypeByLevel(supplierInGroup.getEnterpriseLevel());
    SupplierFileDTO agreementFileDTO;
    if (!StringUtils.isNullOrEmpty(agreementType)) {
      agreementFileDTO =
          CollUtil.emptyIfNull(
                  fileService.getFileListByIdAndType(supplierInGroup.getId(), agreementType))
              .stream()
              .findAny()
              .map(file -> new SupplierFileDTO(file.getName(), file.getUrl()))
              .orElse(null);
    } else {
      agreementFileDTO = null;
    }
    return agreementFileDTO;
  }

  /**
   * 获取供应商模版
   *
   * @param supplierInGroup 组织内供应商（副本）
   */
  public SupplierFileDTO getSupplierInGroupEvaluationTable(BaseSupplierInGroup supplierInGroup) {

    return CollUtil.emptyIfNull(
            fileService.getFileListByIdAndType(supplierInGroup.getId(), Constants.FILE_TYPE_EVALUATE))
        .stream()
        .findAny()
        .map(file -> new SupplierFileDTO(file.getName(), file.getUrl()))
        .orElse(null);
  }

  /**
   * 获取供应商模版
   *
   */
  public SupplierFileDTO getSupplierBrandUrl(String brandId, String type) {

    return CollUtil.emptyIfNull(
            fileService.getFileListByIdAndType(brandId, type))
        .stream()
        .findAny()
        .map(file -> new SupplierFileDTO(file.getName(), file.getUrl()))
        .orElse(new SupplierFileDTO());
  }

  /**
   * 根据组织内供应商获取财务信息列表
   *
   * @param supplierInGroupId 组织内供应商 id
   */
  public List<SupplierFinancialDTO> getFinancialDTOListBySupplierInGroupId(
      String supplierInGroupId) {
    return CollUtil.emptyIfNull(
            financialService.getFinancialListBySupplierInGroupId(supplierInGroupId))
        .stream()
        .map(SupplierFinancialDTO::new)
        .collect(Collectors.toList());
  }

  /**
   * 根据组织内供应商获取联系人列表
   *
   * @param id 组织内供应商 id
   */
  public List<SupplierContactDTO> getContactDTOListBySupplierInGroupId(String id) {
    return CollUtil.emptyIfNull(contactService.getContactListBySupplierInGroupId(id)).stream()
        .map(contact -> new SupplierContactDTO(contact, userService))
        .collect(Collectors.toList());
  }

  /**
   * 根据组织内供应商获取品牌对象列表
   *
   * @param id 组织内供应商 id
   */
  public List<SupplierBrandDTO> getBrandDTOListBySupplierInGroupId(String id,
      boolean isRequestThird) {
    List<Brand> brands = brandService.getBrandsByRelationIdAndType(id,
        BrandRelationTypeEnum.SUPPLIER_IN_GROUP);
    List<SupplierBrandDTO> dtoList = new ArrayList<>();
    for (Brand brand : brands) {
      SupplierBrandDTO brandDTO = new SupplierBrandDTO(brand);
      String brandLogoUrl = StrUtil.EMPTY;
      String associatedName = StrUtil.EMPTY;
      if (isRequestThird) {
        // 请求MPM 品牌logo
        try {
          MdmBrand mpmBrand = mpmService.getMPMBrand(brand.getCode());
          brandLogoUrl = StrUtil.emptyIfNull(mpmBrand.getLogoUrl());
        } catch (Exception e) {
          log.error("请求MPM品牌接口失败，品牌编码：{}，错误信息：{}", brand.getCode(), e.getMessage());
        }
        associatedName = userService.getNameById(StrUtil.emptyIfNull(brand.getAssociatedId()));
      }
      brandDTO.setGeneralLicenseUrl(getSupplierBrandUrl(brand.getId(),
          Constants.FILE_TYPE_BRAND_AUTHORIZATION));
      brandDTO.setProxyLicenseUrl(getSupplierBrandUrl(brand.getId(),Constants.FILE_TYPE_BRAND_AGENT_AUTHORIZATION));
      brandDTO.setTrademarkRegistrationUrl(getSupplierBrandUrl(brand.getId(),Constants.FILE_TYPE_BRAND_TRADEMARK));
      brandDTO.setLogoUrl(brandLogoUrl);
      brandDTO.setAssociatedName(associatedName);
      dtoList.add(brandDTO);
    }
    return dtoList;
  }

  public List<SupplierCategoryDto> getSupplierCategoryDtoBySupplierInGroupId(String id) {
    List<SupplierCategory> supplierCategory =
        supplierCategoryService.getSupplierCategory(Collections.singletonList(id));
    if (CollUtil.isEmpty(supplierCategory)) {
      return Collections.emptyList();
    }
    List<String> updateUserIds =
        supplierCategory.stream().map(SupplierCategory::getCreateMan).distinct()
        .collect(Collectors.toList());
    updateUserIds.add("-1");
    Map<String, String> id2RealName = userRepository.findAllById(updateUserIds).stream()
        .collect(Collectors.toMap(User::getId, User::getRealName, (k1, k2) -> k1));
    return supplierCategory.stream().map(item -> {
      SupplierCategoryDto dto = MapStructFactory.INSTANCE.toSupplierCategoryDto(item);
      dto.setCreateManName(id2RealName.getOrDefault(dto.getCreateMan(), ""));
      return dto;
    }).collect(Collectors.toList());
  }

  public List<SupplierCategorySaveForm> getSupplierCategorySaveFormBySupplierInGroupId(String id) {
    List<SupplierCategory> supplierCategory = supplierCategoryService.getSupplierCategory(Collections.singletonList(id));
    if (CollUtil.isEmpty(supplierCategory)) {
      return Collections.emptyList();
    }
    return supplierCategory.stream().map(MapStructFactory.INSTANCE::toSupplierCategorySaveForm).collect(Collectors.toList());
  }

  /**
   * 获取【资质证照】系列附件对象列表
   *
   * @param id 关联 id（组织内供应商）
   */
  public List<SupplierFileWrapper> getSupplierFiles(String id) {
    List<SupplierFileWrapper> supplierFileWrappers = new ArrayList<>();
    // 固定类型的附件
    for (String type : Constants.FILE_TYPE_TO_NAME.keySet()) {
      List<File> files = CollUtil.emptyIfNull(fileService.getFileListByIdAndType(id, type));
      if (CollUtil.isNotEmpty(files)) {
        supplierFileWrappers.add(
            new SupplierFileWrapper(
                type,
                null,
                files.stream()
                    .map(file -> new SupplierFileDTO(file.getName(), file.getUrl()))
                    .collect(Collectors.toList())));
      }
    }
    // 自定义附件
    Map<String, List<ExtraFile>> listMap =
        CollUtil.emptyIfNull(extraFileService.getFileListByRId(id)).stream()
            .collect(Collectors.groupingBy(ExtraFile::getRelationName, Collectors.toList()));
    listMap.forEach(
        (name, value) -> {
          SupplierFileWrapper wrapper = new SupplierFileWrapper();
          wrapper.setCustomName(name);
          wrapper.setFiles(
              value.stream()
                  .map(file -> new SupplierFileDTO(file.getName(), file.getUrl()))
                  .collect(Collectors.toList()));
          supplierFileWrappers.add(wrapper);
        });
    return supplierFileWrappers;
  }
  /**
   * 根据供应商等级获取其对应协议的类型
   *
   * @param level 供应商等级
   * @return 如果没有对应的协议类型或 level 传空，则返回空值
   */
  public String getAgreementTypeByLevel(String level) {
    if (StringUtils.isNullOrEmpty(level)) {
      return StrUtil.EMPTY;
    } else if (StrUtil.equalsAny(
        level,
        SupplierLevelEnum.STRATEGIC.getCode(),
        SupplierLevelEnum.HIGH_QUALITY.getCode(),
        SupplierLevelEnum.GENERAL.getCode(),
        // 2022-11-29 经产品确认：零星和电商等级的协议归类为采购协议
        // 目前除【潜在合作】等级以外，所有等级都对应有协议类型
        // 因【潜在合作】在前端页面已不再展示，保守实现需求【所有合作等级的供应商都可以上传合作协议】
        SupplierLevelEnum.SPORADIC.getCode())) {
      // 采购协议
      return Constants.FILE_TYPE_CGXY;
    } else {
      return StrUtil.EMPTY;
    }
  }

  /**
   * 处理供应商附件
   *
   * @param supplierInGroupId 组织供应商 id，必传
   * @param wrappers 附件包裹对象列表
   */
  private void handleFiles(String supplierInGroupId, List<SupplierFileWrapper> wrappers) {
    Assert.notEmpty(supplierInGroupId);
    if (CollUtil.isEmpty(wrappers)) {
      // wrappers 为空的情况，认为是删除所有附件
      // 1. 删除固定类型的附件
      Constants.FILE_TYPE_TO_NAME.forEach(
          (k, v) -> {
            fileService.addSupplierFile(supplierInGroupId, Collections.emptyList(), k);
          });
      // 2. 删除自定义附件
      extraFileService.deleteAllZDYFile(supplierInGroupId);
    } else {
      // 删除资质证照的所有附件，然后再新增
      fileService.deleteInformationAllFile(supplierInGroupId);
      for (SupplierFileWrapper dto : wrappers) {
        String type = dto.getType();
        String customName = dto.getCustomName();
        List<SupplierFileDTO> files = dto.getFiles();
        if (!StringUtils.isNullOrEmpty(customName)) {
          // 自定义附件
          extraFileService.addZDYFile(supplierInGroupId, customName, files);
        } else if (!StringUtils.isNullOrEmpty(type)) {
          // 固定类型的附件
          fileService.addSupplierFile(supplierInGroupId, files, type);
        } else {
          throw new CheckException("存在异常类型的附件，请联系管理员！");
        }
      }
    }
  }
}
