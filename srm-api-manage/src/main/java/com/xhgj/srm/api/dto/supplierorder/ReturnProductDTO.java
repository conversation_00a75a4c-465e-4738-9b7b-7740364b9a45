package com.xhgj.srm.api.dto.supplierorder;

import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/1 15:38
 */
@Data
public class ReturnProductDTO extends BaseSupplierOrderProductDTO {
  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("erp 关联 id")
  private String erpId;

  @ApiModelProperty("erp 行号")
  private Integer erpRowIndex;

  @ApiModelProperty("退货数量")
  private BigDecimal returnQty;

  @ApiModelProperty("单价")
  private BigDecimal productPrice;

  @ApiModelProperty("是否开红票")
  private Boolean openRedInvoice;
  public ReturnProductDTO(SupplierOrderDetail supplierOrderDetail) {
    super(supplierOrderDetail.getSupplierOrderProduct());
    SupplierOrderProduct supplierOrderProduct = supplierOrderDetail.getSupplierOrderProduct();
    this.id = supplierOrderDetail.getId();
    this.erpId = supplierOrderDetail.getErpId();
    this.erpRowIndex = supplierOrderDetail.getErpRowNum();
    this.returnQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(
            supplierOrderDetail.getReturnQty(), supplierOrderProduct.getUnitDigit());
    this.productPrice = supplierOrderDetail.getDetailed().getPrice();
    this.openRedInvoice = Boolean.TRUE.equals(supplierOrderDetail.getOpenRedInvoice());
  }
}
