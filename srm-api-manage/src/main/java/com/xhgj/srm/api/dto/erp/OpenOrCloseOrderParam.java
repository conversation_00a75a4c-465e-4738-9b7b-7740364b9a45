package com.xhgj.srm.api.dto.erp;

import com.xhgj.srm.request.dto.erp.OpenOrCloseOrderParams.OpenOrCloseOrderIdsDTO;
import com.xhgj.srm.request.enums.OpenOrCloseOrderType;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/14 18:47
 */
@Data
public class OpenOrCloseOrderParam {

  @ApiModelProperty("操作类型")
  @NotNull(message = "操作类型必传")
  private OpenOrCloseOrderType type;

  @ApiModelProperty("erp 明细行 id")
  private OpenOrCloseOrderIdsDTO ids;
}
