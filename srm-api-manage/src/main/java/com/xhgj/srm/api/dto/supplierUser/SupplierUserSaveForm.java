package com.xhgj.srm.api.dto.supplierUser;/**
 * @since 2024/12/2 14:10
 */

import lombok.Data;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 *<AUTHOR>
 *@date 2024/12/2 14:10:49
 *@description
 */
@Data
public class SupplierUserSaveForm {

  public static final String ROLE_ADMIN = "1";

  /**
   * id,编辑更新传递
   */
  private String id;
  /**
   * 创建人用户id
   */
  @NotBlank(message = "创建人用户id不能为空")
  private String userId;

  /**
   * 用户名
   */
  @NotBlank(message = "用户名不能为空")
  private String name;

  /**
   * 供应商id
   */
  @NotBlank(message = "供应商id不能为空")
  private String supplierId;

  /**
   * 姓名
   */
  @NotBlank(message = "姓名不能为空")
  private String realName;

  /**
   * 邮箱
   */
  @Email(message = "邮箱格式有误")
  @NotBlank(message = "邮箱不能为空")
  private String mail;

  /**
   * 手机号码
   */
  @NotBlank(message = "手机号码不能为空")
  private String mobile;

  /**
   * 角色(1-管理员,2-普通)
   */
  @NotBlank(message = "角色不能为空")
  private String role;

  /**
   * 管理范围 (1-电商供应商， 2-自营供应商)
   * @see com.xhgj.srm.common.enums.supplierUser.SupplierUserPermission
   */
  @NotBlank(message = "管理范围不能为空")
  private String permission;
}
