package com.xhgj.srm.api.dto.supplierorder;

import com.xhgj.srm.common.enums.VerifyConfigTypeEnum;
import com.xhgj.srm.jpa.entity.VerifyConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: fanghuanxu
 * @Date: 2025/3/20 13:33
 * @Description: 配置中心 配置vo
 */
@Data
public class VerifyConfigInfoVo extends VerifyConfigInfoDTO {

  @ApiModelProperty("配置类型")
  private String configType;

  @ApiModelProperty("配置类型")
  private String configTypeName;

  public VerifyConfigInfoVo(VerifyConfig verifyConfig) {
    super(verifyConfig);
    this.configType = verifyConfig.getConfigType();
    this.configTypeName = VerifyConfigTypeEnum.getNameByCode(verifyConfig.getConfigType());
  }
}
