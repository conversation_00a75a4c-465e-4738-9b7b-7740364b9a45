package com.xhgj.srm.api.service.impl;

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.api.dto.supplier.BaseSupplierMainDataDTO;
import com.xhgj.srm.api.dto.supplier.SupplierMainDataAbroadDTO;
import com.xhgj.srm.api.dto.supplier.SupplierMainDataChinaDTO;
import com.xhgj.srm.api.factory.MapStructFactory;
import com.xhgj.srm.api.service.SupplierTempService;
import com.xhgj.srm.api.service.SupplierTemplateService;
import com.xhgj.srm.api.utils.TemplateUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.SupplierTemplateTypeEnum;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierTemp;
import com.xhgj.srm.jpa.repository.SupplierTempRepository;
import com.xhgj.srm.map.domain.IgnoreFieldContext;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/7/31 17:29
 */
@Service
public class SupplierTempServiceImpl implements SupplierTempService {
  @Autowired private SupplierTempRepository repository;
  @Autowired private SupplierTemplateService supplierTemplateService;

  @Override
  public BootBaseRepository<SupplierTemp, String> getRepository() {
    return repository;
  }

  @Override
  public SupplierTemp createBySupplier(Supplier supplier) {
    Assert.notNull(supplier);
    String id = supplier.getId();
    Assert.notEmpty(id);
    SupplierTemp temp = new SupplierTemp();
    MapStructFactory.INSTANCE.updateSupplierTemp(supplier, temp, IgnoreFieldContext.create("id"));
    return repository.save(temp);
  }

  @Override
  public SupplierTemp createByDTO(BaseSupplierMainDataDTO dto, Group group) {
    SupplierTemp temp = new SupplierTemp();
    temp.setEnterpriseName(dto.getName());
    String supType;
    if (dto instanceof SupplierMainDataChinaDTO) {
      temp.setUscc(((SupplierMainDataChinaDTO) dto).getUscc());
      temp.setIndustry(((SupplierMainDataChinaDTO) dto).getIndustry());
      temp.setProvince(((SupplierMainDataChinaDTO) dto).getProvince());
      temp.setCity(((SupplierMainDataChinaDTO) dto).getCity());
      supType = Constants.SUPPLIERTYPE_CHINA;
    } else if (dto instanceof SupplierMainDataAbroadDTO) {
      temp.setCountry(((SupplierMainDataAbroadDTO) dto).getCountry());
      supType = Constants.SUPPLIERTYPE_ABROAD;
    } else {
      throw new IllegalArgumentException();
    }
    temp.setSupType(supType);
    Map<String, Boolean> requiredMap =
        supplierTemplateService.getSupplierRequiredMap(
            group, SupplierTemplateTypeEnum.getSupplierTemplateTypeEnumByType(supType));
    TemplateUtil.validateObjByTemplateMap(dto, requiredMap);
    return repository.save(temp);
  }
}
