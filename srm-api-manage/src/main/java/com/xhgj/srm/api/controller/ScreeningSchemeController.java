package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.dto.screeningScheme.SaveScreeningSchemeDto;
import com.xhgj.srm.api.service.ScreeningSchemeService;
import com.xhgj.srm.jpa.entity.ScreeningScheme;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;

@RestController
@RequestMapping("/screeningScheme")
public class ScreeningSchemeController {

  @Autowired
  private ScreeningSchemeService screeningSchemeService;

  @ApiOperation("新增/修改筛查方案")
  @PostMapping("saveOrUpdate")
  public ResultBean<Boolean> saveOrUpdate(@RequestBody @Valid SaveScreeningSchemeDto param) {
    screeningSchemeService.saveOrUpdate(param);
    return new ResultBean<>(true, "保存成功！");
  }

  @ApiOperation("根据类型查询筛查方案,返回null则表示展示全部")
  @GetMapping("/query")
  public ResultBean<String> query(@RequestParam String type, @RequestParam String userId) {
    return new ResultBean<>(screeningSchemeService.query(type,userId));
  }

}
