package com.xhgj.srm.api.dto.suppliertemplate;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;
/**
 * <AUTHOR>
 * @since 2022/7/11 19:33
 */
@Data
public class DeleteSupplierTemplateParams {

  @ApiModelProperty("用户 id")
  @NotBlank(message = "用户 id 不能为空")
  private String userId;

  @ApiModelProperty("删除模板的 id 集合")
  private List<String> idList;
}
