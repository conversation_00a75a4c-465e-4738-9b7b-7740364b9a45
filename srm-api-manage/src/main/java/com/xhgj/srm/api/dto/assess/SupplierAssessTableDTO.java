package com.xhgj.srm.api.dto.assess;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.AssessStateEnum;
import com.xhgj.srm.common.enums.AssessTypeEnum;
import com.xhgj.srm.jpa.entity.Assess;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.dict.BootDictEnumUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/7/21 23:17
 */
@Data
public class SupplierAssessTableDTO {
  @ApiModelProperty("审核 id")
  private String id;

  @ApiModelProperty("创建时间")
  private Long createTime;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("创建人名称")
  private String createManName;

  @ApiModelProperty("审核类型")
  private AssessTypeEnum assessType;

  @ApiModelProperty("审核状态")
  private AssessStateEnum assessState;

  @ApiModelProperty("审核人名称")
  private String assessManName;

  @ApiModelProperty("审核结果")
  private String assessResult;

  @ApiModelProperty("供应商类型，用于判断调用哪个接口获取详情")
  private String supplierType;

  public SupplierAssessTableDTO(
      Assess assess,
      String supplierName,
      String createManName,
      String assessManName,
      String supplierType) {
    this.id = assess.getId();
    this.createTime = assess.getCreateTime();
    this.supplierName = supplierName;
    this.createManName = createManName;
    this.assessType =
        BootDictEnumUtil.getEnumByKey(AssessTypeEnum.class, assess.getAssessType())
            .orElseThrow(() -> new CheckException("该审核【" + id + "】的审核类型异常，请联系管理员！"));
    this.assessState =
        BootDictEnumUtil.getEnumByKey(AssessStateEnum.class, assess.getAssessState())
            .orElseThrow(() -> new CheckException("该审核【" + id + "】的审核状态异常，请联系管理员！"));
    this.assessManName = assessManName;
    this.assessResult = StrUtil.emptyToDefault(assess.getAssessResult(), "-");
    this.supplierType = supplierType;
  }
}
