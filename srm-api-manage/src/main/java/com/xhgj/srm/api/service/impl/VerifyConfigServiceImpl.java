package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.group.AssessGroupDTO;
import com.xhgj.srm.api.dto.supplierorder.VerifyConfigInfoDTO;
import com.xhgj.srm.api.dto.supplierorder.VerifyConfigInfoVo;
import com.xhgj.srm.api.service.SupplierTemplateFieldService;
import com.xhgj.srm.api.service.VerifyConfigService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.VerifyConfigTypeEnum;
import com.xhgj.srm.jpa.dao.GroupDao;
import com.xhgj.srm.jpa.dao.SupplierInGroupDao;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.VerifyConfig;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.VerifyConfigRepository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * VerifyConfigServiceImpl
 */
@Service
public class VerifyConfigServiceImpl implements VerifyConfigService {

  @Resource
  private VerifyConfigRepository repository;
  @Resource
  private GroupRepository groupRepository;
  @Resource
  private GroupDao groupDao;
  @Resource
  private SupplierTemplateFieldService supplierTemplateFieldService;
  @Autowired
  private SupplierInGroupDao supplierInGroupDao;

  @Override
  public BootBaseRepository<VerifyConfig, String> getRepository() {
    return repository;
  }

  @Override
  public VerifyConfigInfoDTO getVerifyConfigInfo() {
    return repository.findFirstByConfigType(VerifyConfigTypeEnum.SUPPLIER_ORDER_SUBMIT.getCode())
        .map(VerifyConfigInfoDTO::new).orElse(null);
  }

  @Override
  public VerifyConfig getVerifyConfigByType(VerifyConfigTypeEnum typeEnum) {
    return repository.findFirstByConfigType(typeEnum.getCode()).orElseThrow(() -> new  CheckException("无此类型配置"));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void enableConfig(String id, Boolean enable) {
    repository.findById(id).ifPresent(verifyConfig -> {
      verifyConfig.setEnable(enable);
      repository.saveAndFlush(verifyConfig);
    });
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void updateOrganizationRole(VerifyConfigInfoDTO param) {
    // 检查传入组织编码是否存在
    validateOrganizationRoles(param.getOrganizationRole());
    repository.findById(param.getId()).ifPresent(verifyConfig -> {
      verifyConfig.setOrganizationRole(param.getOrganizationRole());
      repository.saveAndFlush(verifyConfig);
    });
  }

  private void validateOrganizationRoles(String organizationRole) {
    if (StrUtil.isBlank(organizationRole)) {
      return;
    }
    List<String> roleList = StrUtil.splitTrim(organizationRole, StrUtil.COMMA);
    // 批量检查所有组织编码是否存在
    Set<String> existingErpCodes =
        groupRepository.findAllByErpCodeInAndState(roleList,Constants.STATE_OK).stream()
        .map(Group::getErpCode)
        .collect(Collectors.toSet());
    List<String> nonExistingCodes = roleList.stream()
        .filter(code -> !existingErpCodes.contains(code))
        .collect(Collectors.toList());
    if (CollUtil.isNotEmpty(nonExistingCodes)) {
      throw new CheckException("以下传入的组织编码不存在：" + String.join(",", nonExistingCodes));
    }
  }

  @Override
  public List<AssessGroupDTO> getAllGroupsByKeyWord(String keyword) {
    return CollUtil.emptyIfNull(groupDao.getAllGroupsByKeyWord(keyword)).stream()
        .map(AssessGroupDTO::new).collect(Collectors.toList());
  }

  @Override
  public List<VerifyConfigInfoDTO> getAllConfig() {
    List<VerifyConfig> all = repository.findAll();
    return all.stream().map(VerifyConfigInfoVo::new).collect(Collectors.toList());
  }

  @Override
  public void verifyAll(Supplier supplier, String groupCode) {
    for (VerifyConfigTypeEnum value : VerifyConfigTypeEnum.values()) {
      verify(value, supplier, groupCode);
    }
  }

  @Override
  public void verify(VerifyConfigTypeEnum verifyConfigTypeEnum, Supplier supplier,
      String groupCode) {
    VerifyConfig verifyConfig =
        repository.findFirstByConfigTypeAndEnable(verifyConfigTypeEnum.getCode(), true);
    if (verifyConfig == null || !StrUtil.contains(verifyConfig.getOrganizationRole(), groupCode)) {
      return;
    }
    //采购订单提交潜在供应商校验配置
    if (verifyConfigTypeEnum == VerifyConfigTypeEnum.SUPPLIER_ORDER_SUBMIT) {
      supplierTemplateFieldService.checkPotentialSupplierQualification(supplier.getId(), groupCode);
      return;
    }
    // 国内供应商完善度≥90
    if (verifyConfigTypeEnum == VerifyConfigTypeEnum.SUPPLIER_ORDER_SUBMIT_2) {
      if (!StrUtil.equals(supplier.getSupType(), Constants.SUPPLIERTYPE_CHINA)) {
        return;
      }
      Integer integrity =
          Optional.ofNullable(supplierInGroupDao.getByGroupAndSupplier(supplier.getId(), groupCode))
              .map(SupplierInGroup::getIntegrity)
              .filter(StrUtil::isNotBlank)
              .map(NumberUtil::parseInt)
              .orElse(0);
      if (integrity < 90) {
        throw new CheckException("供应商的完善度必须达到90才能做单");
      }
    }
  }
}
