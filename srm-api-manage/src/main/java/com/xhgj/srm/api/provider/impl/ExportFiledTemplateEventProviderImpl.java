package com.xhgj.srm.api.provider.impl;/**
 * @since 2025/4/22 9:29
 */

import com.xhgj.srm.api.event.ExportFiledTemplateEventPublisher;
import com.xhgj.srm.common.dto.exportfiled.ExportTemplateParamProvider;
import com.xhgj.srm.v2.provider.ExportFiledTemplateEventProvider;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 *<AUTHOR>
 *@date 2025/4/22 09:29:05
 *@description
 */
@Service
public class ExportFiledTemplateEventProviderImpl implements ExportFiledTemplateEventProvider {

  @Resource
  private ExportFiledTemplateEventPublisher templateEventPublisher;

  @Override
  public void exportTemplateEvent(Object source, ExportTemplateParamProvider param, String type) {
    templateEventPublisher.publish(source, param, type);
  }
}
