package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.ProductTableVO;
import com.xhgj.srm.api.dto.SrmProductInfoDTO;
import com.xhgj.srm.api.dto.WebProductInfoList;
import com.xhgj.srm.api.dto.WebProductInfoList.WebProductInfo;
import com.xhgj.srm.jpa.dto.form.ProductSearchManageForm;
import com.xhgj.srm.request.vo.mpm.MPMUnitVO;
import com.xhgj.srm.v2.vo.ProductResultVO;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.List;

public interface ProductService {
  /**
   * 物料列表信息 重构
   */
  PageResult<ProductTableVO> getNormalProductPageRef(ProductSearchManageForm form);

  List<SrmProductInfoDTO> getSrmProductInfoList(List<String> codes, String useCode);

  byte[] downloadInvoiceZipByOrderId(List<WebProductInfoList.WebProductInfo> webProductInfoList)
      throws IOException;


  List<WebProductInfo> fillWebProductInfo(MultipartFile file) throws IOException;

  /**
   * @Description 获取计量单位列表
   **/
  PageResult<MPMUnitVO> getUnitList(String name, Integer pageNo, Integer pageSize);

  ProductResultVO getNormalProductDetail(String code);
}
