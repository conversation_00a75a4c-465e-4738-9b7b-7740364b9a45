package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.service.ContactService;
import com.xhgj.srm.api.service.EntryRegistrationLandingMerchantService;
import com.xhgj.srm.api.service.SupplierInGroupService;
import com.xhgj.srm.api.service.SupplierToMenuService;
import com.xhgj.srm.api.service.SupplierUserService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.RandomPasswordGeneratorUtil;
import com.xhgj.srm.common.enums.AssessStateEnum;
import com.xhgj.srm.common.enums.ShortMessageEnum;
import com.xhgj.srm.jpa.dao.EntryRegistrationLandingMerchantDao;
import com.xhgj.srm.jpa.entity.Assess;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.repository.AssessRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationLandingMerchantRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationOrderRepository;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.request.service.third.xhgj.XhgjSMSRequest;
import com.xhgj.srm.service.ShareLandingMerchantContractService;
import com.xhgj.srm.service.ShareSupplierInGroupService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**入驻报备单落地商信息*/
@Service
public class EntryRegistrationLandingMerchantServiceImpl implements
    EntryRegistrationLandingMerchantService {
  @Autowired
  private EntryRegistrationLandingMerchantRepository entryRegistrationLandingMerchantRepository;
  @Autowired
  EntryRegistrationLandingMerchantDao dao;
  @Resource
  private ShareLandingMerchantContractService landingMerchantContractService;
  @Resource
  private EntryRegistrationOrderRepository entryRegistrationOrderRepository;
  @Resource
  private AssessRepository assessRepository;
  @Resource
  private XhgjSMSRequest xhgjSMSRequest;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private SupplierUserService supplierUserService;
  @Resource
  private ShareSupplierInGroupService shareSupplierInGroupService;
  @Resource
  private SupplierInGroupService supplierInGroupService;
  @Resource
  private FileRepository fileRepository;
  @Resource
  private SupplierToMenuService supplierToMenuService;
  @Resource
  private ContactService contactService;
  @Override
  public BootBaseRepository<EntryRegistrationLandingMerchant, String> getRepository() {
    return entryRegistrationLandingMerchantRepository;
  }

  @Override
  @Transactional
  @Deprecated
  public void auditPassedHandle(String assessId, String mdmCode, Boolean first) {
    EntryRegistrationLandingMerchant landingMerchant =
        entryRegistrationLandingMerchantRepository.findFirstByAssessIdAndState(assessId,
            Constants.STATE_OK);
    //通过审核id找不到数据的话说明此审核不是通过准入报备单生成的，所以不需要处理。
    if (landingMerchant == null) return;
    checkAuditStatus(landingMerchant);
    landingMerchant.setSupplierAuditStatus(AssessStateEnum.PASS.getKey());
    landingMerchant.setMdmCode(mdmCode);
    entryRegistrationLandingMerchantRepository.save(landingMerchant);
    EntryRegistrationOrder entryRegistrationOrder =
        entryRegistrationOrderRepository.findById(landingMerchant.getEntryRegistrationOrderId())
            .orElseThrow(() -> new CheckException(
                "未找到对应的报备单，报备单供应商id：" + landingMerchant.getId()));
    Assess assess = assessRepository.findById(assessId).orElseThrow(() -> new CheckException("未找到相关审核记录"));
    Supplier supplier = supplierRepository.findById(assess.getTargetId())
        .orElseThrow(() -> new CheckException("主数据审核结束，但未找到对应供应商信息"));
    supplier.setState(Constants.STATE_OK);
    supplier.setMdmCode(mdmCode);
    //（跟随电商供应商所有权限）
    supplier.openOrderPermission();
    supplierRepository.save(supplier);
    // 开通前台权限
    supplierToMenuService.deleteAllBySupplierId(supplier.getId());
    SupplierInGroup supplierInGroup =
        shareSupplierInGroupService.createIfNotExist(Constants.HEADQUARTERS_CODE, supplier.getId(),
            landingMerchant, entryRegistrationOrder);
    // 生产6位随机密码 英文大小写+数字
    String pwd = RandomPasswordGeneratorUtil.generateRandomPassword(6);
    //生成供应商账号
    supplierUserService.createIfNotExist(supplier, landingMerchant.getEnterpriseName(),
        landingMerchant.getAccountUserPhone(), landingMerchant.getEmailAddress(), pwd,
        entryRegistrationOrder.getUpdateMan());
    // 生成合同 同时写入履约信息状态为开启
    landingMerchantContractService.addByEntryRegistrationOrderAndLandingMerchantAndSupplier(entryRegistrationOrder, landingMerchant, supplier);
    // 添加供应商联系人
    contactService.addContactByEntryRegistrationOrder(entryRegistrationOrder,supplier,
        supplierInGroup.getId());
    //更新其他组织下面的营业执照
    String licenseUrl = fileRepository.findFirstByRelationIdAndRelationTypeAndState(landingMerchant.getId(),
            Constants.FILE_TYPE_LANDING_MERCHANT_LICENSE, Constants.STATE_OK).map(File::getUrl)
        .orElse(StrUtil.EMPTY);
    supplierInGroupService.updateLicensesForOtherGroups(supplier.getId(), licenseUrl);
    //报备单审批通过，发送短信给落地商（第一次通过发送短信）
    if (first){
      //发送短信
      if (StrUtil.isNotBlank(landingMerchant.getAccountUserPhone())){
        String sendSmsMobile = landingMerchant.getAccountUserPhone();
        xhgjSMSRequest.sendSms(
            ShortMessageEnum.APPROVED_SEND_MESSAGE_TO_THE_LANDER_NEW_SUPPLIER,
            sendSmsMobile,
            new HashMap<String, String>() {
              {
                put("name", landingMerchant.getEnterpriseName());
                put("Password", pwd);
              }
            });
      }
    }
    // 设置MDM审核时间
    entryRegistrationOrder.setMdmCompletionTime(System.currentTimeMillis());
    entryRegistrationOrderRepository.save(entryRegistrationOrder);
  }

  private void checkAuditStatus(EntryRegistrationLandingMerchant landingMerchant) {
    if (!Objects.equals(landingMerchant.getSupplierAuditStatus(),
        AssessStateEnum.UN_ASSESS.getKey())) {
      throw new CheckException("该准入报备单主数据非待审核状态");
    }
  }

  @Override
  @Deprecated
  public void auditRejectionHandle(String assessId, String rejectReason) {
    EntryRegistrationLandingMerchant landingMerchant =
        entryRegistrationLandingMerchantRepository.findFirstByAssessIdAndState(assessId,
            Constants.STATE_OK);
    //通过审核id找不到数据的话说明此审核不是通过准入报备单生成的，所以不需要处理。
    if (landingMerchant == null) return;
    EntryRegistrationOrder entryRegistrationOrder =
        entryRegistrationOrderRepository.findById(landingMerchant.getEntryRegistrationOrderId())
            .orElseThrow(() -> new CheckException(
                "未找到对应的报备单，报备单供应商id：" + landingMerchant.getId()));
    if (StrUtil.isBlank(rejectReason)) {
      throw new CheckException("请填写驳回原因");
    }
    checkAuditStatus(landingMerchant);
    landingMerchant.setSupplierAuditStatus(AssessStateEnum.REJECT.getKey());
    landingMerchant.setRejectReason(rejectReason);
    entryRegistrationLandingMerchantRepository.save(landingMerchant);
    // 设置MDM审核时间
    entryRegistrationOrder.setMdmCompletionTime(System.currentTimeMillis());
    entryRegistrationOrderRepository.save(entryRegistrationOrder);
  }

  @Override
  public EntryRegistrationLandingMerchant getEntryRegistrationOrderId(String id) {
    return dao.getEntryRegistrationOrderId(id);
  }


  @Override
  public Supplier getSupplier(String entryRegistrationLandingMerchantId) {
    EntryRegistrationLandingMerchant landingMerchant = get(entryRegistrationLandingMerchantId,
        () -> CheckException.noFindException(EntryRegistrationLandingMerchant.class,
            entryRegistrationLandingMerchantId));
    if (StrUtil.isBlank(landingMerchant.getSupplierId())) {
      throw new CheckException("报备单落地商信息异常");
    }
    return supplierRepository.findById(landingMerchant.getSupplierId()).orElseThrow(() -> new CheckException("报备单落地商信息异常"));
  }

  @Override
  public List<EntryRegistrationLandingMerchant> findFirstByEntryRegistrationOrderIds(
      List<String> entryRegistrationOrderIds) {
    if (CollUtil.isEmpty(entryRegistrationOrderIds)) {
      return CollUtil.newArrayList();
    }
    List<EntryRegistrationLandingMerchant> list = entryRegistrationLandingMerchantRepository.findByEntryRegistrationOrderIdInAndState(
            entryRegistrationOrderIds, Constants.STATE_OK);
    // 去重
    Map<String, EntryRegistrationLandingMerchant> firstMap = list.stream().collect(
        Collectors.toMap(EntryRegistrationLandingMerchant::getEntryRegistrationOrderId,
            merchant -> merchant,
            (existing, replacement) -> existing));
    return new ArrayList<>(firstMap.values());
  }
}
