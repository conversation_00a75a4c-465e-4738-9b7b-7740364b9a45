package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.entity.Group;
import com.xhiot.boot.core.common.util.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DeptPageDTO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "SRM部门编码")
    private String code;

    @ApiModelProperty(value = "SRM部门名称")
    private String name;

    @ApiModelProperty(value = "SRM上级部门编码")
    private String parentCode;

    @ApiModelProperty(value = "SRM上级部门名称")
    private String parentName;

    @ApiModelProperty(value = "所属组织编码")
    private String orgCode;

    @ApiModelProperty(value = "组织全程")
    private String fullName;

    @ApiModelProperty(value = "ERP部门编码")
    private String erpCode;

    @ApiModelProperty(value = "创建人")
    private String createMan;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "应处理人")
    private String operator;

    public DeptPageDTO(Group group) {
        this.id = group.getId();
        this.code = group.getCode();
        this.name = group.getName();
        this.orgCode = group.getGroupCode();
        this.erpCode = group.getErpCode();
        this.createTime = group.getCreateTime()>0? DateUtils.formatTimeStampToNormalDateTime(group.getCreateTime()):"";
        this.fullName = group.getFullName();
        this.operator = group.getOperator();
    }

}
