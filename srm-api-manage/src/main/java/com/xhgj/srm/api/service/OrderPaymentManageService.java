package com.xhgj.srm.api.service;

import com.xhgj.srm.dto.order.OrderNeedPaymentListQuery;
import com.xhgj.srm.dto.order.OrderPaymentListQuery;
import com.xhgj.srm.jpa.entity.OrderPayment;
import com.xhiot.boot.framework.jpa.service.BootBaseService;

/**
 * OrderPaymentManageService
 */
public interface OrderPaymentManageService extends BootBaseService<OrderPayment, String> {

  /**
   * @description: 导出付款单
   * @param: param
   **/
  void exportOrderPayment(OrderPaymentListQuery param);

  void exportOrderNeedPayment(OrderNeedPaymentListQuery form);
}
