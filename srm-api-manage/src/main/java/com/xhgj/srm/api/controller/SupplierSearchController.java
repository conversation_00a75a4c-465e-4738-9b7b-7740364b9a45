package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.dto.supplier.search.AbroadSupplierSearchResult;
import com.xhgj.srm.api.dto.supplier.search.ChinaSupplierSearchResult;
import com.xhgj.srm.api.service.GroupService;
import com.xhgj.srm.api.service.SupplierInGroupService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("supplierSearch")
@Api(tags = {"供应商查询管理接口"})
public class SupplierSearchController extends AbstractRestController {

  @Autowired private SupplierInGroupService supplierInGroupService;
  @Resource
  private UserRepository userRepository;
  @Autowired
  private GroupService groupService;


  @ApiOperation(value = "搜索国内供应商", notes = "新增查询")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "keywords", value = "关键字"),
    @ApiImplicitParam(name = "groupCode", value = "操作组织编码", required = true),
  })
  @GetMapping("searchChinaSupplier")
  public ResultBean<List<ChinaSupplierSearchResult>> searchChinaSupplier(
      String keywords, @RequestParam String groupCode) {
      User user;
      try {
          user = getUser();
      } catch (Exception e) {
        // 为了支持准入报备单供应商h5页面完整信息
          user = userRepository.findFirstByRealNameAndState(Constants.USER_NAME_GUO_JIA_LEI,
                  Constants.STATE_OK);
      }
    Group group = groupService.getGroupByErpCode(groupCode);
    if (group == null) {
      throw new CheckException("操作组织【" + groupCode + "】不存在，请核实或联系管理员！");
    }
    return new ResultBean<>(
              supplierInGroupService.searchChinaSupplier(
                      keywords, group));
  }

  @ApiOperation(value = "搜索海外供应商", notes = "新增查询")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "keywords", value = "关键字"),
    @ApiImplicitParam(name = "groupCode", value = "操作组织编码", required = true),
  })
  @GetMapping("searchAbroadSupplier")
  public ResultBean<List<AbroadSupplierSearchResult>> searchAbroadSupplier(
      String keywords, @RequestParam String groupCode) {
    Group group = groupService.getGroupByErpCode(groupCode);
    if (group == null) {
      throw new CheckException("操作组织【" + groupCode + "】不存在，请核实或联系管理员！");
    }
    return new ResultBean<>(
        supplierInGroupService.searchAbroadSupplier(
            keywords, group));
  }
}
