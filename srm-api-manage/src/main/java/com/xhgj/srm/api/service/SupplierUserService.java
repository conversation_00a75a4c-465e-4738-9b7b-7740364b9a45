package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.SupplierUserAddDTO;
import com.xhgj.srm.api.dto.supplierUser.SupplierUserSaveForm;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface SupplierUserService  extends BootBaseService<SupplierUser, String> {

  @Deprecated
  void addSupplierUser(SupplierUserAddDTO supplierUserAddDTO);

  /**
   * 保存供应商用户
   * @param form
   */
  void saveSupplierUser(SupplierUserSaveForm form);

  /**
   * 供应商用户删除
   * @param supplierUserId
   */
  void deleteSupplierUser(String supplierUserId);

  /**
   * 供应商用户重置密码
   * @param supplierUserId
   */
  void updateSupplierUserPassword(String supplierUserId);

  /**
   * 通过供应商 id 获得供应商账号
   * @param supplierId 供应商 id 必传
   */
  List<SupplierUser> getSupplierUserListBySupplierIdAsc(String supplierId);

  /**
   * 根据供应商id获取供应商所有用户的邮箱地址
   * @param supplierId
   * @return 去重之后的邮箱地址
   */
  Optional<Set<String>> getSupplierMailbox(String supplierId);

  /**
   * 根据供应商id获取供应商所有用户的手机号
   * @param supplierId
   * @return 去重之后的手机号
   */
  Optional<Set<String>> getSupplierPhoneNumber(String supplierId);

  /**
   * 根据真实姓名查询
   */
  Optional<SupplierUser> getSupplierUserByRealName(String realName);

  /**
   * 查询用户真实姓名
   */
  Optional<String> getSupplierUserRealName(String userId);

  /**
   * 根据id查询手机号
   * @param supplierUserId 供应商用户id
   * @return 手机号
   */
  Optional<String> getMobileById(final String supplierUserId);

  boolean createIfNotExist(Supplier supplier, String name, String mobile, String emailAddress,
      String pwd, String createMan);
}
