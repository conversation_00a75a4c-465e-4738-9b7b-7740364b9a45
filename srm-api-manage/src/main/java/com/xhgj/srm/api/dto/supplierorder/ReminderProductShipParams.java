package com.xhgj.srm.api.dto.supplierorder;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-03-06 14:23
 */
@Data
public class ReminderProductShipParams {

  @ApiModelProperty("供应商订单 id")
  @NotBlank(message = "供应商订单 id 必传")
  private String supplierOrderId;
  @ApiModelProperty("物料编号")
  @NotEmpty(message = "物料编号必传")
  private List<String> productCodes;
}
