package com.xhgj.srm.api.dto.customerReceivable;

import com.xhgj.srm.api.dto.FileDTO;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> @date 2023/6/5
 */
@Data
public class AddSignParam {
  @NotBlank(message = "订单id不能为空")
  @ApiModelProperty(name = "订单id")
  String orderId;
  @NotBlank(message = "用户id不能为空")
  @ApiModelProperty("用户id")
  String userId;
  @NotNull(message = "回款凭证不能为空")
  @ApiModelProperty("回款凭证")
  FileDTO file;
}
