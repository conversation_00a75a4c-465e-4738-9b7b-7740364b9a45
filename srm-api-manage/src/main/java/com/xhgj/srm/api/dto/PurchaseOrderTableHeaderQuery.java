package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 采购订单列表表头筛选
 */
@Data
public class PurchaseOrderTableHeaderQuery extends PurchaseOrderPageQuery{

  @ApiModelProperty("筛选类型: 1.供应商名称，2.采购员，3.是否走scp,4.是否亏本 5.订单状态，6.订单类型，7.是否直发，8.供应商开票")
  @NotBlank(message = "筛选类型 必传")
  private String filterType;

  public Map<String, Object> toQueryMap(MergeUserPermission mergeUserPermission) {
    Map<String, Object> queryMap = super.toQueryMap(mergeUserPermission);
    queryMap.put("filterType", this.filterType);
    return queryMap;
  }

}
