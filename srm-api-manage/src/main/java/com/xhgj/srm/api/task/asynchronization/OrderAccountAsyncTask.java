package com.xhgj.srm.api.task.asynchronization;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.api.service.OrderAccountToOrderService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.jpa.dao.OrderAccountDao;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.dao.OrderDeliveryDao;
import com.xhgj.srm.jpa.dao.SupplierPerformanceDao;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderAccount;
import com.xhgj.srm.jpa.entity.OrderDelivery;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.repository.OrderInvoiceRelationRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.jpa.repository.OrderSupplierInvoiceRepository;
import com.xhgj.srm.request.dto.erp.AddPayableParams;
import com.xhgj.srm.request.dto.erp.AddPayableResult.Data;
import com.xhgj.srm.request.service.third.erp.ERPRequest;
import com.xhgj.srm.service.SharePlatformService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.config.BootConfig;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Shangyi @date 2023/7/5
 */
@Component
@Slf4j
public class OrderAccountAsyncTask {

  @Resource
  private OrderAccountDao orderAccountDao;
  @Resource
  private OrderDao orderDao;
  @Resource
  private OrderAccountToOrderService orderAccountToOrderService;
  @Resource
  private OrderDeliveryDao orderDeliveryDao;
  @Resource
  private OrderSupplierInvoiceRepository orderAccountInvoiceRepository;
  @Resource
  private SupplierPerformanceDao supplierPerformanceDao;
  @Resource
  private ERPRequest erpRequest;
  @Resource
  private BootConfig bootConfig;
  @Resource
  private OrderRepository orderRepository;
  @Resource
  private OrderInvoiceRelationRepository orderInvoiceRelationRepository;
  @Resource
  private SharePlatformService platformService;

  @Transactional
  public void addPayableUseSync(String accountId) {
    addPayable(accountId);
  }
  @Async
  @Transactional
  public void addPayable(String accountId) {
    if (StrUtil.isBlank(accountId)) {
      throw new CheckException("参数丢失，请联系管理员！");
    }
    OrderAccount orderAccount = orderAccountDao.get(accountId);
    if (orderAccount == null) {
      throw new CheckException("对账单状态异常，请联系管理员！");
    }
    ArrayList<Order> orders = new ArrayList<>();
    ArrayList<String> purchaseReceiptNumbers = new ArrayList<>();
    List<String> orderIds = orderAccountToOrderService.getOrderIdLIstByOrderAccountId(accountId);
    if (CollUtil.isEmpty(orderIds)) {
      handleAddPayableFail(orderAccount.getAccountNo(), "对账单内没有关联订单");
      throw new CheckException("对账单内没有关联订单");
    }
    for (String orderId : orderIds) {
      Order order = orderDao.get(orderId);
      if (order == null) {
        handleAddPayableFail(orderAccount.getAccountNo(), "对账单内含有异常状态的订单");
        throw new CheckException("对账单内含有异常状态的订单");
      }
      orders.add(order);
      List<OrderDelivery> orderDelivers = orderDeliveryDao.getOrderDeliveryByOrderId(orderId);
      if (!verifyErpReceiptNumber(orderDelivers)) {
        handleAddPayableFail(orderAccount.getAccountNo(), "订单没有入库单号");
        throw new CheckException("对账单含有没有入库单号的订单");
      }
      purchaseReceiptNumbers.addAll(getPurchaseReceiptNumber(orderDelivers));
    }
    List<OrderSupplierInvoice> orderAccountInvoices =
        orderAccountInvoiceRepository.findAllByOrderAccountId(accountId);
    if (CollUtil.isEmpty(orderAccountInvoices)) {
      handleAddPayableFail(orderAccount.getAccountNo(), "没有发票信息");
      throw new CheckException("对账单发票信息缺失，请联系管理员");
    }
    String invoiceNumber = getInvoiceNumber(orderAccountInvoices);
    SupplierPerformance supplierPerformance =
        supplierPerformanceDao.getBySupplierIdAndPlatformCode(orderAccount.getSupplierId(),
            orderAccount.getPlatformCode());
    if (supplierPerformance == null || StrUtil.isBlank(supplierPerformance.getDockingPurchaseErpCode())) {
      handleAddPayableFail(orderAccount.getAccountNo(), "没有采购编码信息");
      throw new CheckException("对账单对应平台没有采购员信息");
    }
    String buyerNo = supplierPerformance.getDockingPurchaseErpCode();
    String remark = generateRemarks(orders, orderAccount);
    AddPayableParams params =
        new AddPayableParams(StrUtil.join(",", purchaseReceiptNumbers), invoiceNumber, buyerNo,
            remark, orderAccount.getAccountNo());
    Data data = erpRequest.addPayable(params);
    orderAccount.setErpPayableNo(data.getPayableNo());
    orderAccount.setErpPayableMoney(data.getAmountPayable());
    log.info("成功生成应付单：{}", JSON.toJSONString(orderAccount));
    orderAccountDao.save(orderAccount);
  }

  @Async
  @Transactional
  public void createErpOrder(String addInvoiceNum,
      InputInvoiceOrder orderInvoiceRelation) {
    //审核通过 调用erp生成应付单
    ArrayList<String> purchaseReceiptNumbers = new ArrayList<>();
    List<String> orderIdsList = orderInvoiceRelation.getOrderIdsList();
    orderIdsList.add("-1");
    List<Order> orderList = orderRepository.findAllById(orderIdsList);
    log.info("订单查询====================================：{}",orderList);
    for (Order order : orderList) {
      if (order == null) {
        handleAddPayableFail(addInvoiceNum, "异常状态的订单");
        throw new CheckException("对账单内含有异常状态的订单");
      }
      List<OrderDelivery> orderDelivers =
          orderDeliveryDao.getOrderDeliveryByOrderId(order.getId());
      if (!verifyErpReceiptNumber(orderDelivers)) {
        handleAddPayableFail(order.getOrderNo(), "订单没有入库单号");
        throw new CheckException("订单含有没有入库单号的订单");
      }
      purchaseReceiptNumbers.addAll(orderDelivers.stream().map(OrderDelivery::getErpNo).collect(Collectors.toList()));
    }
    SupplierPerformance supplierPerformance =
        supplierPerformanceDao.getBySupplierIdAndPlatformCode(orderInvoiceRelation.getSupplierId(),
            orderInvoiceRelation.getPlatform());
    if (supplierPerformance == null || StrUtil.isBlank(supplierPerformance.getDockingPurchaseErpCode())) {
      List<String> orderNums =
          orderRepository.findAllById(orderIdsList).stream().map(Order::getOrderNo)
              .collect(Collectors.toList());
      handleAddPayableFail(StrUtil.join(StrUtil.SLASH, orderNums), "没有采购编码信息");
      throw new CheckException("订单对应平台没有采购员信息");
    }
    String buyerNo = supplierPerformance.getDockingPurchaseErpCode();
    String remark = generateRemarksRelation(orderList, orderInvoiceRelation.getPlatform());
    AddPayableParams params =
        new AddPayableParams(StrUtil.join(",", purchaseReceiptNumbers),
            addInvoiceNum
            , buyerNo,
            remark, null);
    Data data = erpRequest.addPayable(params);
    orderInvoiceRelation.setErpPayableNo(data.getPayableNo());
    orderInvoiceRelation.setErpPayableMoney(data.getAmountPayable());
    log.info("成功生成应付单：{}", JSON.toJSONString(orderInvoiceRelation));
    orderInvoiceRelationRepository.save(orderInvoiceRelation);
  }

  private String generateRemarks(List<Order> orders, OrderAccount orderAccount) {
    StringBuilder remark = new StringBuilder("SCP自动生成应付单，");
    String name = platformService.findNameByCode(orderAccount.getPlatformCode());
    remark.append(name).append("，");
    remark.append("具体订单号详见SCP【进项票】模块");
    return remark.toString();
  }

  private String generateRemarksRelation(List<Order> orders, String platform) {
    StringBuilder remark = new StringBuilder("SCP自动生成应付单，");
    String name = platformService.findNameByCode(platform);
    remark.append(name).append("，");
    remark.append("具体订单号详见SCP【进项票】模块");
    return remark.toString();
  }

  private String getInvoiceNumber(List<OrderSupplierInvoice> orderAccountInvoices) {
    List<String> invoiceNums = orderAccountInvoices.stream().map(OrderSupplierInvoice::getInvoiceNum)
        .collect(Collectors.toList());
    return StrUtil.join("/", invoiceNums);
  }

  private boolean verifyErpReceiptNumber(List<OrderDelivery> orderDeliveries) {
    if (CollUtil.isEmpty(orderDeliveries)) {
      return false;
    }
    for (OrderDelivery orderDelivery : orderDeliveries) {
      String erpNo = orderDelivery.getErpNo();
      if (StrUtil.isBlank(erpNo)) {
        return false;
      }
    }
    return true;
  }

  private List<String> getPurchaseReceiptNumber(List<OrderDelivery> orderDeliveries) {
    return orderDeliveries.stream().map(OrderDelivery::getErpNo).collect(Collectors.toList());
  }

  /**
   * 处理生成erp应付单失败的情况
   */
  public void handleAddPayableFail(String accountNo, String message) {
    log.error("新增erp应付单失败，对账单号：{},原因：{}", accountNo, message);
    sendDingTalkMessageForAddPayable(accountNo, message);
  }

  /**
   * 发送生成erp应付单失败钉钉消息
   */
  private void sendDingTalkMessageForAddPayable(String accountNo, String message) {
    String env = bootConfig.getEnv();
    DingUtils.sendMsgByWarningRobot(
        "【" + env + "环境 " + bootConfig.getAppName() + "】 对账单号：【" + accountNo + "】调用ERP"
            + "新增应付单报错：" + message + "请及时处理！", env);
  }
}
