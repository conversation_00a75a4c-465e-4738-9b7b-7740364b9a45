package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.Mission.MissionPageData;
import com.xhgj.srm.api.dto.Mission.MissionPageDetailParam;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;

public interface MissionService extends BootBaseService<Mission, String> {

  /**
   * 分页获取任务列表
   * @param type 操作类型
   * @param startDate 开始时间
   * @param endDate 结束时间
   * @param code 任务编码
   * @param userId 用户
   * @param curpage 页数
   * @param pagesize 条数
   * @return
   */
    PageResult<MissionPageData> getMissionPage(String type, String state, String startDate, String endDate, String code, String userId,Integer curpage, Integer pagesize);

  /**
   * 获取任务详情
   * @param id 任务id
   * @param curpage 页数
   * @param pagesize 显示条数
   * @return
   */
    MissionPageDetailParam getMissionDetailPage(String id, Integer curpage, Integer pagesize);

    /**
     * 任务id
     * @param id
     * @return
     */
    byte[] exportMission(String id);

    /**
     * 创建任务中心任务
     * @param user 用户 必传
     * @param type 任务类型 必传
     * @param source 信息来源 必传
     * @param link excel 链接
     * @param fileName 文件名
     */
    Mission createMission(User user,String type,String source,String link,String fileName);


}
