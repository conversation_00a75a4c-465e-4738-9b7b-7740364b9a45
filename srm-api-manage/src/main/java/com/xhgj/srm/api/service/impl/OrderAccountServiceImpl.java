package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.dto.account.AccountDetailDTO;
import com.xhgj.srm.api.dto.account.AccountOrderDetailDTO;
import com.xhgj.srm.api.dto.account.AccountPageInfoDTO;
import com.xhgj.srm.api.dto.account.AccountProductDetailDTO;
import com.xhgj.srm.api.dto.account.AccountQuery;
import com.xhgj.srm.api.dto.account.AccountQueryForm;
import com.xhgj.srm.api.dto.account.AccountRejectParams;
import com.xhgj.srm.api.dto.account.AccountReturnDetailDTO;
import com.xhgj.srm.api.dto.account.ExportAccountParam;
import com.xhgj.srm.dto.account.OrderAccountInvoiceInfo;
import com.xhgj.srm.dto.account.OrderInfoDTO;
import com.xhgj.srm.dto.account.ProductInfoDTO;
import com.xhgj.srm.api.service.OrderAccountService;
import com.xhgj.srm.api.service.OrderAccountToOrderService;
import com.xhgj.srm.api.service.OrderService;
import com.xhgj.srm.api.service.SearchSchemeService;
import com.xhgj.srm.api.service.SupplierUserService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.api.task.asynchronization.OrderAccountAsyncTask;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Batch;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.dto.ConfirmNeedNoticeCarDTO;
import com.xhgj.srm.common.enums.ShortMessageEnum;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.common.utils.MissionUtil;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dao.DingCardInfoDao;
import com.xhgj.srm.jpa.dao.MissionDao;
import com.xhgj.srm.jpa.dao.OrderAccountDao;
import com.xhgj.srm.jpa.dao.OrderAccountDetailDao;
import com.xhgj.srm.jpa.dao.OrderAccountProductDetailDao;
import com.xhgj.srm.jpa.dao.OrderAccountReturnDetailDao;
import com.xhgj.srm.jpa.dao.OrderAccountToOrderDao;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.dao.OrderDeliveryDao;
import com.xhgj.srm.jpa.dao.OrderDetailDao;
import com.xhgj.srm.jpa.dao.SearchSchemeDao;
import com.xhgj.srm.jpa.dao.SupplierPerformanceDao;
import com.xhgj.srm.jpa.dto.account.AccountStatistics;
import com.xhgj.srm.jpa.entity.*;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.repository.OrderAccountRepository;
import com.xhgj.srm.jpa.repository.OrderInvoiceRelationRepository;
import com.xhgj.srm.jpa.repository.PlatformRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.request.service.third.xhgj.XhgjSMSRequest;
import com.xhgj.srm.sender.mq.sender.BatchTaskMqSender;
import com.xhgj.srm.service.OrderAcceptService;
import com.xhgj.srm.service.ShareOrderAccountInvoiceService;
import com.xhgj.srm.service.SharePlatformService;
import com.xhgj.srm.service.TempOrderAccountService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
public class OrderAccountServiceImpl implements OrderAccountService {

    private final String url;

    public OrderAccountServiceImpl(SrmConfig config) {
        this.url = config.getUploadUrl();
    }

    @Autowired
    private OrderAccountRepository repository;

    @Autowired
    private SearchSchemeDao searchSchemeDao;

    @Autowired
    private OrderAccountDao orderAccountDao;

    @Autowired
    private OrderAccountDetailDao orderAccountDetailDao;

    @Autowired
    private OrderAccountReturnDetailDao orderAccountReturnDetailDao;

    @Autowired
    private OrderAccountProductDetailDao orderAccountProductDetailDao;

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    @Autowired private SearchSchemeService searchSchemeService;
    @Autowired private OrderAccountToOrderService orderAccountToOrderService;
    @Autowired private OrderService orderService;
    @Autowired private OrderDetailDao orderDetailDao;
    @Autowired private TempOrderAccountService tempOrderAccountService;
    @Autowired private OrderAccountRepository orderAccountRepository;
    @Autowired private OrderDao orderDao;
    @Resource
    private DingCardInfoDao dingCardInfoDao;
    @Resource
    private DingUtils dingUtils;
    @Resource
    private MissionDao missionDao;
    @Resource
    private MissionUtil missionUtil;
    @Resource
    private MissionRepository missionRepository;
    @Resource
    private BatchTaskMqSender batchTaskMqSender;
    @Resource
    private XhgjSMSRequest xhgjSMSRequest;
    @Resource
    private SupplierUserService supplierUserService;
    @Resource
    private OrderAccountAsyncTask orderAccountAsyncTask;
    @Resource
    private OrderDeliveryDao orderDeliveryDao;
    @Override
    public BootBaseRepository<OrderAccount, String> getRepository() {
        return repository;
    }
    @Resource
    private SrmConfig srmConfig;
    @Resource
    private OrderAccountToOrderDao orderAccountToOrderDao;

    @Resource
    OrderInvoiceRelationRepository orderInvoiceRelationRepository;
    @Resource
    private SharePlatformService platformService;
    @Resource
    PlatformRepository platformRepository;


    @Override
    public PageResult<AccountOrderDetailDTO> getAccountOrderDetail(String accountId, int pageNo, int pageSize) {
        Page<OrderAccountDetail> page = orderAccountDetailDao.getOrderAccountDetailPageByAccount(accountId, pageNo, pageSize);
        List<AccountOrderDetailDTO> pageDataList = new ArrayList<>();
        int totalPages = page.getTotalPages();
        if (!(pageNo > totalPages)) {
            List<OrderAccountDetail> orderAccounts = page.getContent();
            PageUtil.setOneAsFirstPageNo();
            pageDataList = CollUtil.emptyIfNull(orderAccounts).stream().map(AccountOrderDetailDTO::new).collect(Collectors.toList());
        }
        return new PageResult<>(pageDataList, page.getTotalElements(), totalPages, pageNo, pageSize);
    }

    @Override
    public PageResult<AccountProductDetailDTO> getAccountProductDetail(String accountId, Integer pageNo, Integer pageSize) {
        Page<OrderAccountProductDetail> page = orderAccountProductDetailDao.getOrderAccountProductDetailPageByAccount(accountId, pageNo, pageSize);
        List<AccountProductDetailDTO> pageDataList = new ArrayList<>();
        int totalPages = page.getTotalPages();
        if (!(pageNo > totalPages)) {
            List<OrderAccountProductDetail> orderAccountProducts = page.getContent();
            PageUtil.setOneAsFirstPageNo();
            pageDataList = CollUtil.emptyIfNull(orderAccountProducts).stream().map(AccountProductDetailDTO::new).collect(Collectors.toList());
        }
        return new PageResult<>(pageDataList, page.getTotalElements(), totalPages, pageNo, pageSize);
    }

    @Override
    public PageResult<AccountReturnDetailDTO> getAccountReturnDetail(String accountId, Integer pageNo, Integer pageSize) {
        Page<OrderAccountReturnDetail> page = orderAccountReturnDetailDao.getOrderAccountReturnDetailPageByAccount(accountId, pageNo, pageSize);
        List<AccountReturnDetailDTO> pageDataList = new ArrayList<>();
        int totalPages = page.getTotalPages();
        if (!(pageNo > totalPages)) {
            List<OrderAccountReturnDetail> orderAccountReturns = page.getContent();
            PageUtil.setOneAsFirstPageNo();
            pageDataList = CollUtil.emptyIfNull(orderAccountReturns).stream()
                    .map(orderAccountReturnDetail -> {
                        AccountReturnDetailDTO data = new AccountReturnDetailDTO(orderAccountReturnDetail);
                        String files = StringUtils.emptyIfNull(orderAccountReturnDetail.getFileUrls());
                        List<String> fileList = new ArrayList<>();
                        if (!StringUtils.isNullOrEmpty(files)) {
                            String[] urls = files.split(",");
                            for (String fileUrl : urls) {
                                fileList.add(url + fileUrl);
                            }
                        }
                        data.setIsUpload(StringUtils.isNullOrEmpty(files) ? Constants.NO : Constants.YES);
                        data.setFileList(fileList);
                        return data;
                    }).collect(Collectors.toList());
        }
        return new PageResult<>(pageDataList, page.getTotalElements(), totalPages, pageNo, pageSize);
    }

    @Override
    public PageResult<AccountPageInfoDTO> getAccountPageInfo(AccountQuery accountQuery,User user, PageParam param) {
        Long startCommitTime = accountQuery.getStartCommitTime();
        Long endCommitTime = accountQuery.getEndCommitTime();
        Long startAssessTime = accountQuery.getStartAssessTime();
        Long endAssessTime = accountQuery.getEndAssessTime();
        String accountStatus = accountQuery.getAccountStatus();
        String createSupplier = accountQuery.getCreateSupplier();
        String accountNo = accountQuery.getAccountNo();
        String accountOpenInvoiceStatus = accountQuery.getAccountOpenInvoiceStatus();
        String schemeId = accountQuery.getSchemeId();
        String userId = user.getId();
        List<String> platforms = new ArrayList<>();
        if(StrUtil.isNotBlank(accountQuery.getPlatforms())){
          platforms = Arrays.asList(accountQuery.getPlatforms().split(","));
        }
      // 查询方案
        if (StrUtil.isBlank(schemeId)) {
            SearchScheme search =
                searchSchemeService.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_SUPPLIER_ACCOUNT);
            if (search != null) {
                schemeId = search.getId();
            }
        }
        if (StrUtil.isNotEmpty(schemeId)) {
            SearchScheme search = searchSchemeService.get(schemeId);
            if (search != null && StrUtil.isNotEmpty(search.getContent())) {
                AccountQuery supplierChinaSchemeDTO =
                    JSON.parseObject(search.getContent(), new TypeReference<AccountQuery>() {});
                if (supplierChinaSchemeDTO != null) {
                    startCommitTime = ObjectUtil.defaultIfNull( startCommitTime,
                        supplierChinaSchemeDTO.getStartCommitTime());
                    endCommitTime =
                        ObjectUtil.defaultIfNull( endCommitTime,supplierChinaSchemeDTO.getEndCommitTime());
                    startAssessTime =
                        ObjectUtil.defaultIfNull( startAssessTime,supplierChinaSchemeDTO.getStartAssessTime());
                    endAssessTime =
                        ObjectUtil.defaultIfNull(endAssessTime,supplierChinaSchemeDTO.getEndAssessTime());
                    accountStatus =
                        ObjectUtil.defaultIfNull(accountStatus,supplierChinaSchemeDTO.getAccountStatus());
                    createSupplier = StrUtil.blankToDefault( createSupplier,supplierChinaSchemeDTO.getCreateSupplier());
                    accountOpenInvoiceStatus =
                        StrUtil.blankToDefault(accountOpenInvoiceStatus,supplierChinaSchemeDTO.getAccountOpenInvoiceStatus());
                    accountNo = StrUtil.blankToDefault(accountNo,supplierChinaSchemeDTO.getAccountNo());
                    String searchPlatforms = supplierChinaSchemeDTO.getPlatforms();
                    if (StrUtil.isNotBlank(searchPlatforms)) {
                      platforms = Arrays.asList(searchPlatforms.split(","));
                  }
                }
            }
        }
      Page page =
          orderAccountDao.findAccountPage(accountNo, createSupplier, accountStatus,
              accountOpenInvoiceStatus, platforms,
              startCommitTime, endCommitTime, startAssessTime,
              endAssessTime, param.getPageNo(), param.getPageSize());
      int totalPages = page.getTotalPages();
      List<AccountPageInfoDTO> pageDataList = new ArrayList<>();
      if (totalPages >= param.getPageNo()) {
        List<String> accountIdList = page.getContent();
        PageUtil.setOneAsFirstPageNo();
        for (String id : accountIdList) {
          OrderAccount orderAccount =
              orderAccountRepository
                  .findById(id)
                  .orElseThrow(() -> CheckException.noFindException(Product.class, id));
          AccountPageInfoDTO data = new AccountPageInfoDTO(orderAccount);
          long orderCountByOrderAccount =
              orderAccountToOrderDao.getOrderCountByOrderAccount(orderAccount.getId());
          data.setOrderCount((int) orderCountByOrderAccount);
          // 设置平台
          String platformName = platformService.findNameByCode(orderAccount.getPlatformCode());
          data.setPlatformNames(platformName);
          pageDataList.add(data);
        }
      }
      return new PageResult<>(pageDataList, page.getTotalElements(), totalPages, param.getPageNo(), param.getPageSize());
    }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_SUPPLIER_ACCOUNT)
  public PageResult<AccountPageInfoDTO> getAccountPageInfoRef(AccountQueryForm form) {
    Page<OrderAccount> page = orderAccountDao.findAccountPageRef(form.toQueryMap());
    int totalPages = page.getTotalPages();
    List<OrderAccount> content = page.getContent();
    if (CollUtil.isEmpty(content)) {
      return new PageResult<>(new ArrayList<>(), page.getTotalElements(), totalPages, form.getPageNo(),
          form.getPageSize());
    }
    List<String> orderAccountIds = content.stream().map(OrderAccount::getId).collect(Collectors.toList());
    List<String> platformCodes =
        content.stream().map(OrderAccount::getPlatformCode).filter(Objects::nonNull).distinct()
            .collect(Collectors.toList());
    // 过滤出数量为NULL的Ids
    List<String> nullCountIds =
        content.stream().filter(item -> item.getOrderCount() == null).map(OrderAccount::getId)
            .collect(Collectors.toList());
    nullCountIds.add("-1");
    Map<String, Long> nullOrderCountByOrderAccounts =
        orderAccountToOrderDao.getOrderCountByOrderAccounts(nullCountIds);
    Map<String, Platform> code2Platform = platformRepository.findByCodeIn(platformCodes).stream()
        .collect(Collectors.toMap(Platform::getCode, Function.identity(), (k1, k2) -> k1));
    List<AccountPageInfoDTO> result = content.stream().map(item -> {
      AccountPageInfoDTO data = new AccountPageInfoDTO(item);
      if (item.getOrderCount() == null) {
        data.setOrderCount(nullOrderCountByOrderAccounts.getOrDefault(item.getId(), 0L).intValue());
      }
      data.setPlatformNames(
          code2Platform.getOrDefault(item.getPlatformCode(), new Platform()).getName());
      return data;
    }).collect(Collectors.toList());
    return new PageResult<>(result, page.getTotalElements(), totalPages, form.getPageNo(),
        form.getPageSize());
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_SUPPLIER_ACCOUNT)
  public AccountStatistics getAccountStatistics(AccountQueryForm form) {
    List<OrderAccount> orderAccountList = orderAccountDao.getAccountStatistics(form.toQueryMap());
    AccountStatistics res = new AccountStatistics();
    BigDecimal totalPrice =
        orderAccountList.stream().map(OrderAccount::getPrice).filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    Integer totalOrderCount =
        orderAccountList.stream().map(OrderAccount::getOrderCount).filter(Objects::nonNull)
            .reduce(0, Integer::sum);
    // 过滤出数量为NULL的
    List<OrderAccount> nullCount =
        orderAccountList.stream().filter(item -> item.getOrderCount() == null)
            .collect(Collectors.toList());
    List<String> nullCountIds =
        nullCount.stream().map(OrderAccount::getId).collect(Collectors.toList());
    if (CollUtil.isNotEmpty(nullCountIds)) {
      Map<String, Long> orderCountByOrderAccounts =
          orderAccountToOrderDao.getOrderCountByOrderAccounts(nullCountIds);
      totalOrderCount += orderCountByOrderAccounts.values().stream().reduce(0L, Long::sum).intValue();
    }
    res.setPrice(totalPrice);
    res.setOrderCount(totalOrderCount);
    return res;
  }

  @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmOrderAccount(List<String> orderAccountIds,String userGroup,String userId) {
      if (ObjectUtil.isEmpty(orderAccountIds)) {
        return;
      }
      orderAccountIds.forEach(id ->{
        OrderAccount orderAccount = get(id,
            () -> CheckException.noFindException(OrderAccount.class, id));
        User user =
            userService.get(userId, () -> CheckException.noFindException(User.class, userId));
        if (!Constants_order.ORDER_ACCOUNT_STATUS_CONDUCT.equals(orderAccount.getAccountState())) {
          throw new CheckException(
              "【"+Constants_order.ORDER_ACCOUNT_STATUS_MAP.get(orderAccount.getAccountState())+"】"
                  + "状态的对账单无法进行确认!");
        }
        BigDecimal finalPrince =
            tempOrderAccountService.getFinalAccountPrice(orderAccountToOrderService.getOrderIdLIstByOrderAccountId(orderAccount.getId()));
      long currentTimeMillis = System.currentTimeMillis();
      orderAccount.setAssessTime(currentTimeMillis);
        orderAccount.setPrice(finalPrince);
        orderAccount.setConfirmMan(user.getRealName());
        orderAccount.setAccountState(Constants_order.ORDER_ACCOUNT_STATUS_COMPLETE);
        save(orderAccount);
        List<String> orderIdList = orderAccountToOrderService.getOrderIdLIstByOrderAccountId(id);
        for (String orderId : orderIdList) {
          Order order = orderService.get(orderId,
              ()-> CheckException.noFindException(Order.class, orderId));
          orderDao.optimisticLockUpdateOrder(order, o -> {
            o.setAccountStatus(Constants_order.ORDER_ACCOUNT_STATUS_COMPLETE);
            o.setConfirmAccountTime(currentTimeMillis);
            if (StrUtil.isNotBlank(o.getOrderInvoiceRelationId())) {
              Optional<InputInvoiceOrder> invoiceRelation =
                  orderInvoiceRelationRepository.findById(o.getOrderInvoiceRelationId());
              if (invoiceRelation.isPresent()) {
                InputInvoiceOrder orderInvoiceRelation = invoiceRelation.get();
                orderInvoiceRelation.setInvoiceState(Constants.ORDER_INVOICE_STATE_PASS);
                orderInvoiceRelationRepository.save(orderInvoiceRelation);
              }
            }
          });
        }
        String phoneNumber = getCreatePhoneNumberByOrderAccount(orderAccount);
        sendShortMessageForOrderAccount(ShortMessageEnum.ACCOMPLISH_ORDER_ACCOUNT, phoneNumber,
            orderAccount.getAccountNo());
      });
    }

  /**
   * 获取对账单创建人手机号
   * @param orderAccount
   * @return
   */
  private String getCreatePhoneNumberByOrderAccount(OrderAccount orderAccount) {
    String createUserId = orderAccount.getCreateSupplierUserId();
    if (StrUtil.isBlank(createUserId)) {
      return null;
    }
    SupplierUser supplierUser = supplierUserService.get(createUserId);
    if (supplierUser == null) {
      return null;
    }
    return supplierUser.getMobile();
  }

    @Override
    public Long getAccountCountByStatus(String status) {
        Assert.notBlank(status);
        if (!Constants_order.ORDER_ACCOUNT_STATUS_MAP.containsKey(status)) {
            throw new CheckException("参数非法");
        }
        return repository.countByAccountStateAndState(status,Constants.STATE_OK);
    }

  @Override
  public void confirmAccountInvoice(String accountId,String confirmMan) {
    OrderAccount orderAccount =
        get(accountId, () -> CheckException.noFindException(OrderAccount.class, accountId));
    orderAccount.setAccountOpenInvoiceStatus(Constants.ORDER_INVOICE_STATE_PASS);
    orderAccount.setInvoiceConfirmMan(StringUtils.emptyIfNull(confirmMan));
    orderAccount.setInvoiceConfirmTime(System.currentTimeMillis());
    save(orderAccount);
    long currentTimeMillis = System.currentTimeMillis();
    List<Order> orders = orderDao.getOrderByAccountId(accountId);
    if (CollUtil.isEmpty(orders)) {
      throw new CheckException("对账单关联订单异常");
    }
    orders.stream().filter(Objects::nonNull).forEach(order -> {
      orderDao.optimisticLockUpdateOrder(order, o -> {
        o.setSupplierOpenInvoiceStatus(Constants.ORDER_INVOICE_STATE_PASS);
        o.setConfirmAccountOpenInvoiceTime(currentTimeMillis);
      });
      orderService.setAccountStatusAllow(order);
    });
    updateDingCardByAccount(accountId);
    updateDingToDoStatus(orderAccount);
    if (isAddPayable(orders)) {
      orderAccountAsyncTask.addPayable(accountId);
    }
  }

  /**
   * 更新钉钉代办状态
   * @param orderAccount
   */
  private void updateDingToDoStatus(OrderAccount orderAccount) {
    if (orderAccount == null) {
      return;
    }
    List<DingCardInfo> dingCardInfo =
        dingCardInfoDao.getByLikeRelevanceIdAndType(orderAccount.getId(),
        Constants.SAVE_MESSAGE_TYPE_DING_TODO);
    if (CollUtil.isEmpty(dingCardInfo)) {
      return;
    }
    List<String> phoneNums = srmConfig.getOrderOpenInvoiceDingTaskAndNewNotice();
    for (DingCardInfo cardInfo : dingCardInfo) {
      if (StrUtil.isBlank(cardInfo.getRelevanceId())) {
        return;
      }
      String relevanceId = cardInfo.getRelevanceId();
      String tackId = StrUtil.removeAll(relevanceId, orderAccount.getId() + ",");
      for (String phoneNum : phoneNums) {
        dingUtils.updateToDoStatus(phoneNum, tackId);
      }
    }
  }

  /**
   * 是否满足生成应付单的条件
   */
  public boolean isAddPayable(List<Order> orders) {
    if (CollUtil.isEmpty(orders)) {
      return false;
    }
    orders = orders.stream().filter(Objects::nonNull).collect(Collectors.toList());
    for (Order order : orders) {
      String erpOrderNo = order.getErpOrderNo();
      if (StrUtil.isBlank(erpOrderNo)) {
        return false;
      }
      boolean verified = verifyOrderDeliveryErpNo(order.getId());
      if (!verified) {
        return false;
      }
    }
    return true;
  }

  private boolean verifyOrderDeliveryErpNo(String orderId) {
    if (StrUtil.isBlank(orderId)) {
      return false;
    }
    List<OrderDelivery> orderDelivers =
        orderDeliveryDao.getOrderDeliveryByOrderId(orderId);
    if (CollUtil.isEmpty(orderDelivers)) {
      return false;
    }
    for (OrderDelivery orderDelivery : orderDelivers) {
      //erp入库单号
      String erpNo = orderDelivery.getErpNo();
      if (StrUtil.isBlank(erpNo)) {
        return false;
      }
    }
    return true;
  }

  @Override
  public void updateSuperDingTaskRobotCar(ConfirmNeedNoticeCarDTO confirmNeedNoticeCarDTO) {
    log.info("接受钉钉开票通知的回调");
    log.info(JSON.toJSONString(confirmNeedNoticeCarDTO));
    // 获得卡片的唯一标识,就是对账单id
    String outTrackId = confirmNeedNoticeCarDTO.getOutTrackId().trim();
    log.info("卡片唯一id:{}", outTrackId);
    String[] split = outTrackId.split(",");
    confirmAccountInvoice(split[0],"");
    updateDingCard(outTrackId);
  }

  private void updateDingCard(String outTrackId) {
    if (!ObjectUtils.isEmpty(outTrackId)) {
      DingCardInfo dingCardInfo = dingCardInfoDao.getByRelevanceId(outTrackId);
      updateSuperiorDingTaskRobotCar(outTrackId, dingCardInfo);
    }
  }

  private void updateSuperiorDingTaskRobotCar(String outTrackId, DingCardInfo dingCardInfo) {
    if (dingCardInfo != null) {
      String info = dingCardInfo.getInfo();
      Map<String, Object> stringObjectMap =
          JSON.parseObject(info, new TypeReference<Map<String, Object>>(){});
      String buttonConceal = "false";
      stringObjectMap.put("assess_button", buttonConceal);
      log.info("钉钉回调卡片参数：{}", JSON.toJSONString(stringObjectMap));
      dingUtils.updateSuperiorDingTaskRobotCar(stringObjectMap, outTrackId);
    }
  }

  private void updateDingCardByAccount(String accountId) {
    if (!ObjectUtils.isEmpty(accountId)) {
      List<DingCardInfo> dingCardInfo = dingCardInfoDao.getByLikeRelevanceIdAndType(accountId,
          Constants.SAVE_MESSAGE_TYPE_DING_CARD);
      for (DingCardInfo cardInfo : dingCardInfo) {
        updateSuperiorDingTaskRobotCar(cardInfo.getRelevanceId(), cardInfo);
      }
    }
  }

  @Override
  public void export(ExportAccountParam param) {
    String userId = param.getUserId();
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    if (user == null) {
      throw new CheckException("用户为空");
    }
    // 设置任务编号
    // 新增任务
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(user.getCode()),
        "导出-对账单导出",
        userId,
        Constants.PLATFORM_TYPE_AFTER
    );
    missionRepository.save(mission);
    batchTaskMqSender.toHandleBatchTask(
        mission.getId(), JSON.toJSONString(param), Constants_Batch.BATCH_TASK_ORDER_ACCOUNT_OUT);
  }

  @Override
  @Transactional
  public void reject(AccountRejectParams params) {
    OrderAccount orderAccount = orderAccountDao.get(params.getId());
    if (orderAccount == null)
      return;
    if (!Objects.equals(orderAccount.getAccountState(),
        Constants_order.ORDER_ACCOUNT_STATUS_CONDUCT)) {
      throw new CheckException("对账状态为“对账中”的对账单才能被驳回");
    }
    setRejectStatus(orderAccount, params.getReason());
    List<String> orderIds =
        orderAccountToOrderService.getOrderIdLIstByOrderAccountId(params.getId());
    //更改关联订单的对账状态
    if (CollUtil.isNotEmpty(orderIds)) {
      for (String orderId : orderIds) {
        Order order = orderDao.get(orderId);
        if (order == null) {
          continue;
        }
        order.setAccountStatus(Constants_order.ORDER_ACCOUNT_STATE_REJECT);
        orderDao.save(order);
      }
    }
    orderAccountDao.save(orderAccount);
    String phoneNumber = getCreatePhoneNumberByOrderAccount(orderAccount);
    sendShortMessageForOrderAccount(ShortMessageEnum.REJECT_ORDER_ACCOUNT, phoneNumber,
        orderAccount.getAccountNo());
  }

  /**
   * 发送短信
   * @param type
   * @param phoneNumber
   * @param accountNo
   */
  private void sendShortMessageForOrderAccount(ShortMessageEnum type, String phoneNumber, String accountNo) {
    if (StrUtil.isBlank(phoneNumber) || StrUtil.isBlank(accountNo)) {
      return;
    }
    HashMap<String, String> param = new HashMap<>();
    param.put("accountNo", accountNo);
    sendShortMessage(type, phoneNumber, param);

  }

  private void sendShortMessage(ShortMessageEnum type, String phoneNumber,
      Map<String, String> param) {
    log.info("供应商手机号：{}，参数：{}", phoneNumber, JSON.toJSONString(param));
    xhgjSMSRequest.sendSms(type, phoneNumber, param);
  }

  /**
   * 修改对账单状态为“驳回待修改”
   * @param orderAccount
   * @param reason
   */
  private void setRejectStatus(OrderAccount orderAccount, String reason) {
    orderAccount.setAccountState(Constants_order.ORDER_ACCOUNT_STATE_REJECT);
    orderAccount.setGroundsForRejection(reason);
  }

  @Override
  public Boolean examinePurchaseNo(String accountId) {
    OrderAccount orderAccount = orderAccountDao.get(accountId);
    if (orderAccount == null) {
      orderAccountAsyncTask.handleAddPayableFail(accountId, "参数异常");
      throw new CheckException("参数异常");
    }
    List<Order> orders = orderDao.getOrderByAccountId(accountId);
    if (CollUtil.isEmpty(orders)) {
      orderAccountAsyncTask.handleAddPayableFail(orderAccount.getAccountNo(), "对账单关联的订单异常");
      throw new CheckException("对账单关联的订单异常");
    }
    if (isAddPayable(orders)) {
      //如果对账单没有erp应付单的数据，重新尝试。
      if (orderAccount.getErpPayableMoney() == null || StrUtil.isBlank(
          orderAccount.getErpPayableNo())) {
        log.info("对账单重新尝试生成erp应付单，对账单号：{}", orderAccount.getAccountNo());
        orderAccountAsyncTask.addPayableUseSync(accountId);
      }
    } else {
      for (Order order : orders) {
        if (order == null) {
          orderAccountAsyncTask.handleAddPayableFail(orderAccount.getAccountNo(), "订单状态异常，请联系管理员");
          throw new CheckException("订单状态异常，请联系管理员");
        }
        //校验哪些订单缺少采购单号
        String erpOrderNo = order.getErpOrderNo();
        if (StrUtil.isBlank(erpOrderNo)) {
          orderAccountAsyncTask.handleAddPayableFail(orderAccount.getAccountNo(), "客户单号" + order.getOrderNo() + "订单缺少采购订单号");
          throw new CheckException("客户单号" + order.getOrderNo() + "订单缺少采购订单号");
        }
        //校验哪些发货单缺少入库单号
        List<OrderDelivery> orderDelivers =
            orderDeliveryDao.getOrderDeliveryByOrderId(order.getId());
        if (CollUtil.isEmpty(orderDelivers)) {
          orderAccountAsyncTask.handleAddPayableFail(orderAccount.getAccountNo(), "客户单号：" + order.getOrderNo() + "，缺少发货信息");
          throw new CheckException("客户单号：" + order.getOrderNo() + "，缺少发货信息");
        }
        for (OrderDelivery orderDeliver : orderDelivers) {
          String erpNo = orderDeliver.getErpNo();
          if (StrUtil.isBlank(erpNo)) {
            orderAccountAsyncTask.handleAddPayableFail(orderAccount.getAccountNo(), "客户单号：" + order.getOrderNo() + "，缺少入库单号");
            throw new CheckException("客户单号：" + order.getOrderNo() + "，缺少入库单号");
          }
        }
      }
    }
    return true;
  }

}
