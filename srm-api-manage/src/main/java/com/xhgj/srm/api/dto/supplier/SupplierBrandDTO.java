package com.xhgj.srm.api.dto.supplier;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.Brand;
import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/15 15:01
 */
@Data
@NoArgsConstructor
public class SupplierBrandDTO {

  @ApiModelProperty("品牌 id（MDM id）")
  @NotBlank(message = "品牌 id 必传！")
  private String id;

  @ApiModelProperty("srm 品牌 id")
  private String srmBrandId;

  @ApiModelProperty("中文名")
  @NotBlank(message = "品牌中文名必传！")
  private String nameCn;

  @ApiModelProperty("英文名")
  @NotBlank(message = "品牌英文名必传！")
  private String nameEn;

  @ApiModelProperty("品牌编码")
  private String brandCode;

  @ApiModelProperty("经营类型：1：品牌原厂；2：品牌代理商")
  private String businessType;
  /**
   * 通用授权
   */
  @ApiModelProperty("通用授权")
  private SupplierFileDTO generalLicenseUrl;
  /**
   * 代理授权
   */
  @ApiModelProperty("代理授权")
  private SupplierFileDTO proxyLicenseUrl;


  @ApiModelProperty("商标注册证书")
  private SupplierFileDTO trademarkRegistrationUrl;

  @ApiModelProperty("品牌logo")
  private String logoUrl;
  @ApiModelProperty("关联人名称")
  private String associatedName;

  @ApiModelProperty("创建时间")
  private Long createTime;

  @ApiModelProperty("授权开始时间")
  private Long accreditStartTime;
  @ApiModelProperty("授权结束时间")
  private Long accreditEndTime;

  public SupplierBrandDTO(Brand brand) {
    this.id = brand.getBrandMdmId();
    this.nameCn = brand.getBrandnameCn();
    this.nameEn = brand.getBrandnameEn();
    this.brandCode = StrUtil.emptyIfNull(brand.getCode());
    this.businessType = StrUtil.emptyIfNull(brand.getBusinessType());
    this.createTime = brand.getCreateTime();
    this.srmBrandId = brand.getId();
    this.accreditStartTime = Objects.isNull(brand.getAccreditStartTime()) ? 0 :
        brand.getAccreditStartTime();
    this.accreditEndTime =  Objects.isNull(brand.getAccreditEndTime()) ? 0 :
        brand.getAccreditEndTime();
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SupplierBrandDTO that = (SupplierBrandDTO) o;
    return Objects.equals(id, that.id);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id);
  }
}
