package com.xhgj.srm.api.service.impl;

import static com.xhgj.srm.common.enums.PayTypeSAPEnums.getContractDownloadPayType;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.api.dto.FileDetails;
import com.xhgj.srm.api.dto.LandingContract.ImportFileParams;
import com.xhgj.srm.api.dto.LandingContract.LandingMerchantContractExportQuery;
import com.xhgj.srm.api.dto.LandingContract.form.LandingMerchantContractQueryForm;
import com.xhgj.srm.api.dto.LandingContractAddParamDTO;
import com.xhgj.srm.api.dto.LandingContractDTO;
import com.xhgj.srm.api.dto.LandingContractDetailsDTO;
import com.xhgj.srm.api.dto.LandingContractDetailsDTO.LandingContractDetailsDTOBuilder;
import com.xhgj.srm.api.dto.LandingContractOrderDetailsDTO;
import com.xhgj.srm.api.dto.LandingContractPageDTO;
import com.xhgj.srm.api.dto.LandingContractPaymentDetailsDTO;
import com.xhgj.srm.api.factory.MapStructFactory;
import com.xhgj.srm.api.service.AssessService;
import com.xhgj.srm.api.service.EntryRegistrationLandingMerchantService;
import com.xhgj.srm.api.service.EntryRegistrationService;
import com.xhgj.srm.api.service.FileService;
import com.xhgj.srm.api.service.LandingMerchantContractService;
import com.xhgj.srm.api.service.MissionService;
import com.xhgj.srm.api.service.SupplierChangeRecordService;
import com.xhgj.srm.api.service.SupplierPerformanceService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.util.ImportExcelUtil;
import com.xhgj.srm.api.utils.ManageSecurityUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Batch;
import com.xhgj.srm.common.Constants_DingCarTemplate;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.dto.LandingContract.LandingMerchantContractPut2Pdf;
import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.common.enums.AssessTypeEnum;
import com.xhgj.srm.common.enums.FileReviewStateEnum;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.PurchaseOrderInvoiceType;
import com.xhgj.srm.common.enums.TitleOfTheContractEnum;
import com.xhgj.srm.common.enums.contract.LandingMerchantContractKeywordEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationCooperationTypeEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationDiscountTypeEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationStatusEnum;
import com.xhgj.srm.common.enums.landingContract.ContractStatus;
import com.xhgj.srm.common.enums.supplierUser.SupplierChangeLogEnum;
import com.xhgj.srm.common.utils.FileUtil;
import com.xhgj.srm.common.utils.MissionUtil;
import com.xhgj.srm.common.utils.MoneyUtil;
import com.xhgj.srm.common.utils.WordPoiUtils;
import com.xhgj.srm.common.utils.ZIPUtils;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.domain.Contract;
import com.xhgj.srm.dto.bundle.LandingContractBundleDto;
import com.xhgj.srm.dto.supplier.SupplierChangeCreateForm;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.dao.GroupDao;
import com.xhgj.srm.jpa.dao.LandingMerchantContractDao;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.dao.PlatformDao;
import com.xhgj.srm.jpa.dao.SupplierDao;
import com.xhgj.srm.jpa.dao.SupplierInGroupDao;
import com.xhgj.srm.jpa.dao.SupplierUserDao;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.dto.landingContract.LandingContractStatistics;
import com.xhgj.srm.jpa.dto.landingContract.SupplierId2PlatformCode;
import com.xhgj.srm.jpa.entity.Assess;
import com.xhgj.srm.jpa.entity.EntryRegistrationDiscount;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderPayment;
import com.xhgj.srm.jpa.entity.OrderPaymentToOrder;
import com.xhgj.srm.jpa.entity.Platform;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.AssessRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationDiscountRepository;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.LandingMerchantContractRepository;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentDao;
import com.xhgj.srm.jpa.repository.OrderPaymentRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentToOrderRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.jpa.repository.SupplierPerformanceRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDiscountDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDiscountDetailDTO;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import com.xhgj.srm.registration.repository.EntryRegistrationRepository;
import com.xhgj.srm.request.dto.supplierRate.SupplierRateDetailParam;
import com.xhgj.srm.request.service.third.oms.OMSService;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhgj.srm.sender.mq.sender.BatchTaskMqSender;
import com.xhgj.srm.service.LandingContractBundleService;
import com.xhgj.srm.service.OAUserService;
import com.xhgj.srm.service.OrderAcceptService;
import com.xhgj.srm.service.OrderPaymentService;
import com.xhgj.srm.service.ShareEntryRegistrationService;
import com.xhgj.srm.service.ShareLandingMerchantContractService;
import com.xhgj.srm.service.ShareOrderService;
import com.xhgj.srm.service.SharePlatformService;
import com.xhgj.srm.service.ShareSupplierPerformanceService;
import com.xhgj.srm.vo.record.SupplierChangeRecordVo.SupplierNewRateRecord;
import com.xhgj.srm.vo.record.SupplierChangeRecordVo.SupplierOldRateRecord;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.mvc.base.PageResult;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 落地商合同服务层
 *
 * <AUTHOR> Shangyi
 */
@Service
@Slf4j
public class LandingMerchantContractServiceImpl implements LandingMerchantContractService {

  private static final BigDecimal YUAN_TO_WAN = new BigDecimal("10000");
  @Resource
  private LandingMerchantContractDao contractDao;
  @Resource
  private LandingMerchantContractRepository landingMerchantContractRepository;
  @Resource
  private GroupDao groupDao;
  @Resource
  private SupplierDao supplierDao;
  @Resource
  private SupplierInGroupDao supplierInGroupDao;
  @Resource
  private OrderDao orderDao;
  @Resource
  private UserDao userDao;
  @Resource
  private FileDao fileDao;
  @Resource
  private UserService userService;
  @Resource
  private OrderPaymentToOrderRepository toOrderRepository;
  @Resource
  private SupplierPerformanceRepository supplierPerformanceRepository;
  @Resource
  private OrderRepository orderRepository;
  @Resource
  private OrderPaymentDao orderPaymentDao;
  @Resource
  private SupplierUserDao supplierUserDao;
  @Autowired
  private OrderPaymentRepository orderPaymentRepository;
  @Autowired
  private OrderPaymentService orderPaymentService;
  @Resource
  private SharePlatformService platformService;

  @Autowired
  private LandingMerchantContractRepository repository;
  @Resource
  private OrderAcceptService orderAcceptService;
  @Autowired
  private AssessService assessService;
  @Resource
  private EntryRegistrationService entryRegistrationService;
  @Resource
  private EntryRegistrationLandingMerchantService entryRegistrationLandingMerchantService;
  private final String BASE_URL;
  @Resource
  private MissionService missionService;
  @Resource
  private MissionUtil missionUtil;
  @Resource
  private FileService fileService;
  @Resource
  private BatchTaskMqSender batchTaskMqSender;
  @Resource
  private SupplierPerformanceService supplierPerformanceService;
  @Resource
  private ShareSupplierPerformanceService shareSupplierPerformanceService;
  @Resource
  private SharePlatformService sharePlatformService;

  @Resource
  private GroupRepository groupRepository;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private MissionRepository missionRepository;
  @Resource
  private ShareLandingMerchantContractService shareLandingMerchantContractService;

  @Resource
  private DingUtils dingUtils;

  @Resource
  private UserRepository userRepository;
  @Resource
  private AssessRepository assessRepository;
  @Resource
  ManageSecurityUtil manageSecurityUtil;
  @Resource
  private PlatformDao platformDao;
  @Resource
  EntryRegistrationRepository entryRegistrationRepository;
  @Resource
  private Contract contract;
  @Resource
  SupplierChangeRecordService supplierChangeRecordService;
  @Resource
  private ApplicationContext applicationContext;
  @Resource
  ShareOrderService shareOrderService;

  private static final String DOWNLOAD_THE_CONTRACT_TEMPLATE =
      "srm/model/电商供应商合作框架协议模版V3.2.docx";

  @Autowired
  private EntryRegistrationDiscountRepository entryRegistrationDiscountRepository;
  @Resource
  ShareEntryRegistrationService shareEntryRegistrationService;
  @Resource
  OMSService omsService;
  @Autowired
  private ImportExcelUtil importExcelUtil;
  @Resource
  DownloadThenUpUtil downloadThenUpUtil;
  @Resource
  LandingContractBundleService landingContractBundleService;
  @Resource
  OAUserService oaUserService;

  @Resource
  private SrmConfig srmConfig;

  public LandingMerchantContractServiceImpl(SrmConfig config) {
    this.BASE_URL = config.getUploadUrl();
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_CONTRACT_PAGE)
  public PageResult<LandingContractPageDTO> getContractPageRef(LandingMerchantContractQueryForm form) {
    // 获取当前用户
    User curUser = manageSecurityUtil.getSrmUserDetails().getUser();
    // 判断角色，如果非管理员(超级管理员-管理员-电商供应商管理员)则设置form中的createUserId进行过滤
    if (!curUser.getRoleList().contains(Constants.ROLE_ADMINISTRATOR) &&
        !curUser.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) &&
        !curUser.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_COMMERCE_ADMIN)) {
      form.setCreateUserId(curUser.getId());
    }
    Page<LandingMerchantContract> page =
        contractDao.getContractPageRef(form.toQueryMap(groupRepository));
    List<LandingMerchantContract> pageDataList = page.getContent();
    if (CollUtil.isEmpty(pageDataList)) {
      return new PageResult<>(new ArrayList<>(), page.getTotalElements(), page.getTotalPages(),
          form.getPageNo(), form.getPageSize());
    }

    // 批量获取createMan
    List<String> createMans =
        pageDataList.stream().map(LandingMerchantContract::getCreateMan).filter(StrUtil::isNotEmpty)
            .distinct().collect(Collectors.toList());
    Map<String, String> id2RealName = userDao.getUserByIds(createMans).stream()
        .collect(Collectors.toMap(User::getId, User::getRealName));

    // 批量获取supplier
    List<String> supplierIds =
        pageDataList.stream().map(LandingMerchantContract::getSecondSigningSupplierId)
            .filter(StrUtil::isNotEmpty).distinct().collect(Collectors.toList());
    Map<String, Supplier> id2Supplier = supplierDao.getSupplierByIds(supplierIds).stream()
        .collect(Collectors.toMap(Supplier::getId, Function.identity()));

    // 批量获取EntryRegistrationOrder 根据合同中的entryRegistrationOrderId
    List<String> entryRegistrationOrderIds =
        pageDataList.stream().map(LandingMerchantContract::getEntryRegistrationOrderId)
            .filter(StrUtil::isNotEmpty).distinct().collect(Collectors.toList());
    Map<String, EntryRegistrationOrder> id2EntryRegistrationOrder =
        entryRegistrationService.getByIds(entryRegistrationOrderIds).stream()
            .collect(Collectors.toMap(EntryRegistrationOrder::getId, Function.identity()));
    // 批量获取EntryRegistrationLandingMerchant 根据entryRegistrationOrderId

    Map<String, EntryRegistrationLandingMerchant> id2EntryRegistrationLandingMerchant =
        entryRegistrationLandingMerchantService
            .findFirstByEntryRegistrationOrderIds(entryRegistrationOrderIds).stream()
            .collect(Collectors.toMap(EntryRegistrationLandingMerchant::getEntryRegistrationOrderId,
                Function.identity()));

    // 批量获取SupplierPerformance 根据合同中的合同中的id
    List<String> contractIds =
        pageDataList.stream().map(LandingMerchantContract::getId).filter(StrUtil::isNotEmpty)
            .distinct().collect(Collectors.toList());
    List<SupplierPerformance> supplierPerformances =
        supplierPerformanceRepository.findByLandingContractIdInAndState(contractIds,
            Constants.STATE_OK);
    Map<String, List<SupplierPerformance>> id2SupplierPerformances = supplierPerformances.stream()
            .collect(Collectors.groupingBy(SupplierPerformance::getLandingContractId));
    // SupplierPerformance批量获取supplierId 和 platformCode 并设置为键值对
    List<SupplierId2PlatformCode> supplierId2PlatformCode = supplierPerformances.stream()
        .filter(sp -> StrUtil.isNotEmpty(sp.getSupplierId()) && StrUtil.isNotEmpty(
            sp.getPlatformCode()))
        .map(sp -> new SupplierId2PlatformCode(sp.getSupplierId(), sp.getPlatformCode())).collect(
            Collectors.toList());
    Map<String, BigDecimal> code2OrderAmount = orderDao.getContractOrderAmount(supplierId2PlatformCode);

    // 批量获取file
    Map<String, File> id2File = fileService.findFirstByRelationIdInAndRelationType(contractIds,
            Constants.FILE_TYPE_LANDING_CONTRACT).stream()
        .collect(Collectors.toMap(File::getRelationId, Function.identity()));

    List<LandingContractPageDTO> res = pageDataList.stream().map(item -> {
      LandingContractPageDTO landingContractPageDTO = MapStructFactory.INSTANCE.toLandingContractPageDTO(item);
      // 枚举转换
      landingContractPageDTO.setType(Constants.TYPE_OF_CONTRACT.get(item.getType()));
      landingContractPageDTO.setSigningType(
          Constants.CONTRACT_SIGNING_MODE.get(item.getSigningType()));
      landingContractPageDTO.setSignatureStatus(
          Constants.SIGNATURE_STATUS_TYPE.get(item.getSignatureStatus()));
      // 时间转换
      landingContractPageDTO.setCreateTime(String.valueOf(DateUtil.date(item.getCreateTime())));
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
      if (item.getEffectiveStart() != null && item.getEffectiveEnd() != null) {
        landingContractPageDTO.setCooperationValidity(
            sdf.format(new Date(item.getEffectiveStart())) + "~" + sdf.format(
                new Date(item.getEffectiveEnd())));
      }
      // 获取用户
      String createMan = Optional.ofNullable(id2RealName.get(item.getCreateMan()))
          .orElse(""); // 如果为null则设置为空字符串
      landingContractPageDTO.setCreateMan(createMan);
      // 获取供应商
      Optional.ofNullable(id2Supplier.get(item.getSecondSigningSupplierId()))
          .ifPresent(supplier -> {
            landingContractPageDTO.setSecondSigningSupplierId(supplier.getEnterpriseName());
          });
      // 设置平台code
      landingContractPageDTO.setPlatformCode("");
      landingContractPageDTO.setPlatformCodeList(new ArrayList<>());
      // 落地商入驻单
      Optional.ofNullable(id2EntryRegistrationOrder.get(item.getEntryRegistrationOrderId()))
          .ifPresent(entryRegistrationOrder -> {
//            landingContractPageDTO.setPlatformCode(entryRegistrationOrder.getPlatform());
//            landingContractPageDTO.setPlatformCodeList(
//                StrUtil.splitTrim(entryRegistrationOrder.getPlatform(), StrUtil.COMMA));
            landingContractPageDTO.setRegistrationNumber(
                entryRegistrationOrder.getRegistrationNumber());
            // 落地商
            Optional.ofNullable(
                    id2EntryRegistrationLandingMerchant.get(entryRegistrationOrder.getId()))
                .ifPresent(merchant -> {
                  landingContractPageDTO.setReportReviewState(EntryRegistrationStatusEnum.fromKey(
                      entryRegistrationOrder.getRegistrationStatus()).get().getDescription());

                  // 交由EntryRegistrationContractReviewHandler 处理
                  EntryRegistrationEntity entity =
                      new EntryRegistrationEntity(entryRegistrationRepository,
                          entryRegistrationOrder, merchant);
                  EntryRegistrationStatusEnum entryRegistrationStatus =
                      entity.getRealEntryRegistrationStatus();
                  if (entryRegistrationStatus != null) {
                    landingContractPageDTO.setReportReviewState(entryRegistrationStatus.getDescription());
                  }
                });
          });
      // 履约信息
      List<SupplierPerformance> findSupplierPerformances =
          id2SupplierPerformances.get(item.getId());
      if (CollUtil.isNotEmpty(findSupplierPerformances)) {
        // 如果仍未空设置平台code
        if (CollUtil.isEmpty(landingContractPageDTO.getPlatformCodeList())) {
          landingContractPageDTO.setPlatformCodeList(
              findSupplierPerformances.stream().map(SupplierPerformance::getPlatformCode)
                  .distinct()
                  .collect(Collectors.toList()));
        }
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (SupplierPerformance findSupplierPerformance : findSupplierPerformances) {
          BigDecimal amount = code2OrderAmount.get(String.format("%s-%s", findSupplierPerformance.getSupplierId(), findSupplierPerformance.getPlatformCode()));
          totalAmount = NumberUtil.add(totalAmount, amount);
        }
        landingContractPageDTO.setAccruingAmounts(
            BigDecimalUtil.setScaleBigDecimalHalfUp(totalAmount, 2));
      }

      // 设置file
      if (StrUtil.isNotBlank(item.getFileReviewState())) {
        landingContractPageDTO.setFileReviewState(
            FileReviewStateEnum.fromKey(item.getFileReviewState()).get().getDescription());
        if (ObjectUtil.equals(item.getFileReviewState(),
            FileReviewStateEnum.THROUGH_THE.getKey())) {
          Optional.ofNullable(id2File.get(item.getId())).ifPresent(file -> {
            landingContractPageDTO.setFileDTO(new FileDTO(file));
          });
        }
      }
      // landingContractPageDTO的AccruingAmounts如果为null，设置默认值0
      if (ObjectUtil.isNull(landingContractPageDTO.getAccruingAmounts())) {
        landingContractPageDTO.setAccruingAmounts(
            BigDecimalUtil.setScaleBigDecimalHalfUp(BigDecimal.ZERO, 2));
      }
      // 新增invoiceType
      Optional.ofNullable(PurchaseOrderInvoiceType.fromKey(item.getInvoiceType())).ifPresent(invoiceTypeEnum -> {
        landingContractPageDTO.setInvoiceType(invoiceTypeEnum.getDescription());
      });
      // 新增payType
      Optional.ofNullable(PayTypeSAPEnums.fromKey(item.getPaymentType())).ifPresent(payTypeSAPEnums -> {
        landingContractPageDTO.setPayType(payTypeSAPEnums.getName());
      });
      return landingContractPageDTO;
    }).collect(Collectors.toList());

    return new PageResult<>(res, page.getTotalElements(), page.getTotalPages(),
        form.getPageNo(), form.getPageSize());
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_CONTRACT_PAGE)
  public LandingContractStatistics getContractStatistics(LandingMerchantContractQueryForm form) {
    // 获取当前用户
    User curUser = manageSecurityUtil.getSrmUserDetails().getUser();
    // 判断角色，如果非管理员(超级管理员-管理员-电商供应商管理员)则设置form中的createUserId进行过滤
    if (!curUser.getRoleList().contains(Constants.ROLE_ADMINISTRATOR) &&
        !curUser.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) &&
        !curUser.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_COMMERCE_ADMIN)) {
      form.setCreateUserId(curUser.getId());
    }
    List<LandingMerchantContract> landingMerchantContracts =
        contractDao.getContractStatistics2(form.toQueryMap(groupRepository));
    List<String> contractIds = landingMerchantContracts.stream().map(LandingMerchantContract::getId)
        .collect(Collectors.toList());
    List<SupplierPerformance> supplierPerformances =
        supplierPerformanceRepository.findByLandingContractIdInAndState(contractIds,
            Constants.STATE_OK);
    // SupplierPerformance批量获取supplierId 和 platformCode 并设置为键值对
    List<SupplierId2PlatformCode> supplierId2PlatformCode = supplierPerformances.stream()
        .filter(sp -> StrUtil.isNotEmpty(sp.getSupplierId()) && StrUtil.isNotEmpty(
            sp.getPlatformCode()))
        .map(sp -> new SupplierId2PlatformCode(sp.getSupplierId(), sp.getPlatformCode())).collect(
            Collectors.toList());
    // 去重supplierId2PlatformCode
    supplierId2PlatformCode = supplierId2PlatformCode.stream().distinct()
        .collect(Collectors.toList());
    Map<String, BigDecimal> code2OrderAmount = orderDao.getContractOrderAmount(supplierId2PlatformCode);
    BigDecimal totalAccruingAmounts = code2OrderAmount.values().stream().reduce(BigDecimal.ZERO,
        BigDecimal::add);
    LandingContractStatistics res = new LandingContractStatistics();
    res.setAccruingAmounts(totalAccruingAmounts);
    return res;
  }

  @Override
  @Transactional
  public String addOrUpdate(LandingContractAddParamDTO params,User user) {
    // 参数校验
    Map<String, String> map = checkAddParams(params);
    String result;
    String groupId = map.get("groupId");
    String supplierId = map.get("supplierId");
    String originSupplierId = supplierId;
    List<String> originPlatformCodeList = StrUtil.splitTrim(params.getPlatformCode(), StrUtil.COMMA);
    // 实体类填充参数
    LandingMerchantContract contract;
    // 查询是否已存在该合同
    LandingMerchantContract byContractNo = contractDao.getByContractNo(params.getContractNo());
    //下单平台改为多选
    List<String> platformCodeList = StrUtil.splitTrim(params.getPlatformCode(), StrUtil.COMMA);
    List<OrderPlatformDTO> platformDTOS =
        sharePlatformService.batchFindByCode(platformCodeList);
    for (OrderPlatformDTO platformDTO : platformDTOS) {
      // 下单平台非必填；但下单平台与对方签约主体(供应商)确定唯一一条合同、若重复则上一条失效
      List<LandingMerchantContract> landingMerchantContracts =
          contractDao.findBySecSupplierAndPlatformCode(supplierId, platformDTO.getPlatformCode());
      landingMerchantContracts.forEach(item -> {
        item.setContractStatus(ContractStatus.INVALID.getCode());
        item.setUpdateMan(user.getId());
        item.setUpdateTime(System.currentTimeMillis());
        landingMerchantContractRepository.save(item);
      });
    }
    if (ObjectUtils.isEmpty(params.getContractId())) {
      contract = new LandingMerchantContract();
      // 新增
      if (byContractNo != null) {
        throw new CheckException("合同编号不能重复");
      }
      contract.setCreateTime(System.currentTimeMillis());
      contract.setCreateMan(params.getUserId());
      contract.setAssociationStatus(Constants.CONTRACT_ASSOCIATION_PERFORMANCE_STATUS_NO);
      contract.setContractNo(params.getContractNo());
      contract.setFirstSigningGroupId(groupId);
      contract.setSecondSigningSupplierId(supplierId);
      LandingMerchantContract save = contractDao.save(contract);
      result = save.getId();
    } else {
      // 修改
      contract = contractDao.get(params.getContractId());
      if (ObjectUtils.isEmpty(contract)) {
        throw new CheckException("没有此合同信息，修改失败！");
      }
      if (!Objects.equals(contract.getContractNo(), params.getContractNo())) {
        if (byContractNo != null) {
          throw new CheckException("合同编号不能重复");
        }
      }
      originSupplierId = contract.getSecondSigningSupplierId();
      // 判断是否有报备单生成
      if (StrUtil.isNotEmpty(contract.getEntryRegistrationOrderId())) {
        EntryRegistrationEntity entryRegistrationEntity =
            entryRegistrationRepository.byId(contract.getEntryRegistrationOrderId());
        // 如果合同未归档，不可修改
        if (Boolean.FALSE.equals(entryRegistrationEntity.isContractArchiving())) {
          throw new CheckException("入驻报备合同未归档，不可修改！");
        }
      }
      result = contract.getId();
      ArrayList<String> types = new ArrayList<>();
      types.add(Constants.FILE_TYPE_LANDING_CONTRACT);
      types.add(Constants.FILE_TYPE_LANDING_CONTRACT_SUPPLEMENT);
      fileDao.deleteByRelationIdAndRelationTypeIn(contract.getId(), types);
      // 查询原有的平台
      originPlatformCodeList =
          supplierPerformanceRepository.findAllByLandingContractIdAndState(contract.getId(),
                  Constants.STATE_OK).stream().map(SupplierPerformance::getPlatformCode)
              .collect(Collectors.toList());
    }
    contract.setFileReviewState(FileReviewStateEnum.THROUGH_THE.getKey());
    contract.setPaymentType(params.getPaymentType());
    contract.setContractNo(params.getContractNo());
    contract.setFirstSigningGroupId(groupId);
    contract.setSecondSigningSupplierId(supplierId);
    contract.setEffectiveStart(params.getStartTime());
    contract.setEffectiveEnd(params.getEndTime());
    // 签订方式 - 此版本全为纸质合同
    contract.setSigningType(Constants.PAPER_CONTRACT);
    // 合同类型 - 此版本全部为框架合同
    contract.setType(params.getType());
    contract.setAccountingPeriod(params.getAccountingPeriod());
    contract.setBackToBack(params.getBackToBack());
    contract.setPaymentRatio(params.getPaymentRatioValue());
    contract.setPaymentCondition(StrUtil.join(",", params.getPaymentCondition()));
    // 合同来源默认为SRM新增。
    contract.setSourceType(Constants.CONTRACT_SOURCE_SRM);
    contract.setDeposit(params.getDeposit());
    contract.setDepositState(params.getDepositState());
    contract.setNeedBundle(params.getNeedBundle());
    //当前时间 >= 生效日期 并且 当前时间 < 失效日期
    String contractStatus = ContractStatus.judgeStatus(contract.getEffectiveStart(),
        contract.getEffectiveEnd(), contract.getFileReviewState()).getCode();
    contract.setContractStatus(contractStatus);
    if (ObjectUtils.isEmpty(params.getContractFile())) {
      contract.setSignatureStatus(Constants.SIGNATURE_STATUS_NO);
    } else {
      contract.setSignatureStatus(Constants.SIGNATURE_STATUS_YES);
    }
    contract.setState(Constants.STATE_OK);
    contract.setTypeOfCooperation(params.getTypeOfCooperation());
    // v6.5.1版本去除
    //      contract.setRegularSupply(params.getRegularSupply());
    contract.setCooperationBrand(params.getCooperationBrand());
    contract.setStorage(StrUtil.emptyToDefault(params.getStorage(), Constants.STATE_NO));
    contract.setStorageAddress(params.getStorageAddress());
    contract.setStorageArea(params.getStorageArea());
    contract.setGuaranteedAmount(params.getGuaranteedAmount());
    contract.setPenalty(params.getPenalty());
    contract.setInvoiceType(params.getInvoiceType());
    contract.setTaxRate(params.getTaxRate());
    contract.setPaymentTypeInput(params.getPaymentTypeInput());
    contract.setProjectCategory(params.getProjectCategory());
    contract.setProjectName(params.getProjectName());
    landingMerchantContractRepository.save(contract);
    // 存储绑品信息
    if (Boolean.TRUE.equals(contract.getNeedBundle())) {
      landingContractBundleService.patchUpdate(params.getBundleList(), contract.getId(), user.getId());
    } else {
      landingContractBundleService.patchUpdate(new ArrayList<>(), contract.getId(), user.getId());
    }
    //存储折扣信息
    List<EntryRegistrationDiscountDTO> entryRegistrationDiscountInfoDTO =
        params.getEntryRegistrationDiscountInfo();
    List<EntryRegistrationDiscount> originDiscountInfo =
        deleteEntryRegistrationDiscount(result);
    if (CollUtil.isNotEmpty(entryRegistrationDiscountInfoDTO)) {
      Long now = System.currentTimeMillis();
      entryRegistrationDiscountInfoDTO.forEach(entryRegistrationDiscountDTO -> {
        entryRegistrationDiscountRepository.save(
            entryRegistrationDiscountDTO.toEntity(now, null, user.getId(), result));
      });
    }
    // 原供应商信息
    Supplier originSupplier = supplierRepository.findById(originSupplierId)
        .orElseThrow(() -> new CheckException("供应商不存在"));
    // 现供应商信息
    Supplier nowSupplier = originSupplier;
    // 1.判断修改后的供应商是否一致
    if (!originSupplierId.equals(supplierId)) {
      nowSupplier = supplierRepository.findById(supplierId)
          .orElseThrow(() -> new CheckException("供应商不存在"));
      // 1.1供应商不一致，删除之前的履约信息
      originSupplier.removePlatform(StrUtil.join(StrUtil.COMMA, platformCodeList));
      // 1.2供应商不一致，清空OMS比例
      shareEntryRegistrationService.batchUpdateDiscountToOmsClear(originSupplier, platformCodeList);
      // 1.3保存变动记录
      batchSaveRateRecord(originDiscountInfo, new ArrayList<>(), platformDTOS, originSupplierId);
      // 1.4保存原供应商信息
      supplierRepository.save(originSupplier);
      // 1.5删除原供应商的履约信息
      shareSupplierPerformanceService.deleteRelatedContract(originSupplierId, platformCodeList);
    }
    // 2.筛选出需要删除的平台代码
    List<String> deletePlatformCodeList = originPlatformCodeList.stream()
        .filter(platformCode -> !platformCodeList.contains(platformCode))
        .collect(Collectors.toList());
    if (CollUtil.isNotEmpty(deletePlatformCodeList)) {
      // 2.1删除对应平台的折扣信息
      shareEntryRegistrationService.batchUpdateDiscountToOmsClear(originSupplier, deletePlatformCodeList);
      // 2.2保存变动记录
      batchSaveRateRecord(originDiscountInfo, new ArrayList<>(), platformDTOS, originSupplierId);
      // 2.3删除现在供应商
      nowSupplier.removePlatform(StrUtil.join(StrUtil.COMMA, deletePlatformCodeList));
      // 2.4删除供应商的履约信息
      shareSupplierPerformanceService.deleteRelatedContract(originSupplierId, deletePlatformCodeList);
    }
    // 3.新增合同时存下供应商对应的平台
    nowSupplier.addPlatform(StrUtil.join(StrUtil.COMMA, platformCodeList));
    supplierRepository.save(nowSupplier);
    // 3.1更新折扣信息到OMS
    shareEntryRegistrationService.batchUpdateDiscountToOms(params.getSecondSigningSupplierName(),
        supplierId,
        platformCodeList,
        entryRegistrationDiscountInfoDTO);
    // 3.2保存变动记录
    batchSaveRateRecord(originDiscountInfo, entryRegistrationDiscountInfoDTO, platformDTOS, supplierId);
    // 3.3保存付款发起条件，账期信息
    savePaymentConditionInfo(supplierId, params.getPlatformCode(), contract);
    platformCodeList.forEach(item -> savePaymentConditionInfo(supplierId, item, contract));
    // 保存文件
    if (!ObjectUtils.isEmpty(params.getContractFile())) {
      // 保存合同附件
      saveFile(params.getContractFile(), params.getUserId(), contract.getId(),
          Constants.FILE_TYPE_LANDING_CONTRACT);
    }
    if (!ObjectUtils.isEmpty(params.getSupplementFiles())) {
      // 保存补充协议
      List<FileDTO> supplementFiles = params.getSupplementFiles();
      supplementFiles.forEach(fileDTO -> {
        saveFile(fileDTO, params.getUserId(), contract.getId(),
            Constants.FILE_TYPE_LANDING_CONTRACT_SUPPLEMENT);
      });
    }
    platformDTOS.forEach(item -> shareSupplierPerformanceService.relatedContract(supplierId,
        contract.getId(), item.getPlatformCode(), user.getId()));
    return result;
  }

  /**
   * 批量保存折扣比例变动记录信息
   */
  private void batchSaveRateRecord(List<EntryRegistrationDiscount> originDiscountInfo,
      List<EntryRegistrationDiscountDTO> entryRegistrationDiscountInfoDTO,
      List<OrderPlatformDTO> platformDTOS, String supplierId) {
    for (OrderPlatformDTO platformDTO : platformDTOS) {
      BigDecimal totalPriceBySupplier = shareOrderService.getSupplierTotalPrice(supplierId, platformDTO.getPlatformCode());
      BigDecimal rate = BigDecimal.ZERO;
      List<SupplierRateDetailParam> oldSupplierRateDetailParams = new ArrayList<>();
      List<SupplierRateDetailParam> orderRateBrandDetailOldParams = new ArrayList<>();
      for (EntryRegistrationDiscount oldOne : originDiscountInfo) {
        if (EntryRegistrationDiscountTypeEnum.STEP_DISCOUNT_RATIO.getKey().equals(oldOne.getType())) {
          SupplierRateDetailParam oldDetail =
              new SupplierRateDetailParam(oldOne.getPerformanceAmount(),
                  oldOne.getDiscountRatio());
          oldSupplierRateDetailParams.add(oldDetail);
          if (oldOne.getPerformanceAmount() == null
              || oldOne.getPerformanceAmount().compareTo(BigDecimal.ZERO) == 0) {
            rate = oldOne.getDiscountRatio();
          }
        }
        if (EntryRegistrationDiscountTypeEnum.BRAND_DISCOUNT.getKey().equals(oldOne.getType())) {
          SupplierRateDetailParam oldBrand = new SupplierRateDetailParam();
          oldBrand.setBrandName(oldOne.getBrandName());
          oldBrand.setBrandId(oldOne.getBrandId());
          oldBrand.setRate(oldOne.getDiscountRatio());
          orderRateBrandDetailOldParams.add(oldBrand);
        }
      }
      SupplierOldRateRecord old = new SupplierOldRateRecord();
      old.setSupplierRateDetailOldParams(oldSupplierRateDetailParams);
      old.setOrderRateBrandDetailOldParams(orderRateBrandDetailOldParams);
      // 折扣比例
      old.setRate(rate.stripTrailingZeros().toPlainString());
      List<SupplierRateDetailParam> newSupplierRateDetailParams = new ArrayList<>();
      List<SupplierRateDetailParam> newRateBrandDetailOldParams = new ArrayList<>();
      BigDecimal newRate = null;
      for (EntryRegistrationDiscountDTO newOne : entryRegistrationDiscountInfoDTO) {
        if (EntryRegistrationDiscountTypeEnum.STEP_DISCOUNT_RATIO.getKey().equals(newOne.getType())) {
          SupplierRateDetailParam oldDetail =
              new SupplierRateDetailParam(newOne.getPerformanceAmount(),
                  newOne.getDiscountRatio());
          newSupplierRateDetailParams.add(oldDetail);
          if (newOne.getPerformanceAmount() == null
              || newOne.getPerformanceAmount().compareTo(BigDecimal.ZERO) == 0) {
            newRate = newOne.getDiscountRatio();
          }
        }
        if (EntryRegistrationDiscountTypeEnum.BRAND_DISCOUNT.getKey().equals(newOne.getType())) {
          SupplierRateDetailParam oldBrand = new SupplierRateDetailParam();
          oldBrand.setBrandName(newOne.getBrandName());
          oldBrand.setBrandId(newOne.getBrandId());
          oldBrand.setRate(newOne.getDiscountRatio());
          newSupplierRateDetailParams.add(oldBrand);
        }
      }
      SupplierNewRateRecord newOne = new SupplierNewRateRecord();
      newOne.setSupplierRateDetailParams(newSupplierRateDetailParams);
      newOne.setOrderRateBrandDetailParams(newRateBrandDetailOldParams);
      // 获取当前折扣比例
      if (newRate != null) {
        newOne.setNewRate(newRate.stripTrailingZeros().toPlainString());
      } else {
        String rateStr = omsService.getSupplierRate(supplierId,
            totalPriceBySupplier.toPlainString(),
            platformDTO.getPlatformCode());
        newOne.setNewRate(StrUtil.isNotBlank(rateStr) ? rateStr : StrUtil.DASHED);
      }
      // 判断是否要更新
      SupplierChangeCreateForm createForm =
          SupplierChangeCreateForm.builder()
              .updateField(SupplierChangeLogEnum.DISCOUNT_RATIO.getUpdateField())
              .platform(platformDTO.getPlatformName())
              .supplierId(supplierId)
              .createMan(manageSecurityUtil.getSrmUserDetails().getUser().getRealName())
              .oldData(old).newData(newOne)
              .type(SupplierChangeLogEnum.DISCOUNT_RATIO.getType()).build();
      // 1.rate是否一致
      if (newRate == null || rate.compareTo(newRate) != 0) {
        supplierChangeRecordService.saveRecordRef(createForm);
        return;
      }
      // 2.originDiscountInfo和entryRegistrationDiscountInfoDTO相比有变动
      if (!CollUtil.isEmpty(originDiscountInfo) && !CollUtil.isEmpty(entryRegistrationDiscountInfoDTO)) {
        if (originDiscountInfo.size() != entryRegistrationDiscountInfoDTO.size()) {
          supplierChangeRecordService.saveRecordRef(createForm);
        } else {
          // 一一对应不一致
          for (int i = 0; i < originDiscountInfo.size(); i++) {
            EntryRegistrationDiscount oldOneDiscount = originDiscountInfo.get(i);
            EntryRegistrationDiscountDTO newOneDiscount = entryRegistrationDiscountInfoDTO.get(i);
            // oldOne与newOne的getPerformanceAmount 可能为空
            boolean performanceEquals =
                NumberUtil.equals(oldOneDiscount.getPerformanceAmount(), newOneDiscount.getPerformanceAmount());
            if (oldOneDiscount.getDiscountRatio().compareTo(newOneDiscount.getDiscountRatio()) != 0 || !performanceEquals) {
              supplierChangeRecordService.saveRecordRef(createForm);
              break;
            }
          }
        }
      }
    }
  }

  private List<EntryRegistrationDiscount> deleteEntryRegistrationDiscount(String contractId) {
    List<EntryRegistrationDiscount> entryRegistrationDiscounts =
        entryRegistrationDiscountRepository.findByLandingContractIdAndState(
            contractId, Constants.STATE_OK);
    if (CollUtil.isEmpty(entryRegistrationDiscounts)) {
      return new ArrayList<>();
    }
    for (EntryRegistrationDiscount entryRegistrationDiscount : entryRegistrationDiscounts) {
      entryRegistrationDiscount.setState(Constants.STATE_DELETE);
      entryRegistrationDiscountRepository.save(entryRegistrationDiscount);
    }
    return entryRegistrationDiscounts;
  }

  /* 保存付款发起条件，账期信息 **/
  private void savePaymentConditionInfo(String supplierId, String platformCode,
      LandingMerchantContract contract) {
    // fixme 多平台改动问题
    if (StrUtil.isNotEmpty(supplierId)) {
      List<Order> orderList =
          orderRepository.getAllByTypeAndSupplierIdAndStateAndPaymentConditionIsNull(platformCode,
              supplierId, Constants.STATE_OK);
      orderList.forEach(item -> {
        item.setAccountingPeriod(contract.getAccountingPeriod());
        item.setBackToBack(contract.getBackToBack());
        item.setPaymentCondition(contract.getPaymentCondition());
        //1.货物验收->签收凭证通过时间
        //2.对方开票->供应商开票时间
        //3.客户回款->客户回款日期
        //付款条件满足时间:付款发起条件对应的时间中最晚的时间，如果有一个付款条件的时间为空，满足日期展示“-”
        Long latestPaymentTriggerTime =
            com.xhgj.srm.common.utils.DateUtil.getLatestPaymentTriggerTime(
                contract.getPaymentCondition(), item.getConfirmVoucherTime(),
                item.getConfirmAccountOpenInvoiceTime(), item.getCustomerReturnSignTime());
        item.setPaymentConditionTime(latestPaymentTriggerTime);
        //付款条件满足日期+账期-7天（PS：背靠背视为10天），如果计算后的时间早于今天，就自动取今天。如果以上三个字段有空，这里就是空的
        //预计付款时间
        if (latestPaymentTriggerTime != null) {
          Long predictPaymentTime =
              com.xhgj.srm.common.utils.DateUtil.getPredictPaymentTimeByCondition(
                  latestPaymentTriggerTime, contract.getBackToBack(),
                  contract.getAccountingPeriod());
          item.setPredictPaymentTime(predictPaymentTime);
        }
        orderRepository.save(item);
      });
    }
  }

  @Override
  public boolean isOrganization(String supplierName, String userGroup) {
    Supplier supplier = supplierDao.getSupplierByEnterName(supplierName);
    return isOrganization(supplier, userGroup);
  }

  @Override
  public LandingContractDetailsDTO getContractDetailsById(String contractId) {
    LandingMerchantContract contract = contractDao.get(contractId);
    if (ObjectUtils.isEmpty(contract)) {
      throw new CheckException("未查询到此合同信息");
    }
    // 采购人
    String purchaseMan = "-";
    // 获取合同关联的履约信息
    List<SupplierPerformance> supplierPerformances =
        supplierPerformanceRepository.findAllByLandingContractIdAndState(contractId,
            Constants.STATE_OK);
    if (CollUtil.isNotEmpty(supplierPerformances)) {
      List<String> dockingPurchaseErpCodes =
          supplierPerformances.stream().map(SupplierPerformance::getDockingPurchaseErpCode)
              .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
      purchaseMan = "";
      for (String dockingPurchaseErpCode : dockingPurchaseErpCodes) {
        User user = userService.getByCode(dockingPurchaseErpCode);
        if (user != null) {
          purchaseMan += user.getRealName() + ",";
        }
      }
      purchaseMan = StrUtil.removeSuffix(purchaseMan, ",");
    }
    // 我方签约主体
    Group group = groupDao.get(contract.getFirstSigningGroupId());
    LandingContractDetailsDTOBuilder builder = LandingContractDetailsDTO.builder();
    if (group != null) {
      builder.firstSigningGroupName(group.getName());
    }
    // 对方签约主体
    Supplier supplier = supplierDao.get(contract.getSecondSigningSupplierId());
    if (supplier != null) {
      builder.secondSigningSupplierName(supplier.getEnterpriseName());
    }
    // 合同附件
    FileDTO contractFile = null;
    if (ObjectUtil.equals(contract.getFileReviewState(),
        FileReviewStateEnum.THROUGH_THE.getKey())) {
      contractFile = getContractFile(contract.getId(), Constants.FILE_TYPE_LANDING_CONTRACT);
    }
    // 补充协议
    List<FileDTO> fileDTOS = getContractSupplementFile(contract.getId(),
        Constants.FILE_TYPE_LANDING_CONTRACT_SUPPLEMENT);
    LandingContractDetailsDTO result =
        buildEntity(contract, purchaseMan, builder, contractFile, fileDTOS,
            contract.getContractStatus());
    // 合同金额就是积累派单金额
    result.setContractAmount(result.getAccumulateDispatchPrice());
    // 创建人
    User user = userDao.get(contract.getCreateMan());
    if (user != null) {
      result.setCreateMan(user.getRealName());
    }
//    result.setPlatformCode(getPlatformCode(contract));
    result.setPlatformCodeList(getPlatformCodeList(contract));

    result.setDeposit(contract.getDeposit());
    // 合同默认可编辑
    result.setCanEdit(true);
    // 如果是入驻报备产生的合同
    if (StrUtil.isNotEmpty(contract.getEntryRegistrationOrderId())) {
      EntryRegistrationEntity entryRegistrationOrder =
          entryRegistrationRepository.byId(contract.getEntryRegistrationOrderId());
//      result.setTypeOfCooperation(entryRegistrationOrder.getTypeOfCooperation());
      result.setCooperationRRegion(entryRegistrationOrder.getCooperationRegion());
//      result.setCooperationBrand(entryRegistrationOrder.getCooperationBrand());
      // 如果是入驻报备产生的，设置是否可以编辑
      result.setCanEdit(entryRegistrationOrder.isContractArchiving());
    }
    result.setTypeOfCooperation(contract.getTypeOfCooperation());
    result.setCooperationBrand(contract.getCooperationBrand());
    result.setDepositState(contract.getDepositState());
    if (StringUtils.isEmpty(contract.getCreateMan())) {
      result.setCreateMan("system");
    }
    if (StrUtil.isNotBlank(contract.getFileReviewState())) {
      result.setFileReviewState(
          FileReviewStateEnum.fromKey(contract.getFileReviewState()).get().getDescription());
    }
    List<EntryRegistrationDiscountDetailDTO> entryRegistrationDiscountInfo = new ArrayList<>();
    List<EntryRegistrationDiscount> entryRegistrationDiscountList =
        entryRegistrationDiscountRepository.findByLandingContractIdAndState(
            contractId, Constants.STATE_OK);
    if (entryRegistrationDiscountList != null) {
      for (EntryRegistrationDiscount entryRegistrationDiscount : entryRegistrationDiscountList) {
        entryRegistrationDiscountInfo.add(EntryRegistrationDiscountDetailDTO.fromEntryRegistrationDiscount(entryRegistrationDiscount));
      }
    }
    result.setEntryRegistrationDiscountInfo(entryRegistrationDiscountInfo);
    List<LandingContractBundleDto> landingContractBundleDtoList =
        landingContractBundleService.getLandingContractBundle(Collections.singletonList(contractId),
            true).stream().map(item -> {
          LandingContractBundleDto dto = MapStructFactory.INSTANCE.toLandingContractBundleDto(item);
          return dto;
        }).collect(Collectors.toList());
    result.setBundleList(landingContractBundleDtoList);
    return result;
  }

  /**
   * 获取平台信息
   * @param contract
   * @deprecated 由于EntryRegistrationOrder修改为多平台，此方法废弃
   * @return
   */
  @Deprecated
  private String getPlatformCode(LandingMerchantContract contract) {
    if (StrUtil.isNotBlank(contract.getEntryRegistrationOrderId())) {
      EntryRegistrationOrder entryRegistrationOrder =
          entryRegistrationService.get(contract.getEntryRegistrationOrderId());
      return entryRegistrationOrder == null ? "" : entryRegistrationOrder.getPlatform();
    }
    SupplierPerformance supplierPerformance =
        supplierPerformanceService.findFirstByLandingContractId(contract.getId());
    return supplierPerformance == null ? "" : supplierPerformance.getPlatformCode();
  }

  /**
   * 获取平台信息
   * @param contract
   * @return
   */
  private List<String> getPlatformCodeList(LandingMerchantContract contract) {
//    if (StrUtil.isNotBlank(contract.getEntryRegistrationOrderId())) {
//      EntryRegistrationOrder entryRegistrationOrder =
//          entryRegistrationService.get(contract.getEntryRegistrationOrderId());
//      return entryRegistrationOrder == null ? Collections.emptyList() :
//          StrUtil.splitTrim(entryRegistrationOrder.getPlatform(), StrUtil.COMMA);
//    }

    List<String> supplierPerformanceList =
        supplierPerformanceRepository.findAllByLandingContractIdAndState(contract.getId(),
                Constants.STATE_OK).stream().map(SupplierPerformance::getPlatformCode)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
    return  CollUtil.emptyIfNull(supplierPerformanceList);
  }

  private LandingContractDetailsDTO buildEntity(LandingMerchantContract contract,
      String purchaseMan, LandingContractDetailsDTOBuilder builder, FileDTO contractFile,
      List<FileDTO> fileDTOS, String contractState) {
    LandingContractDetailsDTO result =
        builder.id(contract.getId()).contractNo(contract.getContractNo())
            .type(Constants.TYPE_OF_CONTRACT.get(contract.getType()))
            .cooperationType(Constants.TYPE_OF_COOPERATION).startTime(contract.getEffectiveStart())
            .endTime(contract.getEffectiveEnd())
            .state(ContractStatus.getDescByCode(contractState))
            .signingType(Constants.CONTRACT_SIGNING_MODE.get(contract.getSigningType()))
            .signatureStatus(Constants.SIGNATURE_STATUS_TYPE.get(contract.getSignatureStatus()))
            .sourceName(Constants.CONTRACT_SOURCE_TYPE.get(contract.getSourceType()))
            .purchaseMan(purchaseMan).createMan(contract.getCreateMan())
            .createTime(contract.getCreateTime().toString())
            .accountingPeriod(contract.getAccountingPeriod())
            .paymentRatioValue(contract.getPaymentRatio()).backToBack(contract.getBackToBack())
            .paymentType(contract.getPaymentType())
            .automaticPaymentTerms(contract.getPaymentCondition()).contractFile(contractFile)
            .supplementFiles(fileDTOS).accumulateDispatchPrice(
                BigDecimalUtil.setScaleBigDecimalHalfUp(getContractOrderAmount(contract), 2).toString())
            .accumulatePaymentPrice(
                BigDecimalUtil.setScaleBigDecimalHalfUp(getContractPaymentOrderAmount(contract), 2)
                    .toString()).typeOfCooperation(contract.getTypeOfCooperation()).cooperationBrand(contract.getCooperationBrand())
            // 6.5.1版本去除
//            .regularSupply(contract.getRegularSupply())
            .storage(contract.getStorage())
            .storageAddress(contract.getStorageAddress()).storageArea(contract.getStorageArea()).guaranteedAmount(contract.getGuaranteedAmount())
            .penalty(contract.getPenalty()).invoiceType(contract.getInvoiceType()).taxRate(contract.getTaxRate()).needBundle(contract.getNeedBundle())
            .paymentTypeInput(contract.getPaymentTypeInput()).projectCategory(contract.getProjectCategory()).projectName(contract.getProjectName()).build();
    return result;
  }

  private String getContractState(Long startTime, Long endTime) {
    String ok_state = "生效";
    String no_state = "失效";
    if (startTime == null || endTime == null) {
      return no_state;
    }
    long now = System.currentTimeMillis();
    if (startTime >= now) {
      return no_state;
    }
    if (endTime < now) {
      return no_state;
    }
    return ok_state;
  }

  @Override
  public LandingContractDTO getByContractNo(String contractNo) {
    if (ObjectUtils.isEmpty(contractNo)) {
      return null;
    }
    LandingMerchantContract contract = contractDao.getByContractNo(contractNo);
    LandingContractDTO contractDTO = new LandingContractDTO();
    contractDTO.setContractNo(contract.getContractNo());
    String contractStatus = "失效";
    if (contract.getEffectiveEnd() > System.currentTimeMillis()) {
      contractStatus = "生效";
    }
    contractDTO.setContractStatus(contractStatus);
    SupplierUser supplierUser = supplierUserDao.get(contract.getCreateMan());
    if (!ObjectUtils.isEmpty(supplierUser)) {
      contractDTO.setCreateMan(supplierUser.getRealName());
    }
    contractDTO.setSignatureStatus(
        Constants.SIGNATURE_STATUS_TYPE.get(contract.getSignatureStatus()));
    return contractDTO;
  }

  @Override
  public PageResult<LandingContractOrderDetailsDTO> getOrderByContractId(String contractId,
      Integer pageNo, Integer pageSize) {
    List<LandingContractOrderDetailsDTO> orderDetailsDTOS = new ArrayList<>();
    long totalCount = 0;
    int totalPages = 0;
    Page<Order> pageOrder;
    LandingMerchantContract contract = contractDao.get(contractId);
    if (ObjectUtils.isEmpty(contract)) {
      return new PageResult<>(orderDetailsDTOS, totalCount, totalPages, pageNo, pageSize);
    }
    if (Constants.CONTRACT_ASSOCIATION_PERFORMANCE_STATUS_NO.equals(
        contract.getAssociationStatus())) {
      // 该合同没有关联履约信息
      return new PageResult<>(orderDetailsDTOS, totalCount, totalPages, pageNo, pageSize);
    }
    List<SupplierPerformance> supplierPerformance = supplierPerformanceRepository.findAllByLandingContractIdAndState(contractId, Constants.STATE_OK);
    if (CollUtil.isEmpty(supplierPerformance)) {
      // 此合同没有关联履约信息
      return new PageResult<>(orderDetailsDTOS, totalCount, totalPages, pageNo, pageSize);
    }
    pageOrder = orderDao.getOrderBySupplierIdAndPlatformCodePage(supplierPerformance, pageNo, pageSize);
    if (ObjectUtils.isEmpty(pageOrder) || ObjectUtils.isEmpty(pageOrder.getContent())) {
      // 此合同没有关联的订单
      return new PageResult<>(orderDetailsDTOS, totalCount, totalPages, pageNo, pageSize);
    }
    List<Order> content = pageOrder.getContent();
    content.forEach(order -> {
      String typeName = platformService.findNameByCode(order.getType());
      LandingContractOrderDetailsDTO orderDto = new LandingContractOrderDetailsDTO(order, typeName);
      orderDto.setSignVoucherState(
          Constants_order.SIGN_VOUCHER_MAP.get(orderAcceptService.getAcceptState(order.getId())));
      Supplier supplier = supplierDao.get(order.getSupplier().getId());
      if (!ObjectUtils.isEmpty(supplier)) {
        orderDto.setSupplierName(supplier.getEnterpriseName());
      }
      orderDetailsDTOS.add(orderDto);
    });
    totalCount = pageOrder.getTotalElements();
    totalPages = pageOrder.getTotalPages();
    return new PageResult<>(orderDetailsDTOS, totalCount, totalPages, pageNo, pageSize);
  }

  @Override
  public PageResult<LandingContractPaymentDetailsDTO> getPaymentOrderByContractId(String contractId,
      Integer pageNo, Integer pageSize) {
    LandingMerchantContract contract = contractDao.get(contractId);
    List<LandingContractPaymentDetailsDTO> list = new ArrayList<>();
    long totalCount = 0;
    int totalPages = 0;
    if (ObjectUtils.isEmpty(contract)) {
      // 没有此合同信息
      return new PageResult<>(list, totalCount, totalPages, pageNo, pageSize);
    }
    Page<Object[]> paymentOrders =
        orderPaymentDao.getByContract(contract.getId(), pageNo, pageSize);
    if (ObjectUtils.isEmpty(paymentOrders) || ObjectUtils.isEmpty(paymentOrders.getContent())) {
      return new PageResult<>(list, totalCount, totalPages, pageNo, pageSize);
    }
    List<Object[]> content = paymentOrders.getContent();
    content.forEach(objects -> {
      LandingContractPaymentDetailsDTO dto = new LandingContractPaymentDetailsDTO();
      dto.setId(String.valueOf(ObjectUtil.defaultIfNull(objects[6], "")));
      dto.setPaymentNo(String.valueOf(ObjectUtil.defaultIfNull(objects[0], "")));
      if (!ObjectUtils.isEmpty(objects[1])) {
        String paymentStatus =
            Constants_order.ORDER_PAYMENT_STATUS_MAP.get(String.valueOf(objects[1]));
        dto.setPaymentStatus(paymentStatus);
      }
      if (ObjectUtils.isEmpty(objects[2])) {
        dto.setApplyPrice(new BigDecimal(BigInteger.ZERO));
      } else {
        BigDecimal applyPrice = BigDecimalUtil.setScaleBigDecimalHalfUp((BigDecimal) objects[2], 2);
        String paymentOrderId = "";
        OrderPayment orderPayment = orderPaymentRepository.findById(paymentOrderId).orElse(null);
        if (orderPayment != null && Objects.equals(orderPayment.getPaymentStatus(),
            Constants_order.COMPLETE_PAYMENT_TYPE)) {
          dto.setApplyPrice(applyPrice);
        } else {
          dto.setApplyPrice(orderPaymentService.getPaymentFinalPrice(paymentOrderId));
        }
      }
      if (ObjectUtils.isEmpty(objects[3])) {
        dto.setPaymentPrice(new BigDecimal(BigInteger.ZERO));
      } else {
        BigDecimal paymentPrice =
            BigDecimalUtil.setScaleBigDecimalHalfUp((BigDecimal) objects[3], 2);
        dto.setPaymentPrice(paymentPrice);
      }
      dto.setSubmitMan(String.valueOf(ObjectUtil.defaultIfNull(objects[4], "")));
      if (!ObjectUtils.isEmpty(objects[5])) {
        DateTime date = DateUtil.date(((BigInteger) objects[5]).longValue());
        dto.setCreateTimeStr(date.toString());
      }
      list.add(dto);
    });
    return new PageResult<>(list, paymentOrders.getTotalElements(), paymentOrders.getTotalPages(),
        pageNo, pageSize);
  }

  @Override
  public void updateAssociationStatus(String id, String associationStatus) {
    LandingMerchantContract contract = this.get(id);
    if (contract != null) {
      contract.setAssociationStatus(associationStatus);
      save(contract);
    }
  }

  // 处理合同关联的付款单
  private BigDecimal getContractPaymentOrderAmount(LandingMerchantContract contract) {
    List<String> orderIds = getContractOrderIds(contract);
    if (ObjectUtils.isEmpty(orderIds)) {
      return new BigDecimal(BigInteger.ZERO);
    } else {
      List<OrderPayment> orderPayments = new ArrayList<>();
      orderIds.forEach(id -> {
        List<OrderPaymentToOrder> toOrder =
            toOrderRepository.getByRelationIdAndStateAndType(id, Constants.STATE_OK,
                Constants_order.ORDER_TO_PAYMENT_TYPE_ORDER_ID);
        if (!ObjectUtils.isEmpty(toOrder)) {
          toOrder.forEach(orderPaymentToOrder -> {
            OrderPayment orderPayment =
                orderPaymentDao.get(orderPaymentToOrder.getOrderPaymentId());
            orderPayments.add(orderPayment);
          });
        }
      });
      return orderPayments.stream().filter(orderPayment -> orderPayment.getPaymentPrice() != null)
          .map(OrderPayment::getPaymentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    }
  }

  // 处理合同关联的订单
  private BigDecimal getContractOrderAmount(LandingMerchantContract contract) {
    List<SupplierPerformance> supplierPerformances =
        supplierPerformanceRepository.findAllByLandingContractIdAndState(contract.getId(),
            Constants.STATE_OK);
    BigDecimal totalAmount = BigDecimal.ZERO;
    if (CollUtil.isNotEmpty(supplierPerformances)) {
      BigDecimal totalAmountTemp =
          Convert.toBigDecimal(orderDao.getContractOrderAmountBatch(supplierPerformances));
      // 下单平台
      totalAmount = totalAmountTemp == null ? BigDecimal.ZERO : totalAmountTemp;
    }
    return totalAmount;
  }

  private List<String> getContractOrderIds(LandingMerchantContract contract) {
    List<SupplierPerformance> supplierPerformances =
        supplierPerformanceRepository.findAllByLandingContractIdAndState(contract.getId(),
            Constants.STATE_OK);
    if (CollUtil.isNotEmpty(supplierPerformances)) {
      return orderDao.getContractOrderIdsBatch(supplierPerformances);
    }
    return new ArrayList<>();
  }

  // 处理合同附件以及补充协议
  private List<FileDTO> getContractSupplementFile(String relationId, String type) {
    List<FileDTO> contractFiles = new ArrayList<>();
    List<File> files = fileDao.getFileListBySId(relationId, type);
    if (!ObjectUtils.isEmpty(files)) {
      files.forEach(file -> {
        FileDTO fileDTO = new FileDTO(file, BASE_URL);
        contractFiles.add(fileDTO);
      });
    }
    return contractFiles;
  }

  private FileDTO getContractFile(String relationId, String type) {
    File file = fileDao.getSingleFileBySupplierId(relationId, type);
    if (file == null) {
      return null;
    }
    return new FileDTO(file, BASE_URL);
  }

  private boolean isOrganization(Supplier supplier, String userGroup) {
    if (supplier == null) {
      return false;
    }
    // 查询该供应商所有对应的组织
    List<SupplierInGroup> supplierInGroups = supplierInGroupDao.getAllBySupplier(supplier.getId());
    if (ObjectUtils.isEmpty(supplierInGroups)) {
      return false;
    }
    // 组织去重
    Set<String> set =
        supplierInGroups.stream().map(SupplierInGroup::getGroupId).collect(Collectors.toSet());
    List<String> groupNames = new ArrayList<>();
    set.forEach(s -> {
      groupNames.add(groupDao.get(s).getName());
    });
    if (StrUtil.equals(Constants.GROUP_WANJU_CODE, userGroup)) {
      return groupNames.contains(TitleOfTheContractEnum.TITLE_OF_WAN_JU.getName());
    }
    if (StrUtil.equals(Constants.HEADQUARTERS_CODE, userGroup)) {
      return groupNames.contains(TitleOfTheContractEnum.TITLE_OF_HEADQUARTERS.getName());
    }
    return false;
  }

  /**
   * 校验新增合同参数
   *
   * @param params 新增合同参数
   * @return 组织id和供应商id
   */
  private Map<String, String> checkAddParams(LandingContractAddParamDTO params) {
    if (BooleanUtil.isTrue(params.getBackToBack())) {
      if (params.getAccountingPeriod() != null && Objects.equals(params.getAccountingPeriod(), 0)) {
        throw new CheckException("账期类型不能同时选中");
      }
    }
    if (params.getAccountingPeriod() != null) {
      if (!Objects.equals(params.getAccountingPeriod(), 0) && BooleanUtil.isTrue(
          params.getBackToBack())) {
        throw new CheckException("账期类型不能同时选中");
      }
    }
    // 附件校验
    FileDTO contractFile = params.getContractFile();
    if (contractFile == null) {
      throw new CheckException("合同附件不能为空");
    }
    List<FileDTO> supplementFiles = params.getSupplementFiles();
    if (!ObjectUtils.isEmpty(supplementFiles)) {
      supplementFiles.forEach(fileDTO -> {
        if (!ObjectUtils.isEmpty(fileDTO.getUrl())) {
          if (!FileUtil.isPermitType(fileDTO.getUrl(), Constants.FILE_TYPE_BCXY)) {
            throw new CheckException("补充协议文件类型不支持");
          }
        }
      });
    }
    // 必填信息
    if (ObjectUtils.isEmpty(params.getContractNo()) || ObjectUtils.isEmpty(params.getType())
        || ObjectUtils.isEmpty(params.getFormOfCooperation()) || ObjectUtils.isEmpty(
        params.getFirstSigningGroupName()) || ObjectUtils.isEmpty(
        params.getSecondSigningSupplierName()) || ObjectUtils.isEmpty(params.getStartTime())
        || ObjectUtils.isEmpty(params.getEndTime())) {
      throw new CheckException("缺少必要信息");
    }
    // 检验补充协议数量 - 最多五个
    if (!ObjectUtils.isEmpty(params.getSupplementFiles())) {
      if (params.getSupplementFiles().size() > 5) {
        throw new CheckException("补充协议文件超过五个");
      }
    }
    // 校验合同类型
    if (!Constants.FRAME_CONTRACT.equals(params.getType())) {
      throw new CheckException("合同类型必须为框架合同");
    }
    // 校验合同起止时间
    if (params.getEndTime() < params.getStartTime()) {
      throw new CheckException("合同有效截止时间不能小于生效时间");
    }
    // 校验我方签约主体
    Group group = groupDao.getCurGroupByName(params.getFirstSigningGroupName(), null);
    if (ObjectUtils.isEmpty(group) || !StrUtil.equalsAny(group.getName(),
        TitleOfTheContractEnum.TITLE_OF_HEADQUARTERS.getName(),
        TitleOfTheContractEnum.TITLE_OF_WAN_JU.getName())) {
      throw new CheckException("我方签约主体错误");
    }
    // 校验对方签约主体
    Supplier supplier = supplierDao.getSupplierByEnterName(params.getSecondSigningSupplierName());
    if (!isOrganization(params.getSecondSigningSupplierName(),group.getCode())) {
      throw new CheckException("供应商未在组织下");
    }
    // 校验常规供货
    // v6.5.1去除
    // 校验仓库地址
    if (StrUtil.equals(params.getStorage(),Constants.STATE_OK)) {
      if (params.getStorageArea() == null || StrUtil.isEmpty(params.getStorageAddress())) {
        throw new CheckException("有仓储时，仓库地址和仓库面积必填");
      }
    }

    Map<String, String> map = new HashMap<>();
    map.put("groupId", group.getId());
    map.put("supplierId", supplier.getId());
    return map;
  }

  @Override
  public BootBaseRepository<LandingMerchantContract, String> getRepository() {
    return repository;
  }

  // 保存上传的文件
  private void saveFile(FileDTO contractFile, String userId, String relationId,
      String relationType) {
    if (ObjectUtils.isEmpty(contractFile.getName()) || ObjectUtils.isEmpty(contractFile.getUrl())) {
      return;
    }
    String[] split = contractFile.getUrl().split("/");
    String id = contractFile.getId();
    File file;
    if (StrUtil.isNotBlank(id)) {
      file = Optional.ofNullable(fileDao.get(id)).orElse(new File());
    } else {
      file = new File();
    }
    file.setUrl(contractFile.getUrl());
    file.setName(split[split.length - 1]);
    file.setDescription(contractFile.getName());
    file.setState(Constants.STATE_OK);
    file.setRelationType(relationType);
    file.setRelationId(relationId);
    file.setCreateTime(System.currentTimeMillis());
    file.setUploadMan(userId);
    fileDao.save(file);
  }

  @Override
  public String findContractNoById(String id) {
    Assert.notBlank(id);
    LandingMerchantContract landingMerchantContract = get(id);
    if (landingMerchantContract == null) {
      return "";
    }
    return landingMerchantContract.getContractNo();
  }

  @Override
  public String findContractValidityPeriodById(String id, String format, String linker) {
    Assert.notBlank(id);
    LandingMerchantContract landingMerchantContract = get(id);
    if (landingMerchantContract == null) {
      return "";
    }
    Long startTime = landingMerchantContract.getEffectiveStart();
    Long endTime = landingMerchantContract.getEffectiveEnd();
    if (startTime == null || endTime == null) {
      return "";
    }
    String startTimeFormat = DateUtil.format(new Date(startTime), format);
    String endTimeFormat = DateUtil.format(new Date(endTime), format);
    return startTimeFormat + linker + endTimeFormat;
  }

  @Override
  @SneakyThrows
  public void importContract(MultipartFile file, String userId) {
    if (file == null) {
      throw new CheckException("请上传文件");
    }
    User user = userService.get(userId);
    if (user == null) {
      throw new CheckException("参数不合法");
    }
    String fileName = file.getOriginalFilename();
    if (StrUtil.isBlank(fileName)) {
      throw new CheckException("文件异常,请查看文件");
    }
    // 新增任务
    String newFilePath = importExcelUtil.saveExcel(file);
    Mission mission = Mission.createStartingMission(
        // 设置任务编号
        missionUtil.getMissionCode(user.getCode()),
        "导入-合同信息",
        userId,
        Constants.PLATFORM_TYPE_AFTER,
        file.getOriginalFilename(),
        newFilePath
    );
    missionService.save(mission);
    JSONObject params = new JSONObject();
    params.put("userId", userId);
    batchTaskMqSender.toHandleBatchTask(mission.getId(), params.toString(),
        Constants_Batch.BATCH_TASK_CONTRACT_IMPORT);
  }

  @Override
  @SneakyThrows
  public void batchImportFile(MultipartFile file, String platformCode, String userId) {
    if (file == null) {
      throw new CheckException("参数不合法!");
    }
    String fileExt =
        "." + cn.hutool.core.io.FileUtil.extName(file.getOriginalFilename()); // 获取文件后缀名
    if (!".zip".equals(fileExt)) {
      throw new CheckException("仅支持zip格式的压缩文件");
    }
    User user = userService.get(userId);
    if (user == null) {
      throw new CheckException("参数不合法");
    }
    // 重命名文件 并保存
    String now = String.valueOf(System.currentTimeMillis());
    // 文件本地保存路径
    String newFilePath = importExcelUtil.saveExcel(file);
    // 设置任务编号
    String fileName = "导入落地商合同附件" + now + ".zip";
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(user.getCode()),
        "导入-导入落地商合同附件",
        userId,
        Constants.PLATFORM_TYPE_AFTER,
        fileName,
        newFilePath
    );
    missionService.save(mission);
    JSONObject params = new JSONObject();
    params.put("platformCode", platformCode);
    params.put("userId", userId);
    batchTaskMqSender.toHandleBatchTask(mission.getId(), params.toString(),
        Constants_Batch.BATCH_TASK_IMPORT_CONTRACT_FILE);
  }

  @Override
  @Transactional
  public void importFile(ImportFileParams params) {
    File file = fileService.get(params.getFileId(),
        () -> CheckException.noFindException(File.class, params.getFileId()));
    file.setRelationId(params.getContractId());
    file.setRelationType(Constants.FILE_TYPE_LANDING_CONTRACT);
    LandingMerchantContract landingMerchantContract = get(params.getContractId(),
        () -> CheckException.noFindException(LandingMerchantContract.class,
            params.getContractId()));
    // 判断是否由入驻报备产生的合同
    if (StrUtil.isNotBlank(landingMerchantContract.getEntryRegistrationOrderId())) {
      EntryRegistrationEntity entryRegistrationEntity =
          entryRegistrationRepository.byId(landingMerchantContract.getEntryRegistrationOrderId());
      if (Boolean.FALSE.equals(entryRegistrationEntity.isRegistrationApprovedReal())) {
        throw new CheckException(EntryRegistrationEntity.ENTRY_REGISTRATION_IS_NOT_APPROVED_REAL);
      }
    }
    landingMerchantContract.setSignatureStatus(Constants.SIGNATURE_STATUS_YES);
    save(landingMerchantContract);
    fileService.save(file);
  }

  @Override
  public Optional<LandingMerchantContract> findFirstByContractNoAndState(String contractNo,
      String state) {
    Assert.notBlank(contractNo);
    Assert.notBlank(state);
    return repository.findFirstByContractNoAndState(contractNo, state);
  }

  /**
   * 导出落地商合同处理方案
   * @param form
   */
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_CONTRACT_PAGE)
  public void handlerExportLandingMerchantContractForm(LandingMerchantContractQueryForm form) {
    // 获取当前用户
    User curUser = manageSecurityUtil.getSrmUserDetails().getUser();
    // 判断角色，如果非管理员(超级管理员-管理员-电商供应商管理员)则设置form中的createUserId进行过滤
    if (!curUser.getRoleList().contains(Constants.ROLE_ADMINISTRATOR) &&
        !curUser.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) &&
        !curUser.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_COMMERCE_ADMIN)) {
      form.setCreateUserId(curUser.getId());
    }
  }

  /**
   * 导出落地商合同
   */
  @Override
  public void exportLandingMerchantContract(LandingMerchantContractExportQuery param) {
    if (param == null) {
      throw new CheckException("参数有误！");
    }
    LandingMerchantContractServiceImpl proxy = applicationContext.getBean(LandingMerchantContractServiceImpl.class);
    proxy.handlerExportLandingMerchantContractForm(param.getSearchParam());
    List<String> ids = param.getIds();
    List<LandingMerchantContract> landingMerchantContracts = new ArrayList<>();
    if (CollectionUtils.isEmpty(ids)) {
      // 如果为空 根据searchParam查询
      // 判断searchParam是否存在
      if (param.getSearchParam() == null) {
        throw new CheckException("参数有误！searchParam为空！");
      }
      // 重置页码和每页数量
      param.getSearchParam().setUserId(param.getUserId());
      param.getSearchParam().setPageNo(1);
      param.getSearchParam().setPageSize(Integer.MAX_VALUE);
      landingMerchantContracts =
          this.getContractPageRef(param.getSearchParam()).getContent().stream().map(
              MapStructFactory.INSTANCE::toLandingMerchantContract).collect(Collectors.toList());
      ids = landingMerchantContracts.stream().map(LandingMerchantContract::getId)
          .collect(Collectors.toList());
    } else {
      landingMerchantContracts = contractDao.getFindAllByIds(ids);
    }
    if (CollUtil.isEmpty(ids)) {
      throw new CheckException("未查询到合同信息！");
    }
    // 批量查询出入驻报备单
    List<String> entryRegistrationOrderIds =
        landingMerchantContracts.stream().map(LandingMerchantContract::getEntryRegistrationOrderId)
            .distinct().collect(Collectors.toList());
    Map<String, EntryRegistrationOrder> id2EntryRegistrationOrder =
        entryRegistrationService.getByIds(entryRegistrationOrderIds).stream()
            .collect(Collectors.toMap(EntryRegistrationOrder::getId, Function.identity()));
    landingMerchantContracts.forEach(contract -> {
      Optional.ofNullable(id2EntryRegistrationOrder.get(contract.getEntryRegistrationOrderId()))
          .ifPresent(entryRegistrationOrder -> {
            if (EntryRegistrationStatusEnum.UNDER_REVIEW.getKey().equals(entryRegistrationOrder.getRegistrationStatus())){
              String format = "{" + contract.getContractNo() + "}对应的入驻报备单{"
                  + entryRegistrationOrder.getRegistrationNumber() + "}处于审核中，不可导出！";
              throw new CheckException(format);
            }
          });
    });
    String userId = param.getUserId();
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    if (user == null) {
      throw new CheckException("用户为空");
    }

    // 设置任务编号
    // 新增任务
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(user.getCode()),
        "导出-合同导出",
        userId,
        Constants.PLATFORM_TYPE_AFTER);
    missionRepository.save(mission);
    batchTaskMqSender.toHandleBatchTask(mission.getId(), JSONObject.toJSONString(ids),
        Constants_Batch.THE_GROUND_QUOTIENT_IS_DERIVED);
  }

  /**
   * 根据入住报备id获取合同
   */
  @Override
  public LandingMerchantContract getEntryRegistrationOrderId(String id) {
    return contractDao.getEntryRegistrationOrderId(id);
  }

  /**
   * 维护保证金状态
   */
  @Override
  public void maintenanceDeposit(String id, Boolean state) {
    LandingMerchantContract landingMerchantContract = Optional.ofNullable(get(id)).get();
    landingMerchantContract.setDepositState(state);
    save(landingMerchantContract);
  }

  private String deleAllZeros(BigDecimal number) {
    if (number == null || number.compareTo(BigDecimal.ZERO) == 0) {
      return "";
    } else {
      String numberStr = String.valueOf(number);
      if (StrUtil.isNotBlank(numberStr)) {
        String decimalPart =
            numberStr.split("\\.").length > 1 ? numberStr.split("\\.")[1] : ""; // 小数部分
        if (decimalPart.chars().allMatch(ch -> ch == '0')) {
          return numberStr.split("\\.")[0]; // 整数部分
        }
        return numberStr;
      }
      return "";
    }
  }

  private void handleAmountAndDiscount(List<EntryRegistrationDiscount> entryRegistrationDiscountList,LandingMerchantContractPut2Pdf put2Pdf) {
    //有多少条数据，就有多少条履约金额和折扣比例（按照履约金额正序排列），最大5条，超过5条只取前5条
    if (CollUtil.isEmpty(entryRegistrationDiscountList)) {
      return;
    }
    // 筛选类型为 STEP_DISCOUNT_RATIO 的折扣记录，并按 performanceAmount 升序排序（null 排在最前）
    List<EntryRegistrationDiscount> allRegistrationDiscountList =
        entryRegistrationDiscountList.stream()
            .filter(entryDiscount -> StrUtil.equals(
                EntryRegistrationDiscountTypeEnum.STEP_DISCOUNT_RATIO.getKey(),
                entryDiscount.getType()))
            .sorted(Comparator.comparing(EntryRegistrationDiscount::getPerformanceAmount,
                Comparator.nullsFirst(BigDecimal::compareTo)))
            .skip(1)  // 跳过第一条记录
            .limit(5)  // 只取最多5条记录（即原列表中的第2,3、4、5,6条）
            .collect(Collectors.toList());
    if (CollUtil.isEmpty(allRegistrationDiscountList)) {
      return;
    }
    // 使用 Map 来存储 performanceAmount 和 discountRatio，键为索引（0, 1, 2,3,4）
    Map<Integer, EntryRegistrationDiscount> discountMap = IntStream.range(0, Math.min(5,
            allRegistrationDiscountList.size()))
        .boxed()
        .collect(Collectors.toMap(i -> i, allRegistrationDiscountList::get));

    // 提取每一条记录的履约金额
    BigDecimal performanceAmount1 = Optional.ofNullable(discountMap.get(0)).map(EntryRegistrationDiscount::getPerformanceAmount).orElse(BigDecimal.ZERO);
    BigDecimal performanceAmount2 = Optional.ofNullable(discountMap.get(1)).map(EntryRegistrationDiscount::getPerformanceAmount).orElse(BigDecimal.ZERO);
    BigDecimal performanceAmount3 = Optional.ofNullable(discountMap.get(2)).map(EntryRegistrationDiscount::getPerformanceAmount).orElse(BigDecimal.ZERO);
    BigDecimal performanceAmount4 = Optional.ofNullable(discountMap.get(3)).map(EntryRegistrationDiscount::getPerformanceAmount).orElse(BigDecimal.ZERO);
    BigDecimal performanceAmount5 = Optional.ofNullable(discountMap.get(4)).map(EntryRegistrationDiscount::getPerformanceAmount).orElse(BigDecimal.ZERO);

    // 根据实际存在的记录数赋值
    Optional.ofNullable(discountMap.get(0)).ifPresent(d -> {
      // 计算金额区间 ≥ ? 万
      put2Pdf.setPerformanceAmount1(NumberUtil.toStr(NumberUtil.div(performanceAmount1,YUAN_TO_WAN,2)));
      put2Pdf.setDiscountRatio1(NumberUtil.sub(new BigDecimal(100),
              NumberUtil.toBigDecimal(d.getDiscountRatio()))
          .setScale(2, RoundingMode.HALF_UP).toString());
    });
    Optional.ofNullable(discountMap.get(1)).ifPresent(d -> {
      // 计算金额区间 > ?万、? ≤万
      put2Pdf.setAmount1(NumberUtil.toStr(NumberUtil.div(performanceAmount1, YUAN_TO_WAN, 2)));
      put2Pdf.setPerformanceAmount2(NumberUtil.toStr(NumberUtil.div(performanceAmount2, YUAN_TO_WAN, 2)));
      put2Pdf.setDiscountRatio2(NumberUtil.sub(new BigDecimal(100),
              NumberUtil.toBigDecimal(d.getDiscountRatio()))
          .setScale(2, RoundingMode.HALF_UP).toString());
    });
    Optional.ofNullable(discountMap.get(2)).ifPresent(d -> {
      // 计算金额区间 > ?万、? ≤万
      put2Pdf.setAmount2(NumberUtil.toStr(NumberUtil.div(performanceAmount2, YUAN_TO_WAN, 2)));
      put2Pdf.setPerformanceAmount3(NumberUtil.toStr(NumberUtil.div(performanceAmount3, YUAN_TO_WAN, 2)));
      put2Pdf.setDiscountRatio3(NumberUtil.sub(new BigDecimal(100),
              NumberUtil.toBigDecimal(d.getDiscountRatio()))
          .setScale(2, RoundingMode.HALF_UP).toString());
    });
    Optional.ofNullable(discountMap.get(3)).ifPresent(d -> {
      // 计算金额区间 > ?万、? ≤万
      put2Pdf.setAmount3(NumberUtil.toStr(NumberUtil.div(performanceAmount3, YUAN_TO_WAN, 2)));
      put2Pdf.setPerformanceAmount4(NumberUtil.toStr(NumberUtil.div(performanceAmount4, YUAN_TO_WAN, 2)));
      put2Pdf.setDiscountRatio4(NumberUtil.sub(new BigDecimal(100),
              NumberUtil.toBigDecimal(d.getDiscountRatio()))
          .setScale(2, RoundingMode.HALF_UP).toString());
    });
    Optional.ofNullable(discountMap.get(4)).ifPresent(d -> {
      // 计算金额区间 > ? 万
      put2Pdf.setPerformanceAmount5(NumberUtil.toStr(NumberUtil.div(performanceAmount5, YUAN_TO_WAN, 2)));
      put2Pdf.setDiscountRatio5(NumberUtil.sub(new BigDecimal(100),
              NumberUtil.toBigDecimal(d.getDiscountRatio()))
          .setScale(2, RoundingMode.HALF_UP).toString());
    });


  }

  /**
   * 根据公司ID获取全部合同
   */
  @Override
  public List<LandingMerchantContract> getAllContractsBasedOnCompanyID(String id) {
    return contractDao.getAllContractsBasedOnCompanyID(id);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean identification(String fileUrl, String contractId) {
      LandingMerchantContract landingMerchantContract =
              landingMerchantContractRepository.findById(contractId).orElseThrow(
                      () -> CheckException.noFindException(LandingMerchantContract.class, contractId));
      EntryRegistrationEntity entryRegistrationOrder =
              entryRegistrationRepository.byId(landingMerchantContract.getEntryRegistrationOrderId(), manageSecurityUtil.getOperatingUser());
      if (Boolean.FALSE.equals(entryRegistrationOrder.isRegistrationApprovedReal())) {
          throw new CheckException(EntryRegistrationEntity.ENTRY_REGISTRATION_IS_NOT_APPROVED_REAL);
      }
      Map<String, String> handleFileDiscern = new HashMap<>();
      try {
          handleFileDiscern =
                  shareLandingMerchantContractService.handleFileDiscern(fileUrl);
      } catch (Exception e) {
        //识别失败的话吞掉异常继续执行转人工审核处理
        log.error("合同识别失败", e);
        saveFailureInfo(entryRegistrationOrder,
            "合同识别失败，可能是网络异常、提供商服务不可用、合同模板不正确");
      }
      boolean compared = false;
      try {
          compared = compareContractInformation(entryRegistrationOrder, handleFileDiscern,
                  landingMerchantContract);
      } catch (Exception e) {
          log.error("合同对比校验异常", e);
      }
      if (compared) {
        try {
          entryRegistrationService.contractArchivingHandleRef(entryRegistrationOrder.getId());
          return true;
        } catch (Exception e) {
          log.error("报备单完成合同上传成功业务失败", e);
        }
      }
    try {
      doVerificationFailedHandle(entryRegistrationOrder, landingMerchantContract);
    } catch (Exception e) {
      log.error("报备单合同对比自动审核业务异常", e);
    }
      return false;
  }

  private void saveFailureInfo(EntryRegistrationEntity entryRegistrationOrder, String info) {
    entryRegistrationOrder.setFileOcrInfo(info);
    entryRegistrationOrder.save();
  }

  @Transactional(rollbackFor = Exception.class)
  public void doVerificationFailedHandle(EntryRegistrationEntity entryRegistrationOrder,
      LandingMerchantContract landingMerchantContract) {
    createContractAudit(entryRegistrationOrder.getId());
    landingMerchantContract.setFileReviewState(FileReviewStateEnum.VERIFICATION.getKey());
    landingMerchantContractRepository.save(landingMerchantContract);
    //钉钉通知 合同附件审批
    List<String> mobiles = new ArrayList<>();
    for (String userCode : srmConfig.getContractFileUploadExamineUserCode()) {
     userRepository.findFirstByCodeAndState(userCode, Constants.STATE_OK)
          .ifPresent(user -> {
            if (StrUtil.isNotBlank(user.getMobile())) {
              mobiles.add(user.getMobile());
            }
          });
    }
    String enterpriseName = "";
    Supplier supplier = supplierDao.get(landingMerchantContract.getSecondSigningSupplierId());
    if (supplier != null) {
      enterpriseName = supplier.getEnterpriseName();
    }
    doSendApprovalOfContractAttachmentsDingDingInform(landingMerchantContract.getContractNo(),
        enterpriseName, entryRegistrationOrder.getProjectCategory(), mobiles);
  }

  private boolean compareContractInformation(EntryRegistrationEntity entryRegistrationOrder,
      Map<String, String> contractInformation,
      LandingMerchantContract landingMerchantContract) {
    StringBuilder ocrInfo = new StringBuilder("识别错误信息：");
    if (CollUtil.isEmpty(contractInformation)) {
      throw new CheckException("合同识别失败，未识别到任何内容。");
    }
    for (Entry<String, String> contractInformationEntry : contractInformation.entrySet()) {
        String code = contractInformationEntry.getKey();
      if (Objects.equals(code, "startTime") || Objects.equals(code, "endTime")) {
        continue;
      }
        String value = StrUtil.emptyIfNull(contractInformationEntry.getValue());
        if (StrUtil.isNotBlank(code) && StrUtil.isNotBlank(value)) {
          LandingMerchantContractKeywordEnum keywordEnum =
                    LandingMerchantContractKeywordEnum.throwIfNotFindByCode(code);
          boolean compared = contract.compareContractInformation(entryRegistrationOrder, keywordEnum,
              value, landingMerchantContract);
          if (!compared) {
            ocrInfo.append(keywordEnum.getName()).append(";");
            entryRegistrationOrder.setFileOcrInfo(ocrInfo.toString());
            entryRegistrationOrder.save();
            return false;
          }
        }
    }
    return true;
  }

  private void createContractAudit(String entryRegistrationOrderId) {
    List<Assess> assesses = assessRepository.findAllByTargetId(entryRegistrationOrderId);
    if (CollUtil.isNotEmpty(assesses)) {
      assessRepository.deleteAll(assesses);
    }
    for (String userCode : CollUtil.emptyIfNull(srmConfig.getContractFileUploadExamineUserCode())) {
      Optional<User> user =
          userRepository.findFirstByCodeAndState(userCode, Constants.STATE_OK);
      user.ifPresent(value -> assessService.createAssess(AssessTypeEnum.CONTRACT_FILE,
          entryRegistrationOrderId, value, value));
    }
  }

  private void doSendApprovalOfContractAttachmentsDingDingInform(String contractNo,
      String signingEntity, String projectName, List<String> mobileList) {
    String outTrackId = String.valueOf(System.currentTimeMillis());
    if (StrUtil.isNotBlank(contractNo) && StrUtil.isNotBlank(signingEntity) && StrUtil.isNotBlank(
        projectName)) {
      HashMap<String, Object> map = new HashMap<>();
      map.put("contractNo", contractNo);
      map.put("signingEntity", signingEntity);
      map.put("projectName", projectName);
      dingUtils.sendDingTaskRobotCar(
          Constants_DingCarTemplate.DING_CARD_ID_LANDING_BUSINESS_APPROVAL_OF_CONTRACT_ATTACHMENTS,
          dingUtils.getDingUserIdList(mobileList), outTrackId, map);
    }
  }

  /**
   * 根据合同id获取附件信息
   */
  @Override
  public FileDetails detailsOfTheAnnexToTheContract(String id) {
    LandingMerchantContract contract = contractDao.get(id);
    FileDetails fileDetails = new FileDetails();
    // 合同附件
    Optional<File> first = fileService.findFirstByRelationIdAndRelationType(contract.getId(),
        Constants.FILE_TYPE_LANDING_CONTRACT);
    if (first.isPresent()) {
      fileDetails.setFileDTO(new FileDTO(first.get()));
    } else {
      return null;
    }
    fileDetails.setFileReviewState(contract.getFileReviewState());
    //合同附件 驳回
    if (ObjectUtil.equals(contract.getFileReviewState(),
        FileReviewStateEnum.FAILED_TO_PASS.getKey())) {
      List<Assess> assessList =
          assessRepository.findAllByTargetId(contract.getEntryRegistrationOrderId());
      if (CollUtil.isNotEmpty(assessList)) {
        fileDetails.setReasonForRejection(
            assessList.stream().findFirst().map(Assess::getAssessResult).orElse(StrUtil.EMPTY));
      } else {
        EntryRegistrationOrder entryRegistrationOrder =
            entryRegistrationService.get(contract.getEntryRegistrationOrderId());
        if (entryRegistrationOrder != null) {
          fileDetails.setReasonForRejection(entryRegistrationOrder.getFileOcrInfo());
        }
      }
    }
    return fileDetails;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteContract(String id) {
    Optional.ofNullable(fileService.get(id)).ifPresent(file -> {
      String relationId = file.getRelationId();
      if (StrUtil.isNotBlank(relationId)) {
        Optional.ofNullable(contractDao.get(relationId)).ifPresent(contract -> {
          EntryRegistrationOrder entryRegistrationOrder =
              entryRegistrationService.get(contract.getEntryRegistrationOrderId());
          contract.setFileReviewState(StrUtil.EMPTY);
          contractDao.save(contract);
          if (entryRegistrationOrder != null) {
            CollUtil.emptyIfNull(assessRepository.findAllByTargetId(entryRegistrationOrder.getId()))
                .forEach(assess -> {
                  assessService.delete(assess.getId());
                });
            entryRegistrationOrder.setFileOcrInfo(StrUtil.EMPTY);
            entryRegistrationService.save(entryRegistrationOrder);
          }
        });
      }
      fileService.delete(id);
    });
  }

  @Override
  public void contractExpirationNoticeRef(long time) {
    Date curr = DateTime.of(time);
    List<LandingMerchantContract> landingMerchantContractList = CollUtil.emptyIfNull(
        contractDao.getAllByEffectiveEnd(
            DateUtil.offsetDay(DateUtil.beginOfDay(curr), Constants.IN_CONTRACT_EXPIRATION_EXPIRE)
                .getTime(),
            DateUtil.offsetDay(DateUtil.endOfDay(curr), Constants.IN_CONTRACT_EXPIRATION_EXPIRE)
                .getTime()));
    for (LandingMerchantContract contract : landingMerchantContractList) {
      List<SupplierPerformance> supplierPerformanceList =
          supplierPerformanceRepository.findAllByLandingContractIdAndState(contract.getId(), Constants.STATE_OK);
      Set<String> mobileList = new HashSet<>();
      Set<String> platformNames = new HashSet<>();
      Set<String> enterpriseNames = new HashSet<>();
      for (SupplierPerformance supplierPerformance : supplierPerformanceList) {
        supplierRepository.findById(supplierPerformance.getSupplierId())
            .ifPresent(supplier -> enterpriseNames.add(supplier.getEnterpriseName()));
        Platform platform = platformDao.findByCode(supplierPerformance.getPlatformCode());
        platformNames.add(platform.getName());
        String businessLeader = supplierPerformance.getBusinessLeader();
        String mobile = oaUserService.getOaUserMobile(businessLeader);
        if (StrUtil.isNotBlank(mobile)) {
          // 添加业务负责人mobile
          mobileList.add(mobile);
        }else{
          // 没有则取平台中项目经理mobile
          userRepository.findById(Optional.ofNullable(platform.getProjectManager()).orElse("-1"))
              .ifPresent(user -> {
                if (StrUtil.isNotBlank(user.getMobile())) {
                  mobileList.add(user.getMobile());
                }
              });
        }
      }
      Constants.CONTRACT_EXPIRATION_REMINDERS.stream()
          .map(name -> userRepository.findFirstByRealNameAndState(name, Constants.STATE_OK))
          .filter(ObjectUtil::isNotNull).map(User::getMobile).filter(StrUtil::isNotBlank)
          .forEach(mobileList::add);
      //发送钉钉通知
      doSendDingDingInform(contract.getContractNo(),
          String.join(",", enterpriseNames),
          new ArrayList<>(mobileList),
          String.join(",",
              platformNames));
      CollUtil.emptyIfNull(CollUtil.emptyIfNull(contractDao.getAllByEffectiveEnd(
              DateUtil.offsetDay(DateUtil.beginOfDay(curr), -1).getTime(),
              DateUtil.offsetDay(DateUtil.endOfDay(curr), -1).getTime())))
          .forEach(landingMerchantContract -> {
            landingMerchantContract.setContractStatus(ContractStatus.INVALID.getCode());
            landingMerchantContractRepository.save(contract);
          });
    }
  }

  @Override
  public void whetherTheInvalidContractIsInForce() {
    Date curr = DateTime.of(System.currentTimeMillis());
    List<LandingMerchantContract> list = CollUtil.emptyIfNull(
        contractDao.getAllByEffectiveStart(DateUtil.beginOfDay(curr).getTime(),
            DateUtil.endOfDay(curr).getTime()));

    list.forEach(contract -> {
      ContractStatus contractStatus =
          ContractStatus.judgeStatus(contract.getEffectiveStart(), contract.getEffectiveEnd(),
              contract.getFileReviewState());
      contract.setContractStatus(contractStatus.getCode());
      save(contract);
    });
  }

  @Override
  public void handleOrdersByContractInfo() {
    try {
      List<String> orderIdList = orderDao.getAllOrderListByPaymentCondition();
      int batchSize = 500;
      int offset = 0;
      while (offset < orderIdList.size()) {
        // 获取当前批次的订单ID子列表
        List<String> currentBatchIds =
            orderIdList.subList(offset, Math.min(offset + batchSize, orderIdList.size()));
        List<Order> ordersBatch = orderRepository.findAllByIdIn(currentBatchIds);
        List<Order> updateOrders = new ArrayList<>();
        ordersBatch.forEach(order -> {
          SupplierPerformance supplierPerformance =
              supplierPerformanceRepository.getFirstByPlatformCodeAndSupplierIdAndStateOrderByUpdateTimeDesc(
                  order.getType(), order.getSupplierId(), Constants.STATE_OK);
          if (supplierPerformance != null && StrUtil.isNotEmpty(
              supplierPerformance.getLandingContractId())) {
            landingMerchantContractRepository.findById(supplierPerformance.getLandingContractId())
                .ifPresent(landingMerchantContract -> {
                  order.setPaymentCondition(landingMerchantContract.getPaymentCondition());
                  order.setBackToBack(landingMerchantContract.getBackToBack());
                  order.setAccountingPeriod(landingMerchantContract.getAccountingPeriod());
                  //1.货物验收->签收凭证通过时间
                  //2.对方开票->供应商开票时间
                  //3.客户回款->客户回款日期
                  //付款条件满足时间:付款发起条件对应的时间中最晚的时间，如果有一个付款条件的时间为空，满足日期展示“-”
                  Long latestPaymentTriggerTime =
                      com.xhgj.srm.common.utils.DateUtil.getLatestPaymentTriggerTime(
                          landingMerchantContract.getPaymentCondition(),
                          order.getConfirmVoucherTime(), order.getConfirmAccountOpenInvoiceTime(),
                          order.getCustomerReturnSignTime());
                  order.setPaymentConditionTime(latestPaymentTriggerTime);
                  //付款条件满足日期+账期-7天（PS：背靠背视为10天），如果计算后的时间早于今天，就自动取今天。如果以上三个字段有空，这里就是空的
                  //预计付款时间
                  if (latestPaymentTriggerTime != null) {
                    Long predictPaymentTime =
                        com.xhgj.srm.common.utils.DateUtil.getPredictPaymentTimeByCondition(
                            latestPaymentTriggerTime, order.getBackToBack(),
                            order.getAccountingPeriod());
                    order.setPredictPaymentTime(predictPaymentTime);
                  }
                  updateOrders.add(order);
                });
          }
        });
        //批量保存更新后的订单
        orderRepository.saveAll(updateOrders);
        //更新偏移量，准备处理下一组数据
        offset += batchSize;
      }
    } catch (Exception e) {
      log.error("处理履约订单对应合同信息有误！", e.getMessage());
    }
  }

  @Override
  public void batchHandleOrderStatus() {
    List<String> orderIdList = orderDao.getAllOrderListHandleOrderStatus();
    int batchSize = 1000;
    int offset = 0;
    while (offset < orderIdList.size()) {
      // 获取当前批次的订单ID子列表
      List<String> currentBatchIds =
          orderIdList.subList(offset, Math.min(offset + batchSize, orderIdList.size()));
      List<Order> ordersBatch = orderRepository.findAllByIdIn(currentBatchIds);
      List<Order> updateOrders = new ArrayList<>();
      ordersBatch.forEach(order -> {
        SupplierPerformance supplierPerformance =
            supplierPerformanceRepository.getFirstByPlatformCodeAndSupplierIdAndStateOrderByUpdateTimeDesc(
                order.getType(), order.getSupplierId(), Constants.STATE_OK);
        order.setPaymentStatus(Constants_order.CAN_NOT_PAYMENT_TYPE,true);
        if (supplierPerformance != null && StrUtil.isNotEmpty(
            supplierPerformance.getLandingContractId())) {
          landingMerchantContractRepository.findById(supplierPerformance.getLandingContractId())
              .ifPresent(landingMerchantContract -> {
                order.setPaymentCondition(landingMerchantContract.getPaymentCondition());
                order.setBackToBack(landingMerchantContract.getBackToBack());
                order.setAccountingPeriod(landingMerchantContract.getAccountingPeriod());
                //1.货物验收->签收凭证通过时间
                //2.对方开票->供应商开票时间
                //3.客户回款->客户回款日期
                //付款条件满足时间:付款发起条件对应的时间中最晚的时间，如果有一个付款条件的时间为空，满足日期展示“-”
                Long latestPaymentTriggerTime =
                    com.xhgj.srm.common.utils.DateUtil.getLatestPaymentTriggerTime(
                        landingMerchantContract.getPaymentCondition(),
                        order.getConfirmVoucherTime(), order.getConfirmAccountOpenInvoiceTime(),
                        order.getCustomerReturnSignTime());
                order.setPaymentConditionTime(latestPaymentTriggerTime);
                order.setPaymentStatus(Constants_order.CAN_NOT_PAYMENT_TYPE,true);
                //付款条件满足日期+账期-7天（PS：背靠背视为10天），如果计算后的时间早于今天，就自动取今天。如果以上三个字段有空，这里就是空的
                //预计付款时间
                if (latestPaymentTriggerTime != null) {
                  Long predictPaymentTime =
                      com.xhgj.srm.common.utils.DateUtil.getPredictPaymentTimeByCondition(
                          latestPaymentTriggerTime, order.getBackToBack(),
                          order.getAccountingPeriod());
                  order.setPredictPaymentTime(predictPaymentTime);
                  order.setPaymentStatus(Constants_order.WAIT_APPLY_PAYMENT_TYPE,true);
                }
              });
        }
        updateOrders.add(order);
      });
      //批量保存更新后的订单
      orderRepository.saveAll(updateOrders);
      //更新偏移量，准备处理下一组数据
      offset += batchSize;
    }

  }

  @Override
  public void batchHandleOrderStatusByThreeElements() {
    List<String> orderIdList = orderDao.getAllOrderListHandle();
    int batchSize = 1000;
    int offset = 0;
    while (offset < orderIdList.size()) {
      try {
        // 获取当前批次的订单ID子列表
        List<String> currentBatchIds =
            orderIdList.subList(offset, Math.min(offset + batchSize, orderIdList.size()));
        List<Order> ordersBatch = orderRepository.findAllByIdIn(currentBatchIds);
        List<Order> updateOrders = new ArrayList<>();
        ordersBatch.forEach(order -> {
          //24年之后的订单按照客户回款、供应商开票，签收凭证确认刷新付款状态，
          // 只要没满足这三要素付款状态就为不可付款，满足更改为待申请
          String customerPayback =
              Constants_order.getCustomerPaybackStateNameByReturnProgress(
                  order.getCustomerReturnProgress(), Constants_order.CUSTOMER_PAYBACK_UN);
          String supplierOpenInvoiceStatus =
              StrUtil.blankToDefault(
                  order.getSupplierOpenInvoiceStatus(), Constants.ORDER_INVOICE_STATE_NOT_DONE);
          String signVoucherState = order.getConfirmVoucherAuditStatus();
          if (StrUtil.equals(customerPayback, "已完成") && StrUtil.equals(supplierOpenInvoiceStatus,
              Constants.ORDER_INVOICE_STATE_PASS) && StrUtil.equals(signVoucherState,
              Constants_order.ORDER_ACCEPT_CONSENT)) {
            order.setPaymentStatus(Constants_order.WAIT_APPLY_PAYMENT_TYPE, true);
          } else {
            order.setPaymentStatus(Constants_order.CAN_NOT_PAYMENT_TYPE, true);
          }
          updateOrders.add(order);
        });
        //批量保存更新后的订单
        orderRepository.saveAll(updateOrders);
        //更新偏移量，准备处理下一组数据
        offset += batchSize;
      } catch (Exception e) {
        log.error("批处理程序订单履约根据三要素刷新付款状态有误！", e.getMessage());
      }
    }
  }

  private void doSendDingDingInform(String registrationNumber, String enterpriseName,
      List<String> mobileList, String platformName) {
    String outTrackId = String.valueOf(System.currentTimeMillis());
    if (StrUtil.isNotBlank(registrationNumber) && StrUtil.isNotBlank(enterpriseName)) {
      HashMap<String, Object> map = new HashMap<>();
      map.put("contractNo", registrationNumber);
      map.put("enterpriseName", enterpriseName);
      map.put("signingEntity", enterpriseName);
      map.put("projectName", platformName);
      dingUtils.sendDingTaskRobotCar(
          Constants_DingCarTemplate.DING_CARD_ID_LANDING_BUSINESS_CONTRACT_EXPIRATION_NOTICE,
          dingUtils.getDingUserIdList(mobileList), outTrackId, map);
    }
  }

  @Override
  public void batchHandleContractProjectCategory() {
    List<String> contractIdList = contractDao.getAllIds();
    int batchSize = 1000;
    int offset = 0;
    while (offset < contractIdList.size()) {
      try {
        // 获取当前批次的ID子列表
        List<String> currentBatchIds =
            contractIdList.subList(offset, Math.min(offset + batchSize, contractIdList.size()));
        List<LandingMerchantContract> contractList = repository.findAllById(currentBatchIds);
        List<LandingMerchantContract> updateOrders = new ArrayList<>();
        for (LandingMerchantContract landingMerchantContract : contractList) {
          List<SupplierPerformance> supplierPerformances =
              supplierPerformanceRepository.findAllByLandingContractIdAndState(landingMerchantContract.getId(),
                  Constants.STATE_OK);
          List<String> platformCodeList =
              supplierPerformances.stream().map(SupplierPerformance::getPlatformCode).distinct()
                  .collect(Collectors.toList());
          if (platformCodeList.size() != 1) {
            continue;
          }
          String projectCategory = Optional.ofNullable(platformDao.findByCode(platformCodeList.get(0)))
              .map(Platform::getProjectCategory).orElse(StrUtil.EMPTY);
          landingMerchantContract.setProjectCategory(projectCategory);
          updateOrders.add(landingMerchantContract);
        }
        //批量保存更新后的数据
        landingMerchantContractRepository.saveAll(updateOrders);
        //更新偏移量，准备处理下一组数据
        offset += batchSize;
      } catch (Exception e) {
        log.error("批处理程序合同列表刷项目大类有误！", e.getMessage());
      }

    }
  }

}





