package com.xhgj.srm.api.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.purchase.inforecord.PurchaseInfoRecordAuditStatusEnum;
import com.xhgj.srm.jpa.entity.PurchaseInfoRecord;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/12/29 15:04
 */
@Data
@NoArgsConstructor
public class PurchaseInfoRecordTableDTO {
  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("组织名称（采购组织）")
  private String orgName;

  @ApiModelProperty(value = "计划交货时间", example = "2天")
  private String deliveryDays;

  @ApiModelProperty("部门名称（采购部门）")
  private String deptName;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("物料编码")
  private String productCode;

  @ApiModelProperty("物料名称")
  private String productName;

  @ApiModelProperty("物料品牌名")
  private String productBrandName;

  @ApiModelProperty("物料规格型号")
  private String productManuCode;

  @ApiModelProperty(value = "物料税率", example = "13%")
  private String productTaxRate;

  @ApiModelProperty("含税单价")
  private BigDecimal productTaxPrice;

  @ApiModelProperty(value = "物料单位", example = "个")
  private String productUnit;

  @ApiModelProperty(value = "物料币别（货币码）", example = "人民币")
  private String productCurrency;

  @ApiModelProperty(value = "有效期起始日", example = "2023-05-16")
  private String validDateBegin;

  @ApiModelProperty(value = "有效期截止日", example = "9999-12-31")
  private String validDateEnd;

  @ApiModelProperty("ERP 编码（SAP记录编号）")
  private String erpCode;
  /**
   * #{@link Constants#PURCHASE_PRICE_TYPE_SIGN_COST}
   */
  @ApiModelProperty("价格类型")
  private String priceType;

  @ApiModelProperty("钉钉审批流程实例id")
  private String processInstanceId;

  /**
   * 审核状态 {@link com.xhgj.srm.common.enums.purchase.inforecord.PurchaseInfoRecordAuditStatusEnum}
   */
  @ApiModelProperty("审核状态")
  private String auditStatus;
  @ApiModelProperty("审批意见")
  private String approvalOpinion;

  @ApiModelProperty("物料描述")
  private String description;

  /** 计划交货日拼接模板 */
  private static final String DELIVERY_DAYS_TEMPLATE = "{}天";

  /** 日期类型格式 */
  private static final String DATE_PATTERN = DatePattern.NORM_DATE_PATTERN;

  public PurchaseInfoRecordTableDTO(
      PurchaseInfoRecord purchaseInfoRecord, String orgName, String supplierName) {
    this.orgName = orgName;
    this.supplierName = supplierName;
    this.id = purchaseInfoRecord.getId();
    this.deliveryDays =
        StrUtil.format(DELIVERY_DAYS_TEMPLATE, purchaseInfoRecord.getDeliveryDays());
    this.deptName = purchaseInfoRecord.getDeptName();
    this.productCode = purchaseInfoRecord.getProductCode();
    this.productName = purchaseInfoRecord.getProductName();
    this.productBrandName = purchaseInfoRecord.getProductBrandName();
    this.productManuCode = purchaseInfoRecord.getProductManuCode();
    this.productTaxRate = purchaseInfoRecord.getProductTaxRate();
    this.productTaxPrice = purchaseInfoRecord.getProductTaxPrice();
    this.productUnit = purchaseInfoRecord.getProductUnit();
    this.productCurrency = purchaseInfoRecord.getProductCurrency();
    if (purchaseInfoRecord.getValidDateBegin() != null) {
      this.validDateBegin =
          DateUtil.date(purchaseInfoRecord.getValidDateBegin()).toString(DATE_PATTERN);
    }
    if (purchaseInfoRecord.getValidDateBegin() == null) {
      this.validDateBegin = StrUtil.EMPTY;
    }
    if (purchaseInfoRecord.getValidDateEnd() != null) {
      this.validDateEnd =
          DateUtil.date(purchaseInfoRecord.getValidDateEnd()).toString(DATE_PATTERN);
    }
    if (purchaseInfoRecord.getValidDateEnd() == null) {
      this.validDateEnd = StrUtil.EMPTY;
    }
    this.erpCode = purchaseInfoRecord.getErpCode();
    this.priceType = purchaseInfoRecord.getPriceType();
    this.processInstanceId = purchaseInfoRecord.getProcessInstanceId();
    PurchaseInfoRecordAuditStatusEnum.fromKey(purchaseInfoRecord.getAuditStatus())
        .ifPresent(auditStatusEnum -> this.auditStatus = auditStatusEnum.getDescription());
    this.approvalOpinion = StrUtil.emptyIfNull(purchaseInfoRecord.getApprovalOpinion());
    this.description = purchaseInfoRecord.getDescription();
  }
}
