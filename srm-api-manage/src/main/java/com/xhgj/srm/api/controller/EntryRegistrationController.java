package com.xhgj.srm.api.controller;

import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDetailDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationLandingMerchantAddParam;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationOrderAddParam;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationOrderSubmitAuditParam;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationPageExport;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationPageParams;
import com.xhgj.srm.api.service.EntryRegistrationService;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/** 准入报备单相关api */
@RestController
@RequestMapping("entry-registration")
@Api(tags = {"准入报备单相关api"})
@Slf4j
public class EntryRegistrationController extends AbstractRestController {

  @Resource private EntryRegistrationService service;

  @ApiOperation(value = "准入报备单新增", notes = "准入报备单新增")
  @PostMapping(value = "/add")
  @ResponseBody
  public ResultBean<Boolean> add(@Validated @RequestBody EntryRegistrationOrderAddParam param) {
    return new ResultBean<>(service.addRef(param));
  }

  @ApiOperation(value = "准入报备单提交审核", notes = "准入报备单提交审核")
  @PostMapping(value = "/submit-audit")
  public ResultBean<Boolean> submitAudit(
      @Validated @RequestBody EntryRegistrationOrderSubmitAuditParam param) {
    return new ResultBean<>(service.batchSubmitAudit(param));
  }


  @ApiOperation("入住报备单分页列表")
  @ApiResponses(@ApiResponse(code = 200, message = "成功", response = EntryRegistrationDTO.class))
  @GetMapping(value = "/getEntryRegistrationPage")
  public ResultBean<PageResult<EntryRegistrationDTO>> entryRegistrationPageList(EntryRegistrationPageParams entryRegistrationPageParams) {
    return new ResultBean<>(
        service.entryRegistrationPageListRef(entryRegistrationPageParams));
  }

  /**
   * 入驻报备单导出
   */
  @ApiOperation("入驻报备单导出")
  @PostMapping("/export")
  public ResultBean<Boolean> export(@RequestBody EntryRegistrationPageExport exportForm) {
    service.exportEntryRegistration(exportForm);
    return new ResultBean<>(true);
  }

  /**
   * 入住报备单详情
   * @param id
   * @return
   */
  @ApiOperation("入住报备单详情")
  @ApiImplicitParams(
      @ApiImplicitParam(name = "registrationNumber", value = "报备单号", required = true))
  @GetMapping("/getEntryRegistrationDetail")
  public ResultBean<EntryRegistrationDetailDTO> getEntryRegistrationDetail(
      @RequestParam @NotBlank(message = "入住报备单ID为空") String id) {
    return new ResultBean<>(service.getEntryRegistrationDetailRef(id));
  }

  @ApiOperation("入住报备链接")
  @ApiImplicitParams(
      @ApiImplicitParam(name = "registrationNumber", value = "报备单号", required = true))
  @GetMapping("/checkInReportLink")
  public ResultBean<Boolean> checkInReportLink(
      @RequestParam @NotBlank(message = "入住报备ID为空") String id) {
    service.checkInReportLinkRef(id);
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "新增入驻报备单供应商信息", notes = "新增入驻报备单供应商信息")
  @PostMapping(value = "/landing-merchant/add")
  public ResultBean<Boolean> landingMerchantAdd(
      @RequestBody @Validated EntryRegistrationLandingMerchantAddParam param) {
    return new ResultBean<>(service.landingMerchantAddRef(param));
  }


  @ApiOperation(value = "入驻报备单供 H5 详情", notes = "入驻报备单供 H5 详情")
  @GetMapping(value = "/landing-merchant/detail")
  public ResultBean<EntryRegistrationDetailDTO> settlementRegistrationFormForH5Details(
      @RequestParam @Validated String id) {
    return new ResultBean<>(service.settlementRegistrationFormForH5Details(id));
  }

  @ApiOperation(value = "保存并提交报备单")
  @PostMapping(value = "/addAndSubmit")
  public ResultBean<Boolean> addAndSubmit(@RequestBody @Validated EntryRegistrationOrderAddParam param) {
    return new ResultBean<>(service.addAndSubmitRef(param));
  }

  @ApiOperation(value = "删除报备单--含电商供应商信息")
  @GetMapping(value = "/delete")
  public ResultBean<Boolean> deleteWithLandingMerchantInfo(@RequestParam("ids") @NotEmpty List<String> ids) {
    service.deleteWithLandingMerchantInfo(ids,getUser());
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "复制报备单")
  @GetMapping(value = "/copy")
  public ResultBean<Boolean> copy(
      @RequestParam("id") @NotBlank String id,
      @RequestParam("userGroup") @NotBlank String userGroup) {
    service.copyEntryRegistrationOrder(id, getUser(), userGroup);
    return new ResultBean<>(true);
  }

  @ApiOperation("批处理程序供应商账号里履约信息中的联系人为空数据赋值")
  @GetMapping("/batchUpdateSupplierPerformanceContactsData")
  public ResultBean<Boolean> batchUpdateSupplierPerformanceContactsData() {
    service.batchUpdateSupplierPerformanceContactsData();
    return new ResultBean<>();
  }

  @ApiOperation("批处理程序供应商账号里履约信息中的业务负责人为空数据赋值")
  @GetMapping("/batchUpdateSupplierPerformanceBusinessLeaderData")
  public ResultBean<Boolean> batchUpdateSupplierPerformanceBusinessLeaderData() {
    service.batchUpdateSupplierPerformanceBusinessLeaderData();
    return new ResultBean<>();
  }

  @ApiOperation("批处理程序供应商账号里履约信息中的对接采购员为空数据赋值")
  @GetMapping("/batchUpdateSupplierPerformanceDockingPurchaseErpCode")
  public ResultBean<Boolean> batchUpdateSupplierPerformanceDockingPurchaseErpCode() {
    service.batchUpdateSupplierPerformanceDockingPurchaseErpCode();
    return new ResultBean<>();
  }
}
