package com.xhgj.srm.api.controller.v2Mix;

import com.xhgj.srm.api.controller.AbstractRestController;
import com.xhgj.srm.api.dto.financial.voucher.UpdateAdvanceReversalParam;
import com.xhgj.srm.api.dto.payment.apply.record.PaymentApplyRecordDetailVO;
import com.xhgj.srm.api.dto.payment.apply.record.PaymentApplyRecordPageExportParam;
import com.xhgj.srm.api.dto.payment.apply.record.PaymentApplyRecordPageParam;
import com.xhgj.srm.api.dto.payment.apply.record.PaymentApplyRecordPageVO;
import com.xhgj.srm.api.service.PaymentApplyRecordService;
import com.xhgj.srm.jpa.dto.DrawApplyAddParams;
import com.xhgj.srm.jpa.dto.DrawApplyDetailDTO;
import com.xhgj.srm.jpa.dto.PaymentAdvanceAddParams;
import com.xhgj.srm.jpa.dto.PaymentAdvanceDTO;
import com.xhgj.srm.jpa.dto.PaymentAdvanceDetailDTO;
import com.xhgj.srm.jpa.dto.payment.apply.record.PaymentApplyStatistics;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.service.SharePaymentApplyService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.repeat.annotation.RepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import java.util.concurrent.locks.ReentrantLock;

@RestController
@RequestMapping("/paymentApplyRecord")
@Api(tags = {"付款申请记录相关api"})
@Slf4j
public class PaymentApplyRecordController extends AbstractRestController {
  @Resource
  private PaymentApplyRecordService paymentApplyRecordService;
  @Resource
  private SharePaymentApplyService  sharePaymentApplyService;

  /**
   * 新增编辑提款申请锁
   */
  private final ReentrantLock PAYMENT_APPLY_RECORD_LOCK = new ReentrantLock();

  @ApiOperation(value = "回显预付申请信息", notes = "回显预付申请信息")
  @GetMapping(value = "/getPaymentAdvance")
  public ResultBean<PaymentAdvanceDTO> getPaymentAdvance(@RequestParam("orderId") @ApiParam("订单id") String orderId) {
    return new ResultBean<>(paymentApplyRecordService.getPaymentAdvance(orderId));
  }

  @ApiOperation(value = "新增编辑预付申请", notes = "新增编辑预付申请")
  @PostMapping(value = "/addOrUpdatePaymentApplyRecord")
  @RepeatSubmit
  public ResultBean<Boolean> addOrUpdatePaymentApplyRecord(@RequestBody PaymentAdvanceAddParams params) {
    PAYMENT_APPLY_RECORD_LOCK.lock();
    try {
      User user = getUser();
      params.setApplyMan(user.getRealName());
      params.setApplyManCode(user.getCode());
      paymentApplyRecordService.addOrUpdatePaymentApplyRecord(params);
      return new ResultBean<>();
    }finally {
      PAYMENT_APPLY_RECORD_LOCK.unlock();
    }
  }

  @ApiOperation(value = "预付申请详情", notes = "预付申请详情")
  @GetMapping(value = "/getPaymentAdvanceDetail")
  public ResultBean<PaymentAdvanceDetailDTO> getPaymentAdvanceDetail(@RequestParam("id") @ApiParam("申请id") String id) {
    return new ResultBean<>(paymentApplyRecordService.getPaymentAdvanceDetail(id));
  }

  @ApiOperation(value = "新增编辑提款申请", notes = "新增编辑提款申请")
  @PostMapping(value = "/addOrUpdateDrawApply")
  @RepeatSubmit
  public ResultBean<Boolean> addOrUpdateDrawApply(@RequestBody DrawApplyAddParams params) {
    params.setApplyMan(getUser().getRealName());
    params.setApplyManCode(getUser().getCode());
    paymentApplyRecordService.addOrUpdateDrawApply(params);
    return new ResultBean<>();
  }

  @ApiOperation(value = "提款申请详情", notes = "提款申请详情")
  @GetMapping(value = "/getDrawApplyDetail")
  public ResultBean<DrawApplyDetailDTO> getDrawApplyDetail(@RequestParam("id") @ApiParam("申请id") String id) {
    return new ResultBean<>(paymentApplyRecordService.getDrawApplyDetail(id));
  }

  @ApiOperation(value = "放弃", notes = "放弃")
  @PostMapping(value = "/giveUpApply")
  public ResultBean<Boolean> giveUpApply(@RequestParam("id") @ApiParam("申请id") String id) {
    paymentApplyRecordService.giveUpApply(id);
    return new ResultBean<>();
  }

  @ApiOperation(value = "申请记录列表", notes = "申请记录列表", tags = "0.5.0")
  @GetMapping(value = "/page")
  @ResponseBody
  public ResultBean<PageResult<PaymentApplyRecordPageVO>> getPage(
      @Validated PaymentApplyRecordPageParam param
  ) {
    return new ResultBean<>(paymentApplyRecordService.getPage(param));
  }

  @ApiOperation(value = "申请记录统计", notes = "申请记录统计")
  @GetMapping(value = "/statistics")
  @ResponseBody
  public ResultBean<PaymentApplyStatistics> getStatistics(PaymentApplyRecordPageParam form) {
    return new ResultBean<>(paymentApplyRecordService.getStatisticsRef(form));
  }

  @ApiOperation(value = "申请记录详情", notes = "申请记录详情")
  @GetMapping(value = "/details")
  @ResponseBody
  public ResultBean<PaymentApplyRecordDetailVO> getDetails(@NotBlank String id) {
    return new ResultBean<>(paymentApplyRecordService.getDetails(id));
  }

  @ApiOperation(value = "同步sap付款状态", notes = "同步sap付款状态")
  @PostMapping(value = "/syncSapPayState")
  @ResponseBody
  public ResultBean<Boolean> syncSapPayState() {
    return new ResultBean<>(paymentApplyRecordService.syncSapPayState());
  }

  @ApiOperation(value = "修改预付申请冲销", notes = "修改预付申请冲销")
  @PostMapping(value = "/advance/reversal")
  public ResultBean<Boolean> updateAdvanceReversal(@RequestBody @Valid UpdateAdvanceReversalParam param) {
    param.setCurrentUser(getUser());
    paymentApplyRecordService.updateAdvanceReversal(param);
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "查询采购订单是否有审核中或驳回的预付申请")
  @GetMapping(value = "/checkPurchaseOrderAdvancePayment")
  public ResultBean<Boolean> checkPurchaseOrderAdvancePayment(@RequestParam("purchaseOrderCode") String purchaseOrderCode) {
    throw new CheckException("接口已废弃，不需要查询采购订单是否有审核中或驳回的预付申请");
  }

  @ApiOperation(value = "付款申请导出", notes = "付款申请导出")
  @GetMapping(value = "/export")
  public ResultBean<Long> exportCount(@RequestBody PaymentApplyRecordPageExportParam param) {
    return new ResultBean<>( paymentApplyRecordService.exportApplyRecordExcelCount(param));
  }

  /**
   * 付款申请导出
   */
  @ApiOperation(value = "付款申请导出", notes = "付款申请导出")
  @PostMapping(value = "/export")
  public ResultBean<Boolean> export(@RequestBody PaymentApplyRecordPageExportParam param) {
    paymentApplyRecordService.exportApplyRecordExcel(param);
    return new ResultBean<>(true, "操作成功！");
  }

  @ApiOperation("失败重推SAP")
  @GetMapping("syncSAP")
  public ResultBean<Boolean> syncSAP(@RequestParam @NotBlank String id) {
    sharePaymentApplyService.syncSAP(id);
    return new ResultBean<>(true, "操作成功!");
  }
}
