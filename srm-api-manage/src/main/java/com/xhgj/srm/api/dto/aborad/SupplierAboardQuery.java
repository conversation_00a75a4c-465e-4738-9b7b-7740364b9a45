package com.xhgj.srm.api.dto.aborad;

import com.xhgj.srm.api.dto.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/7/11 10:00
 */
@Data
public class SupplierAboardQuery extends BaseQuery {
  @ApiModelProperty(value = "MDM 编码")
  private String mdmCode;
  @ApiModelProperty(value = "企业名称")
  private String enterpriseName;
  @ApiModelProperty(value = "国家")
  private String country;
  @ApiModelProperty(value = "负责人")
  private String purchaserName;
  @ApiModelProperty(value = "企业等级")
  private String enterpriseLevel;
  @ApiModelProperty(value = "时间范围【开始】")
  private Long startTime;
  @ApiModelProperty(value = "时间范围【结束】")
  private Long endTime;
}
