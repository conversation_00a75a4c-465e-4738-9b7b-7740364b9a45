package com.xhgj.srm.api.dto.enterprise;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EnterPriseInfoDetailDTO {

    @ApiModelProperty(value = "公司名")
    private String enterpriseName;

    @ApiModelProperty(value = "法定代表人")
    private String corporate;

    @ApiModelProperty(value = "统一社会信用代码")
    private String uscc;

    @ApiModelProperty(value = "经营状态")
    private String manageType;

    @ApiModelProperty(value = "营业开始时间")
    private String startDate;

    @ApiModelProperty(value = "营业结束时间")
    private String endDate;

    @ApiModelProperty(value = "成立时间")
    private String date;

    @ApiModelProperty(value = "注册资本")
    private String regCapital;

    @ApiModelProperty(value = "工商注册号")
    private String regNo;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "注册地址")
    private String regAddress;

    @ApiModelProperty(value = "实缴资本")
    private String paidCapital;

    @ApiModelProperty(value = "参保人数")
    private String insNum;

    @ApiModelProperty(value = "纳税人识别号")
    private String taxNumber;

    @ApiModelProperty(value = "曾用名")
    private String usedName;

    @ApiModelProperty(value = "登记机关")
    private String regAuthority;

    @ApiModelProperty(value = "组织机构代码")
    private String orgCode;

    @ApiModelProperty(value = "人员规模")
    private String peopleNum;

    @ApiModelProperty(value = "经营范围")
    private String businessScope;

    @ApiModelProperty(value = "英文名")
    private String englishName;


}
