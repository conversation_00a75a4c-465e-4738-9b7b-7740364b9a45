package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.service.OrderService;
import com.xhgj.srm.common.vo.order.OrderNeedPaymentListVO;
import com.xhgj.srm.common.vo.order.OrderNeedPaymentStatistics;
import com.xhgj.srm.dto.order.OrderNeedPaymentListQuery;
import com.xhgj.srm.service.OrderNeedPaymentService;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * OrderNeedPaymentController
 */
@RestController
@RequestMapping("orderNeedPayment")
@Api(tags = {"需付款订单接口"})
@Validated
@Slf4j
public class OrderNeedPaymentController {

  @Resource
  private OrderNeedPaymentService orderNeedPaymentService;
  @Resource
  private OrderService orderService;

  @ApiOperation(value = "分页获取需付款列表")
  @GetMapping(value = "/getPage")
  public ResultBean<PageResult<OrderNeedPaymentListVO>> getPage(
       @Valid OrderNeedPaymentListQuery query) {
    return new ResultBean<>(orderNeedPaymentService.getPage(query));
  }

  @ApiOperation("需付款列表金额合计统计")
  @GetMapping("/getCount")
  public ResultBean<OrderNeedPaymentStatistics> getCount(OrderNeedPaymentListQuery param) {
    return new ResultBean<>(orderNeedPaymentService.getCount(param));
  }

  /**
   * 处理需付款列表旧数据
   */
  @ApiOperation("处理需付款列表旧数据")
  @GetMapping("/handleOldData")
  public ResultBean<Boolean> handleOldData() {
    orderService.handleOldOrderForReceitable();
    return new ResultBean<>(true);
  }

  /**
   * 处理需付款列表旧数据
   */
  @ApiOperation("处理需付款列表旧数据")
  @GetMapping("/handleOldData2")
  public ResultBean<Boolean> handleOldData2() {
    orderService.handleOldData2();
    return new ResultBean<>(true);
  }

  /**
   * 处理需付款列表旧数据
   */
  @ApiOperation("处理需付款列表旧数据")
  @GetMapping("/handleOldData3")
  public ResultBean<Boolean> handleOldData3() {
    orderService.handleOldData3();
    return new ResultBean<>(true);
  }
}
