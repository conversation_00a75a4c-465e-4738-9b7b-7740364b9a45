package com.xhgj.srm.api.dto.inquiry;

import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ApiModel(description = "")
@Data
public class InquiryQueryForm implements BaseDefaultSearchSchemeForm {

  /**
   * 登录人id
   */
  @ApiModelProperty("登录人id")
  private String userId;

  /**
   * 组织编号
   */
  @ApiModelProperty("组织编号")
  private String userGroup;

  /**
   * 供应商名称
   */
  @ApiModelProperty("供应商名称")
  private String enterpriseName;

  /**
   * 商品名称
   */
  @ApiModelProperty("商品名称")
  private String productName;

  /**
   * 品牌中文
   */
  @ApiModelProperty("品牌中文")
  private String brands;

  /**
   * 型号
   */
  @ApiModelProperty("型号")
  private String model;

  /**
   * 含税市场价
   */
  @ApiModelProperty("含税市场价")
  private String marketPrice;

  /**
   * 经销含税成本
   */
  @ApiModelProperty("经销含税成本")
  private String salesPrice;

  /**
   * 报价人姓名
   */
  @ApiModelProperty("报价人姓名")
  private String offererName;

  /**
   * 报价人手机
   */
  @ApiModelProperty("报价人手机")
  private String offererPhone;

  /**
   * 询价开始时间
   */
  @ApiModelProperty("询价开始时间")
  private String inquiryStartDate;

  /**
   * 询价结束时间
   */
  @ApiModelProperty("询价结束时间")
  private String inquiryEndDate;

  /**
   * 询价人
   */
  @ApiModelProperty("询价人")
  private String inquirer;

  /**
   * 方案id
   */
  @ApiModelProperty("方案id")
  private String schemeId;

  /**
   * 当前页
   */
  @ApiModelProperty("当前页")
  private Integer pageNo = 1;

  /**
   * 每页展示数量
   */
  @ApiModelProperty("每页展示数量")
  private Integer pageSize = 10;

  public Integer getPageNo() {
    if (pageNo == null || pageNo < 1) {
      return 1;
    }
    return pageNo;
  }

  public Integer getPageSize() {
    if (pageSize == null || pageSize < 1) {
      return 10;
    }
    return pageSize;
  }

  public Map<String,Object> toQueryMap(List<String> userNameList) {
    Map<String, Object> map = new HashMap<>();
    map.put("userId", userId);
    map.put("userGroup", userGroup);
    map.put("enterpriseName", enterpriseName);
    map.put("productName", productName);
    map.put("brands", brands);
    map.put("model", model);
    map.put("marketPrice", marketPrice);
    map.put("salesPrice", salesPrice);
    map.put("offererName", offererName);
    map.put("offererPhone", offererPhone);
    map.put("startDate", inquiryStartDate);
    map.put("endDate", inquiryEndDate);
    map.put("inquirer", inquirer);
    map.put("pageNo", getPageNo());
    map.put("pageSize", getPageSize());
    map.put("userNameList", userNameList);
    return map;
  }



}
