package com.xhgj.srm.api.dto.supplier;

import com.xhgj.srm.api.dto.aborad.SupplierAboardQuery;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/7/12 18:01
 */
@Data
public class ExportSupplierInGroupParams {

  /**
   * 用户 id
   */
  @ApiModelProperty("用户 id")
  @NotBlank(message = "用户 id 不能为空")
  private String userId;

  /**
   * 组织编码
   */
  @ApiModelProperty("组织编码")
  @NotBlank(message = "组织编码不能空")
  private String userGroup;
  /**
   * 组织内供应商 id
   */
  @ApiModelProperty("组织内供应商商 id ")
  private List<String> supplierInGroupId;
  /**
   * 类型 CHINA | ABROAD | PERSONAL
   */
  @ApiModelProperty("组织类型")
  private String type;
  /**
   * 国内供应商查询参数
   */
  @ApiModelProperty("国内供应商查询参数")
  private SupplierChinaQuery chinaQuery;
  /**
   * 个人供应商查询参数
   */
  @ApiModelProperty("个人供应商查询参数")
  private SupplierPersonQuery personQuery;
  /**
   * 海外供应商查询参数
   */
  @ApiModelProperty("海外供应商查询参数")
  private SupplierAboardQuery abroadQuery;
}
