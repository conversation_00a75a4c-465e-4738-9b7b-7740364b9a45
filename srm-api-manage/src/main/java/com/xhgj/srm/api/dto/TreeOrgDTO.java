package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.entity.Group;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TreeOrgDTO {

    @ApiModelProperty(value = "组织id")
    private String id;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "组织编码")
    private String orgCode;

    @ApiModelProperty(value = "组织ERP编码")
    private String orgErpCode;

    @ApiModelProperty(value = "部门信息")
    private List<TreeDepartDTO> treeDepartDTOList;

    public TreeOrgDTO(Group group) {
        this.id = group.getId();
        this.orgCode = group.getCode();
        this.orgName = group.getName();
        this.orgErpCode = group.getErpCode();
    }

}
