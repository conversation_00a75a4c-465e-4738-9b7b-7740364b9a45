package com.xhgj.srm.api.factory;/**
 * @since 2024/12/5 13:41
 */
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.api.dto.LandingContractPageDTO;
import com.xhgj.srm.api.dto.SupplierPerformanceDTO;
import com.xhgj.srm.api.dto.SupplierPerformanceParam;
import com.xhgj.srm.api.dto.financial.voucher.FinancialVoucherPageParam;
import com.xhgj.srm.api.dto.financial.voucher.FinancialVoucherPageVO;
import com.xhgj.srm.api.dto.payment.apply.record.PaymentApplyRecordPageExportParam;
import com.xhgj.srm.api.dto.payment.apply.record.PaymentApplyRecordPageParam;
import com.xhgj.srm.api.dto.supplier.SupplierChinaDTO;
import com.xhgj.srm.api.dto.supplierUser.SupplierEOrderPerformanceDetail;
import com.xhgj.srm.api.dto.supplierUser.SupplierUserSaveForm;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPaymentTermsParam;
import com.xhgj.srm.dto.bundle.LandingContractBundleDto;
import com.xhgj.srm.dto.supplier.SupplierChangeCreateForm;
import com.xhgj.srm.jpa.dto.financial.voucher.FinancialVoucherDaoPageParam;
import com.xhgj.srm.jpa.dto.payment.apply.record.PaymentApplyRecordDaoPageParam;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderPaymentTermsDaoParam;
import com.xhgj.srm.jpa.entity.EntryRegistrationDiscount;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.LandingContractBundle;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhgj.srm.jpa.entity.OrderReturnDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyRecord;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierChangeRecord;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.SupplierInGroupTemp;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.SupplierTemp;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.map.domain.BaseMapStruct;
import com.xhgj.srm.map.domain.IgnoreFieldContext;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDTO;
import com.xhgj.srm.vo.record.SupplierChangeRecordVo;
import org.mapstruct.BeanMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 *<AUTHOR>
 *@date 2024/12/5 13:41:35
 *@description
 */
@Mapper
public interface MapStructFactory extends BaseMapStruct {

  MapStructFactory INSTANCE = Mappers.getMapper(MapStructFactory.class);

  /**
   * SupplierPerformance to SupplierPerformanceDTO
   * @param supplierPerformance
   * @return
   */
  SupplierPerformanceDTO toSupplierPerformanceDTO(SupplierPerformance supplierPerformance);

  /**
   * update SupplierPerformance
   * @param supplierPerformanceParam
   * @param supplierPerformance
   */
  @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
  @Mapping(target = "signingStatus", ignore = true)
  void updateSupplierPerformance(SupplierPerformanceParam supplierPerformanceParam, @MappingTarget SupplierPerformance supplierPerformance);

  @Named("objectToJsonStr")
  static String objectToJsonStr(Object obj) {
    if (obj == null) {
      return "{}";
    }
    if (obj instanceof String) {
      return (String) obj;
    }
    return JSON.toJSONString(obj);
  }

  /**
   * SupplierChangeRecord to SupplierChangeRecordVo
   * 忽略泛型字段 oldDataObj 和 newDataObj
   * @param supplierChangeRecord
   * @return
   */
  @Mapping(target = "oldDataObj", ignore = true)
  @Mapping(target = "newDataObj", ignore = true)
  SupplierChangeRecordVo toSupplierChangeRecordVo(SupplierChangeRecord supplierChangeRecord);

  /**
   * SupplierChangeCreateForm to SupplierChangeRecord
   * 忽略泛型字段 oldData 和 newData
   */
  @Mapping(target = "oldData", source = "oldData", qualifiedByName = "objectToJsonStr")
  @Mapping(target = "newData", source = "newData", qualifiedByName = "objectToJsonStr")
  SupplierChangeRecord toSupplierChangeRecord(SupplierChangeCreateForm createForm);

  /**
   * FinancialVoucherPageVO to FinancialVoucherPageVO
   * @param financialVoucherPageVO
   * @return
   */
  FinancialVoucherPageVO toFinancialVoucherPageVO(FinancialVoucherPageVO financialVoucherPageVO);

  /**
   * PaymentApplyDetail to PaymentApplyDetail
   * @param paymentApplyDetail
   * @return
   */
  PaymentApplyDetail toPaymentApplyDetail(PaymentApplyDetail paymentApplyDetail);

  /**
   * PaymentApplyDetail to PaymentApplyDetail
   * @param source
   * @return
   */
  @Mapping(target = "id", expression = "java(context != null && context.shouldIgnore(\"id\") ? null: source.getId())")
  PaymentApplyDetail toPaymentApplyDetail(PaymentApplyDetail source, @Context IgnoreFieldContext context);

  /**
   * EntryRegistrationOrder to EntryRegistrationDTO
   * @param entryRegistrationOrder
   * @return
   */
  EntryRegistrationDTO toEntryRegistrationDTO(EntryRegistrationOrder entryRegistrationOrder);

  /**
   * EntryRegistrationOrder to EntryRegistrationOrder
   * @param entryRegistrationOrder
   * @return
   */
  EntryRegistrationOrder toEntryRegistrationOrder(EntryRegistrationOrder entryRegistrationOrder);

  /**
   * EntryRegistrationLandingMerchant to EntryRegistrationLandingMerchant
   * @param entryRegistrationLandingMerchant
   * @return
   */
  EntryRegistrationLandingMerchant toEntryRegistrationLandingMerchant(EntryRegistrationLandingMerchant entryRegistrationLandingMerchant);

  /**
   * EntryRegistrationDiscount to EntryRegistrationDiscount
   * @param entryRegistrationDiscount
   * @return
   */
  EntryRegistrationDiscount toEntryRegistrationDiscount(EntryRegistrationDiscount entryRegistrationDiscount);

  /**
   * File to File
   * @param file
   * @return
   */
  File toFile(File file);

  /**
   * LandingMerchantContract to LandingContractPageDTO
   * @param landingMerchantContract
   * @return
   */
  LandingContractPageDTO toLandingContractPageDTO(LandingMerchantContract landingMerchantContract);

  /**
   * LandingContractBundle to LandingContractBundleDto
   * @param landingContractBundle
   * @return
   */
  LandingContractBundleDto toLandingContractBundleDto(LandingContractBundle landingContractBundle);

  /**
   * LandingMerchantContract to LandingMerchantContract
   * @param landingContractPageDTO
   * @return
   */
  LandingMerchantContract toLandingMerchantContract(LandingContractPageDTO landingContractPageDTO);

  /**
   * updateSupplierInGroup
   */
  @Mapping(target = "id", expression = "java(context != null && context.shouldIgnore(\"id\") ? target.getId() : source.getId())")
  void updateSupplierInGroup(
      SupplierInGroupTemp source,
      @MappingTarget SupplierInGroup target,
      @Context IgnoreFieldContext context
  );

  /**
   * updateSupplierInGroup
   */
  @Mapping(target = "id", expression = "java(context != null && context.shouldIgnore(\"id\") ? target.getId() : source.getId())")
  void updateSupplierInGroupTemp(
      SupplierInGroup source,
      @MappingTarget SupplierInGroupTemp target,
      @Context IgnoreFieldContext context
  );

  /**
   * updateSupplier
   */
  @Mapping(target = "id", expression = "java(context != null && context.shouldIgnore(\"id\") ? target.getId() : source.getId())")
  void updateSupplier(
      SupplierTemp source,
      @MappingTarget Supplier target,
      @Context IgnoreFieldContext context
  );

  /**
   * updateSupplier
   */
  @Mapping(target = "id", expression = "java(context != null && context.shouldIgnore(\"id\") ? target.getId() : source.getId())")
  void updateSupplierTemp(
      Supplier source,
      @MappingTarget SupplierTemp target,
      @Context IgnoreFieldContext context
  );

  /**
   * SupplierPerformanceDTO to SupplierEOrderPerformanceDetail
   * @param supplierPerformanceDTO
   * @return
   */
  SupplierEOrderPerformanceDetail toSupplierEOrderPerformanceDetail(SupplierPerformanceDTO supplierPerformanceDTO);

  /**
   * SupplierUserSaveForm to SupplierUser
   * @param form
   * @return
   */
  @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
  void updateSupplier(SupplierUserSaveForm form, @MappingTarget SupplierUser supplierUser);

  /**
   * FinancialVoucherPageParam to FinancialVoucherDaoPageParam
   * @param source
   * @return
   */
  FinancialVoucherDaoPageParam toFinancialVoucherDaoPageParam(FinancialVoucherPageParam source);


  /**
   * PaymentApplyRecordPageExportParam to PaymentApplyRecordDaoPageParam
   * @param source
   * @return
   */
  PaymentApplyRecordDaoPageParam toPaymentApplyRecordDaoPageParam(PaymentApplyRecordPageExportParam source);

  /**
   * PaymentApplyRecordPageParam to PaymentApplyRecordDaoPageParam
   * @param source
   * @return
   */
  PaymentApplyRecordDaoPageParam toPaymentApplyRecordDaoPageParam(PaymentApplyRecordPageParam source);

  /**
   * FinancialVoucher to FinancialVoucher
   * @param financialVoucher
   * @return
   */
  FinancialVoucher toFinancialVoucher(FinancialVoucher financialVoucher);


  /**
   * PaymentApplyRecord to PaymentApplyRecord
   * @param paymentApplyRecord
   * @return
   */
  PaymentApplyRecord toPaymentApplyRecord(PaymentApplyRecord paymentApplyRecord);


  /**
   * PurchaseOrderPaymentTermsParam to PurchaseOrderPaymentTermsDaoParam
   * @param source
   * @return
   */
  PurchaseOrderPaymentTermsDaoParam toPurchaseOrderPaymentTermsDaoParam(PurchaseOrderPaymentTermsParam source);

  /**
   * OrderReturnDetail to OrderReturnDetail
   * @param orderReturnDetail
   * @return
   */
  OrderReturnDetail toOrderReturnDetail(OrderReturnDetail orderReturnDetail);

  /**
   * OrderReturn to OrderReturn
   * @param orderReturn
   * @return
   */
  OrderReturn toOrderReturn(OrderReturn orderReturn);

  /**
   * OrderDetail to OrderDetail
   * @param orderDetail
   * @return
   */
  OrderDetail toOrderDetail(OrderDetail orderDetail);

  /**
   * Supplier to Supplier
   * @param supplier
   * @return
   */
  Supplier toSupplier(Supplier supplier);

  /**
   * SupplierInGroup to SupplierChinaDTO
   * @param supplierInGroup
   * @return
   */
  SupplierChinaDTO toSupplierChinaDTO(SupplierInGroup supplierInGroup);
}
