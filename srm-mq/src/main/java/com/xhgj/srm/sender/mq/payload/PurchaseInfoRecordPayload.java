package com.xhgj.srm.sender.mq.payload;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/1/2 11:23
 */
@Data
@NoArgsConstructor
public class PurchaseInfoRecordPayload {

  /** 订单编码 */
  private String orderCode;

  /**
   * version上下文
   */
  private String versionContext;

  /** 发送消息的时间 */
  private long msgTime;

  public PurchaseInfoRecordPayload(String orderCode, long msgTime) {
    this.orderCode = orderCode;
    this.msgTime = msgTime;
  }

  public PurchaseInfoRecordPayload(String orderCode, String versionContext, long msgTime) {
    this.orderCode = orderCode;
    this.versionContext = versionContext;
    this.msgTime = msgTime;
  }
}
