package com.xhgj.srm.sender.mq.sender;

import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.sender.mq.QueueEnum;
import com.xhgj.srm.sender.mq.payload.MissionCreatedPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class BatchTaskMqSender {

  @Autowired private AmqpTemplate rabbitTemplate;

  /**
   * @Title: @Description:批量任务处理
   *
   * @param missionId
   * <AUTHOR>
   * @date 2021/8/27 15:45
   * @deprecated 见 {@link QueueEnum#QUEUE_MISSION_CREATED} 注释
   */
  public void toHandleBatchTask(String missionId, String params, String type) {
    rabbitTemplate.convertAndSend(
        QueueEnum.QUEUE_BATCH_TASK_HANDLE.getExchange(),
        QueueEnum.QUEUE_BATCH_TASK_HANDLE.getName(),
        JSONObject.toJSONString(
            MissionCreatedPayload.builder()
                .missionId(missionId)
                .params(params)
                .type(type)
                .build()));
  }

  /**
   * 发送任务创建消息（请勿直接使用该方法，可见 srm-mission-dispatcher 包内的调度类）
   *
   * @param missionId 任务 id
   * @param params 任务参数
   * @param type 任务类型
   */
  public void sendMissionCreatedMessage(String missionId, String params, String type) {
    rabbitTemplate.convertAndSend(
        QueueEnum.QUEUE_MISSION_CREATED.getExchange(),
        QueueEnum.QUEUE_MISSION_CREATED.getName(),
        JSONObject.toJSONString(
            MissionCreatedPayload.builder()
                .missionId(missionId)
                .params(params)
                .type(type)
                .build()));
  }

  /**
   * @Title: @Description:批量任务完成通知
   *
   * @param missionId
   * <AUTHOR>
   * @date 2021/8/27 15:45
   */
  public void toDoneBatchTask(String userId, String missionId, String missionName, String state) {
    JSONObject payload = new JSONObject();
    payload.put("userId", userId);
    payload.put("missionId", missionId);
    payload.put("missionName", missionName);
    payload.put("state", state);
    rabbitTemplate.convertAndSend(
        QueueEnum.QUEUE_BATCH_TASK_DONE.getExchange(),
        QueueEnum.QUEUE_BATCH_TASK_DONE.getName(),
        payload.toString());
  }

  /**
   * @Title: @Description:批量任务完成通知(管理端)
   *
   * @param missionId
   * <AUTHOR>
   * @date 2021/8/27 15:45
   */
  public void toDoneManageBatchTask(
      String userId, String missionId, String missionName, String state) {
    JSONObject payload = new JSONObject();
    payload.put("userId", userId);
    payload.put("missionId", missionId);
    payload.put("missionName", missionName);
    payload.put("state", state);
    rabbitTemplate.convertAndSend(
        QueueEnum.QUEUE_BATCH_MANAGE_TASK_DONE.getExchange(),
        QueueEnum.QUEUE_BATCH_MANAGE_TASK_DONE.getName(),
        payload.toString());
  }


  /**
   * @description: 生成客户开票申请时发消息给OMS
   * @param payload 消息体
   **/
  public void srmToOmsSubmitOrderInvoiceTask(String payload) {
    rabbitTemplate.convertAndSend(QueueEnum.QUEUE_SRM_TO_OMS_SUBMIT_ORDER_INVOICE.getExchange(),
        QueueEnum.QUEUE_SRM_TO_OMS_SUBMIT_ORDER_INVOICE.getName(), payload);
  }
}
